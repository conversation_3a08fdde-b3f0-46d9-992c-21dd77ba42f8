<?php
/**
 * Shahid Episodes API
 * Professional Video Streaming Platform
 */

require_once '../core/Database.php';
require_once '../core/Security.php';
require_once '../core/ApiController.php';
require_once '../models/Episode.php';
require_once '../models/Series.php';

class EpisodesAPI extends ApiController {
    
    public function handleRequest() {
        $route = trim($_SERVER['REQUEST_URI'], '/');
        $segments = explode('/', $route);
        
        // Remove 'api/episodes' from segments
        $segments = array_slice($segments, 2);
        
        $action = $segments[0] ?? null;
        $id = $segments[1] ?? null;
        
        switch ($action) {
            case null:
            case '':
                $this->sendError('Episode ID required', 400);
                break;
            case 'latest':
                $this->getLatestEpisodes();
                break;
            case 'series':
                $this->getEpisodesBySeries($id);
                break;
            default:
                if (is_numeric($action)) {
                    $this->getEpisodeById($action);
                } else {
                    $this->sendError('Invalid episode endpoint', 404);
                }
        }
    }
    
    private function getEpisodeById($id) {
        if (!$this->validateMethod(['GET'])) return;
        
        $episodeModel = new Episode();
        $episode = $episodeModel->getEpisodeDetailsForAPI($id);
        
        if (!$episode) {
            $this->sendError('Episode not found', 404);
            return;
        }
        
        // Check if user has access to this episode
        $user = $this->authenticateUser(false);
        if ($episode['premium'] && $user) {
            $userModel = new User();
            if (!$userModel->hasActiveSubscription($user['id'])) {
                $episode['has_access'] = false;
            } else {
                $episode['has_access'] = true;
            }
        } else {
            $episode['has_access'] = !$episode['premium'];
        }
        
        // Get watch progress if user is authenticated
        if ($user) {
            $watchProgress = $episodeModel->getWatchProgress($user['id'], $id);
            $episode['watch_progress'] = $watchProgress;
        }
        
        // Get series info
        $seriesModel = new Series();
        $series = $seriesModel->findById($episode['series_id']);
        $episode['series'] = [
            'id' => $series['id'],
            'title' => $series['title'],
            'slug' => $series['slug'],
            'poster' => $series['poster']
        ];
        
        // Get next and previous episodes
        $nextEpisode = $episodeModel->getNextEpisode($episode['series_id'], $episode['season_number'], $episode['episode_number']);
        $prevEpisode = $episodeModel->getPreviousEpisode($episode['series_id'], $episode['season_number'], $episode['episode_number']);
        
        $episode['next_episode'] = $nextEpisode;
        $episode['previous_episode'] = $prevEpisode;
        
        // Generate video token if user has access
        if ($episode['has_access'] && $user) {
            $episode['video_token'] = $this->generateVideoToken($id, $user['id']);
        }
        
        // Increment view count
        $episodeModel->incrementViews($id);
        
        $this->sendSuccess($episode);
    }
    
    private function getLatestEpisodes() {
        if (!$this->validateMethod(['GET'])) return;
        
        $limit = min(50, intval($_GET['limit'] ?? 20));
        $episodeModel = new Episode();
        $episodes = $episodeModel->getLatestEpisodes($limit);
        
        $this->sendSuccess($episodes);
    }
    
    private function getEpisodesBySeries($seriesId) {
        if (!$this->validateMethod(['GET'])) return;
        
        if (!$seriesId || !is_numeric($seriesId)) {
            $this->sendError('Valid series ID required', 400);
            return;
        }
        
        $season = intval($_GET['season'] ?? 0);
        $episodeModel = new Episode();
        
        if ($season > 0) {
            $episodes = $episodeModel->getEpisodesBySeason($seriesId, $season);
        } else {
            $episodes = $episodeModel->getEpisodesBySeriesGrouped($seriesId);
        }
        
        $this->sendSuccess($episodes);
    }
}

// Handle the request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

$api = new EpisodesAPI();
$api->handleRequest();
?>
