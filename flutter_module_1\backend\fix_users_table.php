<?php
/**
 * إصلاح جدول المستخدمين بشكل شامل
 * Complete Users Table Fix
 */

echo "<h1>🔧 إصلاح جدول المستخدمين</h1>";

try {
    $pdo = new PDO("mysql:host=localhost;dbname=shahid_platform;charset=utf8mb4", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>✅ تم الاتصال بقاعدة البيانات بنجاح</h3>";
    echo "</div>";
    
    // 1. فحص الهيكل الحالي للجدول
    echo "<h2>🔍 فحص الهيكل الحالي</h2>";
    
    try {
        $stmt = $pdo->query("DESCRIBE users");
        $currentColumns = [];
        $columnDetails = [];
        
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $currentColumns[] = $row['Field'];
            $columnDetails[$row['Field']] = $row;
        }
        
        echo "<p style='color: #2196F3;'>الأعمدة الموجودة حالياً:</p>";
        echo "<ul style='color: #ccc; margin-right: 2rem;'>";
        foreach ($currentColumns as $column) {
            echo "<li><strong>$column</strong> - {$columnDetails[$column]['Type']}</li>";
        }
        echo "</ul>";
        
    } catch (Exception $e) {
        echo "<p style='color: #F44336;'>❌ خطأ في فحص الجدول: " . htmlspecialchars($e->getMessage()) . "</p>";
        exit;
    }
    
    // 2. إضافة الأعمدة المفقودة واحداً تلو الآخر
    echo "<h2>➕ إضافة الأعمدة المفقودة</h2>";
    
    $requiredColumns = [
        'username' => [
            'definition' => 'varchar(50) DEFAULT NULL',
            'after' => 'id'
        ],
        'full_name' => [
            'definition' => 'varchar(100) DEFAULT NULL',
            'after' => 'password'
        ],
        'phone' => [
            'definition' => 'varchar(20) DEFAULT NULL',
            'after' => 'full_name'
        ],
        'avatar' => [
            'definition' => 'varchar(255) DEFAULT NULL',
            'after' => 'phone'
        ],
        'birth_date' => [
            'definition' => 'date DEFAULT NULL',
            'after' => 'avatar'
        ],
        'gender' => [
            'definition' => "enum('male','female','other') DEFAULT NULL",
            'after' => 'birth_date'
        ],
        'country' => [
            'definition' => 'varchar(50) DEFAULT NULL',
            'after' => 'gender'
        ],
        'language' => [
            'definition' => "varchar(10) DEFAULT 'ar'",
            'after' => 'country'
        ],
        'subscription_type' => [
            'definition' => "enum('free','premium','vip') DEFAULT 'free'",
            'after' => 'language'
        ],
        'subscription_start' => [
            'definition' => 'datetime DEFAULT NULL',
            'after' => 'subscription_type'
        ],
        'subscription_end' => [
            'definition' => 'datetime DEFAULT NULL',
            'after' => 'subscription_start'
        ],
        'is_active' => [
            'definition' => 'tinyint(1) DEFAULT 1',
            'after' => 'subscription_end'
        ],
        'is_verified' => [
            'definition' => 'tinyint(1) DEFAULT 0',
            'after' => 'is_active'
        ],
        'verification_token' => [
            'definition' => 'varchar(255) DEFAULT NULL',
            'after' => 'is_verified'
        ],
        'reset_token' => [
            'definition' => 'varchar(255) DEFAULT NULL',
            'after' => 'verification_token'
        ],
        'last_login' => [
            'definition' => 'datetime DEFAULT NULL',
            'after' => 'reset_token'
        ],
        'login_attempts' => [
            'definition' => 'int(11) DEFAULT 0',
            'after' => 'last_login'
        ]
    ];
    
    $addedCount = 0;
    $skippedCount = 0;
    
    foreach ($requiredColumns as $columnName => $columnInfo) {
        if (!in_array($columnName, $currentColumns)) {
            try {
                $sql = "ALTER TABLE users ADD COLUMN `$columnName` {$columnInfo['definition']} AFTER `{$columnInfo['after']}`";
                $pdo->exec($sql);
                echo "<p style='color: #4CAF50;'>✅ تم إضافة عمود: <strong>$columnName</strong></p>";
                $addedCount++;
                
                // إضافة العمود للقائمة الحالية
                $currentColumns[] = $columnName;
                
            } catch (Exception $e) {
                echo "<p style='color: #F44336;'>❌ خطأ في إضافة عمود $columnName: " . htmlspecialchars($e->getMessage()) . "</p>";
            }
        } else {
            echo "<p style='color: #2196F3;'>ℹ️ عمود <strong>$columnName</strong> موجود مسبقاً</p>";
            $skippedCount++;
        }
    }
    
    echo "<p style='color: #FF9800;'>📊 تم إضافة $addedCount عمود، $skippedCount عمود موجود مسبقاً</p>";
    
    // 3. إضافة فهرس فريد لـ username إذا لم يكن موجوداً
    echo "<h2>🔑 إضافة الفهارس</h2>";
    
    try {
        // التحقق من وجود فهرس username
        $stmt = $pdo->query("SHOW INDEX FROM users WHERE Column_name = 'username'");
        if ($stmt->rowCount() == 0) {
            $pdo->exec("ALTER TABLE users ADD UNIQUE KEY `unique_username` (`username`)");
            echo "<p style='color: #4CAF50;'>✅ تم إضافة فهرس فريد لـ username</p>";
        } else {
            echo "<p style='color: #2196F3;'>ℹ️ فهرس username موجود مسبقاً</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: #FF9800;'>⚠️ تحذير في إضافة فهرس username: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
    
    // 4. تحديث البيانات الموجودة
    echo "<h2>🔄 تحديث البيانات الموجودة</h2>";
    
    try {
        // تحديث username للمستخدمين الذين لا يملكونه
        $stmt = $pdo->query("SELECT id, email FROM users WHERE username IS NULL OR username = ''");
        $usersToUpdate = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        foreach ($usersToUpdate as $user) {
            $username = explode('@', $user['email'])[0];
            // التأكد من عدم تكرار username
            $counter = 1;
            $originalUsername = $username;
            
            while (true) {
                $checkStmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE username = ? AND id != ?");
                $checkStmt->execute([$username, $user['id']]);
                
                if ($checkStmt->fetchColumn() == 0) {
                    break;
                }
                
                $username = $originalUsername . $counter;
                $counter++;
            }
            
            $updateStmt = $pdo->prepare("UPDATE users SET username = ? WHERE id = ?");
            $updateStmt->execute([$username, $user['id']]);
        }
        
        if (count($usersToUpdate) > 0) {
            echo "<p style='color: #4CAF50;'>✅ تم تحديث username لـ " . count($usersToUpdate) . " مستخدم</p>";
        }
        
        // تحديث full_name للمستخدمين الذين لا يملكونه
        $pdo->exec("UPDATE users SET full_name = username WHERE full_name IS NULL OR full_name = ''");
        
        // تحديث القيم الافتراضية
        $pdo->exec("UPDATE users SET language = 'ar' WHERE language IS NULL OR language = ''");
        $pdo->exec("UPDATE users SET subscription_type = 'free' WHERE subscription_type IS NULL OR subscription_type = ''");
        $pdo->exec("UPDATE users SET is_active = 1 WHERE is_active IS NULL");
        $pdo->exec("UPDATE users SET is_verified = 0 WHERE is_verified IS NULL");
        $pdo->exec("UPDATE users SET login_attempts = 0 WHERE login_attempts IS NULL");
        
        echo "<p style='color: #4CAF50;'>✅ تم تحديث البيانات الافتراضية</p>";
        
    } catch (Exception $e) {
        echo "<p style='color: #F44336;'>❌ خطأ في تحديث البيانات: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
    
    // 5. إنشاء المستخدمين التجريبيين
    echo "<h2>👤 إنشاء المستخدمين التجريبيين</h2>";
    
    $testUsers = [
        [
            'username' => 'admin',
            'email' => '<EMAIL>',
            'password' => 'admin123',
            'full_name' => 'مدير النظام',
            'subscription_type' => 'vip'
        ],
        [
            'username' => 'testuser',
            'email' => '<EMAIL>',
            'password' => '123456',
            'full_name' => 'مستخدم تجريبي',
            'subscription_type' => 'free'
        ],
        [
            'username' => 'premium',
            'email' => '<EMAIL>',
            'password' => '123456',
            'full_name' => 'مستخدم مميز',
            'subscription_type' => 'premium'
        ]
    ];
    
    foreach ($testUsers as $user) {
        try {
            // التحقق من وجود المستخدم
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE email = ?");
            $stmt->execute([$user['email']]);
            
            if ($stmt->fetchColumn() == 0) {
                $hashedPassword = password_hash($user['password'], PASSWORD_DEFAULT);
                
                $stmt = $pdo->prepare("
                    INSERT INTO users (username, email, password, full_name, subscription_type, is_active, is_verified, language, login_attempts) 
                    VALUES (?, ?, ?, ?, ?, 1, 1, 'ar', 0)
                ");
                
                $stmt->execute([
                    $user['username'],
                    $user['email'],
                    $hashedPassword,
                    $user['full_name'],
                    $user['subscription_type']
                ]);
                
                echo "<p style='color: #4CAF50;'>✅ تم إنشاء مستخدم: <strong>{$user['email']}</strong> / {$user['password']}</p>";
            } else {
                echo "<p style='color: #2196F3;'>ℹ️ المستخدم موجود: <strong>{$user['email']}</strong></p>";
            }
            
        } catch (Exception $e) {
            echo "<p style='color: #F44336;'>❌ خطأ في إنشاء المستخدم {$user['email']}: " . htmlspecialchars($e->getMessage()) . "</p>";
        }
    }
    
    // 6. فحص نهائي للجدول
    echo "<h2>🔍 فحص نهائي للجدول</h2>";
    
    try {
        $stmt = $pdo->query("DESCRIBE users");
        $finalColumns = [];
        
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $finalColumns[] = $row['Field'];
        }
        
        echo "<p style='color: #4CAF50;'>✅ الجدول يحتوي الآن على " . count($finalColumns) . " عمود:</p>";
        echo "<div style='background: rgba(76, 175, 80, 0.1); border: 1px solid rgba(76, 175, 80, 0.3); border-radius: 10px; padding: 1rem; margin: 1rem 0;'>";
        echo "<ul style='color: #ccc; margin-right: 2rem; columns: 2;'>";
        foreach ($finalColumns as $column) {
            echo "<li>$column</li>";
        }
        echo "</ul>";
        echo "</div>";
        
        // عدد المستخدمين
        $stmt = $pdo->query("SELECT COUNT(*) FROM users");
        $userCount = $stmt->fetchColumn();
        echo "<p style='color: #2196F3;'>👥 عدد المستخدمين: <strong>$userCount</strong></p>";
        
    } catch (Exception $e) {
        echo "<p style='color: #F44336;'>❌ خطأ في الفحص النهائي: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
    
    // 7. ملخص نهائي
    echo "<h2>🎉 تم الانتهاء بنجاح!</h2>";
    
    echo "<div style='background: rgba(76, 175, 80, 0.1); border: 1px solid rgba(76, 175, 80, 0.3); border-radius: 10px; padding: 2rem; margin: 2rem 0; text-align: center;'>";
    echo "<h3 style='color: #4CAF50; margin-bottom: 1rem;'>✅ جدول المستخدمين جاهز للاستخدام!</h3>";
    echo "<ul style='text-align: right; color: #ccc; margin-right: 2rem;'>";
    echo "<li>✅ تم إضافة جميع الأعمدة المطلوبة</li>";
    echo "<li>✅ تم تحديث البيانات الموجودة</li>";
    echo "<li>✅ تم إنشاء المستخدمين التجريبيين</li>";
    echo "<li>✅ الجدول جاهز للاستخدام في جميع الصفحات</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>❌ خطأ في الاتصال بقاعدة البيانات:</h3>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

// روابط سريعة للاختبار
echo "<div style='text-align: center; margin: 3rem 0;'>";
echo "<h3 style='color: #E50914; margin-bottom: 2rem;'>🔗 اختبار الصفحات</h3>";

$testPages = [
    ['url' => 'login.php', 'title' => '🔐 تسجيل الدخول', 'color' => '#4CAF50'],
    ['url' => 'profile.php', 'title' => '👤 الملف الشخصي', 'color' => '#2196F3'],
    ['url' => 'settings.php', 'title' => '⚙️ الإعدادات', 'color' => '#FF9800'],
    ['url' => 'fix_php_errors.php', 'title' => '🔧 إصلاح PHP', 'color' => '#9C27B0'],
    ['url' => 'dashboard_live.php', 'title' => '📊 لوحة المراقبة', 'color' => '#E50914']
];

foreach ($testPages as $page) {
    echo "<a href='{$page['url']}' style='background: {$page['color']}; color: white; padding: 1rem 2rem; text-decoration: none; border-radius: 8px; font-weight: bold; margin: 0.5rem; display: inline-block; transition: transform 0.3s ease;' onmouseover='this.style.transform=\"translateY(-3px)\"' onmouseout='this.style.transform=\"translateY(0)\"'>{$page['title']}</a>";
}

echo "</div>";
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 إصلاح جدول المستخدمين - Shahid Platform</title>
    <style>
        body {
            font-family: 'Segoe UI', 'Cairo', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #2d2d2d 100%);
            color: #fff;
            min-height: 100vh;
            padding: 2rem;
            margin: 0;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(47, 47, 47, 0.95);
            border-radius: 20px;
            padding: 3rem;
            border: 2px solid rgba(229, 9, 20, 0.3);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
        }
        h1, h2, h3 { 
            color: #E50914; 
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
        }
        h1 {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 2rem;
            background: linear-gradient(45deg, #E50914, #FF6B35);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        p, li {
            line-height: 1.6;
            margin: 0.5rem 0;
        }
        ul {
            margin: 1rem 0;
        }
        .btn {
            transition: all 0.3s ease;
        }
        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
        }
        @media (max-width: 768px) {
            .container {
                padding: 1rem;
                margin: 1rem;
            }
            h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- المحتوى يتم عرضه من PHP أعلاه -->
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔧 أداة إصلاح جدول المستخدمين جاهزة!');
        });
    </script>
</body>
</html>
