<?php
/**
 * إصلاح مشاكل جدول المستخدمين
 * Fix Users Table Issues
 */

try {
    echo "<h1>👥 إصلاح مشاكل جدول المستخدمين</h1>";
    
    $fixedIssues = [];
    $errors = [];
    
    // الاتصال بقاعدة البيانات
    $pdo = new PDO("mysql:host=localhost;dbname=shahid_platform;charset=utf8mb4", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>🔍 فحص جدول المستخدمين...</h2>";
    
    // 1. فحص وجود جدول users
    $stmt = $pdo->query("SHOW TABLES LIKE 'users'");
    if ($stmt->rowCount() == 0) {
        echo "<h3>🆕 إنشاء جدول المستخدمين...</h3>";
        
        $createUsersTable = "
        CREATE TABLE users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            username VARCHAR(50) UNIQUE,
            email VARCHAR(100) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL,
            role ENUM('user', 'admin', 'moderator') DEFAULT 'user',
            status ENUM('active', 'inactive', 'banned', 'pending') DEFAULT 'active',
            avatar VARCHAR(255) NULL,
            phone VARCHAR(20) NULL,
            birth_date DATE NULL,
            gender ENUM('male', 'female', 'other') NULL,
            country VARCHAR(50) NULL,
            city VARCHAR(50) NULL,
            bio TEXT NULL,
            preferences JSON NULL,
            email_verified BOOLEAN DEFAULT FALSE,
            email_verified_at TIMESTAMP NULL,
            last_login TIMESTAMP NULL,
            last_activity TIMESTAMP NULL,
            login_count INT DEFAULT 0,
            failed_login_attempts INT DEFAULT 0,
            locked_until TIMESTAMP NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_email (email),
            INDEX idx_username (username),
            INDEX idx_role (role),
            INDEX idx_status (status),
            INDEX idx_last_login (last_login),
            INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        
        try {
            $pdo->exec($createUsersTable);
            $fixedIssues[] = "تم إنشاء جدول users بجميع الأعمدة المطلوبة";
            echo "<p style='color: green;'>✅ تم إنشاء جدول users بنجاح!</p>";
        } catch (Exception $e) {
            $errors[] = "فشل في إنشاء جدول users: " . $e->getMessage();
            echo "<p style='color: red;'>❌ فشل في إنشاء الجدول: {$e->getMessage()}</p>";
        }
    } else {
        echo "<h3>📊 فحص بنية جدول المستخدمين الحالي...</h3>";
        
        // فحص الأعمدة الموجودة
        $stmt = $pdo->query("DESCRIBE users");
        $existingColumns = [];
        $columnDetails = [];
        
        echo "<div style='background: #e3f2fd; border: 1px solid #2196f3; color: #0d47a1; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h4>📋 الأعمدة الموجودة حالياً:</h4>";
        echo "<ul>";
        
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $existingColumns[] = $row['Field'];
            $columnDetails[$row['Field']] = $row;
            echo "<li><strong>{$row['Field']}</strong> - {$row['Type']} " . 
                 ($row['Null'] === 'YES' ? '(NULL)' : '(NOT NULL)') . 
                 ($row['Default'] ? " DEFAULT: {$row['Default']}" : '') . "</li>";
        }
        echo "</ul>";
        echo "</div>";
        
        // الأعمدة المطلوبة
        $requiredColumns = [
            'last_login' => [
                'sql' => "ALTER TABLE users ADD COLUMN last_login TIMESTAMP NULL AFTER email_verified_at",
                'description' => "وقت آخر تسجيل دخول"
            ],
            'last_activity' => [
                'sql' => "ALTER TABLE users ADD COLUMN last_activity TIMESTAMP NULL AFTER last_login",
                'description' => "وقت آخر نشاط"
            ],
            'login_count' => [
                'sql' => "ALTER TABLE users ADD COLUMN login_count INT DEFAULT 0 AFTER last_activity",
                'description' => "عدد مرات تسجيل الدخول"
            ],
            'failed_login_attempts' => [
                'sql' => "ALTER TABLE users ADD COLUMN failed_login_attempts INT DEFAULT 0 AFTER login_count",
                'description' => "عدد محاولات الدخول الفاشلة"
            ],
            'locked_until' => [
                'sql' => "ALTER TABLE users ADD COLUMN locked_until TIMESTAMP NULL AFTER failed_login_attempts",
                'description' => "مقفل حتى"
            ],
            'email_verified' => [
                'sql' => "ALTER TABLE users ADD COLUMN email_verified BOOLEAN DEFAULT FALSE AFTER bio",
                'description' => "البريد الإلكتروني مؤكد"
            ],
            'email_verified_at' => [
                'sql' => "ALTER TABLE users ADD COLUMN email_verified_at TIMESTAMP NULL AFTER email_verified",
                'description' => "وقت تأكيد البريد الإلكتروني"
            ],
            'avatar' => [
                'sql' => "ALTER TABLE users ADD COLUMN avatar VARCHAR(255) NULL AFTER status",
                'description' => "صورة المستخدم"
            ],
            'phone' => [
                'sql' => "ALTER TABLE users ADD COLUMN phone VARCHAR(20) NULL AFTER avatar",
                'description' => "رقم الهاتف"
            ],
            'birth_date' => [
                'sql' => "ALTER TABLE users ADD COLUMN birth_date DATE NULL AFTER phone",
                'description' => "تاريخ الميلاد"
            ],
            'gender' => [
                'sql' => "ALTER TABLE users ADD COLUMN gender ENUM('male', 'female', 'other') NULL AFTER birth_date",
                'description' => "الجنس"
            ],
            'country' => [
                'sql' => "ALTER TABLE users ADD COLUMN country VARCHAR(50) NULL AFTER gender",
                'description' => "البلد"
            ],
            'city' => [
                'sql' => "ALTER TABLE users ADD COLUMN city VARCHAR(50) NULL AFTER country",
                'description' => "المدينة"
            ],
            'bio' => [
                'sql' => "ALTER TABLE users ADD COLUMN bio TEXT NULL AFTER city",
                'description' => "نبذة شخصية"
            ],
            'preferences' => [
                'sql' => "ALTER TABLE users ADD COLUMN preferences JSON NULL AFTER bio",
                'description' => "التفضيلات"
            ],
            'updated_at' => [
                'sql' => "ALTER TABLE users ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP AFTER created_at",
                'description' => "وقت آخر تحديث"
            ]
        ];
        
        echo "<h4>🔧 إضافة الأعمدة المفقودة...</h4>";
        
        foreach ($requiredColumns as $column => $config) {
            if (!in_array($column, $existingColumns)) {
                try {
                    $pdo->exec($config['sql']);
                    $fixedIssues[] = "تم إضافة عمود '$column' - {$config['description']}";
                    echo "<p style='color: green;'>✅ تم إضافة عمود: <strong>$column</strong> - {$config['description']}</p>";
                } catch (Exception $e) {
                    $errors[] = "فشل في إضافة عمود '$column': " . $e->getMessage();
                    echo "<p style='color: red;'>❌ فشل في إضافة عمود: <strong>$column</strong> - {$e->getMessage()}</p>";
                }
            } else {
                echo "<p style='color: blue;'>ℹ️ العمود موجود بالفعل: <strong>$column</strong></p>";
            }
        }
    }
    
    // 2. إنشاء فهارس للأداء
    echo "<h3>📈 إنشاء فهارس الأداء...</h3>";
    
    $indexes = [
        "CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)",
        "CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)",
        "CREATE INDEX IF NOT EXISTS idx_users_role ON users(role)",
        "CREATE INDEX IF NOT EXISTS idx_users_status ON users(status)",
        "CREATE INDEX IF NOT EXISTS idx_users_last_login ON users(last_login)",
        "CREATE INDEX IF NOT EXISTS idx_users_created_at ON users(created_at)",
        "CREATE INDEX IF NOT EXISTS idx_users_last_activity ON users(last_activity)"
    ];
    
    foreach ($indexes as $index) {
        try {
            $pdo->exec($index);
            echo "<p style='color: green;'>✅ تم إنشاء فهرس</p>";
        } catch (Exception $e) {
            echo "<p style='color: blue;'>ℹ️ الفهرس موجود بالفعل أو تم تخطيه</p>";
        }
    }
    $fixedIssues[] = "تم إنشاء جميع الفهارس المطلوبة";
    
    // 3. إضافة بيانات تجريبية
    echo "<h3>👤 فحص وإضافة المستخدمين التجريبيين...</h3>";
    
    try {
        // فحص وجود مستخدمين
        $stmt = $pdo->query("SELECT COUNT(*) FROM users");
        $userCount = $stmt->fetchColumn();
        
        if ($userCount == 0) {
            echo "<h4>🆕 إضافة مستخدمين تجريبيين...</h4>";
            
            $sampleUsers = [
                [
                    'name' => 'مدير النظام',
                    'username' => 'admin',
                    'email' => '<EMAIL>',
                    'password' => password_hash('admin123', PASSWORD_DEFAULT),
                    'role' => 'admin',
                    'status' => 'active',
                    'email_verified' => 1
                ],
                [
                    'name' => 'أحمد محمد',
                    'username' => 'ahmed',
                    'email' => '<EMAIL>',
                    'password' => password_hash('123456', PASSWORD_DEFAULT),
                    'role' => 'user',
                    'status' => 'active',
                    'email_verified' => 1
                ],
                [
                    'name' => 'فاطمة علي',
                    'username' => 'fatima',
                    'email' => '<EMAIL>',
                    'password' => password_hash('123456', PASSWORD_DEFAULT),
                    'role' => 'user',
                    'status' => 'active',
                    'email_verified' => 0
                ],
                [
                    'name' => 'محمد حسن',
                    'username' => 'mohammed',
                    'email' => '<EMAIL>',
                    'password' => password_hash('123456', PASSWORD_DEFAULT),
                    'role' => 'moderator',
                    'status' => 'active',
                    'email_verified' => 1
                ]
            ];
            
            $stmt = $pdo->prepare("
                INSERT INTO users 
                (name, username, email, password, role, status, email_verified, created_at) 
                VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
            ");
            
            foreach ($sampleUsers as $user) {
                try {
                    $stmt->execute([
                        $user['name'],
                        $user['username'],
                        $user['email'],
                        $user['password'],
                        $user['role'],
                        $user['status'],
                        $user['email_verified']
                    ]);
                    echo "<p style='color: green;'>✅ تم إضافة المستخدم: {$user['name']} ({$user['email']})</p>";
                } catch (Exception $e) {
                    echo "<p style='color: orange;'>⚠️ تم تخطي المستخدم {$user['name']}: {$e->getMessage()}</p>";
                }
            }
            
            $fixedIssues[] = "تم إضافة مستخدمين تجريبيين";
        } else {
            echo "<p style='color: blue;'>ℹ️ يوجد $userCount مستخدم في النظام</p>";
            
            // فحص وجود مدير
            $stmt = $pdo->query("SELECT COUNT(*) FROM users WHERE role = 'admin'");
            $adminCount = $stmt->fetchColumn();
            
            if ($adminCount == 0) {
                echo "<h4>👑 إضافة حساب مدير...</h4>";
                
                $stmt = $pdo->prepare("
                    INSERT INTO users 
                    (name, username, email, password, role, status, email_verified, created_at) 
                    VALUES ('مدير النظام', 'admin', '<EMAIL>', ?, 'admin', 'active', 1, NOW())
                ");
                
                if ($stmt->execute([password_hash('admin123', PASSWORD_DEFAULT)])) {
                    $fixedIssues[] = "تم إضافة حساب مدير (<EMAIL> / admin123)";
                    echo "<p style='color: green;'>✅ تم إضافة حساب مدير: <EMAIL> / admin123</p>";
                }
            } else {
                echo "<p style='color: blue;'>ℹ️ يوجد $adminCount حساب مدير</p>";
            }
        }
        
    } catch (Exception $e) {
        $errors[] = "خطأ في إضافة المستخدمين: " . $e->getMessage();
    }
    
    // 4. فحص البنية النهائية
    echo "<h3>🔍 فحص البنية النهائية...</h3>";
    
    try {
        $stmt = $pdo->query("DESCRIBE users");
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h4>📋 البنية النهائية لجدول users:</h4>";
        echo "<table style='width: 100%; border-collapse: collapse;'>";
        echo "<tr style='background: rgba(0,0,0,0.1);'><th style='padding: 8px; border: 1px solid #ccc;'>العمود</th><th style='padding: 8px; border: 1px solid #ccc;'>النوع</th><th style='padding: 8px; border: 1px solid #ccc;'>NULL</th><th style='padding: 8px; border: 1px solid #ccc;'>افتراضي</th></tr>";
        
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            echo "<tr>";
            echo "<td style='padding: 8px; border: 1px solid #ccc;'><strong>{$row['Field']}</strong></td>";
            echo "<td style='padding: 8px; border: 1px solid #ccc;'>{$row['Type']}</td>";
            echo "<td style='padding: 8px; border: 1px solid #ccc;'>{$row['Null']}</td>";
            echo "<td style='padding: 8px; border: 1px solid #ccc;'>{$row['Default']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        echo "</div>";
        
        // إحصائيات المستخدمين
        $stmt = $pdo->query("SELECT 
            COUNT(*) as total,
            SUM(CASE WHEN role = 'admin' THEN 1 ELSE 0 END) as admins,
            SUM(CASE WHEN role = 'user' THEN 1 ELSE 0 END) as users,
            SUM(CASE WHEN role = 'moderator' THEN 1 ELSE 0 END) as moderators,
            SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active,
            SUM(CASE WHEN email_verified = 1 THEN 1 ELSE 0 END) as verified
        FROM users");
        $stats = $stmt->fetch(PDO::FETCH_ASSOC);
        
        echo "<div style='background: #cce5ff; border: 1px solid #99ccff; color: #004085; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h4>📊 إحصائيات المستخدمين:</h4>";
        echo "<ul>";
        echo "<li><strong>إجمالي المستخدمين:</strong> {$stats['total']}</li>";
        echo "<li><strong>المديرين:</strong> {$stats['admins']}</li>";
        echo "<li><strong>المستخدمين العاديين:</strong> {$stats['users']}</li>";
        echo "<li><strong>المشرفين:</strong> {$stats['moderators']}</li>";
        echo "<li><strong>النشطين:</strong> {$stats['active']}</li>";
        echo "<li><strong>البريد مؤكد:</strong> {$stats['verified']}</li>";
        echo "</ul>";
        echo "</div>";
        
    } catch (Exception $e) {
        $errors[] = "فشل في فحص البنية النهائية: " . $e->getMessage();
    }
    
    echo "<br><h2>🎉 تم إصلاح جدول المستخدمين!</h2>";
    
    if (!empty($fixedIssues)) {
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h3>✅ الإصلاحات التي تمت:</h3>";
        echo "<ul>";
        foreach ($fixedIssues as $issue) {
            echo "<li>$issue</li>";
        }
        echo "</ul>";
        echo "</div>";
    }
    
    if (!empty($errors)) {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h3>⚠️ مشاكل تحتاج انتباه:</h3>";
        echo "<ul>";
        foreach ($errors as $error) {
            echo "<li>$error</li>";
        }
        echo "</ul>";
        echo "</div>";
    }
    
    echo "<div style='text-align: center; margin: 2rem 0;'>";
    echo "<a href='admin/users.php' style='background: #E50914; color: white; padding: 1rem 2rem; text-decoration: none; border-radius: 8px; font-weight: bold; margin: 0.5rem;'>👥 إدارة المستخدمين</a>";
    echo "<a href='admin/simple_dashboard.php' style='background: #28a745; color: white; padding: 1rem 2rem; text-decoration: none; border-radius: 8px; font-weight: bold; margin: 0.5rem;'>🎛️ لوحة الإدارة</a>";
    echo "<a href='dashboard_live.php' style='background: #007bff; color: white; padding: 1rem 2rem; text-decoration: none; border-radius: 8px; font-weight: bold; margin: 0.5rem;'>📊 لوحة المراقبة</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px;'>";
    echo "<h3>❌ خطأ في الإصلاح:</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "<p><strong>تفاصيل الخطأ:</strong></p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
    echo "</div>";
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح مشاكل جدول المستخدمين</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #2d2d2d 100%);
            color: #fff;
            min-height: 100vh;
            padding: 2rem;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(47, 47, 47, 0.95);
            border-radius: 20px;
            padding: 3rem;
            border: 2px solid rgba(229, 9, 20, 0.3);
        }
        h1, h2, h3, h4 { color: #E50914; }
        ul { margin: 1rem 0; padding-right: 2rem; }
        ol { margin: 1rem 0; padding-right: 2rem; }
        li { margin: 0.5rem 0; }
        table { margin: 1rem 0; }
        th, td { text-align: right; }
        pre {
            background: rgba(0, 0, 0, 0.3);
            padding: 1rem;
            border-radius: 8px;
            overflow-x: auto;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- المحتوى يتم عرضه من PHP أعلاه -->
    </div>
</body>
</html>
