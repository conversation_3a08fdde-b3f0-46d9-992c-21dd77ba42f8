<?php
/**
 * إعدادات النظام المتقدمة
 * يحتوي على جميع الإعدادات القابلة للتخصيص في النظام
 */

return [
    // إعدادات قاعدة البيانات
    'database' => [
        'host' => 'localhost',
        'username' => 'root',
        'password' => '',
        'database' => 'shahid_platform',
        'charset' => 'utf8mb4',
        'options' => [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
        ]
    ],

    // إعدادات التطبيق العامة
    'app' => [
        'name' => 'Shahid Platform',
        'version' => '2.0.0',
        'environment' => 'development', // development, staging, production
        'debug' => true,
        'timezone' => 'Asia/Riyadh',
        'locale' => 'ar',
        'url' => 'http://localhost/amr2/flutter_module_1',
        'api_url' => 'http://localhost/amr2/flutter_module_1/backend/api',
    ],

    // إعدادات الأمان
    'security' => [
        'csrf_protection' => true,
        'xss_protection' => true,
        'sql_injection_protection' => true,
        'rate_limiting' => [
            'enabled' => true,
            'max_requests' => 100,
            'time_window' => 3600, // ثانية
        ],
        'session' => [
            'lifetime' => 7200, // ثانية
            'secure' => false, // true في HTTPS
            'httponly' => true,
            'samesite' => 'Strict',
        ],
        'password' => [
            'min_length' => 8,
            'require_uppercase' => true,
            'require_lowercase' => true,
            'require_numbers' => true,
            'require_symbols' => false,
        ]
    ],

    // إعدادات رفع الملفات
    'upload' => [
        'max_file_size' => 100 * 1024 * 1024, // 100 MB
        'allowed_image_types' => ['jpg', 'jpeg', 'png', 'gif', 'webp'],
        'allowed_video_types' => ['mp4', 'avi', 'mkv', 'mov', 'wmv'],
        'allowed_document_types' => ['pdf', 'doc', 'docx', 'txt'],
        'upload_path' => 'uploads/',
        'image_optimization' => [
            'enabled' => true,
            'max_width' => 1920,
            'max_height' => 1080,
            'quality' => 85,
        ],
        'virus_scan' => false, // يتطلب ClamAV
    ],

    // إعدادات البريد الإلكتروني
    'email' => [
        'driver' => 'smtp', // smtp, mail, sendmail
        'smtp' => [
            'host' => 'smtp.gmail.com',
            'port' => 587,
            'encryption' => 'tls', // tls, ssl
            'username' => '<EMAIL>',
            'password' => 'your-app-password',
        ],
        'from' => [
            'address' => '<EMAIL>',
            'name' => 'Shahid Platform',
        ],
        'templates_path' => 'templates/email/',
    ],

    // إعدادات التخزين المؤقت
    'cache' => [
        'enabled' => true,
        'driver' => 'file', // file, redis, memcached
        'ttl' => 3600, // ثانية
        'path' => 'cache/',
        'redis' => [
            'host' => '127.0.0.1',
            'port' => 6379,
            'password' => null,
            'database' => 0,
        ],
    ],

    // إعدادات السجلات
    'logging' => [
        'enabled' => true,
        'level' => 'debug', // debug, info, warning, error
        'path' => 'logs/',
        'max_files' => 30,
        'channels' => [
            'application' => 'app.log',
            'security' => 'security.log',
            'performance' => 'performance.log',
            'api' => 'api.log',
        ],
    ],

    // إعدادات الأداء
    'performance' => [
        'monitoring' => [
            'enabled' => true,
            'slow_query_threshold' => 0.1, // ثانية
            'memory_limit_warning' => 80, // نسبة مئوية
        ],
        'optimization' => [
            'gzip_compression' => true,
            'minify_html' => false,
            'minify_css' => false,
            'minify_js' => false,
        ],
    ],

    // إعدادات API
    'api' => [
        'version' => 'v1',
        'rate_limiting' => [
            'enabled' => true,
            'max_requests' => 1000,
            'time_window' => 3600,
        ],
        'authentication' => [
            'required' => false,
            'jwt_secret' => 'your-jwt-secret-key',
            'jwt_expiry' => 86400, // ثانية
        ],
        'cors' => [
            'enabled' => true,
            'allowed_origins' => ['*'],
            'allowed_methods' => ['GET', 'POST', 'PUT', 'DELETE'],
            'allowed_headers' => ['Content-Type', 'Authorization'],
        ],
    ],

    // إعدادات التحليلات
    'analytics' => [
        'google_analytics' => [
            'enabled' => true,
            'tracking_id' => 'G-XXXXXXXXXX',
        ],
        'internal_analytics' => [
            'enabled' => true,
            'track_page_views' => true,
            'track_user_actions' => true,
            'track_performance' => true,
        ],
        'data_retention' => [
            'page_views' => 90, // أيام
            'user_sessions' => 30,
            'performance_logs' => 7,
        ],
    ],

    // إعدادات الإشعارات
    'notifications' => [
        'email_notifications' => true,
        'push_notifications' => false,
        'sms_notifications' => false,
        'real_time_notifications' => true,
        'notification_queue' => [
            'enabled' => false,
            'driver' => 'database', // database, redis
        ],
    ],

    // إعدادات النسخ الاحتياطي
    'backup' => [
        'enabled' => true,
        'auto_backup' => [
            'enabled' => false,
            'frequency' => 'daily', // daily, weekly, monthly
            'time' => '02:00', // HH:MM
        ],
        'retention' => [
            'max_backups' => 10,
            'compress' => true,
        ],
        'include' => [
            'database' => true,
            'files' => true,
            'logs' => false,
        ],
    ],

    // إعدادات SEO
    'seo' => [
        'enabled' => true,
        'sitemap' => [
            'auto_generate' => true,
            'update_frequency' => 'daily',
        ],
        'meta_tags' => [
            'default_title' => 'Shahid - منصة البث الاحترافية',
            'default_description' => 'منصة بث فيديو احترافية تقدم أفضل الأفلام والمسلسلات',
            'default_keywords' => 'أفلام, مسلسلات, بث, فيديو, ترفيه',
        ],
        'structured_data' => [
            'enabled' => true,
            'organization' => [
                'name' => 'Shahid Platform',
                'url' => 'https://shahid.com',
                'logo' => 'https://shahid.com/logo.png',
            ],
        ],
    ],

    // إعدادات المحتوى
    'content' => [
        'pagination' => [
            'movies_per_page' => 20,
            'series_per_page' => 15,
            'episodes_per_page' => 30,
        ],
        'search' => [
            'min_query_length' => 2,
            'max_results' => 50,
            'fuzzy_search' => true,
        ],
        'ratings' => [
            'enabled' => true,
            'scale' => 10, // 1-10
            'require_login' => false,
            'moderation' => false,
        ],
        'comments' => [
            'enabled' => true,
            'require_login' => true,
            'moderation' => true,
            'max_length' => 1000,
        ],
    ],

    // إعدادات المستخدمين
    'users' => [
        'registration' => [
            'enabled' => true,
            'email_verification' => false,
            'admin_approval' => false,
        ],
        'profiles' => [
            'allow_avatar_upload' => true,
            'max_avatar_size' => 2 * 1024 * 1024, // 2 MB
        ],
        'sessions' => [
            'max_concurrent_sessions' => 3,
            'remember_me_duration' => 30 * 24 * 3600, // 30 يوم
        ],
    ],

    // إعدادات الصيانة
    'maintenance' => [
        'enabled' => false,
        'message' => 'الموقع تحت الصيانة. سنعود قريباً!',
        'allowed_ips' => ['127.0.0.1'],
        'retry_after' => 3600, // ثانية
    ],

    // إعدادات التطوير
    'development' => [
        'debug_toolbar' => true,
        'query_logging' => true,
        'error_reporting' => E_ALL,
        'display_errors' => true,
        'profiling' => [
            'enabled' => true,
            'save_profiles' => false,
        ],
    ],

    // إعدادات الإنتاج
    'production' => [
        'debug_toolbar' => false,
        'query_logging' => false,
        'error_reporting' => E_ERROR | E_WARNING,
        'display_errors' => false,
        'profiling' => [
            'enabled' => false,
            'save_profiles' => false,
        ],
    ],

    // إعدادات التكامل مع خدمات خارجية
    'integrations' => [
        'payment_gateways' => [
            'stripe' => [
                'enabled' => false,
                'public_key' => 'pk_test_...',
                'secret_key' => 'sk_test_...',
            ],
            'paypal' => [
                'enabled' => false,
                'client_id' => 'your-paypal-client-id',
                'client_secret' => 'your-paypal-client-secret',
                'sandbox' => true,
            ],
        ],
        'social_login' => [
            'google' => [
                'enabled' => false,
                'client_id' => 'your-google-client-id',
                'client_secret' => 'your-google-client-secret',
            ],
            'facebook' => [
                'enabled' => false,
                'app_id' => 'your-facebook-app-id',
                'app_secret' => 'your-facebook-app-secret',
            ],
        ],
        'cdn' => [
            'enabled' => false,
            'provider' => 'cloudflare', // cloudflare, aws, azure
            'url' => 'https://cdn.shahid.com',
        ],
    ],

    // إعدادات الأمان المتقدمة
    'advanced_security' => [
        'two_factor_auth' => [
            'enabled' => false,
            'methods' => ['email', 'sms', 'app'],
        ],
        'ip_whitelist' => [
            'enabled' => false,
            'admin_ips' => ['127.0.0.1'],
        ],
        'brute_force_protection' => [
            'enabled' => true,
            'max_attempts' => 5,
            'lockout_duration' => 900, // ثانية
        ],
        'content_security_policy' => [
            'enabled' => false,
            'policy' => "default-src 'self'; script-src 'self' 'unsafe-inline';",
        ],
    ],
];
?>
