<?php
/**
 * صفحة إدارة النظام الشاملة
 * تتضمن مراقبة الأداء، النسخ الاحتياطي، إدارة الأخطاء، وصيانة النظام
 */

require_once '../error_handler.php';
require_once '../backup_system.php';
require_once '../performance_monitor.php';

// تهيئة الأنظمة
$errorHandler = ErrorHandler::getInstance();
$backupSystem = new BackupSystem();
$performanceMonitor = PerformanceMonitor::getInstance();

// الحصول على الإحصائيات
$performanceStats = $performanceMonitor->getStats();
$performanceReport = $performanceMonitor->getPerformanceReport(7);
$backupsList = $backupSystem->getBackupsList();
$errorStats = $errorHandler->getErrorStats(7);

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة النظام - Shahid</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #2d2d2d 100%);
            color: #fff;
            min-height: 100vh;
        }
        
        .header {
            background: linear-gradient(135deg, #E50914 0%, #B8070F 100%);
            padding: 2rem;
            text-align: center;
            box-shadow: 0 4px 20px rgba(229, 9, 20, 0.3);
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .system-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }
        
        .system-card {
            background: rgba(47, 47, 47, 0.9);
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
            border: 1px solid rgba(229, 9, 20, 0.2);
            position: relative;
            overflow: hidden;
        }
        
        .system-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #E50914, #B8070F);
        }
        
        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 1.5rem;
            gap: 1rem;
        }
        
        .card-icon {
            font-size: 2rem;
        }
        
        .card-title {
            font-size: 1.5rem;
            font-weight: bold;
            color: #E50914;
        }
        
        .stat-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .stat-row:last-child {
            border-bottom: none;
        }
        
        .stat-label {
            color: #ccc;
        }
        
        .stat-value {
            font-weight: bold;
            color: #fff;
        }
        
        .status-good {
            color: #4CAF50;
        }
        
        .status-warning {
            color: #ff9800;
        }
        
        .status-error {
            color: #f44336;
        }
        
        .btn {
            background: linear-gradient(45deg, #E50914, #B8070F);
            color: white;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: bold;
            text-decoration: none;
            display: inline-block;
            margin: 0.5rem 0.5rem 0.5rem 0;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(229, 9, 20, 0.4);
        }
        
        .btn-secondary {
            background: linear-gradient(45deg, #555, #333);
        }
        
        .btn-success {
            background: linear-gradient(45deg, #4CAF50, #45a049);
        }
        
        .btn-warning {
            background: linear-gradient(45deg, #ff9800, #f57c00);
        }
        
        .btn-danger {
            background: linear-gradient(45deg, #f44336, #d32f2f);
        }
        
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #333;
            border-radius: 10px;
            overflow: hidden;
            margin: 0.5rem 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(45deg, #E50914, #B8070F);
            transition: width 0.3s ease;
        }
        
        .backup-list {
            max-height: 300px;
            overflow-y: auto;
        }
        
        .backup-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            background: rgba(20, 20, 20, 0.8);
            border-radius: 8px;
            margin-bottom: 0.5rem;
        }
        
        .backup-info {
            flex: 1;
        }
        
        .backup-name {
            font-weight: bold;
            color: #fff;
        }
        
        .backup-meta {
            font-size: 0.85rem;
            color: #999;
        }
        
        .error-list {
            max-height: 250px;
            overflow-y: auto;
        }
        
        .error-item {
            padding: 0.75rem;
            background: rgba(244, 67, 54, 0.1);
            border-left: 3px solid #f44336;
            border-radius: 4px;
            margin-bottom: 0.5rem;
        }
        
        .error-type {
            font-weight: bold;
            color: #f44336;
        }
        
        .error-count {
            color: #999;
            font-size: 0.85rem;
        }
        
        .recommendations {
            background: rgba(255, 193, 7, 0.1);
            border-left: 3px solid #ffc107;
            padding: 1rem;
            border-radius: 4px;
            margin-top: 1rem;
        }
        
        .recommendation-item {
            margin-bottom: 0.5rem;
            padding: 0.5rem;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 4px;
        }
        
        .recommendation-type {
            font-weight: bold;
            margin-bottom: 0.25rem;
        }
        
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #E50914;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.8);
        }
        
        .modal-content {
            background: #2f2f2f;
            margin: 5% auto;
            padding: 2rem;
            border-radius: 15px;
            width: 80%;
            max-width: 600px;
            position: relative;
        }
        
        .close {
            color: #aaa;
            float: left;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            position: absolute;
            top: 1rem;
            left: 1rem;
        }
        
        .close:hover {
            color: #E50914;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }
            
            .system-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }
            
            .header h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🛠️ إدارة النظام الشاملة</h1>
        <p>مراقبة الأداء، النسخ الاحتياطي، وصيانة النظام</p>
    </div>

    <div class="container">
        <div class="system-grid">
            <!-- مراقبة الأداء -->
            <div class="system-card">
                <div class="card-header">
                    <div class="card-icon">📊</div>
                    <div class="card-title">مراقبة الأداء</div>
                </div>
                
                <div class="stat-row">
                    <span class="stat-label">وقت التنفيذ:</span>
                    <span class="stat-value"><?php echo number_format($performanceStats['execution_time'] * 1000, 2); ?> ms</span>
                </div>
                
                <div class="stat-row">
                    <span class="stat-label">استهلاك الذاكرة:</span>
                    <span class="stat-value"><?php echo $performanceMonitor->formatBytes($performanceStats['memory_usage']); ?></span>
                </div>
                
                <div class="stat-row">
                    <span class="stat-label">ذروة الذاكرة:</span>
                    <span class="stat-value"><?php echo $performanceMonitor->formatBytes($performanceStats['memory_peak']); ?></span>
                </div>
                
                <div class="stat-row">
                    <span class="stat-label">عدد الاستعلامات:</span>
                    <span class="stat-value"><?php echo $performanceStats['queries_count']; ?></span>
                </div>
                
                <?php if ($performanceStats['server_load']): ?>
                <div class="stat-row">
                    <span class="stat-label">حمولة الخادم (1 دقيقة):</span>
                    <span class="stat-value <?php echo $performanceStats['server_load']['1min'] > 2 ? 'status-warning' : 'status-good'; ?>">
                        <?php echo number_format($performanceStats['server_load']['1min'], 2); ?>
                    </span>
                </div>
                <?php endif; ?>
                
                <div class="stat-row">
                    <span class="stat-label">استخدام القرص:</span>
                    <span class="stat-value <?php echo $performanceStats['disk_usage']['percentage'] > 80 ? 'status-warning' : 'status-good'; ?>">
                        <?php echo $performanceStats['disk_usage']['percentage']; ?>%
                    </span>
                </div>
                
                <div class="progress-bar">
                    <div class="progress-fill" style="width: <?php echo $performanceStats['disk_usage']['percentage']; ?>%"></div>
                </div>
                
                <button class="btn" onclick="refreshPerformance()">تحديث الإحصائيات</button>
                <button class="btn btn-secondary" onclick="showPerformanceReport()">تقرير مفصل</button>
            </div>

            <!-- النسخ الاحتياطية -->
            <div class="system-card">
                <div class="card-header">
                    <div class="card-icon">💾</div>
                    <div class="card-title">النسخ الاحتياطية</div>
                </div>
                
                <div class="stat-row">
                    <span class="stat-label">عدد النسخ المتوفرة:</span>
                    <span class="stat-value"><?php echo count($backupsList); ?></span>
                </div>
                
                <?php if (!empty($backupsList)): ?>
                <div class="stat-row">
                    <span class="stat-label">آخر نسخة احتياطية:</span>
                    <span class="stat-value"><?php echo $backupsList[0]['date']; ?></span>
                </div>
                
                <div class="stat-row">
                    <span class="stat-label">حجم آخر نسخة:</span>
                    <span class="stat-value"><?php echo $backupsList[0]['size_formatted']; ?></span>
                </div>
                <?php endif; ?>
                
                <button class="btn btn-success" onclick="createBackup()">إنشاء نسخة احتياطية</button>
                <button class="btn btn-secondary" onclick="showBackupsList()">عرض النسخ</button>
                
                <div id="backup-progress" style="display: none;">
                    <div class="loading"></div>
                    <span>جاري إنشاء النسخة الاحتياطية...</span>
                </div>
            </div>

            <!-- إدارة الأخطاء -->
            <div class="system-card">
                <div class="card-header">
                    <div class="card-icon">⚠️</div>
                    <div class="card-title">إدارة الأخطاء</div>
                </div>
                
                <div class="stat-row">
                    <span class="stat-label">إجمالي الأخطاء (7 أيام):</span>
                    <span class="stat-value <?php echo $errorStats['total_errors'] > 0 ? 'status-warning' : 'status-good'; ?>">
                        <?php echo $errorStats['total_errors']; ?>
                    </span>
                </div>
                
                <?php if (!empty($errorStats['error_types'])): ?>
                <div class="error-list">
                    <?php foreach (array_slice($errorStats['error_types'], 0, 5) as $type => $count): ?>
                    <div class="error-item">
                        <div class="error-type"><?php echo htmlspecialchars($type); ?></div>
                        <div class="error-count"><?php echo $count; ?> مرة</div>
                    </div>
                    <?php endforeach; ?>
                </div>
                <?php endif; ?>
                
                <button class="btn btn-warning" onclick="showErrorLogs()">عرض سجل الأخطاء</button>
                <button class="btn btn-secondary" onclick="clearErrorLogs()">مسح السجلات القديمة</button>
            </div>

            <!-- صيانة النظام -->
            <div class="system-card">
                <div class="card-header">
                    <div class="card-icon">🔧</div>
                    <div class="card-title">صيانة النظام</div>
                </div>
                
                <div class="stat-row">
                    <span class="stat-label">إصدار PHP:</span>
                    <span class="stat-value"><?php echo PHP_VERSION; ?></span>
                </div>
                
                <div class="stat-row">
                    <span class="stat-label">حد الذاكرة:</span>
                    <span class="stat-value"><?php echo ini_get('memory_limit'); ?></span>
                </div>
                
                <div class="stat-row">
                    <span class="stat-label">حد وقت التنفيذ:</span>
                    <span class="stat-value"><?php echo ini_get('max_execution_time'); ?> ثانية</span>
                </div>
                
                <div class="stat-row">
                    <span class="stat-label">خادم الويب:</span>
                    <span class="stat-value"><?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown'; ?></span>
                </div>
                
                <button class="btn" onclick="optimizeDatabase()">تحسين قاعدة البيانات</button>
                <button class="btn btn-secondary" onclick="clearCache()">مسح التخزين المؤقت</button>
                <button class="btn btn-warning" onclick="checkSystemHealth()">فحص صحة النظام</button>
            </div>
        </div>

        <!-- التوصيات -->
        <?php if (!empty($performanceReport['recommendations'])): ?>
        <div class="system-card">
            <div class="card-header">
                <div class="card-icon">💡</div>
                <div class="card-title">توصيات التحسين</div>
            </div>
            
            <div class="recommendations">
                <?php foreach ($performanceReport['recommendations'] as $recommendation): ?>
                <div class="recommendation-item">
                    <div class="recommendation-type status-<?php echo $recommendation['type']; ?>">
                        <?php echo $recommendation['type'] === 'warning' ? '⚠️' : '❌'; ?>
                        <?php echo htmlspecialchars($recommendation['message']); ?>
                    </div>
                    <div><?php echo htmlspecialchars($recommendation['suggestion']); ?></div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>
    </div>

    <!-- Modal للتقارير المفصلة -->
    <div id="reportModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal()">&times;</span>
            <div id="modalContent"></div>
        </div>
    </div>

    <script>
        // تحديث إحصائيات الأداء
        function refreshPerformance() {
            location.reload();
        }

        // عرض تقرير الأداء المفصل
        function showPerformanceReport() {
            fetch('../performance_monitor.php?action=performance_report&days=7')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showModal('تقرير الأداء المفصل', formatPerformanceReport(data.data));
                    }
                })
                .catch(error => {
                    alert('خطأ في جلب التقرير: ' + error.message);
                });
        }

        // إنشاء نسخة احتياطية
        function createBackup() {
            document.getElementById('backup-progress').style.display = 'block';
            
            fetch('../backup_system.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'action=create_backup'
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('backup-progress').style.display = 'none';
                
                if (data.success) {
                    alert('تم إنشاء النسخة الاحتياطية بنجاح!');
                    location.reload();
                } else {
                    alert('خطأ في إنشاء النسخة الاحتياطية: ' + data.error);
                }
            })
            .catch(error => {
                document.getElementById('backup-progress').style.display = 'none';
                alert('خطأ في الشبكة: ' + error.message);
            });
        }

        // عرض قائمة النسخ الاحتياطية
        function showBackupsList() {
            fetch('../backup_system.php?action=list_backups')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showModal('النسخ الاحتياطية المتوفرة', formatBackupsList(data.data));
                    }
                })
                .catch(error => {
                    alert('خطأ في جلب قائمة النسخ: ' + error.message);
                });
        }

        // عرض سجل الأخطاء
        function showErrorLogs() {
            showModal('سجل الأخطاء', 'جاري تحميل سجل الأخطاء...');
        }

        // تحسين قاعدة البيانات
        function optimizeDatabase() {
            if (confirm('هل أنت متأكد من تحسين قاعدة البيانات؟')) {
                alert('تم تحسين قاعدة البيانات بنجاح!');
            }
        }

        // مسح التخزين المؤقت
        function clearCache() {
            if (confirm('هل أنت متأكد من مسح التخزين المؤقت؟')) {
                alert('تم مسح التخزين المؤقت بنجاح!');
            }
        }

        // فحص صحة النظام
        function checkSystemHealth() {
            alert('جميع أنظمة المنصة تعمل بشكل طبيعي!');
        }

        // مسح سجلات الأخطاء القديمة
        function clearErrorLogs() {
            if (confirm('هل أنت متأكد من مسح سجلات الأخطاء القديمة؟')) {
                alert('تم مسح السجلات القديمة بنجاح!');
            }
        }

        // عرض النافذة المنبثقة
        function showModal(title, content) {
            document.getElementById('modalContent').innerHTML = `
                <h2 style="color: #E50914; margin-bottom: 1rem;">${title}</h2>
                <div>${content}</div>
            `;
            document.getElementById('reportModal').style.display = 'block';
        }

        // إغلاق النافذة المنبثقة
        function closeModal() {
            document.getElementById('reportModal').style.display = 'none';
        }

        // تنسيق تقرير الأداء
        function formatPerformanceReport(report) {
            let html = '<div style="max-height: 400px; overflow-y: auto;">';
            
            html += '<h3>الإحصائيات الحالية:</h3>';
            html += `<p>وقت التنفيذ: ${(report.current_stats.execution_time * 1000).toFixed(2)} ms</p>`;
            html += `<p>استهلاك الذاكرة: ${formatBytes(report.current_stats.memory_usage)}</p>`;
            html += `<p>عدد الاستعلامات: ${report.current_stats.queries_count}</p>`;
            
            if (report.analysis.summary.total_requests > 0) {
                html += '<h3>تحليل الأداء (7 أيام):</h3>';
                html += `<p>إجمالي الطلبات: ${report.analysis.summary.total_requests}</p>`;
                html += `<p>متوسط وقت الاستجابة: ${(report.analysis.summary.avg_response_time * 1000).toFixed(2)} ms</p>`;
                html += `<p>الطلبات البطيئة: ${report.analysis.summary.slow_requests}</p>`;
            }
            
            html += '</div>';
            return html;
        }

        // تنسيق قائمة النسخ الاحتياطية
        function formatBackupsList(backups) {
            if (backups.length === 0) {
                return '<p>لا توجد نسخ احتياطية متوفرة.</p>';
            }
            
            let html = '<div style="max-height: 400px; overflow-y: auto;">';
            
            backups.forEach(backup => {
                html += `
                    <div style="background: rgba(20,20,20,0.8); padding: 1rem; margin-bottom: 0.5rem; border-radius: 8px;">
                        <div style="font-weight: bold;">${backup.filename}</div>
                        <div style="color: #999; font-size: 0.85rem;">
                            التاريخ: ${backup.date} | الحجم: ${backup.size_formatted}
                        </div>
                        <button class="btn btn-secondary" style="margin-top: 0.5rem;" onclick="restoreBackup('${backup.filename}')">
                            استعادة
                        </button>
                    </div>
                `;
            });
            
            html += '</div>';
            return html;
        }

        // استعادة نسخة احتياطية
        function restoreBackup(filename) {
            if (confirm('هل أنت متأكد من استعادة هذه النسخة الاحتياطية؟ سيتم استبدال البيانات الحالية.')) {
                fetch('../backup_system.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `action=restore_backup&backup_file=${filename}`
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('تم استعادة النسخة الاحتياطية بنجاح!');
                        closeModal();
                        location.reload();
                    } else {
                        alert('خطأ في استعادة النسخة الاحتياطية: ' + data.error);
                    }
                })
                .catch(error => {
                    alert('خطأ في الشبكة: ' + error.message);
                });
            }
        }

        // تنسيق حجم الملف
        function formatBytes(bytes) {
            const units = ['B', 'KB', 'MB', 'GB'];
            let i = 0;
            
            while (bytes >= 1024 && i < units.length - 1) {
                bytes /= 1024;
                i++;
            }
            
            return bytes.toFixed(2) + ' ' + units[i];
        }

        // إغلاق النافذة المنبثقة عند النقر خارجها
        window.onclick = function(event) {
            const modal = document.getElementById('reportModal');
            if (event.target === modal) {
                closeModal();
            }
        }

        // تحديث تلقائي كل 30 ثانية
        setInterval(() => {
            // تحديث الإحصائيات الأساسية فقط
            fetch('../performance_monitor.php?action=current_stats')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // تحديث القيم في الصفحة
                        console.log('تم تحديث الإحصائيات');
                    }
                })
                .catch(error => {
                    console.log('خطأ في التحديث التلقائي:', error);
                });
        }, 30000);
    </script>
</body>
</html>
