<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>404 - الصفحة غير موجودة | Shahid Platform</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #2d2d2d 100%);
            color: #fff;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }
        
        .error-container {
            text-align: center;
            max-width: 600px;
            padding: 2rem;
            position: relative;
            z-index: 2;
        }
        
        .error-code {
            font-size: 8rem;
            font-weight: bold;
            background: linear-gradient(45deg, #E50914, #B8070F);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 1rem;
            text-shadow: 0 0 30px rgba(229, 9, 20, 0.5);
            animation: glow 2s ease-in-out infinite alternate;
        }
        
        @keyframes glow {
            from { text-shadow: 0 0 20px rgba(229, 9, 20, 0.5); }
            to { text-shadow: 0 0 40px rgba(229, 9, 20, 0.8); }
        }
        
        .error-title {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            color: #fff;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }
        
        .error-message {
            font-size: 1.2rem;
            color: #ccc;
            margin-bottom: 2rem;
            line-height: 1.6;
        }
        
        .error-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .btn {
            background: linear-gradient(45deg, #E50914, #B8070F);
            color: white;
            padding: 1rem 2rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: bold;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(229, 9, 20, 0.3);
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(229, 9, 20, 0.4);
        }
        
        .btn-secondary {
            background: linear-gradient(45deg, #555, #333);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        }
        
        .btn-secondary:hover {
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
        }
        
        .background-animation {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
            opacity: 0.1;
        }
        
        .floating-icon {
            position: absolute;
            font-size: 2rem;
            color: #E50914;
            animation: float 6s ease-in-out infinite;
        }
        
        .floating-icon:nth-child(1) { top: 10%; left: 10%; animation-delay: 0s; }
        .floating-icon:nth-child(2) { top: 20%; right: 10%; animation-delay: 1s; }
        .floating-icon:nth-child(3) { bottom: 20%; left: 15%; animation-delay: 2s; }
        .floating-icon:nth-child(4) { bottom: 10%; right: 20%; animation-delay: 3s; }
        .floating-icon:nth-child(5) { top: 50%; left: 5%; animation-delay: 4s; }
        .floating-icon:nth-child(6) { top: 60%; right: 5%; animation-delay: 5s; }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
        
        .search-container {
            margin-top: 2rem;
            max-width: 400px;
            margin-left: auto;
            margin-right: auto;
        }
        
        .search-box {
            width: 100%;
            padding: 1rem;
            border: 2px solid rgba(229, 9, 20, 0.3);
            border-radius: 25px;
            background: rgba(45, 45, 45, 0.8);
            color: #fff;
            font-size: 1rem;
            text-align: center;
            outline: none;
            transition: all 0.3s ease;
        }
        
        .search-box:focus {
            border-color: #E50914;
            box-shadow: 0 0 20px rgba(229, 9, 20, 0.3);
        }
        
        .search-box::placeholder {
            color: #999;
        }
        
        .suggestions {
            margin-top: 1.5rem;
            text-align: center;
        }
        
        .suggestions h3 {
            color: #E50914;
            margin-bottom: 1rem;
            font-size: 1.3rem;
        }
        
        .suggestion-links {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            justify-content: center;
        }
        
        .suggestion-link {
            background: rgba(229, 9, 20, 0.1);
            color: #E50914;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            text-decoration: none;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            border: 1px solid rgba(229, 9, 20, 0.3);
        }
        
        .suggestion-link:hover {
            background: rgba(229, 9, 20, 0.2);
            transform: translateY(-2px);
        }
        
        @media (max-width: 768px) {
            .error-code {
                font-size: 5rem;
            }
            
            .error-title {
                font-size: 2rem;
            }
            
            .error-message {
                font-size: 1rem;
            }
            
            .error-actions {
                flex-direction: column;
                align-items: center;
            }
            
            .btn {
                width: 100%;
                max-width: 250px;
            }
        }
    </style>
</head>
<body>
    <div class="background-animation">
        <div class="floating-icon">🎬</div>
        <div class="floating-icon">📺</div>
        <div class="floating-icon">🎭</div>
        <div class="floating-icon">🎪</div>
        <div class="floating-icon">🎨</div>
        <div class="floating-icon">🎵</div>
    </div>
    
    <div class="error-container">
        <div class="error-code">404</div>
        <h1 class="error-title">الصفحة غير موجودة</h1>
        <p class="error-message">
            عذراً، الصفحة التي تبحث عنها غير موجودة أو تم نقلها إلى مكان آخر.
            <br>
            ربما تم حذف الرابط أو كتابته بشكل خاطئ.
        </p>
        
        <div class="search-container">
            <input type="text" class="search-box" placeholder="ابحث عن المحتوى الذي تريده..." id="searchBox">
        </div>
        
        <div class="suggestions">
            <h3>اقتراحات مفيدة:</h3>
            <div class="suggestion-links">
                <a href="/backend/" class="suggestion-link">الصفحة الرئيسية</a>
                <a href="/backend/api/?endpoint=movies" class="suggestion-link">الأفلام</a>
                <a href="/backend/api/?endpoint=series" class="suggestion-link">المسلسلات</a>
                <a href="/backend/api/?endpoint=search&q=popular" class="suggestion-link">المحتوى الشائع</a>
                <a href="/backend/admin/" class="suggestion-link">لوحة الإدارة</a>
            </div>
        </div>
        
        <div class="error-actions">
            <a href="javascript:history.back()" class="btn btn-secondary">
                ← العودة للخلف
            </a>
            <a href="/backend/" class="btn">
                🏠 الصفحة الرئيسية
            </a>
            <a href="/backend/admin/" class="btn btn-secondary">
                ⚙️ لوحة الإدارة
            </a>
        </div>
    </div>
    
    <script>
        // تفعيل البحث
        document.getElementById('searchBox').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                const query = this.value.trim();
                if (query) {
                    window.location.href = `/backend/api/?endpoint=search&q=${encodeURIComponent(query)}`;
                }
            }
        });
        
        // تأثيرات بصرية إضافية
        document.addEventListener('mousemove', function(e) {
            const icons = document.querySelectorAll('.floating-icon');
            icons.forEach((icon, index) => {
                const speed = (index + 1) * 0.0001;
                const x = (e.clientX * speed);
                const y = (e.clientY * speed);
                icon.style.transform += ` translate(${x}px, ${y}px)`;
            });
        });
        
        // رسالة ترحيب
        setTimeout(() => {
            console.log(`
🎬 مرحباً بك في Shahid Platform! 🎬

يبدو أنك وصلت إلى صفحة غير موجودة.
لا تقلق، يمكنك:

1. العودة للصفحة الرئيسية
2. البحث عن المحتوى المطلوب
3. زيارة لوحة الإدارة
4. تصفح الأفلام والمسلسلات

نحن هنا لمساعدتك! 😊
            `);
        }, 1000);
        
        // تحديث عنوان الصفحة ديناميكياً
        let titleIndex = 0;
        const titles = [
            '404 - الصفحة غير موجودة | Shahid Platform',
            '🎬 Shahid Platform - منصة البث الاحترافية',
            '📺 ابحث عن المحتوى المفضل لديك',
            '🎭 اكتشف عالم الترفيه معنا'
        ];
        
        setInterval(() => {
            document.title = titles[titleIndex];
            titleIndex = (titleIndex + 1) % titles.length;
        }, 3000);
    </script>
</body>
</html>
