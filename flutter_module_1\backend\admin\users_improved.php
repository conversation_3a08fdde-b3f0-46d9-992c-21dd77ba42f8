<?php
/**
 * إدارة المستخدمين المحسنة - بدون أخطاء
 * Improved Users Management - Error Free
 */

// بدء الجلسة بشكل آمن
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// تسجيل دخول تلقائي للاختبار
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1;
    $_SESSION['user_name'] = 'مدير النظام';
    $_SESSION['user_role'] = 'admin';
}

// الاتصال بقاعدة البيانات
try {
    $pdo = new PDO("mysql:host=localhost;dbname=shahid_platform;charset=utf8mb4", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (Exception $e) {
    die("خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage());
}

$message = '';
$messageType = '';

// معالجة إضافة مستخدم جديد
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_user'])) {
    $name = trim($_POST['name'] ?? '');
    $username = trim($_POST['username'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';
    $role = $_POST['role'] ?? 'user';
    $status = $_POST['status'] ?? 'active';

    if (!empty($name) && !empty($email) && !empty($password)) {
        try {
            // فحص وجود البريد الإلكتروني
            $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ?");
            $stmt->execute([$email]);
            if ($stmt->fetch()) {
                $message = 'البريد الإلكتروني موجود مسبقاً';
                $messageType = 'error';
            } else {
                // فحص وجود اسم المستخدم (إذا تم إدخاله)
                if (!empty($username)) {
                    $stmt = $pdo->prepare("SELECT id FROM users WHERE username = ?");
                    $stmt->execute([$username]);
                    if ($stmt->fetch()) {
                        $message = 'اسم المستخدم موجود مسبقاً';
                        $messageType = 'error';
                    }
                }

                if (empty($message)) {
                    // إضافة المستخدم
                    $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
                    $stmt = $pdo->prepare("
                        INSERT INTO users (name, username, email, password, role, status, email_verified, created_at)
                        VALUES (?, ?, ?, ?, ?, ?, 1, NOW())
                    ");

                    if ($stmt->execute([$name, $username ?: null, $email, $hashedPassword, $role, $status])) {
                        $message = 'تم إضافة المستخدم بنجاح';
                        $messageType = 'success';
                    } else {
                        $message = 'فشل في إضافة المستخدم';
                        $messageType = 'error';
                    }
                }
            }
        } catch (Exception $e) {
            $message = 'خطأ في إضافة المستخدم: ' . $e->getMessage();
            $messageType = 'error';
        }
    } else {
        $message = 'يرجى ملء جميع الحقول المطلوبة';
        $messageType = 'error';
    }
}

// معالجة تعديل مستخدم
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['edit_user'])) {
    $userId = $_POST['user_id'] ?? 0;
    $name = trim($_POST['edit_name'] ?? '');
    $username = trim($_POST['edit_username'] ?? '');
    $email = trim($_POST['edit_email'] ?? '');
    $role = $_POST['edit_role'] ?? 'user';
    $status = $_POST['edit_status'] ?? 'active';

    if ($userId && !empty($name) && !empty($email)) {
        try {
            // فحص وجود البريد الإلكتروني لمستخدم آخر
            $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ? AND id != ?");
            $stmt->execute([$email, $userId]);
            if ($stmt->fetch()) {
                $message = 'البريد الإلكتروني موجود لمستخدم آخر';
                $messageType = 'error';
            } else {
                // تحديث المستخدم
                $stmt = $pdo->prepare("
                    UPDATE users
                    SET name = ?, username = ?, email = ?, role = ?, status = ?, updated_at = NOW()
                    WHERE id = ?
                ");

                if ($stmt->execute([$name, $username ?: null, $email, $role, $status, $userId])) {
                    $message = 'تم تحديث المستخدم بنجاح';
                    $messageType = 'success';
                } else {
                    $message = 'فشل في تحديث المستخدم';
                    $messageType = 'error';
                }
            }
        } catch (Exception $e) {
            $message = 'خطأ في تحديث المستخدم: ' . $e->getMessage();
            $messageType = 'error';
        }
    } else {
        $message = 'بيانات غير صحيحة';
        $messageType = 'error';
    }
}

// معالجة حذف مستخدم
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['delete_user'])) {
    $userId = $_POST['user_id'] ?? 0;

    if ($userId && $userId != $_SESSION['user_id']) {
        try {
            $stmt = $pdo->prepare("DELETE FROM users WHERE id = ?");
            if ($stmt->execute([$userId])) {
                $message = 'تم حذف المستخدم بنجاح';
                $messageType = 'success';
            } else {
                $message = 'فشل في حذف المستخدم';
                $messageType = 'error';
            }
        } catch (Exception $e) {
            $message = 'خطأ في حذف المستخدم: ' . $e->getMessage();
            $messageType = 'error';
        }
    } else {
        $message = 'لا يمكن حذف هذا المستخدم';
        $messageType = 'error';
    }
}

// إعدادات الصفحة والبحث
$page = max(1, intval($_GET['page'] ?? 1));
$limit = 10;
$offset = ($page - 1) * $limit;
$search = $_GET['search'] ?? '';

// جلب المستخدمين مع معالجة آمنة للأعمدة
try {
    // فحص الأعمدة الموجودة أولاً
    $stmt = $pdo->query("DESCRIBE users");
    $availableColumns = [];
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $availableColumns[] = $row['Field'];
    }

    // تحديد الأعمدة المطلوبة
    $selectColumns = ['id', 'name', 'email', 'role', 'status', 'created_at'];

    // إضافة الأعمدة الاختيارية إذا كانت موجودة
    if (in_array('username', $availableColumns)) {
        $selectColumns[] = 'username';
    }
    if (in_array('last_login', $availableColumns)) {
        $selectColumns[] = 'last_login';
    }
    if (in_array('email_verified', $availableColumns)) {
        $selectColumns[] = 'email_verified';
    }
    if (in_array('updated_at', $availableColumns)) {
        $selectColumns[] = 'updated_at';
    }

    $selectSQL = implode(', ', $selectColumns);

    if (!empty($search)) {
        $stmt = $pdo->prepare("SELECT $selectSQL FROM users WHERE name LIKE ? OR email LIKE ? ORDER BY created_at DESC LIMIT $limit OFFSET $offset");
        $searchTerm = "%$search%";
        $stmt->execute([$searchTerm, $searchTerm]);
        $users = $stmt->fetchAll(PDO::FETCH_ASSOC);

        $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM users WHERE name LIKE ? OR email LIKE ?");
        $stmt->execute([$searchTerm, $searchTerm]);
        $total = $stmt->fetch()['total'];
    } else {
        $stmt = $pdo->prepare("SELECT $selectSQL FROM users ORDER BY created_at DESC LIMIT $limit OFFSET $offset");
        $stmt->execute();
        $users = $stmt->fetchAll(PDO::FETCH_ASSOC);

        $stmt = $pdo->query("SELECT COUNT(*) as total FROM users");
        $total = $stmt->fetch()['total'];
    }

    $totalPages = ceil($total / $limit);
} catch (Exception $e) {
    $users = [];
    $total = 0;
    $totalPages = 0;
    $message = 'خطأ في جلب المستخدمين: ' . $e->getMessage();
    $messageType = 'error';
}

// جلب الإحصائيات
$stats = [];
try {
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM users");
    $stats['total'] = $stmt->fetch()['total'];

    $stmt = $pdo->query("SELECT COUNT(*) as active FROM users WHERE status = 'active'");
    $stats['active'] = $stmt->fetch()['active'];

    $stmt = $pdo->query("SELECT COUNT(*) as admins FROM users WHERE role = 'admin'");
    $stats['admins'] = $stmt->fetch()['admins'];

    $stmt = $pdo->query("SELECT COUNT(*) as today FROM users WHERE DATE(created_at) = CURDATE()");
    $stats['today'] = $stmt->fetch()['today'];

    // إحصائيات إضافية إذا كانت الأعمدة موجودة
    if (in_array('email_verified', $availableColumns)) {
        $stmt = $pdo->query("SELECT COUNT(*) as verified FROM users WHERE email_verified = 1");
        $stats['verified'] = $stmt->fetch()['verified'];
    } else {
        $stats['verified'] = 'غير متاح';
    }

} catch (Exception $e) {
    $stats = ['total' => 0, 'active' => 0, 'admins' => 0, 'today' => 0, 'verified' => 0];
}

// دالة مساعدة للتحقق من وجود عمود
function hasColumn($column, $availableColumns) {
    return in_array($column, $availableColumns);
}

// دالة مساعدة للحصول على قيمة آمنة
function safeGet($array, $key, $default = '') {
    return isset($array[$key]) ? $array[$key] : $default;
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>👥 إدارة المستخدمين - Shahid Admin</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>👥</text></svg>">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', 'Cairo', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #2d2d2d 100%);
            color: #fff;
            line-height: 1.6;
            min-height: 100vh;
        }

        .header {
            background: linear-gradient(135deg, #E50914 0%, #B8070F 100%);
            padding: 2rem;
            text-align: center;
            box-shadow: 0 4px 20px rgba(229, 9, 20, 0.3);
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }

        .header .subtitle {
            opacity: 0.9;
            font-size: 1.2rem;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: linear-gradient(135deg, rgba(47, 47, 47, 0.9) 0%, rgba(35, 35, 35, 0.9) 100%);
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            border: 1px solid rgba(229, 9, 20, 0.2);
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            border-color: rgba(229, 9, 20, 0.5);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #E50914;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            opacity: 0.8;
        }

        .controls-section {
            background: rgba(47, 47, 47, 0.9);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            border: 1px solid rgba(229, 9, 20, 0.2);
        }

        .controls-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .search-form {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .form-group label {
            font-weight: bold;
            color: #E50914;
        }

        .form-control {
            padding: 0.8rem;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.1);
            color: #fff;
            font-size: 1rem;
        }

        .form-control:focus {
            outline: none;
            border-color: #E50914;
            box-shadow: 0 0 0 2px rgba(229, 9, 20, 0.2);
        }

        .btn {
            background: linear-gradient(45deg, #E50914, #B8070F);
            color: white;
            padding: 0.8rem 1.5rem;
            text-decoration: none;
            border-radius: 8px;
            font-weight: bold;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            font-size: 1rem;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(229, 9, 20, 0.3);
        }

        .btn.secondary {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .btn.success { background: linear-gradient(45deg, #28a745, #20c997); }
        .btn.danger { background: linear-gradient(45deg, #dc3545, #c82333); }
        .btn.info { background: linear-gradient(45deg, #17a2b8, #20c997); }
        .btn.small { padding: 0.5rem 1rem; font-size: 0.9rem; }

        .users-table {
            background: rgba(47, 47, 47, 0.9);
            border-radius: 15px;
            overflow: hidden;
            border: 1px solid rgba(229, 9, 20, 0.2);
            margin-bottom: 2rem;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th,
        .table td {
            padding: 1rem;
            text-align: right;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .table th {
            background: rgba(229, 9, 20, 0.1);
            font-weight: bold;
            color: #E50914;
        }

        .table tr:hover {
            background: rgba(229, 9, 20, 0.05);
        }

        .status-badge {
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .status-active { background: #28a745; color: white; }
        .status-inactive { background: #6c757d; color: white; }
        .status-banned { background: #dc3545; color: white; }
        .status-pending { background: #ffc107; color: #000; }

        .role-badge {
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .role-admin { background: #E50914; color: white; }
        .role-moderator { background: #17a2b8; color: white; }
        .role-user { background: #6c757d; color: white; }

        .actions {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .pagination {
            display: flex;
            justify-content: center;
            gap: 0.5rem;
            margin: 2rem 0;
        }

        .pagination a,
        .pagination span {
            padding: 0.8rem 1rem;
            background: rgba(47, 47, 47, 0.9);
            border: 1px solid rgba(229, 9, 20, 0.2);
            border-radius: 8px;
            color: #fff;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .pagination a:hover {
            background: rgba(229, 9, 20, 0.2);
            border-color: rgba(229, 9, 20, 0.5);
        }

        .pagination .current {
            background: #E50914;
            border-color: #E50914;
        }

        .message {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 2rem;
            font-weight: bold;
        }

        .message.success {
            background: rgba(40, 167, 69, 0.2);
            border: 1px solid #28a745;
            color: #28a745;
        }

        .message.error {
            background: rgba(220, 53, 69, 0.2);
            border: 1px solid #dc3545;
            color: #dc3545;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
        }

        .modal-content {
            background: linear-gradient(135deg, #2d2d2d 0%, #1a1a1a 100%);
            margin: 5% auto;
            padding: 2rem;
            border-radius: 15px;
            width: 90%;
            max-width: 500px;
            border: 2px solid rgba(229, 9, 20, 0.3);
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
        }

        .modal-title {
            font-size: 1.5rem;
            color: #E50914;
            font-weight: bold;
        }

        .close {
            color: #aaa;
            font-size: 2rem;
            font-weight: bold;
            cursor: pointer;
            transition: color 0.3s;
        }

        .close:hover {
            color: #E50914;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        @media (max-width: 768px) {
            .container { padding: 1rem; }
            .header h1 { font-size: 2rem; }
            .controls-header { flex-direction: column; align-items: stretch; }
            .search-form { flex-direction: column; }
            .stats-grid { grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); }
            .table { font-size: 0.9rem; }
            .actions { flex-direction: column; }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>👥 إدارة المستخدمين</h1>
        <div class="subtitle">إدارة شاملة لحسابات المستخدمين - منصة شاهد</div>
    </div>

    <div class="container">
        <!-- رسائل النظام -->
        <?php if (!empty($message)): ?>
        <div class="message <?php echo $messageType; ?>">
            <?php echo htmlspecialchars($message); ?>
        </div>
        <?php endif; ?>

        <!-- إحصائيات سريعة -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number"><?php echo number_format($stats['total']); ?></div>
                <div class="stat-label">إجمالي المستخدمين</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo number_format($stats['active']); ?></div>
                <div class="stat-label">المستخدمين النشطين</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo number_format($stats['admins']); ?></div>
                <div class="stat-label">المديرين</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo number_format($stats['today']); ?></div>
                <div class="stat-label">انضموا اليوم</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo is_numeric($stats['verified']) ? number_format($stats['verified']) : $stats['verified']; ?></div>
                <div class="stat-label">البريد مؤكد</div>
            </div>
        </div>

        <!-- أدوات التحكم -->
        <div class="controls-section">
            <div class="controls-header">
                <h2 style="color: #E50914; margin: 0;">🔧 أدوات الإدارة</h2>
                <div style="display: flex; gap: 1rem; flex-wrap: wrap;">
                    <button onclick="openAddModal()" class="btn success">➕ إضافة مستخدم</button>
                    <a href="simple_dashboard.php" class="btn info">🎛️ لوحة الإدارة</a>
                    <a href="../fix_users_table_issues.php" class="btn secondary">🔧 إصلاح الجدول</a>
                </div>
            </div>

            <!-- نموذج البحث -->
            <form method="GET" class="search-form">
                <div class="form-group">
                    <label for="search">🔍 البحث:</label>
                    <input type="text" id="search" name="search" class="form-control"
                           placeholder="البحث بالاسم أو البريد الإلكتروني..."
                           value="<?php echo htmlspecialchars($search); ?>">
                </div>
                <button type="submit" class="btn">بحث</button>
                <?php if (!empty($search)): ?>
                <a href="?" class="btn secondary">مسح البحث</a>
                <?php endif; ?>
            </form>
        </div>

        <!-- جدول المستخدمين -->
        <div class="users-table">
            <table class="table">
                <thead>
                    <tr>
                        <th>المعرف</th>
                        <th>الاسم</th>
                        <?php if (hasColumn('username', $availableColumns)): ?>
                        <th>اسم المستخدم</th>
                        <?php endif; ?>
                        <th>البريد الإلكتروني</th>
                        <th>الدور</th>
                        <th>الحالة</th>
                        <?php if (hasColumn('email_verified', $availableColumns)): ?>
                        <th>البريد مؤكد</th>
                        <?php endif; ?>
                        <th>تاريخ التسجيل</th>
                        <?php if (hasColumn('last_login', $availableColumns)): ?>
                        <th>آخر دخول</th>
                        <?php endif; ?>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (!empty($users)): ?>
                        <?php foreach ($users as $user): ?>
                        <tr>
                            <td><?php echo $user['id']; ?></td>
                            <td><?php echo htmlspecialchars($user['name']); ?></td>
                            <?php if (hasColumn('username', $availableColumns)): ?>
                            <td><?php echo htmlspecialchars(safeGet($user, 'username', 'غير محدد')); ?></td>
                            <?php endif; ?>
                            <td><?php echo htmlspecialchars($user['email']); ?></td>
                            <td>
                                <span class="role-badge role-<?php echo $user['role']; ?>">
                                    <?php
                                    switch($user['role']) {
                                        case 'admin': echo 'مدير'; break;
                                        case 'moderator': echo 'مشرف'; break;
                                        case 'user': echo 'مستخدم'; break;
                                        default: echo $user['role'];
                                    }
                                    ?>
                                </span>
                            </td>
                            <td>
                                <span class="status-badge status-<?php echo $user['status']; ?>">
                                    <?php
                                    switch($user['status']) {
                                        case 'active': echo 'نشط'; break;
                                        case 'inactive': echo 'غير نشط'; break;
                                        case 'banned': echo 'محظور'; break;
                                        case 'pending': echo 'في الانتظار'; break;
                                        default: echo $user['status'];
                                    }
                                    ?>
                                </span>
                            </td>
                            <?php if (hasColumn('email_verified', $availableColumns)): ?>
                            <td>
                                <?php echo safeGet($user, 'email_verified', 0) ? '✅ مؤكد' : '❌ غير مؤكد'; ?>
                            </td>
                            <?php endif; ?>
                            <td><?php echo date('Y-m-d', strtotime($user['created_at'])); ?></td>
                            <?php if (hasColumn('last_login', $availableColumns)): ?>
                            <td>
                                <?php
                                $lastLogin = safeGet($user, 'last_login');
                                echo $lastLogin ? date('Y-m-d H:i', strtotime($lastLogin)) : 'لم يدخل بعد';
                                ?>
                            </td>
                            <?php endif; ?>
                            <td>
                                <div class="actions">
                                    <button onclick="editUser(<?php echo htmlspecialchars(json_encode($user)); ?>)"
                                            class="btn small secondary">✏️ تعديل</button>
                                    <?php if ($user['id'] != $_SESSION['user_id']): ?>
                                    <form method="POST" style="display: inline;"
                                          onsubmit="return confirm('هل أنت متأكد من حذف هذا المستخدم؟')">
                                        <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                        <button type="submit" name="delete_user" class="btn small danger">🗑️ حذف</button>
                                    </form>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="<?php echo 6 + (hasColumn('username', $availableColumns) ? 1 : 0) + (hasColumn('email_verified', $availableColumns) ? 1 : 0) + (hasColumn('last_login', $availableColumns) ? 1 : 0); ?>"
                                style="text-align: center; padding: 2rem; opacity: 0.7;">
                                <?php echo !empty($search) ? 'لا توجد نتائج للبحث' : 'لا توجد مستخدمين'; ?>
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>

        <!-- ترقيم الصفحات -->
        <?php if ($totalPages > 1): ?>
        <div class="pagination">
            <?php if ($page > 1): ?>
                <a href="?page=<?php echo $page - 1; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?>">السابق</a>
            <?php endif; ?>

            <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                <?php if ($i == $page): ?>
                    <span class="current"><?php echo $i; ?></span>
                <?php else: ?>
                    <a href="?page=<?php echo $i; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?>"><?php echo $i; ?></a>
                <?php endif; ?>
            <?php endfor; ?>

            <?php if ($page < $totalPages): ?>
                <a href="?page=<?php echo $page + 1; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?>">التالي</a>
            <?php endif; ?>
        </div>
        <?php endif; ?>

        <!-- روابط سريعة -->
        <div style="text-align: center; margin: 2rem 0;">
            <a href="../homepage.php" class="btn success">🏠 الصفحة الرئيسية</a>
            <a href="../dashboard_live.php" class="btn info">📊 لوحة المراقبة</a>
            <a href="../streaming/simple_video_player.php" class="btn secondary">🎬 مشغل الفيديو</a>
        </div>
    </div>

    <!-- نافذة إضافة مستخدم -->
    <div id="addModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">➕ إضافة مستخدم جديد</h2>
                <span class="close" onclick="closeAddModal()">&times;</span>
            </div>
            <form method="POST">
                <div class="form-grid">
                    <div class="form-group">
                        <label for="name">الاسم الكامل *</label>
                        <input type="text" id="name" name="name" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label for="username">اسم المستخدم</label>
                        <input type="text" id="username" name="username" class="form-control">
                    </div>
                    <div class="form-group">
                        <label for="email">البريد الإلكتروني *</label>
                        <input type="email" id="email" name="email" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label for="password">كلمة المرور *</label>
                        <input type="password" id="password" name="password" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label for="role">الدور</label>
                        <select id="role" name="role" class="form-control">
                            <option value="user">مستخدم</option>
                            <option value="moderator">مشرف</option>
                            <option value="admin">مدير</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="status">الحالة</label>
                        <select id="status" name="status" class="form-control">
                            <option value="active">نشط</option>
                            <option value="inactive">غير نشط</option>
                            <option value="pending">في الانتظار</option>
                        </select>
                    </div>
                </div>
                <div style="display: flex; gap: 1rem; justify-content: flex-end;">
                    <button type="button" onclick="closeAddModal()" class="btn secondary">إلغاء</button>
                    <button type="submit" name="add_user" class="btn success">➕ إضافة</button>
                </div>
            </form>
        </div>
    </div>

    <!-- نافذة تعديل مستخدم -->
    <div id="editModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">✏️ تعديل مستخدم</h2>
                <span class="close" onclick="closeEditModal()">&times;</span>
            </div>
            <form method="POST">
                <input type="hidden" id="edit_user_id" name="user_id">
                <div class="form-grid">
                    <div class="form-group">
                        <label for="edit_name">الاسم الكامل *</label>
                        <input type="text" id="edit_name" name="edit_name" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label for="edit_username">اسم المستخدم</label>
                        <input type="text" id="edit_username" name="edit_username" class="form-control">
                    </div>
                    <div class="form-group">
                        <label for="edit_email">البريد الإلكتروني *</label>
                        <input type="email" id="edit_email" name="edit_email" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label for="edit_role">الدور</label>
                        <select id="edit_role" name="edit_role" class="form-control">
                            <option value="user">مستخدم</option>
                            <option value="moderator">مشرف</option>
                            <option value="admin">مدير</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="edit_status">الحالة</label>
                        <select id="edit_status" name="edit_status" class="form-control">
                            <option value="active">نشط</option>
                            <option value="inactive">غير نشط</option>
                            <option value="banned">محظور</option>
                            <option value="pending">في الانتظار</option>
                        </select>
                    </div>
                </div>
                <div style="display: flex; gap: 1rem; justify-content: flex-end; margin-top: 2rem;">
                    <button type="button" onclick="closeEditModal()" class="btn secondary">إلغاء</button>
                    <button type="submit" name="edit_user" class="btn success">💾 حفظ التغييرات</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // فتح نافذة إضافة مستخدم
        function openAddModal() {
            document.getElementById('addModal').style.display = 'block';
        }

        // إغلاق نافذة إضافة مستخدم
        function closeAddModal() {
            document.getElementById('addModal').style.display = 'none';
        }

        // فتح نافذة تعديل مستخدم
        function editUser(user) {
            document.getElementById('edit_user_id').value = user.id;
            document.getElementById('edit_name').value = user.name;
            document.getElementById('edit_username').value = user.username || '';
            document.getElementById('edit_email').value = user.email;
            document.getElementById('edit_role').value = user.role;
            document.getElementById('edit_status').value = user.status;

            document.getElementById('editModal').style.display = 'block';
        }

        // إغلاق نافذة تعديل مستخدم
        function closeEditModal() {
            document.getElementById('editModal').style.display = 'none';
        }

        // إغلاق النوافذ عند النقر خارجها
        window.onclick = function(event) {
            const addModal = document.getElementById('addModal');
            const editModal = document.getElementById('editModal');

            if (event.target === addModal) {
                closeAddModal();
            }
            if (event.target === editModal) {
                closeEditModal();
            }
        }

        // اختصارات لوحة المفاتيح
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeAddModal();
                closeEditModal();
            }
        });

        console.log('👥 صفحة إدارة المستخدمين المحسنة جاهزة!');
        console.log('📊 إحصائيات المستخدمين:');
        console.log('- إجمالي المستخدمين: <?php echo $stats['total']; ?>');
        console.log('- المستخدمين النشطين: <?php echo $stats['active']; ?>');
        console.log('- المديرين: <?php echo $stats['admins']; ?>');
        console.log('- انضموا اليوم: <?php echo $stats['today']; ?>');
    </script>
</body>
</html>