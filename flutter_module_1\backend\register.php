<?php
session_start();

// إعادة توجيه إذا كان المستخدم مسجل دخول بالفعل
if (isset($_SESSION['user_id'])) {
    header('Location: index.php');
    exit;
}

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $pdo = new PDO("mysql:host=localhost;dbname=shahid_platform;charset=utf8mb4", "root", "");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        $username = trim($_POST['username'] ?? '');
        $email = trim($_POST['email'] ?? '');
        $password = $_POST['password'] ?? '';
        $confirm_password = $_POST['confirm_password'] ?? '';
        $full_name = trim($_POST['full_name'] ?? '');
        
        // التحقق من البيانات
        if (empty($username) || empty($email) || empty($password) || empty($full_name)) {
            $error = 'جميع الحقول مطلوبة';
        } elseif (strlen($username) < 3) {
            $error = 'اسم المستخدم يجب أن يكون 3 أحرف على الأقل';
        } elseif (strlen($password) < 6) {
            $error = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
        } elseif ($password !== $confirm_password) {
            $error = 'كلمة المرور وتأكيد كلمة المرور غير متطابقتين';
        } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $error = 'البريد الإلكتروني غير صحيح';
        } else {
            // التحقق من عدم وجود المستخدم مسبقاً
            $checkStmt = $pdo->prepare("SELECT id FROM users WHERE email = ? OR username = ?");
            $checkStmt->execute([$email, $username]);
            
            if ($checkStmt->fetch()) {
                $error = 'البريد الإلكتروني أو اسم المستخدم موجود مسبقاً';
            } else {
                // إنشاء المستخدم الجديد
                $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
                
                $insertStmt = $pdo->prepare("
                    INSERT INTO users (username, email, password, full_name, subscription_type, is_active, is_verified, created_at) 
                    VALUES (?, ?, ?, ?, 'free', 1, 0, NOW())
                ");
                
                if ($insertStmt->execute([$username, $email, $hashedPassword, $full_name])) {
                    $success = 'تم إنشاء الحساب بنجاح! يمكنك الآن تسجيل الدخول';
                    
                    // مسح البيانات من النموذج
                    $_POST = [];
                } else {
                    $error = 'حدث خطأ أثناء إنشاء الحساب';
                }
            }
        }
    } catch (Exception $e) {
        $error = 'حدث خطأ في النظام. يرجى المحاولة لاحقاً';
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء حساب جديد - Shahid Platform</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', 'Cairo', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #2d2d2d 100%);
            color: #fff;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem;
        }
        
        .register-container {
            background: rgba(47, 47, 47, 0.95);
            border-radius: 20px;
            padding: 3rem;
            border: 2px solid rgba(229, 9, 20, 0.3);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
            width: 100%;
            max-width: 500px;
        }
        
        .logo {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .logo h1 {
            color: #E50914;
            font-size: 2.5rem;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: #ccc;
            font-weight: 500;
        }
        
        .form-group input {
            width: 100%;
            padding: 1rem;
            border: 2px solid rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.05);
            color: #fff;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #E50914;
            background: rgba(255, 255, 255, 0.1);
        }
        
        .btn {
            width: 100%;
            padding: 1rem;
            background: linear-gradient(45deg, #E50914, #B8070F);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 1rem;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(229, 9, 20, 0.3);
        }
        
        .error {
            background: rgba(244, 67, 54, 0.1);
            border: 1px solid rgba(244, 67, 54, 0.3);
            color: #F44336;
            padding: 1rem;
            border-radius: 10px;
            margin-bottom: 1rem;
            text-align: center;
        }
        
        .success {
            background: rgba(76, 175, 80, 0.1);
            border: 1px solid rgba(76, 175, 80, 0.3);
            color: #4CAF50;
            padding: 1rem;
            border-radius: 10px;
            margin-bottom: 1rem;
            text-align: center;
        }
        
        .links {
            text-align: center;
            margin-top: 2rem;
        }
        
        .links a {
            color: #E50914;
            text-decoration: none;
            margin: 0 1rem;
            transition: color 0.3s ease;
        }
        
        .links a:hover {
            color: #FF6B35;
        }
        
        .password-strength {
            margin-top: 0.5rem;
            font-size: 0.9rem;
        }
        
        .strength-weak { color: #F44336; }
        .strength-medium { color: #FF9800; }
        .strength-strong { color: #4CAF50; }
        
        .terms {
            background: rgba(33, 150, 243, 0.1);
            border: 1px solid rgba(33, 150, 243, 0.3);
            border-radius: 10px;
            padding: 1rem;
            margin: 1rem 0;
            font-size: 0.9rem;
            color: #ccc;
            text-align: center;
        }
        
        @media (max-width: 768px) {
            .register-container {
                padding: 2rem;
                margin: 1rem;
            }
            
            .logo h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="register-container">
        <div class="logo">
            <h1>🎬 شاهد</h1>
            <p style="color: #ccc; margin-top: 0.5rem;">إنشاء حساب جديد</p>
        </div>
        
        <?php if ($error): ?>
            <div class="error">
                <strong>خطأ:</strong> <?php echo htmlspecialchars($error); ?>
            </div>
        <?php endif; ?>
        
        <?php if ($success): ?>
            <div class="success">
                <?php echo htmlspecialchars($success); ?>
                <br><br>
                <a href="login.php" style="color: #4CAF50; text-decoration: underline;">انقر هنا لتسجيل الدخول</a>
            </div>
        <?php endif; ?>
        
        <form method="POST" action="" id="registerForm">
            <div class="form-group">
                <label for="full_name">الاسم الكامل</label>
                <input type="text" id="full_name" name="full_name" required 
                       value="<?php echo htmlspecialchars($_POST['full_name'] ?? ''); ?>"
                       placeholder="أدخل اسمك الكامل">
            </div>
            
            <div class="form-group">
                <label for="username">اسم المستخدم</label>
                <input type="text" id="username" name="username" required 
                       value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>"
                       placeholder="اختر اسم مستخدم (3 أحرف على الأقل)">
            </div>
            
            <div class="form-group">
                <label for="email">البريد الإلكتروني</label>
                <input type="email" id="email" name="email" required 
                       value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>"
                       placeholder="أدخل بريدك الإلكتروني">
            </div>
            
            <div class="form-group">
                <label for="password">كلمة المرور</label>
                <input type="password" id="password" name="password" required 
                       placeholder="أدخل كلمة مرور قوية (6 أحرف على الأقل)">
                <div id="passwordStrength" class="password-strength"></div>
            </div>
            
            <div class="form-group">
                <label for="confirm_password">تأكيد كلمة المرور</label>
                <input type="password" id="confirm_password" name="confirm_password" required 
                       placeholder="أعد إدخال كلمة المرور">
                <div id="passwordMatch" class="password-strength"></div>
            </div>
            
            <div class="terms">
                بإنشاء حساب، أنت توافق على <a href="#" style="color: #2196F3;">شروط الاستخدام</a> و <a href="#" style="color: #2196F3;">سياسة الخصوصية</a>
            </div>
            
            <button type="submit" class="btn" id="submitBtn">
                📝 إنشاء الحساب
            </button>
        </form>
        
        <div class="links">
            <a href="login.php">🔐 لديك حساب بالفعل؟ سجل دخولك</a>
        </div>
        
        <div class="links" style="margin-top: 1rem;">
            <a href="index.php">🏠 العودة للرئيسية</a>
        </div>
    </div>
    
    <script>
        // تحقق من قوة كلمة المرور
        document.getElementById('password').addEventListener('input', function() {
            const password = this.value;
            const strengthDiv = document.getElementById('passwordStrength');
            
            if (password.length === 0) {
                strengthDiv.textContent = '';
                return;
            }
            
            let strength = 0;
            if (password.length >= 6) strength++;
            if (password.match(/[a-z]/)) strength++;
            if (password.match(/[A-Z]/)) strength++;
            if (password.match(/[0-9]/)) strength++;
            if (password.match(/[^a-zA-Z0-9]/)) strength++;
            
            if (strength < 2) {
                strengthDiv.textContent = 'ضعيفة';
                strengthDiv.className = 'password-strength strength-weak';
            } else if (strength < 4) {
                strengthDiv.textContent = 'متوسطة';
                strengthDiv.className = 'password-strength strength-medium';
            } else {
                strengthDiv.textContent = 'قوية';
                strengthDiv.className = 'password-strength strength-strong';
            }
        });
        
        // تحقق من تطابق كلمة المرور
        document.getElementById('confirm_password').addEventListener('input', function() {
            const password = document.getElementById('password').value;
            const confirmPassword = this.value;
            const matchDiv = document.getElementById('passwordMatch');
            
            if (confirmPassword.length === 0) {
                matchDiv.textContent = '';
                return;
            }
            
            if (password === confirmPassword) {
                matchDiv.textContent = 'كلمات المرور متطابقة ✓';
                matchDiv.className = 'password-strength strength-strong';
            } else {
                matchDiv.textContent = 'كلمات المرور غير متطابقة ✗';
                matchDiv.className = 'password-strength strength-weak';
            }
        });
        
        // التحقق من النموذج قبل الإرسال
        document.getElementById('registerForm').addEventListener('submit', function(e) {
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirm_password').value;
            
            if (password !== confirmPassword) {
                e.preventDefault();
                alert('كلمات المرور غير متطابقة');
                return false;
            }
            
            if (password.length < 6) {
                e.preventDefault();
                alert('كلمة المرور يجب أن تكون 6 أحرف على الأقل');
                return false;
            }
        });
        
        // تركيز تلقائي على حقل الاسم
        document.addEventListener('DOMContentLoaded', function() {
            const nameInput = document.getElementById('full_name');
            if (nameInput && !nameInput.value) {
                nameInput.focus();
            }
        });
    </script>
</body>
</html>
