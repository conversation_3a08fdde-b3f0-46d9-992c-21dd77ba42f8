<?php
/**
 * إنشاء الجداول المفقودة لمشروع Shahid Platform
 * Create Missing Tables for Shahid Platform
 */

echo "<h1>🔧 إنشاء الجداول المفقودة</h1>";

try {
    $pdo = new PDO("mysql:host=localhost;dbname=shahid_platform;charset=utf8mb4", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>✅ تم الاتصال بقاعدة البيانات بنجاح</h3>";
    echo "</div>";
    
    // تعريف الجداول المطلوبة مع هياكلها
    $tables = [
        'movies' => "
            CREATE TABLE IF NOT EXISTS `movies` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `title` varchar(255) NOT NULL,
                `title_en` varchar(255) DEFAULT NULL,
                `description` text,
                `description_en` text DEFAULT NULL,
                `poster` varchar(255) DEFAULT NULL,
                `banner` varchar(255) DEFAULT NULL,
                `trailer_url` varchar(500) DEFAULT NULL,
                `video_url` varchar(500) DEFAULT NULL,
                `duration` int(11) DEFAULT NULL COMMENT 'Duration in minutes',
                `release_year` int(4) DEFAULT NULL,
                `rating` decimal(3,1) DEFAULT 0.0,
                `imdb_rating` decimal(3,1) DEFAULT NULL,
                `genre` varchar(255) DEFAULT NULL,
                `country` varchar(100) DEFAULT NULL,
                `language` varchar(50) DEFAULT 'ar',
                `quality` enum('HD','FHD','4K','CAM','TS') DEFAULT 'HD',
                `status` enum('active','inactive','coming_soon') DEFAULT 'active',
                `views` int(11) DEFAULT 0,
                `likes` int(11) DEFAULT 0,
                `dislikes` int(11) DEFAULT 0,
                `featured` tinyint(1) DEFAULT 0,
                `trending` tinyint(1) DEFAULT 0,
                `category_id` int(11) DEFAULT NULL,
                `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                KEY `idx_status` (`status`),
                KEY `idx_featured` (`featured`),
                KEY `idx_trending` (`trending`),
                KEY `idx_category` (`category_id`),
                KEY `idx_rating` (`rating`),
                KEY `idx_views` (`views`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ",
        
        'series' => "
            CREATE TABLE IF NOT EXISTS `series` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `title` varchar(255) NOT NULL,
                `title_en` varchar(255) DEFAULT NULL,
                `description` text,
                `description_en` text DEFAULT NULL,
                `poster` varchar(255) DEFAULT NULL,
                `banner` varchar(255) DEFAULT NULL,
                `trailer_url` varchar(500) DEFAULT NULL,
                `total_seasons` int(11) DEFAULT 1,
                `total_episodes` int(11) DEFAULT 0,
                `release_year` int(4) DEFAULT NULL,
                `rating` decimal(3,1) DEFAULT 0.0,
                `imdb_rating` decimal(3,1) DEFAULT NULL,
                `genre` varchar(255) DEFAULT NULL,
                `country` varchar(100) DEFAULT NULL,
                `language` varchar(50) DEFAULT 'ar',
                `status` enum('active','inactive','completed','ongoing') DEFAULT 'active',
                `views` int(11) DEFAULT 0,
                `likes` int(11) DEFAULT 0,
                `dislikes` int(11) DEFAULT 0,
                `featured` tinyint(1) DEFAULT 0,
                `trending` tinyint(1) DEFAULT 0,
                `category_id` int(11) DEFAULT NULL,
                `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                KEY `idx_status` (`status`),
                KEY `idx_featured` (`featured`),
                KEY `idx_trending` (`trending`),
                KEY `idx_category` (`category_id`),
                KEY `idx_rating` (`rating`),
                KEY `idx_views` (`views`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ",
        
        'categories' => "
            CREATE TABLE IF NOT EXISTS `categories` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `name` varchar(100) NOT NULL,
                `name_en` varchar(100) DEFAULT NULL,
                `description` text DEFAULT NULL,
                `icon` varchar(255) DEFAULT NULL,
                `color` varchar(7) DEFAULT '#E50914',
                `sort_order` int(11) DEFAULT 0,
                `status` enum('active','inactive') DEFAULT 'active',
                `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                UNIQUE KEY `name` (`name`),
                KEY `idx_status` (`status`),
                KEY `idx_sort` (`sort_order`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ",
        
        'ratings' => "
            CREATE TABLE IF NOT EXISTS `ratings` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `user_id` int(11) NOT NULL,
                `content_type` enum('movie','series') NOT NULL,
                `content_id` int(11) NOT NULL,
                `rating` tinyint(1) NOT NULL CHECK (rating >= 1 AND rating <= 5),
                `review` text DEFAULT NULL,
                `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                UNIQUE KEY `unique_user_content` (`user_id`, `content_type`, `content_id`),
                KEY `idx_content` (`content_type`, `content_id`),
                KEY `idx_rating` (`rating`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ",
        
        'favorites' => "
            CREATE TABLE IF NOT EXISTS `favorites` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `user_id` int(11) NOT NULL,
                `content_type` enum('movie','series') NOT NULL,
                `content_id` int(11) NOT NULL,
                `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                UNIQUE KEY `unique_user_favorite` (`user_id`, `content_type`, `content_id`),
                KEY `idx_user` (`user_id`),
                KEY `idx_content` (`content_type`, `content_id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ",
        
        'watch_history' => "
            CREATE TABLE IF NOT EXISTS `watch_history` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `user_id` int(11) NOT NULL,
                `content_type` enum('movie','series','episode') NOT NULL,
                `content_id` int(11) NOT NULL,
                `episode_id` int(11) DEFAULT NULL,
                `watch_time` int(11) DEFAULT 0 COMMENT 'Watch time in seconds',
                `total_time` int(11) DEFAULT 0 COMMENT 'Total content time in seconds',
                `progress` decimal(5,2) DEFAULT 0.00 COMMENT 'Watch progress percentage',
                `completed` tinyint(1) DEFAULT 0,
                `last_watched` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                UNIQUE KEY `unique_user_content` (`user_id`, `content_type`, `content_id`, `episode_id`),
                KEY `idx_user` (`user_id`),
                KEY `idx_content` (`content_type`, `content_id`),
                KEY `idx_last_watched` (`last_watched`),
                KEY `idx_progress` (`progress`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ",

        'episodes' => "
            CREATE TABLE IF NOT EXISTS `episodes` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `series_id` int(11) NOT NULL,
                `season_number` int(11) NOT NULL DEFAULT 1,
                `episode_number` int(11) NOT NULL,
                `title` varchar(255) NOT NULL,
                `title_en` varchar(255) DEFAULT NULL,
                `description` text DEFAULT NULL,
                `description_en` text DEFAULT NULL,
                `thumbnail` varchar(255) DEFAULT NULL,
                `video_url` varchar(500) DEFAULT NULL,
                `duration` int(11) DEFAULT NULL COMMENT 'Duration in minutes',
                `air_date` date DEFAULT NULL,
                `rating` decimal(3,1) DEFAULT 0.0,
                `views` int(11) DEFAULT 0,
                `status` enum('active','inactive','coming_soon') DEFAULT 'active',
                `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                UNIQUE KEY `unique_series_episode` (`series_id`, `season_number`, `episode_number`),
                KEY `idx_series` (`series_id`),
                KEY `idx_season` (`season_number`),
                KEY `idx_status` (`status`),
                KEY `idx_air_date` (`air_date`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        "
    ];
    
    // فحص الجداول الموجودة
    $stmt = $pdo->query("SHOW TABLES");
    $existingTables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "<h2>🔄 جاري إنشاء الجداول...</h2>";
    
    $createdTables = [];
    $existingCount = 0;
    $errorTables = [];
    
    foreach ($tables as $tableName => $sql) {
        if (in_array($tableName, $existingTables)) {
            $existingCount++;
            echo "<p style='color: #4CAF50;'>✅ الجدول <strong>$tableName</strong> موجود مسبقاً</p>";
        } else {
            try {
                $pdo->exec($sql);
                $createdTables[] = $tableName;
                echo "<p style='color: #2196F3;'>🆕 تم إنشاء الجدول <strong>$tableName</strong> بنجاح</p>";
            } catch (Exception $e) {
                $errorTables[] = ['table' => $tableName, 'error' => $e->getMessage()];
                echo "<p style='color: #F44336;'>❌ فشل إنشاء الجدول <strong>$tableName</strong>: " . htmlspecialchars($e->getMessage()) . "</p>";
            }
        }
    }
    
    // إدراج بيانات تجريبية للتصنيفات
    if (in_array('categories', $createdTables)) {
        echo "<h3>📋 إدراج بيانات التصنيفات التجريبية...</h3>";
        
        $categories = [
            ['اكشن', 'Action', 'أفلام ومسلسلات الأكشن والإثارة', '⚔️', '#E50914'],
            ['دراما', 'Drama', 'أفلام ومسلسلات الدراما والرومانسية', '🎭', '#FF6B35'],
            ['كوميديا', 'Comedy', 'أفلام ومسلسلات الكوميديا والضحك', '😂', '#4CAF50'],
            ['رعب', 'Horror', 'أفلام ومسلسلات الرعب والإثارة', '👻', '#9C27B0'],
            ['خيال علمي', 'Sci-Fi', 'أفلام ومسلسلات الخيال العلمي', '🚀', '#2196F3'],
            ['وثائقي', 'Documentary', 'الأفلام الوثائقية والتعليمية', '📚', '#FF9800'],
            ['أطفال', 'Kids', 'محتوى آمن للأطفال', '🧸', '#E91E63'],
            ['رياضة', 'Sports', 'المحتوى الرياضي والمباريات', '⚽', '#00BCD4']
        ];
        
        $stmt = $pdo->prepare("INSERT INTO categories (name, name_en, description, icon, color, sort_order) VALUES (?, ?, ?, ?, ?, ?)");
        
        foreach ($categories as $index => $category) {
            try {
                $stmt->execute([$category[0], $category[1], $category[2], $category[3], $category[4], $index + 1]);
                echo "<p style='color: #4CAF50;'>✅ تم إدراج تصنيف: <strong>{$category[0]}</strong></p>";
            } catch (Exception $e) {
                echo "<p style='color: #FF9800;'>⚠️ تصنيف {$category[0]} موجود مسبقاً</p>";
            }
        }
    }
    
    // إدراج بيانات تجريبية للأفلام
    if (in_array('movies', $createdTables)) {
        echo "<h3>🎬 إدراج بيانات الأفلام التجريبية...</h3>";
        
        $movies = [
            ['الفيلم الأول', 'First Movie', 'وصف الفيلم الأول', 'First movie description', 2024, 8.5, 'اكشن، دراما', 'مصر'],
            ['الفيلم الثاني', 'Second Movie', 'وصف الفيلم الثاني', 'Second movie description', 2024, 7.8, 'كوميديا', 'لبنان'],
            ['الفيلم الثالث', 'Third Movie', 'وصف الفيلم الثالث', 'Third movie description', 2023, 9.1, 'دراما', 'الأردن']
        ];
        
        $stmt = $pdo->prepare("INSERT INTO movies (title, title_en, description, description_en, release_year, rating, genre, country, featured, trending) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 1, 1)");
        
        foreach ($movies as $movie) {
            try {
                $stmt->execute($movie);
                echo "<p style='color: #4CAF50;'>✅ تم إدراج فيلم: <strong>{$movie[0]}</strong></p>";
            } catch (Exception $e) {
                echo "<p style='color: #FF9800;'>⚠️ فيلم {$movie[0]} موجود مسبقاً أو خطأ في الإدراج</p>";
            }
        }
    }
    
    // إدراج بيانات تجريبية للمسلسلات
    if (in_array('series', $createdTables)) {
        echo "<h3>📺 إدراج بيانات المسلسلات التجريبية...</h3>";
        
        $series = [
            ['المسلسل الأول', 'First Series', 'وصف المسلسل الأول', 'First series description', 2024, 8.7, 'دراما', 'مصر', 2, 20],
            ['المسلسل الثاني', 'Second Series', 'وصف المسلسل الثاني', 'Second series description', 2024, 7.9, 'كوميديا', 'لبنان', 1, 15],
            ['المسلسل الثالث', 'Third Series', 'وصف المسلسل الثالث', 'Third series description', 2023, 9.2, 'اكشن', 'الأردن', 3, 45]
        ];
        
        $stmt = $pdo->prepare("INSERT INTO series (title, title_en, description, description_en, release_year, rating, genre, country, total_seasons, total_episodes, featured, trending) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1, 1)");
        
        foreach ($series as $serie) {
            try {
                $stmt->execute($serie);
                echo "<p style='color: #4CAF50;'>✅ تم إدراج مسلسل: <strong>{$serie[0]}</strong></p>";
            } catch (Exception $e) {
                echo "<p style='color: #FF9800;'>⚠️ مسلسل {$serie[0]} موجود مسبقاً أو خطأ في الإدراج</p>";
            }
        }
    }
    
    // ملخص النتائج
    echo "<h2>📊 ملخص العملية</h2>";
    
    echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin: 2rem 0;'>";
    
    // بطاقة الجداول المنشأة
    echo "<div style='background: rgba(33, 150, 243, 0.1); border: 1px solid rgba(33, 150, 243, 0.3); border-radius: 10px; padding: 1.5rem; text-align: center;'>";
    echo "<h3 style='color: #2196F3; margin-bottom: 1rem;'>🆕 جداول جديدة</h3>";
    echo "<div style='font-size: 2.5rem; font-weight: bold; color: #2196F3; margin-bottom: 0.5rem;'>" . count($createdTables) . "</div>";
    echo "<div style='opacity: 0.8; color: #ccc;'>تم إنشاؤها</div>";
    echo "</div>";
    
    // بطاقة الجداول الموجودة
    echo "<div style='background: rgba(76, 175, 80, 0.1); border: 1px solid rgba(76, 175, 80, 0.3); border-radius: 10px; padding: 1.5rem; text-align: center;'>";
    echo "<h3 style='color: #4CAF50; margin-bottom: 1rem;'>✅ جداول موجودة</h3>";
    echo "<div style='font-size: 2.5rem; font-weight: bold; color: #4CAF50; margin-bottom: 0.5rem;'>$existingCount</div>";
    echo "<div style='opacity: 0.8; color: #ccc;'>موجودة مسبقاً</div>";
    echo "</div>";
    
    // بطاقة الأخطاء
    echo "<div style='background: rgba(244, 67, 54, 0.1); border: 1px solid rgba(244, 67, 54, 0.3); border-radius: 10px; padding: 1.5rem; text-align: center;'>";
    echo "<h3 style='color: #F44336; margin-bottom: 1rem;'>❌ أخطاء</h3>";
    echo "<div style='font-size: 2.5rem; font-weight: bold; color: #F44336; margin-bottom: 0.5rem;'>" . count($errorTables) . "</div>";
    echo "<div style='opacity: 0.8; color: #ccc;'>فشل الإنشاء</div>";
    echo "</div>";
    
    // بطاقة الإجمالي
    echo "<div style='background: rgba(229, 9, 20, 0.1); border: 1px solid rgba(229, 9, 20, 0.3); border-radius: 10px; padding: 1.5rem; text-align: center;'>";
    echo "<h3 style='color: #E50914; margin-bottom: 1rem;'>📊 الإجمالي</h3>";
    echo "<div style='font-size: 2.5rem; font-weight: bold; color: #E50914; margin-bottom: 0.5rem;'>" . count($tables) . "</div>";
    echo "<div style='opacity: 0.8; color: #ccc;'>جدول مطلوب</div>";
    echo "</div>";
    
    echo "</div>";
    
    // عرض الأخطاء إن وجدت
    if (!empty($errorTables)) {
        echo "<div style='background: rgba(244, 67, 54, 0.1); border: 1px solid rgba(244, 67, 54, 0.3); border-radius: 10px; padding: 2rem; margin: 2rem 0;'>";
        echo "<h3 style='color: #F44336; margin-bottom: 1rem;'>⚠️ الأخطاء التي حدثت:</h3>";
        foreach ($errorTables as $error) {
            echo "<div style='margin: 1rem 0; padding: 1rem; background: rgba(0,0,0,0.3); border-radius: 5px;'>";
            echo "<strong style='color: #F44336;'>الجدول: {$error['table']}</strong><br>";
            echo "<span style='color: #ccc;'>الخطأ: " . htmlspecialchars($error['error']) . "</span>";
            echo "</div>";
        }
        echo "</div>";
    }
    
    // نسبة النجاح
    $totalTables = count($tables);
    $successTables = count($createdTables) + $existingCount;
    $successRate = round(($successTables / $totalTables) * 100, 1);
    
    echo "<div style='background: linear-gradient(135deg, rgba(47, 47, 47, 0.9) 0%, rgba(35, 35, 35, 0.9) 100%); border-radius: 15px; padding: 2rem; margin: 2rem 0; border: 1px solid rgba(229, 9, 20, 0.2);'>";
    echo "<h3 style='text-align: center; color: #E50914; margin-bottom: 2rem;'>🎯 نسبة نجاح العملية</h3>";
    
    $color = $successRate >= 90 ? '#4CAF50' : ($successRate >= 70 ? '#FF9800' : '#F44336');
    echo "<div style='text-align: center;'>";
    echo "<div style='font-size: 4rem; font-weight: bold; color: $color; margin-bottom: 1rem;'>$successRate%</div>";
    echo "<div style='font-size: 1.2rem; opacity: 0.8;'>$successTables من $totalTables جدول جاهز</div>";
    echo "</div>";
    
    // شريط التقدم
    echo "<div style='background: rgba(0,0,0,0.3); border-radius: 10px; height: 20px; margin: 2rem 0; overflow: hidden;'>";
    echo "<div style='background: linear-gradient(45deg, #E50914, #B8070F); height: 100%; width: $successRate%; transition: width 0.5s ease;'></div>";
    echo "</div>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>❌ خطأ في الاتصال بقاعدة البيانات:</h3>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

// روابط سريعة
echo "<div style='text-align: center; margin: 3rem 0;'>";
echo "<h3 style='color: #E50914; margin-bottom: 2rem;'>🔗 الخطوات التالية</h3>";

$next_steps = [
    ['url' => 'check_database_tables.php', 'title' => '🔍 فحص الجداول مرة أخرى', 'color' => '#4CAF50'],
    ['url' => 'dashboard_live.php', 'title' => '📊 لوحة المراقبة المباشرة', 'color' => '#E50914'],
    ['url' => 'fix_database_structure.php', 'title' => '🗄️ إصلاح قاعدة البيانات الكاملة', 'color' => '#2196F3'],
    ['url' => 'project_audit_complete.php', 'title' => '📋 المراجعة الشاملة', 'color' => '#FF9800'],
    ['url' => 'homepage_working.php', 'title' => '🏠 الصفحة الرئيسية', 'color' => '#9C27B0']
];

foreach ($next_steps as $step) {
    echo "<a href='{$step['url']}' style='background: {$step['color']}; color: white; padding: 1rem 2rem; text-decoration: none; border-radius: 8px; font-weight: bold; margin: 0.5rem; display: inline-block; transition: transform 0.3s ease;' onmouseover='this.style.transform=\"translateY(-3px)\"' onmouseout='this.style.transform=\"translateY(0)\"'>{$step['title']}</a>";
}

echo "</div>";
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 إنشاء الجداول المفقودة - Shahid Platform</title>
    <style>
        body {
            font-family: 'Segoe UI', 'Cairo', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #2d2d2d 100%);
            color: #fff;
            min-height: 100vh;
            padding: 2rem;
            margin: 0;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: rgba(47, 47, 47, 0.95);
            border-radius: 20px;
            padding: 3rem;
            border: 2px solid rgba(229, 9, 20, 0.3);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
        }
        h1, h2, h3 { 
            color: #E50914; 
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
        }
        h1 {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 2rem;
            background: linear-gradient(45deg, #E50914, #FF6B35);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        p {
            line-height: 1.6;
            margin: 0.5rem 0;
        }
        .btn {
            transition: all 0.3s ease;
        }
        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
        }
        @media (max-width: 768px) {
            .container {
                padding: 1rem;
                margin: 1rem;
            }
            h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- المحتوى يتم عرضه من PHP أعلاه -->
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔧 أداة إنشاء الجداول المفقودة جاهزة!');
            
            // تأثير شريط التقدم
            const progressBar = document.querySelector('[style*="width: <?php echo isset($successRate) ? $successRate : 0; ?>%"]');
            if (progressBar) {
                progressBar.style.width = '0%';
                setTimeout(() => {
                    progressBar.style.width = '<?php echo isset($successRate) ? $successRate : 0; ?>%';
                }, 500);
            }
        });
    </script>
</body>
</html>
