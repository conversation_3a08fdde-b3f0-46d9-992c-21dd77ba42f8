-- Shahid Database Schema (Simplified)
-- Professional Video Streaming Platform

-- Users table
CREATE TABLE IF NOT EXISTS `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `email` varchar(100) NOT NULL UNIQUE,
  `password` varchar(255) NOT NULL,
  `role` enum('user','admin') DEFAULT 'user',
  `status` enum('active','inactive','banned') DEFAULT 'active',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Movies table
CREATE TABLE IF NOT EXISTS `movies` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL,
  `slug` varchar(255) NOT NULL UNIQUE,
  `description` text,
  `poster` varchar(500),
  `backdrop` varchar(500),
  `year` int(4),
  `duration` int(11),
  `rating` decimal(3,1),
  `premium` tinyint(1) DEFAULT 0,
  `featured` tinyint(1) DEFAULT 0,
  `status` enum('published','draft','archived') DEFAULT 'published',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Series table
CREATE TABLE IF NOT EXISTS `series` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL,
  `slug` varchar(255) NOT NULL UNIQUE,
  `description` text,
  `poster` varchar(500),
  `backdrop` varchar(500),
  `year` int(4),
  `rating` decimal(3,1),
  `premium` tinyint(1) DEFAULT 0,
  `featured` tinyint(1) DEFAULT 0,
  `status` enum('published','draft','archived') DEFAULT 'published',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Episodes table
CREATE TABLE IF NOT EXISTS `episodes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `series_id` int(11) NOT NULL,
  `title` varchar(255) NOT NULL,
  `slug` varchar(255) NOT NULL,
  `description` text,
  `season_number` int(11) NOT NULL,
  `episode_number` int(11) NOT NULL,
  `duration` int(11),
  `video_url` varchar(500),
  `thumbnail` varchar(500),
  `premium` tinyint(1) DEFAULT 0,
  `status` enum('published','draft','archived') DEFAULT 'published',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  FOREIGN KEY (`series_id`) REFERENCES `series`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- User favorites
CREATE TABLE IF NOT EXISTS `user_favorites` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `content_id` int(11) NOT NULL,
  `content_type` enum('movie','series') NOT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_content` (`user_id`, `content_id`, `content_type`),
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- User watchlist
CREATE TABLE IF NOT EXISTS `user_watchlist` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `content_id` int(11) NOT NULL,
  `content_type` enum('movie','series') NOT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_content` (`user_id`, `content_id`, `content_type`),
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Watch history
CREATE TABLE IF NOT EXISTS `watch_history` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `content_id` int(11) NOT NULL,
  `content_type` enum('movie','episode') NOT NULL,
  `progress` int(11) DEFAULT 0,
  `duration` int(11) DEFAULT 0,
  `completed` tinyint(1) DEFAULT 0,
  `last_watched` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_content` (`user_id`, `content_id`, `content_type`),
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Subscriptions
CREATE TABLE IF NOT EXISTS `subscriptions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `price` decimal(10,2) NOT NULL,
  `currency` varchar(3) DEFAULT 'USD',
  `duration_days` int(11) NOT NULL,
  `features` json,
  `status` enum('active','inactive') DEFAULT 'active',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- User subscriptions
CREATE TABLE IF NOT EXISTS `user_subscriptions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `subscription_id` int(11) NOT NULL,
  `status` enum('active','expired','cancelled') DEFAULT 'active',
  `start_date` timestamp DEFAULT CURRENT_TIMESTAMP,
  `end_date` timestamp NULL,
  `payment_method` varchar(50),
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`subscription_id`) REFERENCES `subscriptions`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Insert default subscription plans
INSERT INTO `subscriptions` (`name`, `price`, `currency`, `duration_days`, `features`) VALUES
('Basic', 9.99, 'USD', 30, '{"quality": "720p", "devices": 1, "downloads": false}'),
('Premium', 19.99, 'USD', 30, '{"quality": "1080p", "devices": 3, "downloads": true}'),
('Ultimate', 29.99, 'USD', 30, '{"quality": "4K", "devices": 5, "downloads": true}');

-- Insert sample movies
INSERT INTO `movies` (`title`, `slug`, `description`, `year`, `duration`, `rating`, `featured`) VALUES
('The Matrix', 'the-matrix', 'A computer programmer discovers reality is a simulation.', 1999, 136, 8.7, 1),
('Inception', 'inception', 'A thief who steals corporate secrets through dream-sharing technology.', 2010, 148, 8.8, 1),
('Interstellar', 'interstellar', 'A team of explorers travel through a wormhole in space.', 2014, 169, 8.6, 0);

-- Insert sample series
INSERT INTO `series` (`title`, `slug`, `description`, `year`, `rating`, `featured`) VALUES
('Breaking Bad', 'breaking-bad', 'A high school chemistry teacher turned methamphetamine producer.', 2008, 9.5, 1),
('Game of Thrones', 'game-of-thrones', 'Nine noble families fight for control over the Iron Throne.', 2011, 9.3, 1),
('Stranger Things', 'stranger-things', 'A group of kids uncover supernatural mysteries in their town.', 2016, 8.7, 0);

-- Insert sample episodes for Breaking Bad
INSERT INTO `episodes` (`series_id`, `title`, `slug`, `description`, `season_number`, `episode_number`, `duration`) VALUES
(1, 'Pilot', 'breaking-bad-s01e01', 'Walter White, a struggling high school chemistry teacher, is diagnosed with inoperable lung cancer.', 1, 1, 58),
(1, 'Cat\'s in the Bag...', 'breaking-bad-s01e02', 'Walt and Jesse attempt to tie up loose ends.', 1, 2, 48),
(1, '...And the Bag\'s in the River', 'breaking-bad-s01e03', 'Walt and Jesse clean up after the bathtub incident.', 1, 3, 48);
