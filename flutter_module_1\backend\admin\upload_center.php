<?php
session_start();

// التحقق من تسجيل الدخول والصلاحيات
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit;
}

try {
    $pdo = new PDO("mysql:host=localhost;dbname=shahid_platform;charset=utf8mb4", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // التحقق من صلاحيات الإدارة
    $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ? AND (subscription_type = 'vip' OR email LIKE '%admin%')");
    $stmt->execute([$_SESSION['user_id']]);
    $admin = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$admin) {
        header('Location: ../index.php');
        exit;
    }
    
    $message = '';
    $error = '';
    
    // معالجة رفع الملفات
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['file'])) {
        $uploadDir = '../uploads/';
        $allowedTypes = ['video/mp4', 'video/avi', 'video/mkv', 'image/jpeg', 'image/png', 'image/gif'];
        $maxFileSize = 500 * 1024 * 1024; // 500MB
        
        $file = $_FILES['file'];
        $fileName = $file['name'];
        $fileType = $file['type'];
        $fileSize = $file['size'];
        $fileTmpName = $file['tmp_name'];
        
        // التحقق من نوع الملف
        if (!in_array($fileType, $allowedTypes)) {
            $error = "نوع الملف غير مدعوم. الأنواع المدعومة: MP4, AVI, MKV, JPEG, PNG, GIF";
        }
        // التحقق من حجم الملف
        elseif ($fileSize > $maxFileSize) {
            $error = "حجم الملف كبير جداً. الحد الأقصى 500MB";
        }
        else {
            // إنشاء اسم ملف فريد
            $fileExtension = pathinfo($fileName, PATHINFO_EXTENSION);
            $newFileName = uniqid() . '_' . time() . '.' . $fileExtension;
            $uploadPath = $uploadDir . $newFileName;
            
            // رفع الملف
            if (move_uploaded_file($fileTmpName, $uploadPath)) {
                // حفظ معلومات الملف في قاعدة البيانات
                $stmt = $pdo->prepare("
                    INSERT INTO media_files (original_name, file_name, file_path, file_type, file_size, uploaded_by, upload_date) 
                    VALUES (?, ?, ?, ?, ?, ?, NOW())
                ");
                
                if ($stmt->execute([$fileName, $newFileName, $uploadPath, $fileType, $fileSize, $_SESSION['user_id']])) {
                    $message = "تم رفع الملف بنجاح: " . $fileName;
                } else {
                    $error = "فشل في حفظ معلومات الملف";
                    unlink($uploadPath); // حذف الملف في حالة فشل حفظ البيانات
                }
            } else {
                $error = "فشل في رفع الملف";
            }
        }
    }
    
    // حذف ملف
    if (isset($_POST['delete_file'])) {
        $fileId = $_POST['file_id'];
        
        $stmt = $pdo->prepare("SELECT * FROM media_files WHERE id = ?");
        $stmt->execute([$fileId]);
        $file = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($file) {
            // حذف الملف من النظام
            if (file_exists($file['file_path'])) {
                unlink($file['file_path']);
            }
            
            // حذف السجل من قاعدة البيانات
            $stmt = $pdo->prepare("DELETE FROM media_files WHERE id = ?");
            if ($stmt->execute([$fileId])) {
                $message = "تم حذف الملف بنجاح";
            } else {
                $error = "فشل في حذف الملف";
            }
        }
    }
    
    // الحصول على قائمة الملفات
    $files = $pdo->query("
        SELECT mf.*, u.username 
        FROM media_files mf 
        LEFT JOIN users u ON mf.uploaded_by = u.id 
        ORDER BY mf.upload_date DESC
    ")->fetchAll(PDO::FETCH_ASSOC);
    
    // إحصائيات
    $stats = [
        'total_files' => $pdo->query("SELECT COUNT(*) FROM media_files")->fetchColumn(),
        'total_size' => $pdo->query("SELECT SUM(file_size) FROM media_files")->fetchColumn(),
        'video_files' => $pdo->query("SELECT COUNT(*) FROM media_files WHERE file_type LIKE 'video%'")->fetchColumn(),
        'image_files' => $pdo->query("SELECT COUNT(*) FROM media_files WHERE file_type LIKE 'image%'")->fetchColumn()
    ];
    
} catch (Exception $e) {
    $error = "خطأ في قاعدة البيانات: " . $e->getMessage();
}

function formatFileSize($bytes) {
    if ($bytes >= 1073741824) {
        return number_format($bytes / 1073741824, 2) . ' GB';
    } elseif ($bytes >= 1048576) {
        return number_format($bytes / 1048576, 2) . ' MB';
    } elseif ($bytes >= 1024) {
        return number_format($bytes / 1024, 2) . ' KB';
    } else {
        return $bytes . ' bytes';
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مركز الرفع - Shahid Platform</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', 'Cairo', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #2d2d2d 100%);
            color: #fff;
            min-height: 100vh;
        }
        
        .admin-container {
            display: flex;
            min-height: 100vh;
        }
        
        .sidebar {
            width: 280px;
            background: rgba(47, 47, 47, 0.95);
            border-left: 2px solid rgba(229, 9, 20, 0.3);
            padding: 2rem 0;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
        }
        
        .sidebar-header {
            padding: 0 2rem 2rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            margin-bottom: 2rem;
        }
        
        .logo {
            color: #E50914;
            font-size: 1.8rem;
            font-weight: bold;
            text-decoration: none;
        }
        
        .sidebar-menu {
            list-style: none;
            padding: 0 1rem;
        }
        
        .sidebar-menu li {
            margin-bottom: 0.5rem;
        }
        
        .sidebar-menu a {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            color: #ccc;
            text-decoration: none;
            border-radius: 10px;
            transition: all 0.3s ease;
        }
        
        .sidebar-menu a:hover,
        .sidebar-menu a.active {
            background: rgba(229, 9, 20, 0.2);
            color: #fff;
        }
        
        .main-content {
            flex: 1;
            margin-right: 280px;
            padding: 2rem;
        }
        
        .header {
            background: rgba(47, 47, 47, 0.8);
            padding: 1.5rem 2rem;
            border-radius: 15px;
            margin-bottom: 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .header h1 {
            color: #E50914;
            font-size: 2rem;
        }
        
        .btn {
            padding: 0.75rem 1.5rem;
            background: linear-gradient(45deg, #E50914, #B8070F);
            color: white;
            border: none;
            border-radius: 8px;
            text-decoration: none;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(229, 9, 20, 0.3);
        }
        
        .btn-danger {
            background: linear-gradient(45deg, #F44336, #D32F2F);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: rgba(47, 47, 47, 0.8);
            padding: 1.5rem;
            border-radius: 15px;
            border: 2px solid rgba(229, 9, 20, 0.1);
            text-align: center;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #E50914;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            color: #ccc;
        }
        
        .upload-section {
            background: rgba(47, 47, 47, 0.8);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            border: 2px solid rgba(229, 9, 20, 0.1);
        }
        
        .upload-area {
            border: 2px dashed rgba(229, 9, 20, 0.5);
            border-radius: 15px;
            padding: 3rem;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .upload-area:hover {
            border-color: #E50914;
            background: rgba(229, 9, 20, 0.1);
        }
        
        .upload-area.dragover {
            border-color: #E50914;
            background: rgba(229, 9, 20, 0.2);
        }
        
        .upload-icon {
            font-size: 4rem;
            color: #E50914;
            margin-bottom: 1rem;
        }
        
        .upload-text {
            font-size: 1.2rem;
            margin-bottom: 1rem;
        }
        
        .upload-hint {
            color: #ccc;
            font-size: 0.9rem;
        }
        
        .file-input {
            display: none;
        }
        
        .files-section {
            background: rgba(47, 47, 47, 0.8);
            border-radius: 15px;
            padding: 2rem;
            border: 2px solid rgba(229, 9, 20, 0.1);
        }
        
        .files-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-top: 2rem;
        }
        
        .file-card {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 1.5rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }
        
        .file-card:hover {
            transform: translateY(-5px);
            border-color: rgba(229, 9, 20, 0.3);
        }
        
        .file-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }
        
        .file-icon {
            font-size: 2rem;
            color: #E50914;
        }
        
        .file-name {
            font-weight: bold;
            margin-bottom: 0.5rem;
            word-break: break-all;
        }
        
        .file-info {
            color: #ccc;
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
        }
        
        .file-actions {
            display: flex;
            gap: 0.5rem;
            margin-top: 1rem;
        }
        
        .alert {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 2rem;
        }
        
        .alert-success {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid rgba(76, 175, 80, 0.5);
            color: #4CAF50;
        }
        
        .alert-error {
            background: rgba(244, 67, 54, 0.2);
            border: 1px solid rgba(244, 67, 54, 0.5);
            color: #F44336;
        }
        
        .progress-bar {
            width: 100%;
            height: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            overflow: hidden;
            margin: 1rem 0;
            display: none;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(45deg, #E50914, #B8070F);
            width: 0%;
            transition: width 0.3s ease;
        }
        
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }
            
            .main-content {
                margin-right: 0;
                padding: 1rem;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .files-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <aside class="sidebar">
            <div class="sidebar-header">
                <a href="../index.php" class="logo">
                    <i class="fas fa-play-circle"></i> شاهد
                </a>
            </div>
            
            <ul class="sidebar-menu">
                <li><a href="dashboard.php"><i class="fas fa-tachometer-alt"></i> لوحة التحكم</a></li>
                <li><a href="content_manager.php"><i class="fas fa-film"></i> مدير المحتوى</a></li>
                <li><a href="upload_center.php" class="active"><i class="fas fa-upload"></i> مركز الرفع</a></li>
                <li><a href="users.php"><i class="fas fa-users"></i> إدارة المستخدمين</a></li>
                <li><a href="settings.php"><i class="fas fa-cog"></i> الإعدادات</a></li>
                <li><a href="../logout.php"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a></li>
            </ul>
        </aside>
        
        <main class="main-content">
            <div class="header">
                <h1><i class="fas fa-upload"></i> مركز الرفع</h1>
            </div>
            
            <?php if ($message): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i> <?php echo $message; ?>
                </div>
            <?php endif; ?>
            
            <?php if ($error): ?>
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-circle"></i> <?php echo $error; ?>
                </div>
            <?php endif; ?>
            
            <!-- إحصائيات -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number"><?php echo number_format($stats['total_files']); ?></div>
                    <div class="stat-label">إجمالي الملفات</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo formatFileSize($stats['total_size']); ?></div>
                    <div class="stat-label">إجمالي الحجم</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo number_format($stats['video_files']); ?></div>
                    <div class="stat-label">ملفات الفيديو</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo number_format($stats['image_files']); ?></div>
                    <div class="stat-label">ملفات الصور</div>
                </div>
            </div>
            
            <!-- قسم الرفع -->
            <div class="upload-section">
                <h2 style="color: #E50914; margin-bottom: 2rem;">
                    <i class="fas fa-cloud-upload-alt"></i> رفع ملفات جديدة
                </h2>
                
                <form id="uploadForm" method="POST" enctype="multipart/form-data">
                    <div class="upload-area" onclick="document.getElementById('fileInput').click()">
                        <div class="upload-icon">
                            <i class="fas fa-cloud-upload-alt"></i>
                        </div>
                        <div class="upload-text">اضغط هنا لاختيار الملفات أو اسحبها إلى هنا</div>
                        <div class="upload-hint">الأنواع المدعومة: MP4, AVI, MKV, JPEG, PNG, GIF (حتى 500MB)</div>
                    </div>
                    
                    <input type="file" id="fileInput" name="file" class="file-input" accept="video/*,image/*">
                    
                    <div class="progress-bar" id="progressBar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                </form>
            </div>
            
            <!-- قسم الملفات -->
            <div class="files-section">
                <h2 style="color: #E50914; margin-bottom: 1rem;">
                    <i class="fas fa-folder-open"></i> الملفات المرفوعة
                </h2>
                
                <div class="files-grid">
                    <?php foreach ($files as $file): ?>
                        <div class="file-card">
                            <div class="file-header">
                                <div class="file-icon">
                                    <?php if (strpos($file['file_type'], 'video') !== false): ?>
                                        <i class="fas fa-film"></i>
                                    <?php elseif (strpos($file['file_type'], 'image') !== false): ?>
                                        <i class="fas fa-image"></i>
                                    <?php else: ?>
                                        <i class="fas fa-file"></i>
                                    <?php endif; ?>
                                </div>
                                
                                <form method="POST" style="display: inline;" onsubmit="return confirm('هل أنت متأكد من حذف هذا الملف؟')">
                                    <input type="hidden" name="delete_file" value="1">
                                    <input type="hidden" name="file_id" value="<?php echo $file['id']; ?>">
                                    <button type="submit" class="btn btn-danger" style="padding: 0.5rem;">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </div>
                            
                            <div class="file-name"><?php echo htmlspecialchars($file['original_name']); ?></div>
                            <div class="file-info">الحجم: <?php echo formatFileSize($file['file_size']); ?></div>
                            <div class="file-info">النوع: <?php echo $file['file_type']; ?></div>
                            <div class="file-info">رفع بواسطة: <?php echo htmlspecialchars($file['username']); ?></div>
                            <div class="file-info">التاريخ: <?php echo date('Y-m-d H:i', strtotime($file['upload_date'])); ?></div>
                            
                            <div class="file-actions">
                                <button class="btn" onclick="copyToClipboard('<?php echo $file['file_path']; ?>')">
                                    <i class="fas fa-copy"></i> نسخ الرابط
                                </button>
                                
                                <?php if (strpos($file['file_type'], 'image') !== false): ?>
                                    <button class="btn btn-secondary" onclick="previewImage('<?php echo $file['file_path']; ?>')">
                                        <i class="fas fa-eye"></i> معاينة
                                    </button>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </main>
    </div>
    
    <script>
        // معالجة رفع الملفات
        document.getElementById('fileInput').addEventListener('change', function() {
            if (this.files.length > 0) {
                document.getElementById('uploadForm').submit();
            }
        });
        
        // معالجة السحب والإفلات
        const uploadArea = document.querySelector('.upload-area');
        
        uploadArea.addEventListener('dragover', function(e) {
            e.preventDefault();
            this.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', function(e) {
            e.preventDefault();
            this.classList.remove('dragover');
        });
        
        uploadArea.addEventListener('drop', function(e) {
            e.preventDefault();
            this.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                document.getElementById('fileInput').files = files;
                document.getElementById('uploadForm').submit();
            }
        });
        
        // نسخ الرابط
        function copyToClipboard(text) {
            navigator.clipboard.writeText(window.location.origin + '/' + text).then(function() {
                alert('تم نسخ الرابط!');
            });
        }
        
        // معاينة الصورة
        function previewImage(src) {
            const img = new Image();
            img.src = src;
            img.style.maxWidth = '100%';
            img.style.maxHeight = '80vh';
            
            const popup = window.open('', '_blank', 'width=800,height=600');
            popup.document.body.appendChild(img);
            popup.document.title = 'معاينة الصورة';
        }
        
        console.log('مركز الرفع جاهز!');
    </script>
</body>
</html>
