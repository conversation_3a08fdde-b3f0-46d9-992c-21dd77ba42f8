<?php
/**
 * Test API Permissions - Shahid Platform
 * Quick test to verify API access permissions
 */

echo "<h1>🔧 اختبار صلاحيات API - Shahid Platform</h1>";

$baseUrl = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']);
$apiBaseUrl = $baseUrl . '/api';

// Test API directory access
echo "<h2>📁 اختبار الوصول لمجلد API:</h2>";

$apiUrls = [
    $apiBaseUrl . '/' => 'مجلد API الرئيسي',
    $apiBaseUrl . '/index.html' => 'صفحة HTML الاحتياطية',
    $apiBaseUrl . '/index_simple.php' => 'API البسيط',
    $apiBaseUrl . '/test_api.php' => 'صفحة اختبار API'
];

foreach ($apiUrls as $url => $description) {
    echo "<h3>🧪 اختبار: $description</h3>";
    echo "<p><strong>URL:</strong> <a href='$url' target='_blank'>$url</a></p>";
    
    try {
        $context = stream_context_create([
            'http' => [
                'method' => 'GET',
                'timeout' => 10,
                'header' => 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            ]
        ]);
        
        $response = @file_get_contents($url, false, $context);
        
        if ($response === false) {
            $error = error_get_last();
            if (strpos($error['message'], '403') !== false) {
                echo "<div class='error'>❌ خطأ 403 Forbidden - مشكلة في الصلاحيات</div>";
            } elseif (strpos($error['message'], '404') !== false) {
                echo "<div class='error'>❌ خطأ 404 Not Found - الملف غير موجود</div>";
            } else {
                echo "<div class='error'>❌ لا يمكن الوصول: " . htmlspecialchars($error['message']) . "</div>";
            }
        } else {
            $responseLength = strlen($response);
            
            if ($responseLength > 0) {
                echo "<div class='success'>✅ يعمل بشكل صحيح (حجم الاستجابة: $responseLength بايت)</div>";
                
                // Check response type
                if (strpos($response, '<!DOCTYPE html>') !== false || strpos($response, '<html>') !== false) {
                    echo "<p>📄 نوع الاستجابة: HTML</p>";
                } elseif (strpos($response, '{"') !== false) {
                    echo "<p>📄 نوع الاستجابة: JSON</p>";
                } else {
                    echo "<p>📄 نوع الاستجابة: نص عادي</p>";
                }
                
                // Check for errors in response
                if (strpos($response, 'Fatal error') !== false) {
                    echo "<div class='warning'>⚠️ يحتوي على Fatal Error</div>";
                } elseif (strpos($response, 'Warning') !== false) {
                    echo "<div class='warning'>⚠️ يحتوي على تحذيرات PHP</div>";
                } elseif (strpos($response, 'Forbidden') !== false) {
                    echo "<div class='warning'>⚠️ يحتوي على رسالة Forbidden</div>";
                }
            } else {
                echo "<div class='warning'>⚠️ استجابة فارغة</div>";
            }
        }
        
    } catch (Exception $e) {
        echo "<div class='error'>❌ خطأ في الاختبار: " . htmlspecialchars($e->getMessage()) . "</div>";
    }
    
    echo "<hr>";
}

// Check file permissions
echo "<h2>📋 فحص الملفات والصلاحيات:</h2>";

$apiPath = __DIR__ . '/api';
$files = [
    '.htaccess' => 'إعدادات Apache',
    'index.html' => 'صفحة HTML الاحتياطية',
    'index_simple.php' => 'API البسيط',
    'test_api.php' => 'صفحة اختبار API'
];

foreach ($files as $file => $description) {
    $filePath = $apiPath . '/' . $file;
    
    echo "<h4>📄 $description ($file):</h4>";
    
    if (file_exists($filePath)) {
        echo "<div class='success'>✅ الملف موجود</div>";
        
        if (is_readable($filePath)) {
            echo "<div class='success'>✅ قابل للقراءة</div>";
        } else {
            echo "<div class='error'>❌ غير قابل للقراءة</div>";
        }
        
        $perms = fileperms($filePath);
        $permsString = substr(sprintf('%o', $perms), -4);
        echo "<p><strong>الصلاحيات:</strong> $permsString</p>";
        
        $size = filesize($filePath);
        echo "<p><strong>الحجم:</strong> $size بايت</p>";
        
    } else {
        echo "<div class='error'>❌ الملف غير موجود</div>";
    }
    
    echo "<hr>";
}

// Check .htaccess content
echo "<h2>⚙️ فحص محتوى .htaccess:</h2>";

$htaccessPath = $apiPath . '/.htaccess';
if (file_exists($htaccessPath)) {
    $htaccessContent = file_get_contents($htaccessPath);
    
    echo "<div class='info'>";
    echo "<h4>📋 محتوى .htaccess:</h4>";
    echo "<pre>" . htmlspecialchars($htaccessContent) . "</pre>";
    echo "</div>";
    
    // Check for important directives
    $checks = [
        'Require all granted' => 'السماح بالوصول',
        'DirectoryIndex' => 'تحديد الصفحة الافتراضية',
        'Options +Indexes' => 'السماح بعرض محتويات المجلد',
        'RewriteEngine On' => 'تفعيل إعادة الكتابة'
    ];
    
    echo "<h4>🔍 فحص التوجيهات المهمة:</h4>";
    foreach ($checks as $directive => $description) {
        if (strpos($htaccessContent, $directive) !== false) {
            echo "<div class='success'>✅ $description ($directive)</div>";
        } else {
            echo "<div class='warning'>⚠️ مفقود: $description ($directive)</div>";
        }
    }
    
} else {
    echo "<div class='error'>❌ ملف .htaccess غير موجود</div>";
}

// Recommendations
echo "<h2>💡 التوصيات والحلول:</h2>";

echo "<div class='info'>";
echo "<h3>🔧 إذا كانت هناك مشاكل في الصلاحيات:</h3>";
echo "<ol>";
echo "<li><strong>تحقق من إعدادات Apache:</strong> تأكد من تفعيل mod_rewrite</li>";
echo "<li><strong>أعد تشغيل Apache:</strong> من XAMPP Control Panel</li>";
echo "<li><strong>تحقق من صلاحيات المجلدات:</strong> يجب أن تكون قابلة للقراءة والتنفيذ</li>";
echo "<li><strong>تحقق من httpd.conf:</strong> تأكد من السماح بـ .htaccess</li>";
echo "</ol>";
echo "</div>";

echo "<div class='success'>";
echo "<h3>✅ الحلول المطبقة:</h3>";
echo "<ul>";
echo "<li>إضافة <code>Require all granted</code> في .htaccess</li>";
echo "<li>إضافة <code>Options +Indexes</code> للسماح بعرض المجلد</li>";
echo "<li>إنشاء صفحة index.html احتياطية</li>";
echo "<li>تحديد DirectoryIndex متعدد الخيارات</li>";
echo "</ul>";
echo "</div>";

echo "<h3>🔗 جرب الروابط:</h3>";
echo "<p><a href='api/' target='_blank' class='btn btn-primary'>🔗 مجلد API</a></p>";
echo "<p><a href='api/test_api.php' target='_blank' class='btn btn-info'>🧪 اختبار API</a></p>";
echo "<p><a href='admin/' target='_blank' class='btn btn-success'>🎛️ لوحة الإدارة</a></p>";

?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 40px;
    background: #f8f9fa;
    line-height: 1.6;
}

h1 {
    color: #E50914;
    border-bottom: 3px solid #E50914;
    padding-bottom: 10px;
    margin-bottom: 30px;
}

h2 {
    color: #333;
    margin-top: 30px;
    margin-bottom: 15px;
    border-left: 4px solid #E50914;
    padding-left: 15px;
}

h3, h4 {
    color: #555;
    margin-top: 20px;
    margin-bottom: 10px;
}

.success {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
    padding: 15px;
    border-radius: 5px;
    margin: 10px 0;
}

.error {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
    padding: 15px;
    border-radius: 5px;
    margin: 10px 0;
}

.warning {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
    padding: 15px;
    border-radius: 5px;
    margin: 10px 0;
}

.info {
    background: #d1ecf1;
    border: 1px solid #bee5eb;
    color: #0c5460;
    padding: 15px;
    border-radius: 5px;
    margin: 10px 0;
}

.btn {
    display: inline-block;
    padding: 10px 20px;
    margin: 5px;
    text-decoration: none;
    border-radius: 5px;
    font-weight: bold;
    transition: all 0.3s;
}

.btn-primary {
    background: #E50914;
    color: white;
}

.btn-info {
    background: #17a2b8;
    color: white;
}

.btn-success {
    background: #28a745;
    color: white;
}

.btn:hover {
    opacity: 0.8;
    text-decoration: none;
}

hr {
    border: none;
    border-top: 1px solid #ddd;
    margin: 20px 0;
}

pre {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    overflow-x: auto;
    font-size: 0.9em;
    border: 1px solid #ddd;
}

code {
    background: #f8f9fa;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
}

p {
    margin: 10px 0;
}

ol, ul {
    margin: 10px 0;
    padding-left: 20px;
}

li {
    margin: 5px 0;
}

a {
    color: #E50914;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}
</style>
