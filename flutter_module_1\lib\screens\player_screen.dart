import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:video_player/video_player.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';

class PlayerScreen extends StatefulWidget {
  final String contentId;
  final String contentType;
  final String title;
  final String? videoUrl;

  const PlayerScreen({
    Key? key,
    required this.contentId,
    required this.contentType,
    required this.title,
    this.videoUrl,
  }) : super(key: key);

  @override
  State<PlayerScreen> createState() => _PlayerScreenState();
}

class _PlayerScreenState extends State<PlayerScreen> {
  VideoPlayerController? _controller;
  bool _isLoading = true;
  bool _hasError = false;
  String _errorMessage = '';
  bool _showControls = true;
  bool _isPlaying = false;
  bool _isFullscreen = false;
  Duration _position = Duration.zero;
  Duration _duration = Duration.zero;
  String? _actualVideoUrl;

  @override
  void initState() {
    super.initState();
    _initializePlayer();
  }

  Future<void> _initializePlayer() async {
    try {
      // إذا لم يتم توفير رابط الفيديو، نحصل عليه من API
      if (widget.videoUrl == null || widget.videoUrl!.isEmpty) {
        await _startWatchSession();
      } else {
        _actualVideoUrl = widget.videoUrl;
        await _setupVideoPlayer();
      }
    } catch (e) {
      setState(() {
        _hasError = true;
        _errorMessage = 'خطأ في تحميل الفيديو: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _startWatchSession() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('user_token');

      if (token == null) {
        throw Exception('يجب تسجيل الدخول أولاً');
      }

      final response = await http.post(
        Uri.parse('http://localhost/amr2/flutter_module_1/backend/api/watch/start.php'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: json.encode({
          'content_type': widget.contentType,
          'content_id': widget.contentId,
          'watch_time': 0,
          'quality': 'auto',
          'device': 'mobile',
        }),
      );

      final data = json.decode(response.body);

      if (response.statusCode == 200 && data['success']) {
        _actualVideoUrl = data['data']['content']['video_url'];
        await _setupVideoPlayer();
      } else {
        throw Exception(data['message'] ?? 'فشل في بدء المشاهدة');
      }
    } catch (e) {
      setState(() {
        _hasError = true;
        _errorMessage = e.toString();
        _isLoading = false;
      });
    }
  }

  Future<void> _setupVideoPlayer() async {
    if (_actualVideoUrl == null || _actualVideoUrl!.isEmpty) {
      setState(() {
        _hasError = true;
        _errorMessage = 'رابط الفيديو غير متاح';
        _isLoading = false;
      });
      return;
    }

    try {
      // إنشاء مشغل الفيديو
      _controller = VideoPlayerController.network(_actualVideoUrl!);
      
      await _controller!.initialize();
      
      _controller!.addListener(_videoListener);
      
      setState(() {
        _isLoading = false;
        _duration = _controller!.value.duration;
      });

      // بدء التشغيل تلقائياً
      _controller!.play();
      setState(() {
        _isPlaying = true;
      });

    } catch (e) {
      setState(() {
        _hasError = true;
        _errorMessage = 'خطأ في تحميل الفيديو: $e';
        _isLoading = false;
      });
    }
  }

  void _videoListener() {
    if (_controller != null) {
      setState(() {
        _position = _controller!.value.position;
        _isPlaying = _controller!.value.isPlaying;
      });
    }
  }

  void _togglePlayPause() {
    if (_controller != null) {
      if (_controller!.value.isPlaying) {
        _controller!.pause();
      } else {
        _controller!.play();
      }
    }
  }

  void _seekTo(Duration position) {
    if (_controller != null) {
      _controller!.seekTo(position);
    }
  }

  void _toggleFullscreen() {
    setState(() {
      _isFullscreen = !_isFullscreen;
    });

    if (_isFullscreen) {
      SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);
      SystemChrome.setPreferredOrientations([
        DeviceOrientation.landscapeLeft,
        DeviceOrientation.landscapeRight,
      ]);
    } else {
      SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
      SystemChrome.setPreferredOrientations([
        DeviceOrientation.portraitUp,
      ]);
    }
  }

  void _showControlsTemporarily() {
    setState(() {
      _showControls = true;
    });

    Future.delayed(const Duration(seconds: 3), () {
      if (mounted && _isPlaying) {
        setState(() {
          _showControls = false;
        });
      }
    });
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);

    if (hours > 0) {
      return '${twoDigits(hours)}:${twoDigits(minutes)}:${twoDigits(seconds)}';
    } else {
      return '${twoDigits(minutes)}:${twoDigits(seconds)}';
    }
  }

  @override
  void dispose() {
    _controller?.removeListener(_videoListener);
    _controller?.dispose();
    
    // إعادة تعيين اتجاه الشاشة
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
    ]);
    
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: SafeArea(
        child: _isLoading
            ? _buildLoadingWidget()
            : _hasError
                ? _buildErrorWidget()
                : _buildPlayerWidget(),
      ),
    );
  }

  Widget _buildLoadingWidget() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Color(0xFFE50914)),
          ),
          SizedBox(height: 20),
          Text(
            'جاري تحميل الفيديو...',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              color: Color(0xFFE50914),
              size: 80,
            ),
            const SizedBox(height: 20),
            const Text(
              'خطأ في تشغيل الفيديو',
              style: TextStyle(
                color: Colors.white,
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 10),
            Text(
              _errorMessage,
              style: const TextStyle(
                color: Colors.grey,
                fontSize: 16,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 30),
            ElevatedButton(
              onPressed: () {
                setState(() {
                  _hasError = false;
                  _isLoading = true;
                });
                _initializePlayer();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFFE50914),
                padding: const EdgeInsets.symmetric(horizontal: 30, vertical: 15),
              ),
              child: const Text(
                'إعادة المحاولة',
                style: TextStyle(color: Colors.white),
              ),
            ),
            const SizedBox(height: 10),
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text(
                'العودة',
                style: TextStyle(color: Colors.grey),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPlayerWidget() {
    return GestureDetector(
      onTap: _showControlsTemporarily,
      child: Stack(
        children: [
          // مشغل الفيديو
          Center(
            child: AspectRatio(
              aspectRatio: _controller!.value.aspectRatio,
              child: VideoPlayer(_controller!),
            ),
          ),

          // أدوات التحكم
          if (_showControls)
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.black.withOpacity(0.7),
                    Colors.transparent,
                    Colors.transparent,
                    Colors.black.withOpacity(0.7),
                  ],
                ),
              ),
              child: Column(
                children: [
                  // شريط علوي
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Row(
                      children: [
                        IconButton(
                          onPressed: () => Navigator.pop(context),
                          icon: const Icon(
                            Icons.arrow_back,
                            color: Colors.white,
                            size: 28,
                          ),
                        ),
                        const SizedBox(width: 10),
                        Expanded(
                          child: Text(
                            widget.title,
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        IconButton(
                          onPressed: _toggleFullscreen,
                          icon: Icon(
                            _isFullscreen ? Icons.fullscreen_exit : Icons.fullscreen,
                            color: Colors.white,
                            size: 28,
                          ),
                        ),
                      ],
                    ),
                  ),

                  const Spacer(),

                  // أزرار التحكم الوسطى
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      IconButton(
                        onPressed: () {
                          final newPosition = _position - const Duration(seconds: 10);
                          _seekTo(newPosition);
                        },
                        icon: const Icon(
                          Icons.replay_10,
                          color: Colors.white,
                          size: 40,
                        ),
                      ),
                      const SizedBox(width: 30),
                      IconButton(
                        onPressed: _togglePlayPause,
                        icon: Icon(
                          _isPlaying ? Icons.pause : Icons.play_arrow,
                          color: Colors.white,
                          size: 60,
                        ),
                      ),
                      const SizedBox(width: 30),
                      IconButton(
                        onPressed: () {
                          final newPosition = _position + const Duration(seconds: 10);
                          _seekTo(newPosition);
                        },
                        icon: const Icon(
                          Icons.forward_10,
                          color: Colors.white,
                          size: 40,
                        ),
                      ),
                    ],
                  ),

                  const Spacer(),

                  // شريط التقدم والوقت
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      children: [
                        SliderTheme(
                          data: SliderTheme.of(context).copyWith(
                            activeTrackColor: const Color(0xFFE50914),
                            inactiveTrackColor: Colors.grey[600],
                            thumbColor: const Color(0xFFE50914),
                            overlayColor: const Color(0xFFE50914).withOpacity(0.2),
                            thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 8),
                            trackHeight: 4,
                          ),
                          child: Slider(
                            value: _duration.inMilliseconds > 0
                                ? _position.inMilliseconds / _duration.inMilliseconds
                                : 0.0,
                            onChanged: (value) {
                              final newPosition = Duration(
                                milliseconds: (value * _duration.inMilliseconds).round(),
                              );
                              _seekTo(newPosition);
                            },
                          ),
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              _formatDuration(_position),
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 14,
                              ),
                            ),
                            Text(
                              _formatDuration(_duration),
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 14,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }
}
