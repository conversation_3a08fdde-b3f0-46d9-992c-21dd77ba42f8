<?php
/**
 * مشغل فيديو بسيط - بدون قاعدة بيانات
 * Simple Video Player - No Database Required
 */

// بدء الجلسة بشكل آمن
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// تسجيل دخول تلقائي للاختبار
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1;
    $_SESSION['user_name'] = 'مستخدم تجريبي';
    $_SESSION['user_role'] = 'user';
}

// الحصول على معاملات الفيديو
$video_id = $_GET['id'] ?? 1;
$type = $_GET['type'] ?? 'movie';

// فيديوهات تجريبية عالية الجودة
$videos = [
    1 => [
        'title' => 'Big Buck Bunny',
        'description' => 'فيلم رسوم متحركة قصير مفتوح المصدر من إنتاج Blender Foundation',
        'url' => 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
        'poster' => 'https://peach.blender.org/wp-content/uploads/bbb-splash.png',
        'duration' => 596,
        'year' => 2008,
        'rating' => 4.8
    ],
    2 => [
        'title' => 'Elephant Dream',
        'description' => 'أول فيلم رسوم متحركة مفتوح المصدر في العالم',
        'url' => 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4',
        'poster' => 'https://orange.blender.org/wp-content/themes/orange/images/media/gallery/s1_proog.jpg',
        'duration' => 653,
        'year' => 2006,
        'rating' => 4.5
    ],
    3 => [
        'title' => 'For Bigger Blazes',
        'description' => 'فيديو تجريبي عالي الجودة للاختبار',
        'url' => 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerBlazes.mp4',
        'poster' => 'https://via.placeholder.com/640x360/E50914/FFFFFF?text=For+Bigger+Blazes',
        'duration' => 15,
        'year' => 2023,
        'rating' => 4.2
    ],
    4 => [
        'title' => 'For Bigger Escapes',
        'description' => 'فيديو تجريبي آخر عالي الجودة',
        'url' => 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerEscapes.mp4',
        'poster' => 'https://via.placeholder.com/640x360/E50914/FFFFFF?text=For+Bigger+Escapes',
        'duration' => 15,
        'year' => 2023,
        'rating' => 4.3
    ],
    5 => [
        'title' => 'Sintel',
        'description' => 'فيلم رسوم متحركة قصير مذهل من Blender Foundation',
        'url' => 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/Sintel.mp4',
        'poster' => 'https://durian.blender.org/wp-content/uploads/2010/06/sintel_trailer_1080p.still009.jpg',
        'duration' => 888,
        'year' => 2010,
        'rating' => 4.9
    ]
];

// الحصول على بيانات الفيديو
$video = $videos[$video_id] ?? $videos[1];
$video['id'] = $video_id;

// إحصائيات وهمية
$video['views'] = rand(10000, 100000);
$video['likes'] = rand(1000, 10000);
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎬 <?php echo htmlspecialchars($video['title']); ?> - Shahid Player</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🎬</text></svg>">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', 'Cairo', Tahoma, Geneva, Verdana, sans-serif;
            background: #000;
            color: #fff;
            overflow-x: hidden;
        }
        
        .player-container {
            position: relative;
            width: 100%;
            min-height: 100vh;
            background: linear-gradient(135deg, #000 0%, #1a1a1a 100%);
        }
        
        .video-wrapper {
            position: relative;
            width: 100%;
            height: 70vh;
            background: #000;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .video-player {
            width: 100%;
            height: 100%;
            max-width: 100%;
            max-height: 100%;
            outline: none;
        }
        
        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            z-index: 10;
            transition: opacity 0.5s ease;
        }
        
        .loading-overlay.hidden {
            opacity: 0;
            pointer-events: none;
        }
        
        .loading-spinner {
            width: 60px;
            height: 60px;
            border: 4px solid rgba(229, 9, 20, 0.3);
            border-top: 4px solid #E50914;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 1.5rem;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .loading-text {
            font-size: 1.2rem;
            color: #E50914;
            font-weight: bold;
        }
        
        .video-info {
            padding: 3rem;
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            min-height: 30vh;
        }
        
        .video-header {
            display: flex;
            gap: 2rem;
            margin-bottom: 2rem;
            align-items: flex-start;
        }
        
        .video-poster {
            width: 200px;
            height: 300px;
            border-radius: 15px;
            object-fit: cover;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.5);
            border: 2px solid rgba(229, 9, 20, 0.3);
        }
        
        .video-details {
            flex: 1;
        }
        
        .video-title {
            font-size: 3rem;
            font-weight: bold;
            margin-bottom: 1rem;
            color: #E50914;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
            line-height: 1.2;
        }
        
        .video-meta {
            display: flex;
            gap: 2rem;
            margin-bottom: 2rem;
            flex-wrap: wrap;
        }
        
        .meta-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 1.2rem;
            opacity: 0.9;
            background: rgba(229, 9, 20, 0.1);
            padding: 0.5rem 1rem;
            border-radius: 25px;
            border: 1px solid rgba(229, 9, 20, 0.3);
        }
        
        .video-description {
            font-size: 1.3rem;
            line-height: 1.8;
            opacity: 0.9;
            margin-bottom: 2rem;
            max-width: 800px;
            background: rgba(47, 47, 47, 0.5);
            padding: 1.5rem;
            border-radius: 15px;
            border-left: 4px solid #E50914;
        }
        
        .controls-section {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
            margin-bottom: 2rem;
        }
        
        .btn {
            background: linear-gradient(45deg, #E50914, #B8070F);
            color: white;
            padding: 1rem 2rem;
            text-decoration: none;
            border-radius: 12px;
            font-weight: bold;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            font-size: 1.1rem;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            box-shadow: 0 4px 15px rgba(229, 9, 20, 0.2);
        }
        
        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(229, 9, 20, 0.4);
        }
        
        .btn.secondary {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
        
        .btn.secondary:hover {
            background: rgba(255, 255, 255, 0.2);
        }
        
        .btn.success {
            background: linear-gradient(45deg, #28a745, #20c997);
        }
        
        .btn.info {
            background: linear-gradient(45deg, #17a2b8, #20c997);
        }
        
        .btn.warning {
            background: linear-gradient(45deg, #ffc107, #fd7e14);
            color: #000;
        }
        
        .quality-selector {
            position: relative;
            display: inline-block;
        }
        
        .quality-menu {
            position: absolute;
            bottom: 100%;
            left: 0;
            background: rgba(0, 0, 0, 0.95);
            border: 1px solid rgba(229, 9, 20, 0.3);
            border-radius: 12px;
            padding: 1rem;
            display: none;
            z-index: 20;
            min-width: 150px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.5);
        }
        
        .quality-menu.show {
            display: block;
        }
        
        .quality-option {
            padding: 0.8rem 1rem;
            cursor: pointer;
            border-radius: 8px;
            transition: all 0.2s;
            margin-bottom: 0.5rem;
            border: 1px solid transparent;
        }
        
        .quality-option:hover {
            background: rgba(229, 9, 20, 0.2);
            border-color: rgba(229, 9, 20, 0.5);
        }
        
        .quality-option:last-child {
            margin-bottom: 0;
        }
        
        .video-list {
            margin-top: 3rem;
        }
        
        .section-title {
            font-size: 2rem;
            color: #E50914;
            margin-bottom: 2rem;
            text-align: center;
            position: relative;
        }
        
        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 3px;
            background: linear-gradient(45deg, #E50914, #B8070F);
            border-radius: 2px;
        }
        
        .video-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
        }
        
        .video-card {
            background: rgba(47, 47, 47, 0.8);
            border-radius: 15px;
            overflow: hidden;
            transition: all 0.3s ease;
            border: 1px solid rgba(229, 9, 20, 0.2);
            cursor: pointer;
        }
        
        .video-card:hover {
            transform: translateY(-8px);
            border-color: rgba(229, 9, 20, 0.5);
            box-shadow: 0 10px 30px rgba(229, 9, 20, 0.3);
        }
        
        .video-card img {
            width: 100%;
            height: 150px;
            object-fit: cover;
        }
        
        .video-card-content {
            padding: 1.5rem;
        }
        
        .video-card h3 {
            color: #E50914;
            margin-bottom: 0.5rem;
            font-size: 1.2rem;
        }
        
        .video-card p {
            opacity: 0.8;
            font-size: 0.9rem;
            line-height: 1.4;
        }
        
        @media (max-width: 768px) {
            .video-header {
                flex-direction: column;
                align-items: center;
                text-align: center;
            }
            
            .video-poster {
                width: 150px;
                height: 225px;
            }
            
            .video-title {
                font-size: 2rem;
            }
            
            .video-meta {
                gap: 1rem;
                justify-content: center;
            }
            
            .controls-section {
                gap: 0.5rem;
                justify-content: center;
            }
            
            .btn {
                padding: 0.8rem 1.5rem;
                font-size: 1rem;
            }
            
            .video-info {
                padding: 2rem 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="player-container">
        <div class="video-wrapper">
            <div class="loading-overlay" id="loadingOverlay">
                <div class="loading-spinner"></div>
                <div class="loading-text">جاري تحميل الفيديو...</div>
            </div>
            <video id="videoPlayer" class="video-player" controls preload="metadata" poster="<?php echo $video['poster']; ?>">
                <source src="<?php echo $video['url']; ?>" type="video/mp4">
                متصفحك لا يدعم تشغيل الفيديو.
            </video>
        </div>
        
        <div class="video-info">
            <div class="video-header">
                <img src="<?php echo $video['poster']; ?>" alt="<?php echo htmlspecialchars($video['title']); ?>" class="video-poster">
                
                <div class="video-details">
                    <h1 class="video-title"><?php echo htmlspecialchars($video['title']); ?></h1>
                    
                    <div class="video-meta">
                        <div class="meta-item">
                            <span>📅</span>
                            <span><?php echo $video['year']; ?></span>
                        </div>
                        <div class="meta-item">
                            <span>⏱️</span>
                            <span><?php echo gmdate('H:i:s', $video['duration']); ?></span>
                        </div>
                        <div class="meta-item">
                            <span>⭐</span>
                            <span><?php echo $video['rating']; ?>/5</span>
                        </div>
                        <div class="meta-item">
                            <span>👁️</span>
                            <span><?php echo number_format($video['views']); ?> مشاهدة</span>
                        </div>
                        <div class="meta-item">
                            <span>👍</span>
                            <span><?php echo number_format($video['likes']); ?> إعجاب</span>
                        </div>
                    </div>
                    
                    <div class="controls-section">
                        <button class="btn" onclick="togglePlay()">
                            <span id="playIcon">▶️</span>
                            <span id="playText">تشغيل</span>
                        </button>
                        
                        <button class="btn" onclick="toggleFullscreen()">
                            🔳 ملء الشاشة
                        </button>
                        
                        <div class="quality-selector">
                            <button class="btn secondary" onclick="toggleQualityMenu()">
                                ⚙️ الجودة
                            </button>
                            <div class="quality-menu" id="qualityMenu">
                                <div class="quality-option" onclick="changeQuality('1080p')">🎬 1080p HD</div>
                                <div class="quality-option" onclick="changeQuality('720p')">📺 720p</div>
                                <div class="quality-option" onclick="changeQuality('480p')">📱 480p</div>
                                <div class="quality-option" onclick="changeQuality('360p')">⚡ 360p</div>
                            </div>
                        </div>
                        
                        <button class="btn secondary" onclick="toggleMute()">
                            <span id="muteIcon">🔊</span>
                            <span id="muteText">كتم الصوت</span>
                        </button>
                        
                        <button class="btn secondary" onclick="changeSpeed()">
                            <span id="speedIcon">⚡</span>
                            <span id="speedText">السرعة: 1x</span>
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="video-description">
                <strong>📝 الوصف:</strong><br>
                <?php echo htmlspecialchars($video['description']); ?>
            </div>
            
            <!-- روابط التنقل -->
            <div style="text-align: center; margin: 2rem 0;">
                <a href="../admin/simple_dashboard.php" class="btn info">🎛️ لوحة الإدارة</a>
                <a href="../homepage.php" class="btn success">🏠 الصفحة الرئيسية</a>
                <a href="simple_player.php" class="btn warning">🎬 مشغل بسيط</a>
            </div>
            
            <!-- قائمة الفيديوهات الأخرى -->
            <div class="video-list">
                <h2 class="section-title">🎬 فيديوهات أخرى</h2>
                <div class="video-grid">
                    <?php foreach ($videos as $id => $vid): ?>
                        <?php if ($id != $video_id): ?>
                        <div class="video-card" onclick="location.href='?id=<?php echo $id; ?>&type=<?php echo $type; ?>'">
                            <img src="<?php echo $vid['poster']; ?>" alt="<?php echo htmlspecialchars($vid['title']); ?>">
                            <div class="video-card-content">
                                <h3><?php echo htmlspecialchars($vid['title']); ?></h3>
                                <p><?php echo htmlspecialchars(substr($vid['description'], 0, 100)); ?>...</p>
                            </div>
                        </div>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        const video = document.getElementById('videoPlayer');
        const loadingOverlay = document.getElementById('loadingOverlay');
        const playIcon = document.getElementById('playIcon');
        const playText = document.getElementById('playText');
        const muteIcon = document.getElementById('muteIcon');
        const muteText = document.getElementById('muteText');
        const speedIcon = document.getElementById('speedIcon');
        const speedText = document.getElementById('speedText');
        
        let currentSpeed = 1;
        const speeds = [0.5, 0.75, 1, 1.25, 1.5, 2];
        let speedIndex = 2; // البداية بسرعة 1x
        
        // إخفاء شاشة التحميل عند تحميل الفيديو
        video.addEventListener('loadeddata', () => {
            loadingOverlay.classList.add('hidden');
            console.log('🎬 تم تحميل الفيديو بنجاح!');
        });
        
        // تحديث أيقونة التشغيل
        video.addEventListener('play', () => {
            playIcon.textContent = '⏸️';
            playText.textContent = 'إيقاف';
        });
        
        video.addEventListener('pause', () => {
            playIcon.textContent = '▶️';
            playText.textContent = 'تشغيل';
        });
        
        // وظائف التحكم
        function togglePlay() {
            if (video.paused) {
                video.play();
            } else {
                video.pause();
            }
        }
        
        function toggleFullscreen() {
            if (video.requestFullscreen) {
                video.requestFullscreen();
            } else if (video.webkitRequestFullscreen) {
                video.webkitRequestFullscreen();
            } else if (video.msRequestFullscreen) {
                video.msRequestFullscreen();
            }
        }
        
        function toggleMute() {
            video.muted = !video.muted;
            muteIcon.textContent = video.muted ? '🔇' : '🔊';
            muteText.textContent = video.muted ? 'إلغاء الكتم' : 'كتم الصوت';
        }
        
        function changeSpeed() {
            speedIndex = (speedIndex + 1) % speeds.length;
            currentSpeed = speeds[speedIndex];
            video.playbackRate = currentSpeed;
            speedText.textContent = `السرعة: ${currentSpeed}x`;
            
            // تأثير بصري
            speedIcon.textContent = currentSpeed > 1 ? '⚡' : currentSpeed < 1 ? '🐌' : '⚡';
        }
        
        function toggleQualityMenu() {
            const menu = document.getElementById('qualityMenu');
            menu.classList.toggle('show');
        }
        
        function changeQuality(quality) {
            alert(`🎥 تم تغيير الجودة إلى: ${quality}`);
            toggleQualityMenu();
        }
        
        // اختصارات لوحة المفاتيح
        document.addEventListener('keydown', (e) => {
            switch(e.code) {
                case 'Space':
                    e.preventDefault();
                    togglePlay();
                    break;
                case 'KeyF':
                    toggleFullscreen();
                    break;
                case 'KeyM':
                    toggleMute();
                    break;
                case 'ArrowRight':
                    video.currentTime += 10;
                    break;
                case 'ArrowLeft':
                    video.currentTime -= 10;
                    break;
                case 'ArrowUp':
                    video.volume = Math.min(1, video.volume + 0.1);
                    break;
                case 'ArrowDown':
                    video.volume = Math.max(0, video.volume - 0.1);
                    break;
            }
        });
        
        // إغلاق قائمة الجودة عند النقر خارجها
        document.addEventListener('click', (e) => {
            const qualitySelector = document.querySelector('.quality-selector');
            if (!qualitySelector.contains(e.target)) {
                document.getElementById('qualityMenu').classList.remove('show');
            }
        });
        
        // تسجيل المشاهدة (محلياً)
        video.addEventListener('timeupdate', () => {
            const progress = (video.currentTime / video.duration) * 100;
            if (progress > 10) { // بعد مشاهدة 10%
                localStorage.setItem(`video_${<?php echo $video_id; ?>}_watched`, Date.now());
            }
        });
        
        console.log('🎬 مشغل الفيديو البسيط يعمل بنجاح!');
        console.log('⌨️ اختصارات لوحة المفاتيح:');
        console.log('Space: تشغيل/إيقاف');
        console.log('F: ملء الشاشة');
        console.log('M: كتم/إلغاء كتم الصوت');
        console.log('←/→: تقديم/تأخير 10 ثواني');
        console.log('↑/↓: رفع/خفض الصوت');
    </script>
</body>
</html>
