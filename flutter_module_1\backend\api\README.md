# 🔗 **Shahid API - واجهة برمجة التطبيقات الاحترافية**

## 📋 **نظرة عامة**

واجهة برمجة تطبيقات احترافية ومتطورة لمنصة شاهد، توفر جميع الوظائف المطلوبة لتطوير تطبيقات البث الاحترافية.

## 🌐 **Base URL**
```
http://localhost/backend/api/
```

## 🔐 **المصادقة**

معظم endpoints تتطلب مصادقة باستخدام JWT tokens. أضف الـ token في header:

```
Authorization: Bearer YOUR_JWT_TOKEN
```

## 📊 **تنسيق الاستجابة**

جميع استجابات API تتبع هذا التنسيق:

### Success Response
```json
{
    "success": true,
    "data": {...},
    "timestamp": "2024-01-15T10:30:00Z"
}
```

### Error Response
```json
{
    "success": false,
    "error": "Error message",
    "timestamp": "2024-01-15T10:30:00Z"
}
```

### Paginated Response
```json
{
    "success": true,
    "data": [...],
    "pagination": {
        "current_page": 1,
        "per_page": 20,
        "total": 100,
        "total_pages": 5,
        "has_next": true,
        "has_prev": false
    },
    "timestamp": "2024-01-15T10:30:00Z"
}
```

## Authentication Endpoints

### POST /auth/login
Login with email and password.

**Request Body:**
```json
{
    "email": "<EMAIL>",
    "password": "password123"
}
```

**Response:**
```json
{
    "success": true,
    "token": "jwt_token_here",
    "user": {
        "id": 1,
        "name": "John Doe",
        "email": "<EMAIL>",
        "role": "user",
        "avatar": "/storage/avatars/avatar.jpg"
    }
}
```

### POST /auth/register
Register a new user account.

**Request Body:**
```json
{
    "name": "John Doe",
    "email": "<EMAIL>",
    "password": "password123"
}
```

### POST /auth/refresh
Refresh JWT token.

**Headers:** `Authorization: Bearer TOKEN`

### POST /auth/logout
Logout user (client-side token invalidation).

### GET /auth/verify
Verify token validity.

## Movies Endpoints

### GET /movies
Get list of movies with pagination and filters.

**Query Parameters:**
- `page` (int): Page number (default: 1)
- `limit` (int): Items per page (default: 20, max: 50)
- `genre` (string): Filter by genre
- `year` (int): Filter by year
- `sort` (string): Sort order (latest, popular, rating, title)
- `search` (string): Search query

### GET /movies/{id}
Get movie details by ID.

### GET /movies/featured
Get featured movies.

### GET /movies/popular
Get popular movies.

### GET /movies/latest
Get latest movies.

## Series Endpoints

### GET /series
Get list of series with pagination and filters.

### GET /series/{id}
Get series details with episodes.

### GET /series/featured
Get featured series.

### GET /series/popular
Get popular series.

### GET /series/latest
Get latest series.

### GET /series/genres
Get all available genres.

## Episodes Endpoints

### GET /episodes/{id}
Get episode details.

### GET /episodes/latest
Get latest episodes.

### GET /episodes/series/{series_id}
Get episodes by series ID.

**Query Parameters:**
- `season` (int): Filter by season number

## User Endpoints

### GET /user/profile
Get user profile information.

**Headers:** `Authorization: Bearer TOKEN`

### PUT /user/update
Update user profile.

**Headers:** `Authorization: Bearer TOKEN`

**Request Body:**
```json
{
    "name": "New Name",
    "phone": "+1234567890",
    "date_of_birth": "1990-01-01",
    "gender": "male",
    "country": "US"
}
```

### POST /user/change-password
Change user password.

**Headers:** `Authorization: Bearer TOKEN`

**Request Body:**
```json
{
    "current_password": "old_password",
    "new_password": "new_password"
}
```

### POST /user/upload-avatar
Upload user avatar image.

**Headers:** `Authorization: Bearer TOKEN`

**Form Data:**
- `avatar`: Image file (JPEG, PNG, GIF, max 5MB)

### GET /user/subscription
Get user's current subscription.

### GET /user/favorites
Get user's favorite content.

**Query Parameters:**
- `type` (string): Filter by type (all, movies, series)
- `page`, `limit`: Pagination

### POST /user/favorites/add
Add content to favorites.

**Request Body:**
```json
{
    "content_id": 123,
    "content_type": "movie"
}
```

### POST /user/favorites/remove
Remove content from favorites.

### POST /user/favorites/toggle
Toggle favorite status.

### GET /user/watchlist
Get user's watchlist.

### POST /user/watchlist/add
Add content to watchlist.

### POST /user/watchlist/remove
Remove content from watchlist.

### GET /user/history
Get user's watch history.

### POST /user/history/update
Update watch progress.

**Request Body:**
```json
{
    "content_id": 123,
    "content_type": "movie",
    "progress": 1800,
    "duration": 7200
}
```

### GET /user/devices
Get user's registered devices.

### POST /user/devices/register
Register a new device.

### POST /user/devices/remove
Remove a device.

### GET /user/notifications
Get user notifications.

### POST /user/notifications/mark-read
Mark notification as read.

### POST /user/notifications/mark-all-read
Mark all notifications as read.

### GET /user/preferences
Get user preferences.

### PUT /user/preferences
Update user preferences.

## Search Endpoints

### GET /search
Search for content.

**Query Parameters:**
- `q` (string): Search query (required)
- `type` (string): Content type (all, movies, series)
- `page`, `limit`: Pagination

### GET /search/suggestions
Get search suggestions.

**Query Parameters:**
- `q` (string): Partial query
- `limit` (int): Max suggestions (default: 5)

### GET /search/trending
Get trending searches.

**Query Parameters:**
- `period` (string): Time period (day, week, month)
- `limit` (int): Max results

### GET /search/filters
Get available search filters.

### GET /search/advanced
Advanced search with filters.

**Query Parameters:**
- `q`, `type`, `genre`, `year`, `country`, `language`
- `rating_min`, `rating_max`, `duration_min`, `duration_max`
- `quality`, `premium`, `sort`

## Subscription Endpoints

### GET /subscriptions
Get available subscription plans.

### POST /subscriptions/subscribe
Subscribe to a plan.

**Headers:** `Authorization: Bearer TOKEN`

**Request Body:**
```json
{
    "plan_id": 1,
    "payment_method": "stripe"
}
```

### POST /subscriptions/cancel
Cancel subscription.

### POST /subscriptions/upgrade
Upgrade subscription.

## Streaming Endpoints

### GET /stream/{id}
Stream video content.

**Query Parameters:**
- `token` (string): Video streaming token (required)
- `quality` (string): Video quality preference

**Headers:**
- `Range`: For partial content requests

### GET /subtitles/{id}
Get subtitle file.

**Query Parameters:**
- `token` (string): Streaming token (required)

### GET /subtitles/list
List available subtitles for content.

## Download Endpoints

### GET /download-check
Check download permissions.

**Headers:** `Authorization: Bearer TOKEN`

**Query Parameters:**
- `content_id` (int): Content ID
- `content_type` (string): Content type

### GET /download/{id}
Download content file.

**Headers:** `Authorization: Bearer TOKEN`

**Query Parameters:**
- `token` (string): Download token (required)
- `quality` (string): Quality preference

## Analytics Endpoints

### POST /analytics
Track general events.

**Request Body:**
```json
{
    "event_type": "button_click",
    "event_data": {
        "button_name": "play",
        "page": "/movies/123"
    }
}
```

### POST /analytics/video
Track video events.

**Request Body:**
```json
{
    "content_id": 123,
    "content_type": "movie",
    "event_type": "play",
    "current_time": 1800,
    "duration": 7200,
    "quality": "1080p",
    "device_type": "desktop"
}
```

### POST /analytics/page
Track page views.

### POST /analytics/search
Track search events.

### POST /analytics/error
Track error events.

## Error Codes

- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `405` - Method Not Allowed
- `429` - Too Many Requests
- `500` - Internal Server Error
- `503` - Service Unavailable

## Rate Limiting

API requests are limited to:
- 100 requests per hour per IP address
- 1000 requests per hour for authenticated users

## CORS

The API supports Cross-Origin Resource Sharing (CORS) for web applications.

## Webhooks

For payment processing and subscription updates, webhooks are available:

### POST /webhooks/stripe
Stripe webhook endpoint for payment events.

### POST /webhooks/paypal
PayPal webhook endpoint for payment events.

## SDK and Libraries

Official SDKs are available for:
- JavaScript/TypeScript
- Flutter/Dart
- PHP
- Python

## Support

For API support and questions:
- Email: <EMAIL>
- Documentation: https://docs.shahid.com
- Status Page: https://status.shahid.com
