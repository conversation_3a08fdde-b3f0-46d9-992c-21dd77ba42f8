<?php
/**
 * Shahid Download API
 * Professional Video Streaming Platform
 */

require_once '../core/Database.php';
require_once '../core/Security.php';
require_once '../core/ApiController.php';
require_once '../models/User.php';
require_once '../models/Movie.php';
require_once '../models/Episode.php';

class DownloadAPI extends ApiController {
    
    public function handleRequest() {
        $route = trim($_SERVER['REQUEST_URI'], '/');
        $segments = explode('/', $route);
        
        // Remove 'api' from segments
        $segments = array_slice($segments, 1);
        
        $action = $segments[0] ?? null;
        $id = $segments[1] ?? null;
        
        switch ($action) {
            case 'download-check':
                $this->checkDownloadPermission();
                break;
            case 'download':
                if ($id && is_numeric($id)) {
                    $this->downloadContent($id);
                } else {
                    $this->sendError('Content ID is required', 400);
                }
                break;
            default:
                $this->sendError('Invalid download endpoint', 404);
        }
    }
    
    private function checkDownloadPermission() {
        if (!$this->validateMethod(['GET'])) return;
        
        $user = $this->authenticateUser();
        if (!$user) return;
        
        $contentId = intval($_GET['content_id'] ?? $_GET['movie_id'] ?? 0);
        $contentType = $_GET['content_type'] ?? 'movie';
        
        if ($contentId <= 0) {
            $this->sendError('Valid content ID is required', 400);
            return;
        }
        
        // Check if user has active subscription
        $userModel = new User();
        if (!$userModel->hasActiveSubscription($user['id'])) {
            $this->sendError('Active subscription required for downloads', 403);
            return;
        }
        
        // Get content info
        $content = $this->getContentInfo($contentId, $contentType);
        if (!$content) {
            $this->sendError('Content not found', 404);
            return;
        }
        
        // Check if content allows downloads
        if (!$content['download_enabled']) {
            $this->sendError('Downloads not available for this content', 403);
            return;
        }
        
        // Check download limits
        $downloadLimits = $this->checkDownloadLimits($user['id']);
        if (!$downloadLimits['allowed']) {
            $this->sendError($downloadLimits['message'], 403);
            return;
        }
        
        $this->sendSuccess([
            'allowed' => true,
            'content' => [
                'id' => $content['id'],
                'title' => $content['title'],
                'type' => $contentType
            ],
            'download_limits' => $downloadLimits
        ]);
    }
    
    private function downloadContent($contentId) {
        if (!$this->validateMethod(['GET'])) return;
        
        $user = $this->authenticateUser();
        if (!$user) return;
        
        $contentType = $_GET['content_type'] ?? 'movie';
        $quality = $_GET['quality'] ?? 'default';
        $token = $_GET['token'] ?? '';
        
        // Validate download token
        if (!$this->validateDownloadToken($token, $contentId, $user['id'])) {
            $this->sendError('Invalid or expired download token', 403);
            return;
        }
        
        // Check permissions again
        $userModel = new User();
        if (!$userModel->hasActiveSubscription($user['id'])) {
            $this->sendError('Active subscription required', 403);
            return;
        }
        
        // Get content info
        $content = $this->getContentInfo($contentId, $contentType);
        if (!$content || !$content['download_enabled']) {
            $this->sendError('Content not available for download', 404);
            return;
        }
        
        // Check download limits
        $downloadLimits = $this->checkDownloadLimits($user['id']);
        if (!$downloadLimits['allowed']) {
            $this->sendError($downloadLimits['message'], 403);
            return;
        }
        
        // Get file path
        $filePath = $this->getDownloadFilePath($content, $contentType, $quality);
        if (!$filePath || !file_exists($filePath)) {
            $this->sendError('Download file not found', 404);
            return;
        }
        
        // Log download
        $this->logDownload($user['id'], $contentId, $contentType, $quality);
        
        // Stream the file
        $this->streamDownloadFile($filePath, $content['title'], $contentType);
    }
    
    private function getContentInfo($contentId, $contentType) {
        try {
            if ($contentType === 'movie') {
                $movieModel = new Movie();
                return $movieModel->findById($contentId);
            } elseif ($contentType === 'episode') {
                $episodeModel = new Episode();
                return $episodeModel->findById($contentId);
            }
            return null;
        } catch (Exception $e) {
            error_log('Get Content Info Error: ' . $e->getMessage());
            return null;
        }
    }
    
    private function checkDownloadLimits($userId) {
        try {
            // Get user's subscription plan
            $userModel = new User();
            $subscription = $userModel->getCurrentSubscription($userId);
            
            if (!$subscription) {
                return ['allowed' => false, 'message' => 'No active subscription'];
            }
            
            // Get plan limits (assuming they're stored in subscription plan)
            $maxDownloads = $subscription['max_downloads'] ?? 10;
            $maxConcurrentDownloads = $subscription['max_concurrent_downloads'] ?? 3;
            
            // Count current downloads (last 30 days)
            $sql = "SELECT COUNT(*) as download_count 
                    FROM download_logs 
                    WHERE user_id = :user_id 
                    AND created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)";
            
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':user_id', $userId);
            $stmt->execute();
            $currentDownloads = $stmt->fetch()['download_count'];
            
            // Count active downloads (in progress)
            $sql = "SELECT COUNT(*) as active_downloads 
                    FROM download_logs 
                    WHERE user_id = :user_id 
                    AND status = 'in_progress'";
            
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':user_id', $userId);
            $stmt->execute();
            $activeDownloads = $stmt->fetch()['active_downloads'];
            
            if ($currentDownloads >= $maxDownloads) {
                return [
                    'allowed' => false,
                    'message' => 'Monthly download limit reached',
                    'current' => $currentDownloads,
                    'limit' => $maxDownloads
                ];
            }
            
            if ($activeDownloads >= $maxConcurrentDownloads) {
                return [
                    'allowed' => false,
                    'message' => 'Too many concurrent downloads',
                    'active' => $activeDownloads,
                    'limit' => $maxConcurrentDownloads
                ];
            }
            
            return [
                'allowed' => true,
                'current_downloads' => $currentDownloads,
                'max_downloads' => $maxDownloads,
                'active_downloads' => $activeDownloads,
                'max_concurrent' => $maxConcurrentDownloads
            ];
            
        } catch (Exception $e) {
            error_log('Check Download Limits Error: ' . $e->getMessage());
            return ['allowed' => false, 'message' => 'Unable to check download limits'];
        }
    }
    
    private function validateDownloadToken($token, $contentId, $userId) {
        try {
            $payload = $this->security->validateJWT($token);
            
            if (!$payload) {
                return false;
            }
            
            return isset($payload['content_id']) && 
                   isset($payload['user_id']) &&
                   $payload['content_id'] == $contentId &&
                   $payload['user_id'] == $userId &&
                   isset($payload['type']) &&
                   $payload['type'] === 'download';
                   
        } catch (Exception $e) {
            return false;
        }
    }
    
    private function getDownloadFilePath($content, $contentType, $quality) {
        $baseDir = '../storage/downloads/';
        $contentDir = $contentType === 'movie' ? 'movies/' : 'episodes/';
        
        // Try specific quality first
        if ($quality !== 'default') {
            $qualityPath = $baseDir . $contentDir . $content['id'] . '/' . $quality . '.mp4';
            if (file_exists($qualityPath)) {
                return $qualityPath;
            }
        }
        
        // Fallback to default
        $defaultPath = $baseDir . $contentDir . $content['id'] . '/default.mp4';
        if (file_exists($defaultPath)) {
            return $defaultPath;
        }
        
        // Try common qualities
        $qualities = ['1080p', '720p', '480p'];
        foreach ($qualities as $q) {
            $path = $baseDir . $contentDir . $content['id'] . '/' . $q . '.mp4';
            if (file_exists($path)) {
                return $path;
            }
        }
        
        return null;
    }
    
    private function logDownload($userId, $contentId, $contentType, $quality) {
        try {
            $sql = "INSERT INTO download_logs (
                        user_id, content_id, content_type, quality,
                        status, ip_address, user_agent, created_at
                    ) VALUES (
                        :user_id, :content_id, :content_type, :quality,
                        'in_progress', :ip_address, :user_agent, NOW()
                    )";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute([
                ':user_id' => $userId,
                ':content_id' => $contentId,
                ':content_type' => $contentType,
                ':quality' => $quality,
                ':ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
                ':user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
            ]);
            
        } catch (Exception $e) {
            error_log('Log Download Error: ' . $e->getMessage());
        }
    }
    
    private function streamDownloadFile($filePath, $title, $contentType) {
        $fileSize = filesize($filePath);
        $fileName = $this->sanitizeFileName($title) . '.mp4';
        
        // Set headers for download
        header('Content-Type: application/octet-stream');
        header('Content-Disposition: attachment; filename="' . $fileName . '"');
        header('Content-Length: ' . $fileSize);
        header('Accept-Ranges: bytes');
        header('Cache-Control: no-cache');
        header('Pragma: no-cache');
        
        // Handle range requests for resume support
        $range = $_SERVER['HTTP_RANGE'] ?? '';
        if ($range) {
            $this->handleRangeRequest($filePath, $fileSize, $range);
        } else {
            // Stream entire file
            $this->streamFile($filePath);
        }
    }
    
    private function handleRangeRequest($filePath, $fileSize, $range) {
        if (preg_match('/bytes=(\d+)-(\d*)/', $range, $matches)) {
            $start = intval($matches[1]);
            $end = !empty($matches[2]) ? intval($matches[2]) : $fileSize - 1;
            
            if ($start > $end || $start >= $fileSize || $end >= $fileSize) {
                header('HTTP/1.1 416 Range Not Satisfiable');
                header("Content-Range: bytes */{$fileSize}");
                exit;
            }
            
            $contentLength = $end - $start + 1;
            
            header('HTTP/1.1 206 Partial Content');
            header("Content-Range: bytes {$start}-{$end}/{$fileSize}");
            header("Content-Length: {$contentLength}");
            
            $this->streamFileRange($filePath, $start, $contentLength);
        } else {
            $this->streamFile($filePath);
        }
    }
    
    private function streamFile($filePath) {
        $handle = fopen($filePath, 'rb');
        if (!$handle) {
            $this->sendError('Cannot open file for download', 500);
            return;
        }
        
        while (!feof($handle)) {
            echo fread($handle, 8192);
            flush();
            
            if (connection_aborted()) {
                break;
            }
        }
        
        fclose($handle);
    }
    
    private function streamFileRange($filePath, $start, $length) {
        $handle = fopen($filePath, 'rb');
        if (!$handle) {
            $this->sendError('Cannot open file for download', 500);
            return;
        }
        
        fseek($handle, $start);
        $bytesRemaining = $length;
        
        while ($bytesRemaining > 0 && !feof($handle)) {
            $chunkSize = min(8192, $bytesRemaining);
            $chunk = fread($handle, $chunkSize);
            
            if ($chunk === false) {
                break;
            }
            
            echo $chunk;
            flush();
            
            $bytesRemaining -= strlen($chunk);
            
            if (connection_aborted()) {
                break;
            }
        }
        
        fclose($handle);
    }
    
    private function sanitizeFileName($fileName) {
        // Remove or replace invalid characters
        $fileName = preg_replace('/[^a-zA-Z0-9\s\-_\.]/', '', $fileName);
        $fileName = preg_replace('/\s+/', '_', $fileName);
        $fileName = trim($fileName, '_');
        
        return $fileName ?: 'download';
    }
}

// Handle the request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

$api = new DownloadAPI();
$api->handleRequest();
?>
