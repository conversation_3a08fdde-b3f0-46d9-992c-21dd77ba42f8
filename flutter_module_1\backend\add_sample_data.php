<?php
/**
 * Add Sample Data - Shahid Platform
 * This script adds sample movies, series, and users for testing
 */

// Check if database exists
if (!file_exists('config/database.php')) {
    die('❌ ملف إعدادات قاعدة البيانات غير موجود');
}

$config = include 'config/database.php';

echo "<h1>📊 إضافة بيانات تجريبية - Shahid Platform</h1>";

try {
    $pdo = new PDO("mysql:host={$config['host']};dbname={$config['name']};charset=utf8mb4", 
                   $config['username'], $config['password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<p>✅ <strong>اتصال قاعدة البيانات ناجح</strong></p>";
    
    // Sample Movies
    echo "<h2>🎬 إضافة أفلام تجريبية:</h2>";
    
    $movies = [
        [
            'title' => 'الفيل الأزرق',
            'title_en' => 'The Blue Elephant',
            'description' => 'فيلم مصري من إخراج مروان حامد، يحكي قصة طبيب نفسي يواجه تحديات كبيرة',
            'year' => 2014,
            'duration' => 170,
            'genre' => 'دراما، إثارة',
            'rating' => 8.2,
            'poster' => 'https://image.tmdb.org/t/p/w500/blue_elephant.jpg',
            'director' => 'مروان حامد',
            'cast' => 'كريم عبد العزيز، خالد الصاوي، نيللي كريم',
            'country' => 'مصر',
            'language' => 'العربية',
            'age_rating' => '+15'
        ],
        [
            'title' => 'الجزيرة',
            'title_en' => 'The Island',
            'description' => 'فيلم أكشن مثير يحكي قصة مجموعة من الأشخاص المحاصرين في جزيرة نائية',
            'year' => 2023,
            'duration' => 135,
            'genre' => 'أكشن، مغامرة',
            'rating' => 7.8,
            'poster' => 'https://image.tmdb.org/t/p/w500/island.jpg',
            'director' => 'أحمد خالد موسى',
            'cast' => 'أحمد عز، منة شلبي، أحمد فهمي',
            'country' => 'مصر',
            'language' => 'العربية',
            'age_rating' => '+13'
        ],
        [
            'title' => 'ولد ملكة',
            'title_en' => 'Son of a Queen',
            'description' => 'كوميديا اجتماعية تحكي قصة شاب يحاول إثبات نفسه في المجتمع',
            'year' => 2023,
            'duration' => 110,
            'genre' => 'كوميديا، دراما',
            'rating' => 6.9,
            'poster' => 'https://image.tmdb.org/t/p/w500/son_queen.jpg',
            'director' => 'علي إدريس',
            'cast' => 'فتحي عبد الوهاب، ياسمين عبد العزيز، بيومي فؤاد',
            'country' => 'مصر',
            'language' => 'العربية',
            'age_rating' => 'عام'
        ],
        [
            'title' => 'الممر',
            'title_en' => 'The Passage',
            'description' => 'فيلم حربي يحكي قصة بطولات الجيش المصري في حرب أكتوبر',
            'year' => 2019,
            'duration' => 155,
            'genre' => 'حرب، دراما',
            'rating' => 8.5,
            'poster' => 'https://image.tmdb.org/t/p/w500/passage.jpg',
            'director' => 'شريف عرفة',
            'cast' => 'أحمد عز، أيتن عامر، أحمد فالح',
            'country' => 'مصر',
            'language' => 'العربية',
            'age_rating' => '+15'
        ],
        [
            'title' => 'كيرة والجن',
            'title_en' => 'Kira and the Jinn',
            'description' => 'فيلم خيال علمي يحكي قصة فتاة تكتشف قدرات خارقة',
            'year' => 2022,
            'duration' => 125,
            'genre' => 'خيال علمي، مغامرة',
            'rating' => 7.2,
            'poster' => 'https://image.tmdb.org/t/p/w500/kira_jinn.jpg',
            'director' => 'مروان حامد',
            'cast' => 'كندة علوش، علي ربيع، بيومي فؤاد',
            'country' => 'مصر',
            'language' => 'العربية',
            'age_rating' => '+13'
        ]
    ];
    
    $stmt = $pdo->prepare("INSERT INTO movies (title, title_en, description, year, duration, genre, rating, poster, director, cast, country, language, age_rating, status, featured) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'published', ?)");
    
    foreach ($movies as $index => $movie) {
        $featured = $index < 2 ? 1 : 0; // First 2 movies are featured
        $stmt->execute([
            $movie['title'], $movie['title_en'], $movie['description'], $movie['year'],
            $movie['duration'], $movie['genre'], $movie['rating'], $movie['poster'],
            $movie['director'], $movie['cast'], $movie['country'], $movie['language'],
            $movie['age_rating'], $featured
        ]);
        echo "<p>✅ تم إضافة فيلم: {$movie['title']}</p>";
    }
    
    // Sample Series
    echo "<h2>📺 إضافة مسلسلات تجريبية:</h2>";
    
    $series = [
        [
            'title' => 'لعبة نيوتن',
            'title_en' => 'Newton\'s Game',
            'description' => 'مسلسل إثارة يحكي قصة عالم فيزياء يتورط في لعبة خطيرة',
            'year' => 2021,
            'seasons' => 2,
            'genre' => 'إثارة، دراما',
            'rating' => 8.7,
            'poster' => 'https://image.tmdb.org/t/p/w500/newton_game.jpg',
            'director' => 'تامر محسن',
            'cast' => 'محمد ممدوح، منة شلبي، أيتن عامر',
            'country' => 'مصر',
            'language' => 'العربية'
        ],
        [
            'title' => 'الاختيار',
            'title_en' => 'The Choice',
            'description' => 'مسلسل يحكي قصص بطولية من تاريخ مصر الحديث',
            'year' => 2020,
            'seasons' => 3,
            'genre' => 'دراما، تاريخي',
            'rating' => 9.1,
            'poster' => 'https://image.tmdb.org/t/p/w500/choice.jpg',
            'director' => 'بيتر ميمي',
            'cast' => 'كريم عبد العزيز، أحمد عز، أحمد مالك',
            'country' => 'مصر',
            'language' => 'العربية'
        ],
        [
            'title' => 'جعفر العمدة',
            'title_en' => 'Jaafar Al Omda',
            'description' => 'مسلسل كوميدي اجتماعي يحكي قصة عمدة قرية وتحدياته اليومية',
            'year' => 2023,
            'seasons' => 1,
            'genre' => 'كوميديا، اجتماعي',
            'rating' => 7.5,
            'poster' => 'https://image.tmdb.org/t/p/w500/jaafar.jpg',
            'director' => 'رامي إمام',
            'cast' => 'محمد هنيدي، دنيا سمير غانم، حسن حسني',
            'country' => 'مصر',
            'language' => 'العربية'
        ]
    ];
    
    $stmt = $pdo->prepare("INSERT INTO series (title, title_en, description, year, seasons, genre, rating, poster, director, cast, country, language, status, featured) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'published', ?)");
    
    foreach ($series as $index => $show) {
        $featured = $index < 1 ? 1 : 0; // First series is featured
        $stmt->execute([
            $show['title'], $show['title_en'], $show['description'], $show['year'],
            $show['seasons'], $show['genre'], $show['rating'], $show['poster'],
            $show['director'], $show['cast'], $show['country'], $show['language'], $featured
        ]);
        $seriesId = $pdo->lastInsertId();
        echo "<p>✅ تم إضافة مسلسل: {$show['title']}</p>";
        
        // Add sample episodes for each series
        for ($season = 1; $season <= min($show['seasons'], 2); $season++) {
            for ($episode = 1; $episode <= 5; $episode++) {
                $episodeTitle = "الحلقة $episode - الموسم $season";
                $episodeDesc = "وصف الحلقة رقم $episode من الموسم $season";
                
                $episodeStmt = $pdo->prepare("INSERT INTO episodes (series_id, title, description, season_number, episode_number, duration, status) VALUES (?, ?, ?, ?, ?, ?, 'published')");
                $episodeStmt->execute([$seriesId, $episodeTitle, $episodeDesc, $season, $episode, rand(40, 60)]);
            }
        }
        echo "<p>  └─ تم إضافة حلقات للمسلسل</p>";
    }
    
    // Sample Users
    echo "<h2>👥 إضافة مستخدمين تجريبيين:</h2>";
    
    $users = [
        ['name' => 'أحمد محمد', 'email' => '<EMAIL>', 'role' => 'user'],
        ['name' => 'فاطمة علي', 'email' => '<EMAIL>', 'role' => 'user'],
        ['name' => 'محمد حسن', 'email' => '<EMAIL>', 'role' => 'user'],
        ['name' => 'سارة أحمد', 'email' => '<EMAIL>', 'role' => 'user'],
        ['name' => 'عمر خالد', 'email' => '<EMAIL>', 'role' => 'user']
    ];
    
    $userStmt = $pdo->prepare("INSERT INTO users (name, email, password, role, status) VALUES (?, ?, ?, ?, 'active')");
    
    foreach ($users as $user) {
        $password = password_hash('123456', PASSWORD_DEFAULT);
        $userStmt->execute([$user['name'], $user['email'], $password, $user['role']]);
        echo "<p>✅ تم إضافة مستخدم: {$user['name']} ({$user['email']})</p>";
    }
    
    echo "<h2>🎉 تم إضافة البيانات التجريبية بنجاح!</h2>";
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>✅ تم إضافة:</h3>";
    echo "<ul>";
    echo "<li><strong>5 أفلام</strong> مع بيانات كاملة</li>";
    echo "<li><strong>3 مسلسلات</strong> مع حلقات</li>";
    echo "<li><strong>5 مستخدمين</strong> تجريبيين (كلمة المرور: 123456)</li>";
    echo "<li><strong>حلقات متعددة</strong> لكل مسلسل</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h3>🔗 الخطوات التالية:</h3>";
    echo "<p><a href='index_simple.php' style='background: #E50914; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🏠 الصفحة الرئيسية</a></p>";
    echo "<p><a href='admin/' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🎛️ لوحة الإدارة</a></p>";
    echo "<p><a href='api/test_api.php' style='background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🔗 اختبار API</a></p>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>❌ خطأ في إضافة البيانات:</h3>";
    echo "<p><strong>الخطأ:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>تأكد من:</strong> إنشاء قاعدة البيانات والجداول أولاً باستخدام create_database.php</p>";
    echo "</div>";
}

echo "<style>
body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 40px; background: #f8f9fa; }
h1 { color: #E50914; border-bottom: 3px solid #E50914; padding-bottom: 10px; }
h2 { color: #333; margin-top: 30px; }
h3 { color: #555; }
p { margin: 5px 0; }
ul { margin: 10px 0; padding-left: 20px; }
a { color: #E50914; text-decoration: none; }
a:hover { text-decoration: underline; }
</style>";
?>
