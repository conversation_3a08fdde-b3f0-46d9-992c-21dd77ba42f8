<?php
/**
 * نظام الأمان المتقدم
 * يتضمن حماية من CSRF، SQL Injection، XSS، وإدارة الجلسات الآمنة
 */

class SecuritySystem {
    private static $instance = null;
    private $sessionTimeout = 3600; // ساعة واحدة
    private $maxLoginAttempts = 5;
    private $lockoutTime = 900; // 15 دقيقة
    
    private function __construct() {
        $this->initializeSession();
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * تهيئة الجلسة الآمنة
     */
    private function initializeSession() {
        // إعدادات الجلسة الآمنة
        ini_set('session.cookie_httponly', 1);
        ini_set('session.cookie_secure', isset($_SERVER['HTTPS']));
        ini_set('session.use_strict_mode', 1);
        ini_set('session.cookie_samesite', 'Strict');
        
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        
        // تجديد معرف الجلسة دورياً
        if (!isset($_SESSION['last_regeneration'])) {
            $this->regenerateSession();
        } elseif (time() - $_SESSION['last_regeneration'] > 300) { // كل 5 دقائق
            $this->regenerateSession();
        }
        
        // التحقق من انتهاء صلاحية الجلسة
        if (isset($_SESSION['last_activity']) && 
            (time() - $_SESSION['last_activity'] > $this->sessionTimeout)) {
            $this->destroySession();
        }
        
        $_SESSION['last_activity'] = time();
    }
    
    /**
     * تجديد معرف الجلسة
     */
    private function regenerateSession() {
        session_regenerate_id(true);
        $_SESSION['last_regeneration'] = time();
    }
    
    /**
     * تدمير الجلسة
     */
    public function destroySession() {
        $_SESSION = array();
        
        if (ini_get("session.use_cookies")) {
            $params = session_get_cookie_params();
            setcookie(session_name(), '', time() - 42000,
                $params["path"], $params["domain"],
                $params["secure"], $params["httponly"]
            );
        }
        
        session_destroy();
    }
    
    /**
     * إنشاء رمز CSRF
     */
    public function generateCSRFToken() {
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }
        return $_SESSION['csrf_token'];
    }
    
    /**
     * التحقق من رمز CSRF
     */
    public function validateCSRFToken($token) {
        if (!isset($_SESSION['csrf_token'])) {
            return false;
        }
        
        return hash_equals($_SESSION['csrf_token'], $token);
    }
    
    /**
     * تنظيف المدخلات من XSS
     */
    public function sanitizeInput($input) {
        if (is_array($input)) {
            return array_map([$this, 'sanitizeInput'], $input);
        }
        
        // إزالة المسافات الزائدة
        $input = trim($input);
        
        // تحويل الأحرف الخاصة
        $input = htmlspecialchars($input, ENT_QUOTES | ENT_HTML5, 'UTF-8');
        
        // إزالة العلامات الخطيرة
        $input = strip_tags($input);
        
        return $input;
    }
    
    /**
     * التحقق من صحة البريد الإلكتروني
     */
    public function validateEmail($email) {
        $email = filter_var($email, FILTER_SANITIZE_EMAIL);
        return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
    }
    
    /**
     * التحقق من قوة كلمة المرور
     */
    public function validatePassword($password) {
        $errors = [];
        
        if (strlen($password) < 8) {
            $errors[] = 'كلمة المرور يجب أن تكون 8 أحرف على الأقل';
        }
        
        if (!preg_match('/[A-Z]/', $password)) {
            $errors[] = 'كلمة المرور يجب أن تحتوي على حرف كبير واحد على الأقل';
        }
        
        if (!preg_match('/[a-z]/', $password)) {
            $errors[] = 'كلمة المرور يجب أن تحتوي على حرف صغير واحد على الأقل';
        }
        
        if (!preg_match('/[0-9]/', $password)) {
            $errors[] = 'كلمة المرور يجب أن تحتوي على رقم واحد على الأقل';
        }
        
        if (!preg_match('/[^A-Za-z0-9]/', $password)) {
            $errors[] = 'كلمة المرور يجب أن تحتوي على رمز خاص واحد على الأقل';
        }
        
        return empty($errors) ? true : $errors;
    }
    
    /**
     * تشفير كلمة المرور
     */
    public function hashPassword($password) {
        return password_hash($password, PASSWORD_ARGON2ID, [
            'memory_cost' => 65536, // 64 MB
            'time_cost' => 4,       // 4 iterations
            'threads' => 3,         // 3 threads
        ]);
    }
    
    /**
     * التحقق من كلمة المرور
     */
    public function verifyPassword($password, $hash) {
        return password_verify($password, $hash);
    }
    
    /**
     * تسجيل محاولة تسجيل دخول فاشلة
     */
    public function logFailedLogin($identifier) {
        if (!isset($_SESSION['failed_logins'])) {
            $_SESSION['failed_logins'] = [];
        }
        
        if (!isset($_SESSION['failed_logins'][$identifier])) {
            $_SESSION['failed_logins'][$identifier] = [
                'count' => 0,
                'last_attempt' => 0
            ];
        }
        
        $_SESSION['failed_logins'][$identifier]['count']++;
        $_SESSION['failed_logins'][$identifier]['last_attempt'] = time();
    }
    
    /**
     * التحقق من حالة القفل
     */
    public function isLocked($identifier) {
        if (!isset($_SESSION['failed_logins'][$identifier])) {
            return false;
        }
        
        $attempts = $_SESSION['failed_logins'][$identifier];
        
        if ($attempts['count'] >= $this->maxLoginAttempts) {
            $timeSinceLastAttempt = time() - $attempts['last_attempt'];
            
            if ($timeSinceLastAttempt < $this->lockoutTime) {
                return true;
            } else {
                // إعادة تعيين العداد بعد انتهاء فترة القفل
                unset($_SESSION['failed_logins'][$identifier]);
                return false;
            }
        }
        
        return false;
    }
    
    /**
     * إعادة تعيين محاولات تسجيل الدخول الفاشلة
     */
    public function resetFailedLogins($identifier) {
        if (isset($_SESSION['failed_logins'][$identifier])) {
            unset($_SESSION['failed_logins'][$identifier]);
        }
    }
    
    /**
     * التحقق من عنوان IP
     */
    public function getRealIP() {
        $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ips = explode(',', $_SERVER[$key]);
                $ip = trim($ips[0]);
                
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    }
    
    /**
     * تسجيل الأنشطة الأمنية
     */
    public function logSecurityEvent($event, $details = []) {
        $logEntry = [
            'timestamp' => date('Y-m-d H:i:s'),
            'ip' => $this->getRealIP(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            'event' => $event,
            'details' => $details,
            'session_id' => session_id()
        ];
        
        $logFile = __DIR__ . '/logs/security.log';
        $logDir = dirname($logFile);
        
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
        
        file_put_contents($logFile, json_encode($logEntry) . "\n", FILE_APPEND | LOCK_EX);
    }
    
    /**
     * التحقق من صحة الرمز المميز للAPI
     */
    public function validateAPIToken($token) {
        // في التطبيق الحقيقي، يجب التحقق من قاعدة البيانات
        $validTokens = [
            'shahid_api_token_2024',
            'mobile_app_token_v1'
        ];
        
        return in_array($token, $validTokens);
    }
    
    /**
     * إنشاء رمز مميز للAPI
     */
    public function generateAPIToken($userId) {
        $payload = [
            'user_id' => $userId,
            'issued_at' => time(),
            'expires_at' => time() + (24 * 60 * 60) // 24 ساعة
        ];
        
        $header = base64_encode(json_encode(['typ' => 'JWT', 'alg' => 'HS256']));
        $payload = base64_encode(json_encode($payload));
        $signature = hash_hmac('sha256', $header . '.' . $payload, 'your_secret_key', true);
        $signature = base64_encode($signature);
        
        return $header . '.' . $payload . '.' . $signature;
    }
    
    /**
     * فك تشفير رمز API
     */
    public function decodeAPIToken($token) {
        $parts = explode('.', $token);
        
        if (count($parts) !== 3) {
            return false;
        }
        
        $header = json_decode(base64_decode($parts[0]), true);
        $payload = json_decode(base64_decode($parts[1]), true);
        $signature = $parts[2];
        
        // التحقق من التوقيع
        $expectedSignature = base64_encode(
            hash_hmac('sha256', $parts[0] . '.' . $parts[1], 'your_secret_key', true)
        );
        
        if (!hash_equals($signature, $expectedSignature)) {
            return false;
        }
        
        // التحقق من انتهاء الصلاحية
        if ($payload['expires_at'] < time()) {
            return false;
        }
        
        return $payload;
    }
    
    /**
     * حماية من هجمات Rate Limiting
     */
    public function checkRateLimit($action, $limit = 10, $window = 60) {
        $key = $action . '_' . $this->getRealIP();
        
        if (!isset($_SESSION['rate_limits'])) {
            $_SESSION['rate_limits'] = [];
        }
        
        if (!isset($_SESSION['rate_limits'][$key])) {
            $_SESSION['rate_limits'][$key] = [
                'count' => 0,
                'window_start' => time()
            ];
        }
        
        $rateLimit = &$_SESSION['rate_limits'][$key];
        
        // إعادة تعيين النافذة إذا انتهت
        if (time() - $rateLimit['window_start'] > $window) {
            $rateLimit['count'] = 0;
            $rateLimit['window_start'] = time();
        }
        
        $rateLimit['count']++;
        
        if ($rateLimit['count'] > $limit) {
            $this->logSecurityEvent('rate_limit_exceeded', [
                'action' => $action,
                'limit' => $limit,
                'window' => $window
            ]);
            return false;
        }
        
        return true;
    }
    
    /**
     * تنظيف البيانات القديمة
     */
    public function cleanup() {
        // تنظيف محاولات تسجيل الدخول الفاشلة القديمة
        if (isset($_SESSION['failed_logins'])) {
            foreach ($_SESSION['failed_logins'] as $identifier => $data) {
                if (time() - $data['last_attempt'] > $this->lockoutTime * 2) {
                    unset($_SESSION['failed_logins'][$identifier]);
                }
            }
        }
        
        // تنظيف حدود المعدل القديمة
        if (isset($_SESSION['rate_limits'])) {
            foreach ($_SESSION['rate_limits'] as $key => $data) {
                if (time() - $data['window_start'] > 3600) { // ساعة واحدة
                    unset($_SESSION['rate_limits'][$key]);
                }
            }
        }
    }
    
    /**
     * إعداد رؤوس الأمان
     */
    public function setSecurityHeaders() {
        // منع XSS
        header('X-XSS-Protection: 1; mode=block');
        
        // منع MIME type sniffing
        header('X-Content-Type-Options: nosniff');
        
        // منع Clickjacking
        header('X-Frame-Options: DENY');
        
        // HSTS (إذا كان HTTPS مفعل)
        if (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on') {
            header('Strict-Transport-Security: max-age=31536000; includeSubDomains');
        }
        
        // Content Security Policy
        header("Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' https:;");
        
        // Referrer Policy
        header('Referrer-Policy: strict-origin-when-cross-origin');
        
        // Feature Policy
        header("Permissions-Policy: geolocation=(), microphone=(), camera=()");
    }
}

// تهيئة نظام الأمان
$security = SecuritySystem::getInstance();
$security->setSecurityHeaders();
$security->cleanup();

// دالة مساعدة للحصول على رمز CSRF
function csrf_token() {
    global $security;
    return $security->generateCSRFToken();
}

// دالة مساعدة للتحقق من CSRF
function verify_csrf($token) {
    global $security;
    return $security->validateCSRFToken($token);
}

// دالة مساعدة لتنظيف المدخلات
function clean_input($input) {
    global $security;
    return $security->sanitizeInput($input);
}
?>
