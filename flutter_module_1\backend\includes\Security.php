<?php
/**
 * نظام الأمان المتقدم
 * Advanced Security System
 */

class Security {
    private $pdo;
    private $config;
    
    public function __construct($pdo) {
        $this->pdo = $pdo;
        $this->config = [
            'max_login_attempts' => 5,
            'lockout_duration' => 900, // 15 minutes
            'session_timeout' => 3600, // 1 hour
            'password_min_length' => 8,
            'require_special_chars' => true,
            'csrf_token_lifetime' => 3600
        ];
    }
    
    /**
     * تشفير كلمة المرور
     */
    public function hashPassword($password) {
        return password_hash($password, PASSWORD_ARGON2ID, [
            'memory_cost' => 65536,
            'time_cost' => 4,
            'threads' => 3
        ]);
    }
    
    /**
     * التحقق من كلمة المرور
     */
    public function verifyPassword($password, $hash) {
        return password_verify($password, $hash);
    }
    
    /**
     * التحقق من قوة كلمة المرور
     */
    public function validatePassword($password) {
        $errors = [];
        
        if (strlen($password) < $this->config['password_min_length']) {
            $errors[] = "كلمة المرور يجب أن تكون {$this->config['password_min_length']} أحرف على الأقل";
        }
        
        if (!preg_match('/[A-Z]/', $password)) {
            $errors[] = "كلمة المرور يجب أن تحتوي على حرف كبير واحد على الأقل";
        }
        
        if (!preg_match('/[a-z]/', $password)) {
            $errors[] = "كلمة المرور يجب أن تحتوي على حرف صغير واحد على الأقل";
        }
        
        if (!preg_match('/[0-9]/', $password)) {
            $errors[] = "كلمة المرور يجب أن تحتوي على رقم واحد على الأقل";
        }
        
        if ($this->config['require_special_chars'] && !preg_match('/[^A-Za-z0-9]/', $password)) {
            $errors[] = "كلمة المرور يجب أن تحتوي على رمز خاص واحد على الأقل";
        }
        
        return empty($errors) ? true : $errors;
    }
    
    /**
     * تنظيف البيانات من XSS
     */
    public function sanitizeInput($input) {
        if (is_array($input)) {
            return array_map([$this, 'sanitizeInput'], $input);
        }
        
        return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
    }
    
    /**
     * التحقق من CSRF Token
     */
    public function generateCSRFToken() {
        if (!isset($_SESSION['csrf_token']) || 
            !isset($_SESSION['csrf_token_time']) || 
            (time() - $_SESSION['csrf_token_time']) > $this->config['csrf_token_lifetime']) {
            
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
            $_SESSION['csrf_token_time'] = time();
        }
        
        return $_SESSION['csrf_token'];
    }
    
    /**
     * التحقق من صحة CSRF Token
     */
    public function validateCSRFToken($token) {
        if (!isset($_SESSION['csrf_token']) || !isset($_SESSION['csrf_token_time'])) {
            return false;
        }
        
        if ((time() - $_SESSION['csrf_token_time']) > $this->config['csrf_token_lifetime']) {
            unset($_SESSION['csrf_token'], $_SESSION['csrf_token_time']);
            return false;
        }
        
        return hash_equals($_SESSION['csrf_token'], $token);
    }
    
    /**
     * تسجيل محاولة تسجيل دخول فاشلة
     */
    public function recordFailedLogin($identifier, $ip) {
        try {
            $stmt = $this->pdo->prepare("
                INSERT INTO failed_logins (identifier, ip_address, attempted_at) 
                VALUES (?, ?, NOW())
            ");
            $stmt->execute([$identifier, $ip]);
            
            // تنظيف المحاولات القديمة
            $this->cleanupFailedLogins();
            
        } catch (Exception $e) {
            error_log("Failed to record failed login: " . $e->getMessage());
        }
    }
    
    /**
     * التحقق من حالة القفل
     */
    public function isLocked($identifier, $ip) {
        try {
            $stmt = $this->pdo->prepare("
                SELECT COUNT(*) FROM failed_logins 
                WHERE (identifier = ? OR ip_address = ?) 
                AND attempted_at > DATE_SUB(NOW(), INTERVAL ? SECOND)
            ");
            $stmt->execute([$identifier, $ip, $this->config['lockout_duration']]);
            
            $attempts = $stmt->fetchColumn();
            return $attempts >= $this->config['max_login_attempts'];
            
        } catch (Exception $e) {
            error_log("Failed to check lock status: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * إزالة قفل المستخدم
     */
    public function clearFailedLogins($identifier, $ip) {
        try {
            $stmt = $this->pdo->prepare("
                DELETE FROM failed_logins 
                WHERE identifier = ? OR ip_address = ?
            ");
            $stmt->execute([$identifier, $ip]);
            
        } catch (Exception $e) {
            error_log("Failed to clear failed logins: " . $e->getMessage());
        }
    }
    
    /**
     * تنظيف المحاولات القديمة
     */
    private function cleanupFailedLogins() {
        try {
            $stmt = $this->pdo->prepare("
                DELETE FROM failed_logins 
                WHERE attempted_at < DATE_SUB(NOW(), INTERVAL ? SECOND)
            ");
            $stmt->execute([$this->config['lockout_duration']]);
            
        } catch (Exception $e) {
            error_log("Failed to cleanup failed logins: " . $e->getMessage());
        }
    }
    
    /**
     * التحقق من صحة عنوان IP
     */
    public function validateIP($ip) {
        return filter_var($ip, FILTER_VALIDATE_IP) !== false;
    }
    
    /**
     * الحصول على عنوان IP الحقيقي
     */
    public function getRealIP() {
        $headers = [
            'HTTP_CF_CONNECTING_IP',
            'HTTP_X_FORWARDED_FOR',
            'HTTP_X_FORWARDED',
            'HTTP_X_CLUSTER_CLIENT_IP',
            'HTTP_FORWARDED_FOR',
            'HTTP_FORWARDED',
            'REMOTE_ADDR'
        ];
        
        foreach ($headers as $header) {
            if (!empty($_SERVER[$header])) {
                $ips = explode(',', $_SERVER[$header]);
                $ip = trim($ips[0]);
                
                if ($this->validateIP($ip)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }
    
    /**
     * تسجيل نشاط المستخدم
     */
    public function logActivity($userId, $action, $description = null, $modelType = null, $modelId = null, $oldValues = null, $newValues = null) {
        try {
            $stmt = $this->pdo->prepare("
                INSERT INTO activity_logs 
                (user_id, action, description, model_type, model_id, old_values, new_values, ip_address, user_agent, created_at) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
            ");
            
            $stmt->execute([
                $userId,
                $action,
                $description,
                $modelType,
                $modelId,
                $oldValues ? json_encode($oldValues) : null,
                $newValues ? json_encode($newValues) : null,
                $this->getRealIP(),
                $_SERVER['HTTP_USER_AGENT'] ?? null
            ]);
            
        } catch (Exception $e) {
            error_log("Failed to log activity: " . $e->getMessage());
        }
    }
    
    /**
     * التحقق من الصلاحيات
     */
    public function hasPermission($userId, $permission) {
        try {
            $stmt = $this->pdo->prepare("
                SELECT r.permissions 
                FROM user_roles ur 
                JOIN roles r ON ur.role_id = r.id 
                WHERE ur.user_id = ?
            ");
            $stmt->execute([$userId]);
            
            $userPermissions = [];
            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                $permissions = json_decode($row['permissions'], true);
                if (is_array($permissions)) {
                    $userPermissions = array_merge($userPermissions, $permissions);
                }
            }
            
            // التحقق من الصلاحية الشاملة
            if (in_array('*', $userPermissions)) {
                return true;
            }
            
            // التحقق من الصلاحية المحددة
            if (in_array($permission, $userPermissions)) {
                return true;
            }
            
            // التحقق من الصلاحيات بالنمط (مثل admin.*)
            foreach ($userPermissions as $userPerm) {
                if (str_ends_with($userPerm, '*')) {
                    $prefix = rtrim($userPerm, '*');
                    if (str_starts_with($permission, $prefix)) {
                        return true;
                    }
                }
            }
            
            return false;
            
        } catch (Exception $e) {
            error_log("Failed to check permission: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * التحقق من انتهاء الجلسة
     */
    public function checkSessionTimeout() {
        if (isset($_SESSION['last_activity'])) {
            if ((time() - $_SESSION['last_activity']) > $this->config['session_timeout']) {
                session_destroy();
                return false;
            }
        }
        
        $_SESSION['last_activity'] = time();
        return true;
    }
    
    /**
     * تشفير البيانات الحساسة
     */
    public function encrypt($data, $key = null) {
        if ($key === null) {
            $key = $_ENV['ENCRYPTION_KEY'] ?? 'default_key_change_this';
        }
        
        $iv = random_bytes(16);
        $encrypted = openssl_encrypt($data, 'AES-256-CBC', $key, 0, $iv);
        
        return base64_encode($iv . $encrypted);
    }
    
    /**
     * فك تشفير البيانات
     */
    public function decrypt($encryptedData, $key = null) {
        if ($key === null) {
            $key = $_ENV['ENCRYPTION_KEY'] ?? 'default_key_change_this';
        }
        
        $data = base64_decode($encryptedData);
        $iv = substr($data, 0, 16);
        $encrypted = substr($data, 16);
        
        return openssl_decrypt($encrypted, 'AES-256-CBC', $key, 0, $iv);
    }
    
    /**
     * التحقق من معدل الطلبات (Rate Limiting)
     */
    public function checkRateLimit($identifier, $maxRequests = 60, $timeWindow = 60) {
        $key = "rate_limit_" . md5($identifier);
        
        if (!isset($_SESSION[$key])) {
            $_SESSION[$key] = ['count' => 0, 'start_time' => time()];
        }
        
        $rateData = $_SESSION[$key];
        
        // إعادة تعيين العداد إذا انتهت النافزة الزمنية
        if ((time() - $rateData['start_time']) > $timeWindow) {
            $_SESSION[$key] = ['count' => 1, 'start_time' => time()];
            return true;
        }
        
        // زيادة العداد
        $_SESSION[$key]['count']++;
        
        return $_SESSION[$key]['count'] <= $maxRequests;
    }
    
    /**
     * إنشاء رمز مميز آمن
     */
    public function generateSecureToken($length = 32) {
        return bin2hex(random_bytes($length));
    }
    
    /**
     * التحقق من صحة البريد الإلكتروني
     */
    public function validateEmail($email) {
        return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
    }
    
    /**
     * التحقق من صحة رقم الهاتف
     */
    public function validatePhone($phone) {
        return preg_match('/^[\+]?[0-9\s\-\(\)]{10,20}$/', $phone);
    }
}

// إنشاء جدول محاولات تسجيل الدخول الفاشلة إذا لم يكن موجوداً
try {
    $pdo = new PDO("mysql:host=localhost;dbname=shahid_platform;charset=utf8mb4", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $failedLoginsSQL = "
        CREATE TABLE IF NOT EXISTS `failed_logins` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `identifier` varchar(255) NOT NULL,
            `ip_address` varchar(45) NOT NULL,
            `attempted_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `idx_identifier` (`identifier`),
            KEY `idx_ip` (`ip_address`),
            KEY `idx_attempted` (`attempted_at`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    $pdo->exec($failedLoginsSQL);
    
} catch (Exception $e) {
    error_log("Failed to create failed_logins table: " . $e->getMessage());
}
?>
