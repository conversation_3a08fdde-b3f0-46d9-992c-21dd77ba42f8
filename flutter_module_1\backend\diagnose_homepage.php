<?php
/**
 * تشخيص مشاكل الصفحة الرئيسية
 * Homepage Issues Diagnosis
 */

echo "<h1>🔍 تشخيص مشاكل الصفحة الرئيسية</h1>";

$issues = [];
$fixes = [];

// 1. فحص ملف homepage.php
echo "<h2>📄 فحص ملف homepage.php...</h2>";

$homepageFile = 'homepage.php';
if (file_exists($homepageFile)) {
    $fileSize = filesize($homepageFile);
    $content = file_get_contents($homepageFile);
    
    echo "<div style='background: #e3f2fd; border: 1px solid #2196f3; color: #0d47a1; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>📋 معلومات الملف:</h3>";
    echo "<ul>";
    echo "<li><strong>الحجم:</strong> " . number_format($fileSize) . " بايت</li>";
    echo "<li><strong>عدد الأسطر:</strong> " . count(explode("\n", $content)) . "</li>";
    echo "<li><strong>يبدأ بـ PHP:</strong> " . (strpos($content, '<?php') === 0 ? 'نعم ✅' : 'لا ❌') . "</li>";
    echo "<li><strong>ينتهي بـ HTML:</strong> " . (strpos($content, '</html>') !== false ? 'نعم ✅' : 'لا ❌') . "</li>";
    echo "</ul>";
    echo "</div>";
    
    // فحص الأخطاء في PHP
    $syntaxCheck = shell_exec("php -l $homepageFile 2>&1");
    if (strpos($syntaxCheck, 'No syntax errors') === false) {
        $issues[] = "خطأ في بناء الجملة: " . $syntaxCheck;
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h3>❌ خطأ في بناء الجملة:</h3>";
        echo "<pre>" . htmlspecialchars($syntaxCheck) . "</pre>";
        echo "</div>";
    } else {
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h3>✅ بناء الجملة صحيح</h3>";
        echo "</div>";
    }
    
    // فحص الملفات المطلوبة
    $requiredFiles = [
        'config/database.php' => 'ملف إعدادات قاعدة البيانات',
        'assets/css/homepage.css' => 'ملف CSS للصفحة الرئيسية',
        'assets/js/homepage.js' => 'ملف JavaScript للصفحة الرئيسية'
    ];
    
    echo "<h3>📁 فحص الملفات المطلوبة:</h3>";
    foreach ($requiredFiles as $file => $description) {
        $exists = file_exists($file);
        $status = $exists ? '✅ موجود' : '❌ مفقود';
        echo "<p><strong>$description:</strong> $status</p>";
        
        if (!$exists) {
            $issues[] = "ملف مفقود: $file";
        }
    }
    
} else {
    $issues[] = "ملف homepage.php غير موجود";
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>❌ ملف homepage.php غير موجود!</h3>";
    echo "</div>";
}

// 2. فحص إعدادات PHP
echo "<h2>⚙️ فحص إعدادات PHP...</h2>";

$phpSettings = [
    'display_errors' => ini_get('display_errors'),
    'error_reporting' => error_reporting(),
    'short_open_tag' => ini_get('short_open_tag'),
    'output_buffering' => ini_get('output_buffering'),
    'max_execution_time' => ini_get('max_execution_time')
];

echo "<div style='background: #fff3e0; border: 1px solid #ff9800; color: #e65100; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h3>🔧 إعدادات PHP الحالية:</h3>";
echo "<ul>";
foreach ($phpSettings as $setting => $value) {
    echo "<li><strong>$setting:</strong> " . ($value ? $value : 'Off') . "</li>";
}
echo "</ul>";
echo "</div>";

// 3. فحص قاعدة البيانات
echo "<h2>🗄️ فحص قاعدة البيانات...</h2>";

try {
    $pdo = new PDO("mysql:host=localhost;dbname=shahid_platform;charset=utf8mb4", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>✅ قاعدة البيانات متصلة بنجاح</h3>";
    
    $stmt = $pdo->query("SELECT COUNT(*) as table_count FROM information_schema.tables WHERE table_schema = 'shahid_platform'");
    $tableCount = $stmt->fetch()['table_count'];
    echo "<p>عدد الجداول: $tableCount</p>";
    echo "</div>";
    
} catch (Exception $e) {
    $issues[] = "خطأ في قاعدة البيانات: " . $e->getMessage();
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>❌ خطأ في قاعدة البيانات:</h3>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

// 4. فحص مجلد assets
echo "<h2>🎨 فحص مجلد Assets...</h2>";

$assetsDirs = ['assets', 'assets/css', 'assets/js', 'assets/images'];
foreach ($assetsDirs as $dir) {
    if (!is_dir($dir)) {
        $issues[] = "مجلد مفقود: $dir";
        echo "<p>❌ مجلد مفقود: <strong>$dir</strong></p>";
        
        // إنشاء المجلد
        if (mkdir($dir, 0755, true)) {
            $fixes[] = "تم إنشاء مجلد: $dir";
            echo "<p style='color: green;'>✅ تم إنشاء المجلد: <strong>$dir</strong></p>";
        }
    } else {
        echo "<p>✅ مجلد موجود: <strong>$dir</strong></p>";
    }
}

// 5. إنشاء ملفات CSS و JS مفقودة
echo "<h2>📝 إنشاء الملفات المفقودة...</h2>";

// إنشاء ملف CSS
if (!file_exists('assets/css/homepage.css')) {
    $cssContent = "/* Shahid Platform Homepage Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #2d2d2d 100%);
    color: #fff;
    margin: 0;
    padding: 0;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
}

.header {
    text-align: center;
    padding: 3rem 0;
    background: linear-gradient(135deg, #E50914 0%, #B8070F 100%);
}

.header h1 {
    font-size: 3rem;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}";
    
    if (file_put_contents('assets/css/homepage.css', $cssContent)) {
        $fixes[] = "تم إنشاء ملف CSS";
        echo "<p style='color: green;'>✅ تم إنشاء ملف: <strong>assets/css/homepage.css</strong></p>";
    }
}

// إنشاء ملف JavaScript
if (!file_exists('assets/js/homepage.js')) {
    $jsContent = "// Shahid Platform Homepage JavaScript
document.addEventListener('DOMContentLoaded', function() {
    console.log('🎬 Shahid Platform Homepage loaded!');
    
    // تأثيرات تفاعلية
    const cards = document.querySelectorAll('.feature-card');
    cards.forEach((card, index) => {
        card.style.animationDelay = `\${index * 0.1}s`;
    });
});";
    
    if (file_put_contents('assets/js/homepage.js', $jsContent)) {
        $fixes[] = "تم إنشاء ملف JavaScript";
        echo "<p style='color: green;'>✅ تم إنشاء ملف: <strong>assets/js/homepage.js</strong></p>";
    }
}

// 6. إنشاء ملف config/database.php إذا كان مفقود
if (!file_exists('config/database.php')) {
    if (!is_dir('config')) {
        mkdir('config', 0755, true);
    }
    
    $dbConfig = "<?php
/**
 * إعدادات قاعدة البيانات
 */

// إعدادات قاعدة البيانات
define('DB_HOST', 'localhost');
define('DB_NAME', 'shahid_platform');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_CHARSET', 'utf8mb4');

// رابط التطبيق
define('APP_URL', 'http://localhost/amr2/flutter_module_1/backend');

// إعدادات الأمان
define('SECURITY_KEY', 'shahid_platform_2024');
define('ENCRYPTION_METHOD', 'AES-256-CBC');

// إعدادات الجلسة
ini_set('session.cookie_httponly', 1);
ini_set('session.use_only_cookies', 1);
ini_set('session.cookie_secure', 0);
?>";
    
    if (file_put_contents('config/database.php', $dbConfig)) {
        $fixes[] = "تم إنشاء ملف إعدادات قاعدة البيانات";
        echo "<p style='color: green;'>✅ تم إنشاء ملف: <strong>config/database.php</strong></p>";
    }
}

// 7. اختبار تشغيل الصفحة
echo "<h2>🧪 اختبار تشغيل الصفحة...</h2>";

ob_start();
$error = '';
try {
    include 'homepage.php';
    $output = ob_get_contents();
} catch (Exception $e) {
    $error = $e->getMessage();
    $output = '';
} catch (Error $e) {
    $error = $e->getMessage();
    $output = '';
}
ob_end_clean();

if ($error) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>❌ خطأ في تشغيل الصفحة:</h3>";
    echo "<pre>" . htmlspecialchars($error) . "</pre>";
    echo "</div>";
    $issues[] = "خطأ في تشغيل الصفحة: " . $error;
} else {
    $outputLength = strlen($output);
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>✅ الصفحة تعمل بنجاح!</h3>";
    echo "<p>حجم المخرجات: " . number_format($outputLength) . " حرف</p>";
    echo "</div>";
}

// 8. ملخص النتائج
echo "<h2>📊 ملخص التشخيص</h2>";

if (!empty($issues)) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>⚠️ المشاكل المكتشفة:</h3>";
    echo "<ul>";
    foreach ($issues as $issue) {
        echo "<li>$issue</li>";
    }
    echo "</ul>";
    echo "</div>";
}

if (!empty($fixes)) {
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>✅ الإصلاحات التي تمت:</h3>";
    echo "<ul>";
    foreach ($fixes as $fix) {
        echo "<li>$fix</li>";
    }
    echo "</ul>";
    echo "</div>";
}

echo "<div style='text-align: center; margin: 2rem 0;'>";
echo "<a href='homepage.php' style='background: #E50914; color: white; padding: 1rem 2rem; text-decoration: none; border-radius: 8px; font-weight: bold; margin: 0.5rem;'>🏠 اختبار الصفحة الرئيسية</a>";
echo "<a href='homepage_working.php' style='background: #28a745; color: white; padding: 1rem 2rem; text-decoration: none; border-radius: 8px; font-weight: bold; margin: 0.5rem;'>🏠 الصفحة الرئيسية البديلة</a>";
echo "<a href='admin/simple_dashboard.php' style='background: #007bff; color: white; padding: 1rem 2rem; text-decoration: none; border-radius: 8px; font-weight: bold; margin: 0.5rem;'>🎛️ لوحة الإدارة</a>";
echo "</div>";
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص مشاكل الصفحة الرئيسية</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #2d2d2d 100%);
            color: #fff;
            min-height: 100vh;
            padding: 2rem;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: rgba(47, 47, 47, 0.95);
            border-radius: 20px;
            padding: 3rem;
            border: 2px solid rgba(229, 9, 20, 0.3);
        }
        h1, h2, h3 { color: #E50914; }
        ul { margin: 1rem 0; padding-right: 2rem; }
        li { margin: 0.5rem 0; }
        pre {
            background: rgba(0, 0, 0, 0.3);
            padding: 1rem;
            border-radius: 8px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- المحتوى يتم عرضه من PHP أعلاه -->
    </div>
</body>
</html>
