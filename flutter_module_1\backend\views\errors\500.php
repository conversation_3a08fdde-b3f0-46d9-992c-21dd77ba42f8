<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>500 - خطأ في الخادم | Shahid Platform</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #2d2d2d 100%);
            color: #fff;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }
        
        .error-container {
            text-align: center;
            max-width: 600px;
            padding: 2rem;
            position: relative;
            z-index: 2;
        }
        
        .error-code {
            font-size: 8rem;
            font-weight: bold;
            background: linear-gradient(45deg, #F44336, #D32F2F);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 1rem;
            text-shadow: 0 0 30px rgba(244, 67, 54, 0.5);
            animation: glow 2s ease-in-out infinite alternate;
        }
        
        @keyframes glow {
            from { text-shadow: 0 0 20px rgba(244, 67, 54, 0.5); }
            to { text-shadow: 0 0 40px rgba(244, 67, 54, 0.8); }
        }
        
        .error-title {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            color: #fff;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }
        
        .error-message {
            font-size: 1.2rem;
            color: #ccc;
            margin-bottom: 2rem;
            line-height: 1.6;
        }
        
        .server-icon {
            font-size: 4rem;
            color: #F44336;
            margin-bottom: 1rem;
            animation: pulse 2s ease-in-out infinite;
        }
        
        @keyframes pulse {
            0%, 100% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.1); opacity: 0.7; }
        }
        
        .error-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .btn {
            background: linear-gradient(45deg, #E50914, #B8070F);
            color: white;
            padding: 1rem 2rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: bold;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(229, 9, 20, 0.3);
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(229, 9, 20, 0.4);
        }
        
        .btn-secondary {
            background: linear-gradient(45deg, #555, #333);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        }
        
        .btn-secondary:hover {
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
        }
        
        .status-info {
            background: rgba(244, 67, 54, 0.1);
            border: 1px solid rgba(244, 67, 54, 0.3);
            border-radius: 10px;
            padding: 1.5rem;
            margin: 2rem 0;
            text-align: right;
        }
        
        .status-info h3 {
            color: #F44336;
            margin-bottom: 1rem;
            font-size: 1.3rem;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }
        
        .status-item {
            background: rgba(45, 45, 45, 0.8);
            padding: 1rem;
            border-radius: 8px;
            text-align: center;
        }
        
        .status-item .label {
            color: #ccc;
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
        }
        
        .status-item .value {
            color: #F44336;
            font-size: 1.2rem;
            font-weight: bold;
        }
        
        .retry-section {
            background: rgba(45, 45, 45, 0.8);
            border-radius: 10px;
            padding: 1.5rem;
            margin-top: 2rem;
        }
        
        .retry-section h3 {
            color: #E50914;
            margin-bottom: 1rem;
            text-align: center;
        }
        
        .retry-timer {
            font-size: 2rem;
            color: #E50914;
            font-weight: bold;
            margin: 1rem 0;
        }
        
        .progress-bar {
            width: 100%;
            height: 6px;
            background: rgba(229, 9, 20, 0.2);
            border-radius: 3px;
            overflow: hidden;
            margin: 1rem 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(45deg, #E50914, #B8070F);
            width: 0%;
            transition: width 1s ease;
        }
        
        .background-animation {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
            opacity: 0.1;
        }
        
        .floating-icon {
            position: absolute;
            font-size: 2rem;
            color: #F44336;
            animation: float 6s ease-in-out infinite;
        }
        
        .floating-icon:nth-child(1) { top: 10%; left: 10%; animation-delay: 0s; }
        .floating-icon:nth-child(2) { top: 20%; right: 10%; animation-delay: 1s; }
        .floating-icon:nth-child(3) { bottom: 20%; left: 15%; animation-delay: 2s; }
        .floating-icon:nth-child(4) { bottom: 10%; right: 20%; animation-delay: 3s; }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
        
        @media (max-width: 768px) {
            .error-code {
                font-size: 5rem;
            }
            
            .error-title {
                font-size: 2rem;
            }
            
            .error-message {
                font-size: 1rem;
            }
            
            .error-actions {
                flex-direction: column;
                align-items: center;
            }
            
            .btn {
                width: 100%;
                max-width: 250px;
            }
            
            .status-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="background-animation">
        <div class="floating-icon">⚠️</div>
        <div class="floating-icon">🔧</div>
        <div class="floating-icon">💻</div>
        <div class="floating-icon">🛠️</div>
    </div>
    
    <div class="error-container">
        <div class="server-icon">🖥️</div>
        <div class="error-code">500</div>
        <h1 class="error-title">خطأ في الخادم</h1>
        <p class="error-message">
            عذراً، حدث خطأ داخلي في الخادم ولا يمكن معالجة طلبك في الوقت الحالي.
            <br>
            فريقنا التقني يعمل على حل المشكلة. يرجى المحاولة مرة أخرى لاحقاً.
        </p>
        
        <div class="status-info">
            <h3>معلومات الحالة:</h3>
            <div class="status-grid">
                <div class="status-item">
                    <div class="label">حالة الخادم</div>
                    <div class="value" id="serverStatus">فحص...</div>
                </div>
                <div class="status-item">
                    <div class="label">قاعدة البيانات</div>
                    <div class="value" id="dbStatus">فحص...</div>
                </div>
                <div class="status-item">
                    <div class="label">وقت الاستجابة</div>
                    <div class="value" id="responseTime">--</div>
                </div>
                <div class="status-item">
                    <div class="label">آخر فحص</div>
                    <div class="value" id="lastCheck">الآن</div>
                </div>
            </div>
        </div>
        
        <div class="retry-section">
            <h3>إعادة المحاولة التلقائية</h3>
            <div class="retry-timer" id="retryTimer">30</div>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <p>سيتم إعادة تحميل الصفحة تلقائياً خلال <span id="countdown">30</span> ثانية</p>
        </div>
        
        <div class="error-actions">
            <button onclick="location.reload()" class="btn">
                🔄 إعادة المحاولة الآن
            </button>
            <a href="/backend/" class="btn btn-secondary">
                🏠 الصفحة الرئيسية
            </a>
            <a href="/backend/admin/system_health_checker.php" class="btn btn-secondary">
                🔍 فحص النظام
            </a>
        </div>
    </div>
    
    <script>
        // متغيرات العد التنازلي
        let countdown = 30;
        let retryInterval;
        
        // بدء العد التنازلي
        function startCountdown() {
            retryInterval = setInterval(() => {
                countdown--;
                document.getElementById('retryTimer').textContent = countdown;
                document.getElementById('countdown').textContent = countdown;
                
                // تحديث شريط التقدم
                const progress = ((30 - countdown) / 30) * 100;
                document.getElementById('progressFill').style.width = progress + '%';
                
                if (countdown <= 0) {
                    clearInterval(retryInterval);
                    location.reload();
                }
            }, 1000);
        }
        
        // فحص حالة الخادم
        async function checkServerStatus() {
            try {
                const startTime = Date.now();
                const response = await fetch('/backend/api/?endpoint=status', {
                    method: 'GET',
                    cache: 'no-cache'
                });
                const endTime = Date.now();
                const responseTime = endTime - startTime;
                
                document.getElementById('responseTime').textContent = responseTime + 'ms';
                
                if (response.ok) {
                    document.getElementById('serverStatus').textContent = 'متصل';
                    document.getElementById('serverStatus').style.color = '#4CAF50';
                } else {
                    document.getElementById('serverStatus').textContent = 'خطأ';
                }
            } catch (error) {
                document.getElementById('serverStatus').textContent = 'غير متصل';
                document.getElementById('responseTime').textContent = 'انتهت المهلة';
            }
            
            // فحص قاعدة البيانات
            try {
                const dbResponse = await fetch('/backend/system_health_checker.php?check=database');
                if (dbResponse.ok) {
                    const data = await dbResponse.json();
                    if (data.success) {
                        document.getElementById('dbStatus').textContent = 'متصلة';
                        document.getElementById('dbStatus').style.color = '#4CAF50';
                    } else {
                        document.getElementById('dbStatus').textContent = 'خطأ';
                    }
                } else {
                    document.getElementById('dbStatus').textContent = 'غير متصلة';
                }
            } catch (error) {
                document.getElementById('dbStatus').textContent = 'غير متصلة';
            }
            
            // تحديث وقت آخر فحص
            document.getElementById('lastCheck').textContent = new Date().toLocaleTimeString('ar-SA');
        }
        
        // تسجيل الخطأ
        function logError() {
            const errorData = {
                type: 'server_error_500',
                url: window.location.href,
                timestamp: new Date().toISOString(),
                user_agent: navigator.userAgent,
                referrer: document.referrer
            };
            
            fetch('/backend/api/advanced.php?endpoint=log_error', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(errorData)
            }).catch(e => console.log('تم تسجيل الخطأ محلياً'));
        }
        
        // رسالة في وحدة التحكم
        console.error(`
🚨 خطأ خادم - Shahid Platform 🚨

حدث خطأ داخلي في الخادم (500)
الوقت: ${new Date().toLocaleString('ar-SA')}
الصفحة: ${window.location.href}

الإجراءات المتخذة:
✓ تم إشعار فريق الدعم التقني
✓ تم تسجيل تفاصيل الخطأ
✓ سيتم إعادة المحاولة تلقائياً

يرجى الانتظار أو المحاولة لاحقاً.
        `);
        
        // بدء العمليات
        startCountdown();
        checkServerStatus();
        logError();
        
        // فحص دوري كل 10 ثوان
        setInterval(checkServerStatus, 10000);
        
        // تأثيرات بصرية
        document.addEventListener('mousemove', function(e) {
            const icons = document.querySelectorAll('.floating-icon');
            icons.forEach((icon, index) => {
                const speed = (index + 1) * 0.0001;
                const x = (e.clientX * speed);
                const y = (e.clientY * speed);
                icon.style.transform += ` translate(${x}px, ${y}px)`;
            });
        });
        
        // إيقاف العد التنازلي عند التفاعل
        document.addEventListener('click', () => {
            if (retryInterval) {
                clearInterval(retryInterval);
                document.getElementById('retryTimer').textContent = 'متوقف';
                document.querySelector('.retry-section p').textContent = 'تم إيقاف إعادة التحميل التلقائي';
            }
        });
    </script>
</body>
</html>
