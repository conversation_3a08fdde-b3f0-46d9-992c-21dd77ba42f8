<?php
/**
 * نظام إدارة الصلاحيات
 * Permission Management System
 */

class PermissionManager {
    private $pdo;
    private $security;
    
    // قائمة الصلاحيات المتاحة في النظام
    private $availablePermissions = [
        // إدارة النظام
        'system.settings' => 'إدارة إعدادات النظام',
        'system.backup' => 'نسخ احتياطي واستعادة',
        'system.logs' => 'عرض سجلات النظام',
        'system.maintenance' => 'وضع الصيانة',
        
        // إدارة المستخدمين
        'users.view' => 'عرض المستخدمين',
        'users.create' => 'إنشاء مستخدمين',
        'users.edit' => 'تعديل المستخدمين',
        'users.delete' => 'حذف المستخدمين',
        'users.ban' => 'حظر المستخدمين',
        'users.roles' => 'إدارة أدوار المستخدمين',
        
        // إدارة الأدوار
        'roles.view' => 'عرض الأدوار',
        'roles.create' => 'إنشاء أدوار',
        'roles.edit' => 'تعديل الأدوار',
        'roles.delete' => 'حذف الأدوار',
        'roles.permissions' => 'إدارة صلاحيات الأدوار',
        
        // إدارة المحتوى
        'content.view' => 'عرض المحتوى',
        'content.create' => 'إنشاء محتوى',
        'content.edit' => 'تعديل المحتوى',
        'content.delete' => 'حذف المحتوى',
        'content.publish' => 'نشر المحتوى',
        'content.moderate' => 'إشراف على المحتوى',
        
        // إدارة الأفلام
        'movies.view' => 'عرض الأفلام',
        'movies.create' => 'إضافة أفلام',
        'movies.edit' => 'تعديل الأفلام',
        'movies.delete' => 'حذف الأفلام',
        'movies.upload' => 'رفع ملفات الأفلام',
        
        // إدارة المسلسلات
        'series.view' => 'عرض المسلسلات',
        'series.create' => 'إضافة مسلسلات',
        'series.edit' => 'تعديل المسلسلات',
        'series.delete' => 'حذف المسلسلات',
        'series.episodes' => 'إدارة الحلقات',
        
        // إدارة التصنيفات
        'categories.view' => 'عرض التصنيفات',
        'categories.create' => 'إنشاء تصنيفات',
        'categories.edit' => 'تعديل التصنيفات',
        'categories.delete' => 'حذف التصنيفات',
        
        // إدارة الوسائط
        'media.view' => 'عرض الملفات',
        'media.upload' => 'رفع ملفات',
        'media.edit' => 'تعديل الملفات',
        'media.delete' => 'حذف الملفات',
        'media.organize' => 'تنظيم الملفات',
        
        // إدارة التعليقات
        'comments.view' => 'عرض التعليقات',
        'comments.moderate' => 'إشراف على التعليقات',
        'comments.delete' => 'حذف التعليقات',
        'comments.reply' => 'الرد على التعليقات',
        
        // إدارة التقييمات
        'ratings.view' => 'عرض التقييمات',
        'ratings.moderate' => 'إشراف على التقييمات',
        'ratings.delete' => 'حذف التقييمات',
        
        // إدارة الصفحات
        'pages.view' => 'عرض الصفحات',
        'pages.create' => 'إنشاء صفحات',
        'pages.edit' => 'تعديل الصفحات',
        'pages.delete' => 'حذف الصفحات',
        'pages.publish' => 'نشر الصفحات',
        
        // إدارة القوائم
        'menus.view' => 'عرض القوائم',
        'menus.create' => 'إنشاء قوائم',
        'menus.edit' => 'تعديل القوائم',
        'menus.delete' => 'حذف القوائم',
        'menus.organize' => 'تنظيم القوائم',
        
        // إدارة القوالب
        'themes.view' => 'عرض القوالب',
        'themes.install' => 'تثبيت قوالب',
        'themes.edit' => 'تعديل القوالب',
        'themes.delete' => 'حذف القوالب',
        'themes.activate' => 'تفعيل القوالب',
        
        // إدارة الإضافات
        'plugins.view' => 'عرض الإضافات',
        'plugins.install' => 'تثبيت إضافات',
        'plugins.activate' => 'تفعيل الإضافات',
        'plugins.configure' => 'تكوين الإضافات',
        'plugins.delete' => 'حذف الإضافات',
        
        // إدارة التقارير
        'reports.view' => 'عرض التقارير',
        'reports.create' => 'إنشاء تقارير',
        'reports.export' => 'تصدير التقارير',
        'reports.analytics' => 'تحليلات متقدمة',
        
        // إدارة الاشتراكات
        'subscriptions.view' => 'عرض الاشتراكات',
        'subscriptions.create' => 'إنشاء خطط اشتراك',
        'subscriptions.edit' => 'تعديل الاشتراكات',
        'subscriptions.manage' => 'إدارة اشتراكات المستخدمين',
        
        // إدارة المدفوعات
        'payments.view' => 'عرض المدفوعات',
        'payments.process' => 'معالجة المدفوعات',
        'payments.refund' => 'استرداد المدفوعات',
        'payments.reports' => 'تقارير المدفوعات',
        
        // الملف الشخصي
        'profile.view' => 'عرض الملف الشخصي',
        'profile.edit' => 'تعديل الملف الشخصي',
        'profile.password' => 'تغيير كلمة المرور',
        'profile.delete' => 'حذف الحساب',
        
        // المفضلة
        'favorites.view' => 'عرض المفضلة',
        'favorites.add' => 'إضافة للمفضلة',
        'favorites.remove' => 'إزالة من المفضلة',
        'favorites.organize' => 'تنظيم المفضلة'
    ];
    
    public function __construct($pdo, $security) {
        $this->pdo = $pdo;
        $this->security = $security;
    }
    
    /**
     * الحصول على جميع الصلاحيات المتاحة
     */
    public function getAvailablePermissions() {
        return $this->availablePermissions;
    }
    
    /**
     * الحصول على الصلاحيات مجمعة حسب الفئة
     */
    public function getPermissionsByCategory() {
        $categories = [];
        
        foreach ($this->availablePermissions as $permission => $description) {
            $parts = explode('.', $permission);
            $category = $parts[0];
            
            if (!isset($categories[$category])) {
                $categories[$category] = [];
            }
            
            $categories[$category][$permission] = $description;
        }
        
        return $categories;
    }
    
    /**
     * إنشاء دور جديد
     */
    public function createRole($name, $displayName, $description, $permissions = []) {
        try {
            // التحقق من عدم وجود الدور
            $stmt = $this->pdo->prepare("SELECT COUNT(*) FROM roles WHERE name = ?");
            $stmt->execute([$name]);
            
            if ($stmt->fetchColumn() > 0) {
                throw new Exception("الدور موجود مسبقاً");
            }
            
            // إنشاء الدور
            $stmt = $this->pdo->prepare("
                INSERT INTO roles (name, display_name, description, permissions) 
                VALUES (?, ?, ?, ?)
            ");
            
            $stmt->execute([
                $name,
                $displayName,
                $description,
                json_encode($permissions)
            ]);
            
            $roleId = $this->pdo->lastInsertId();
            
            // تسجيل النشاط
            if (isset($_SESSION['user_id'])) {
                $this->security->logActivity(
                    $_SESSION['user_id'],
                    'role.create',
                    "تم إنشاء دور جديد: $displayName",
                    'roles',
                    $roleId,
                    null,
                    ['name' => $name, 'display_name' => $displayName]
                );
            }
            
            return $roleId;
            
        } catch (Exception $e) {
            throw new Exception("فشل في إنشاء الدور: " . $e->getMessage());
        }
    }
    
    /**
     * تحديث دور
     */
    public function updateRole($roleId, $displayName, $description, $permissions = []) {
        try {
            // الحصول على البيانات القديمة
            $stmt = $this->pdo->prepare("SELECT * FROM roles WHERE id = ?");
            $stmt->execute([$roleId]);
            $oldRole = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$oldRole) {
                throw new Exception("الدور غير موجود");
            }
            
            if ($oldRole['is_system']) {
                throw new Exception("لا يمكن تعديل أدوار النظام");
            }
            
            // تحديث الدور
            $stmt = $this->pdo->prepare("
                UPDATE roles 
                SET display_name = ?, description = ?, permissions = ?, updated_at = NOW() 
                WHERE id = ?
            ");
            
            $stmt->execute([
                $displayName,
                $description,
                json_encode($permissions),
                $roleId
            ]);
            
            // تسجيل النشاط
            if (isset($_SESSION['user_id'])) {
                $this->security->logActivity(
                    $_SESSION['user_id'],
                    'role.update',
                    "تم تحديث الدور: $displayName",
                    'roles',
                    $roleId,
                    $oldRole,
                    ['display_name' => $displayName, 'description' => $description, 'permissions' => $permissions]
                );
            }
            
            return true;
            
        } catch (Exception $e) {
            throw new Exception("فشل في تحديث الدور: " . $e->getMessage());
        }
    }
    
    /**
     * حذف دور
     */
    public function deleteRole($roleId) {
        try {
            // التحقق من الدور
            $stmt = $this->pdo->prepare("SELECT * FROM roles WHERE id = ?");
            $stmt->execute([$roleId]);
            $role = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$role) {
                throw new Exception("الدور غير موجود");
            }
            
            if ($role['is_system']) {
                throw new Exception("لا يمكن حذف أدوار النظام");
            }
            
            // التحقق من وجود مستخدمين مرتبطين بالدور
            $stmt = $this->pdo->prepare("SELECT COUNT(*) FROM user_roles WHERE role_id = ?");
            $stmt->execute([$roleId]);
            
            if ($stmt->fetchColumn() > 0) {
                throw new Exception("لا يمكن حذف الدور لوجود مستخدمين مرتبطين به");
            }
            
            // حذف الدور
            $stmt = $this->pdo->prepare("DELETE FROM roles WHERE id = ?");
            $stmt->execute([$roleId]);
            
            // تسجيل النشاط
            if (isset($_SESSION['user_id'])) {
                $this->security->logActivity(
                    $_SESSION['user_id'],
                    'role.delete',
                    "تم حذف الدور: {$role['display_name']}",
                    'roles',
                    $roleId,
                    $role,
                    null
                );
            }
            
            return true;
            
        } catch (Exception $e) {
            throw new Exception("فشل في حذف الدور: " . $e->getMessage());
        }
    }
    
    /**
     * تعيين دور لمستخدم
     */
    public function assignRole($userId, $roleId) {
        try {
            // التحقق من وجود المستخدم والدور
            $stmt = $this->pdo->prepare("SELECT COUNT(*) FROM users WHERE id = ?");
            $stmt->execute([$userId]);
            if ($stmt->fetchColumn() == 0) {
                throw new Exception("المستخدم غير موجود");
            }
            
            $stmt = $this->pdo->prepare("SELECT display_name FROM roles WHERE id = ?");
            $stmt->execute([$roleId]);
            $role = $stmt->fetch(PDO::FETCH_ASSOC);
            if (!$role) {
                throw new Exception("الدور غير موجود");
            }
            
            // تعيين الدور
            $stmt = $this->pdo->prepare("
                INSERT IGNORE INTO user_roles (user_id, role_id, assigned_by) 
                VALUES (?, ?, ?)
            ");
            
            $stmt->execute([
                $userId,
                $roleId,
                $_SESSION['user_id'] ?? null
            ]);
            
            // تسجيل النشاط
            if (isset($_SESSION['user_id'])) {
                $this->security->logActivity(
                    $_SESSION['user_id'],
                    'user.role.assign',
                    "تم تعيين الدور '{$role['display_name']}' للمستخدم",
                    'users',
                    $userId,
                    null,
                    ['role_id' => $roleId, 'role_name' => $role['display_name']]
                );
            }
            
            return true;
            
        } catch (Exception $e) {
            throw new Exception("فشل في تعيين الدور: " . $e->getMessage());
        }
    }
    
    /**
     * إزالة دور من مستخدم
     */
    public function removeRole($userId, $roleId) {
        try {
            // الحصول على معلومات الدور
            $stmt = $this->pdo->prepare("SELECT display_name FROM roles WHERE id = ?");
            $stmt->execute([$roleId]);
            $role = $stmt->fetch(PDO::FETCH_ASSOC);
            
            // إزالة الدور
            $stmt = $this->pdo->prepare("
                DELETE FROM user_roles 
                WHERE user_id = ? AND role_id = ?
            ");
            
            $stmt->execute([$userId, $roleId]);
            
            // تسجيل النشاط
            if (isset($_SESSION['user_id']) && $role) {
                $this->security->logActivity(
                    $_SESSION['user_id'],
                    'user.role.remove',
                    "تم إزالة الدور '{$role['display_name']}' من المستخدم",
                    'users',
                    $userId,
                    ['role_id' => $roleId, 'role_name' => $role['display_name']],
                    null
                );
            }
            
            return true;
            
        } catch (Exception $e) {
            throw new Exception("فشل في إزالة الدور: " . $e->getMessage());
        }
    }
    
    /**
     * الحصول على أدوار المستخدم
     */
    public function getUserRoles($userId) {
        try {
            $stmt = $this->pdo->prepare("
                SELECT r.*, ur.assigned_at 
                FROM user_roles ur 
                JOIN roles r ON ur.role_id = r.id 
                WHERE ur.user_id = ?
                ORDER BY r.display_name
            ");
            
            $stmt->execute([$userId]);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            throw new Exception("فشل في جلب أدوار المستخدم: " . $e->getMessage());
        }
    }
    
    /**
     * الحصول على صلاحيات المستخدم
     */
    public function getUserPermissions($userId) {
        try {
            $stmt = $this->pdo->prepare("
                SELECT r.permissions 
                FROM user_roles ur 
                JOIN roles r ON ur.role_id = r.id 
                WHERE ur.user_id = ?
            ");
            
            $stmt->execute([$userId]);
            
            $allPermissions = [];
            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                $permissions = json_decode($row['permissions'], true);
                if (is_array($permissions)) {
                    $allPermissions = array_merge($allPermissions, $permissions);
                }
            }
            
            return array_unique($allPermissions);
            
        } catch (Exception $e) {
            throw new Exception("فشل في جلب صلاحيات المستخدم: " . $e->getMessage());
        }
    }
    
    /**
     * التحقق من صلاحية محددة
     */
    public function hasPermission($userId, $permission) {
        return $this->security->hasPermission($userId, $permission);
    }
    
    /**
     * الحصول على جميع الأدوار
     */
    public function getAllRoles() {
        try {
            $stmt = $this->pdo->query("
                SELECT r.*, 
                       COUNT(ur.user_id) as user_count 
                FROM roles r 
                LEFT JOIN user_roles ur ON r.id = ur.role_id 
                GROUP BY r.id 
                ORDER BY r.is_system DESC, r.display_name
            ");
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            throw new Exception("فشل في جلب الأدوار: " . $e->getMessage());
        }
    }
}
?>
