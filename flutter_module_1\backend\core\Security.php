<?php
/**
 * Shahid - Security Class
 * Professional Video Streaming Platform
 */

class Security {
    private $config;
    
    public function __construct() {
        $this->config = include 'config/config.php';
    }
    
    public function generateCSRFToken() {
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }
        return $_SESSION['csrf_token'];
    }
    
    public function validateCSRF() {
        $token = $_POST['csrf_token'] ?? $_GET['csrf_token'] ?? '';
        return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
    }
    
    public function sanitizeInput($input) {
        if (is_array($input)) {
            return array_map([$this, 'sanitizeInput'], $input);
        }
        
        return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
    }
    
    public function validateInput($data, $rules) {
        $errors = [];
        
        foreach ($rules as $field => $rule) {
            $value = $data[$field] ?? '';
            
            // Required validation
            if (isset($rule['required']) && $rule['required'] && empty($value)) {
                $errors[$field][] = ucfirst($field) . ' is required';
                continue;
            }
            
            // Skip other validations if field is empty and not required
            if (empty($value) && (!isset($rule['required']) || !$rule['required'])) {
                continue;
            }
            
            // Email validation
            if (isset($rule['email']) && $rule['email'] && !filter_var($value, FILTER_VALIDATE_EMAIL)) {
                $errors[$field][] = ucfirst($field) . ' must be a valid email';
            }
            
            // Min length validation
            if (isset($rule['min_length']) && strlen($value) < $rule['min_length']) {
                $errors[$field][] = ucfirst($field) . ' must be at least ' . $rule['min_length'] . ' characters';
            }
            
            // Max length validation
            if (isset($rule['max_length']) && strlen($value) > $rule['max_length']) {
                $errors[$field][] = ucfirst($field) . ' must not exceed ' . $rule['max_length'] . ' characters';
            }
            
            // Numeric validation
            if (isset($rule['numeric']) && $rule['numeric'] && !is_numeric($value)) {
                $errors[$field][] = ucfirst($field) . ' must be numeric';
            }
            
            // URL validation
            if (isset($rule['url']) && $rule['url'] && !filter_var($value, FILTER_VALIDATE_URL)) {
                $errors[$field][] = ucfirst($field) . ' must be a valid URL';
            }
            
            // Custom regex validation
            if (isset($rule['regex']) && !preg_match($rule['regex'], $value)) {
                $message = $rule['regex_message'] ?? ucfirst($field) . ' format is invalid';
                $errors[$field][] = $message;
            }
        }
        
        return $errors;
    }
    
    public function hashPassword($password) {
        return password_hash($password, PASSWORD_DEFAULT);
    }
    
    public function verifyPassword($password, $hash) {
        return password_verify($password, $hash);
    }
    
    public function generateToken($length = 32) {
        return bin2hex(random_bytes($length));
    }
    
    public function encryptData($data, $key = null) {
        if ($key === null) {
            $key = $this->config['security']['jwt_secret'];
        }
        
        $iv = random_bytes(16);
        $encrypted = openssl_encrypt($data, 'AES-256-CBC', $key, 0, $iv);
        return base64_encode($iv . $encrypted);
    }
    
    public function decryptData($encryptedData, $key = null) {
        if ($key === null) {
            $key = $this->config['security']['jwt_secret'];
        }
        
        $data = base64_decode($encryptedData);
        $iv = substr($data, 0, 16);
        $encrypted = substr($data, 16);
        
        return openssl_decrypt($encrypted, 'AES-256-CBC', $key, 0, $iv);
    }
    
    public function generateJWT($payload) {
        $header = json_encode(['typ' => 'JWT', 'alg' => 'HS256']);
        $payload = json_encode($payload);
        
        $base64Header = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($header));
        $base64Payload = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($payload));
        
        $signature = hash_hmac('sha256', $base64Header . "." . $base64Payload, $this->config['security']['jwt_secret'], true);
        $base64Signature = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($signature));
        
        return $base64Header . "." . $base64Payload . "." . $base64Signature;
    }
    
    public function validateJWT($jwt) {
        $parts = explode('.', $jwt);
        if (count($parts) !== 3) {
            return false;
        }
        
        list($header, $payload, $signature) = $parts;
        
        $validSignature = hash_hmac('sha256', $header . "." . $payload, $this->config['security']['jwt_secret'], true);
        $validSignature = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($validSignature));
        
        if (!hash_equals($signature, $validSignature)) {
            return false;
        }
        
        $payload = json_decode(base64_decode(str_replace(['-', '_'], ['+', '/'], $payload)), true);
        
        // Check expiration
        if (isset($payload['exp']) && $payload['exp'] < time()) {
            return false;
        }
        
        return $payload;
    }
    
    public function rateLimitCheck($identifier, $limit = 100, $window = 3600) {
        // Simple rate limiting using session/database
        $key = 'rate_limit_' . md5($identifier);
        
        if (!isset($_SESSION[$key])) {
            $_SESSION[$key] = ['count' => 1, 'start' => time()];
            return true;
        }
        
        $data = $_SESSION[$key];
        
        // Reset if window has passed
        if (time() - $data['start'] > $window) {
            $_SESSION[$key] = ['count' => 1, 'start' => time()];
            return true;
        }
        
        // Check if limit exceeded
        if ($data['count'] >= $limit) {
            return false;
        }
        
        // Increment counter
        $_SESSION[$key]['count']++;
        return true;
    }
    
    public function preventXSS($input) {
        return htmlspecialchars($input, ENT_QUOTES, 'UTF-8');
    }
    
    public function preventSQLInjection($input) {
        // This should be used with PDO prepared statements
        return addslashes($input);
    }
    
    public function isValidFileUpload($file, $allowedTypes = [], $maxSize = null) {
        if ($file['error'] !== UPLOAD_ERR_OK) {
            return false;
        }
        
        if ($maxSize && $file['size'] > $maxSize) {
            return false;
        }
        
        if (!empty($allowedTypes)) {
            $fileType = mime_content_type($file['tmp_name']);
            if (!in_array($fileType, $allowedTypes)) {
                return false;
            }
        }
        
        return true;
    }
}
?>
