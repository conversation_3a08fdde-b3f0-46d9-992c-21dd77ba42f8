<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// معالجة طلبات OPTIONS
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => 'طريقة الطلب غير مدعومة',
        'error_code' => 'METHOD_NOT_ALLOWED'
    ]);
    exit;
}

try {
    // الاتصال بقاعدة البيانات
    $pdo = new PDO("mysql:host=localhost;dbname=shahid_platform;charset=utf8mb4", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // قراءة البيانات المرسلة
    $input = json_decode(file_get_contents('php://input'), true);
    
    // التحقق من وجود البيانات المطلوبة
    if (!isset($input['email']) || !isset($input['password'])) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'message' => 'البريد الإلكتروني وكلمة المرور مطلوبان',
            'error_code' => 'MISSING_CREDENTIALS'
        ]);
        exit;
    }
    
    $email = trim($input['email']);
    $password = $input['password'];
    
    // التحقق من صحة البريد الإلكتروني
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'message' => 'البريد الإلكتروني غير صحيح',
            'error_code' => 'INVALID_EMAIL'
        ]);
        exit;
    }
    
    // البحث عن المستخدم
    $stmt = $pdo->prepare("SELECT * FROM users WHERE email = ? AND is_active = 1");
    $stmt->execute([$email]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$user) {
        // تسجيل محاولة تسجيل دخول فاشلة
        $stmt = $pdo->prepare("INSERT INTO failed_logins (email, ip_address, attempt_time) VALUES (?, ?, NOW())");
        $stmt->execute([$email, $_SERVER['REMOTE_ADDR']]);
        
        http_response_code(401);
        echo json_encode([
            'success' => false,
            'message' => 'البريد الإلكتروني أو كلمة المرور غير صحيحة',
            'error_code' => 'INVALID_CREDENTIALS'
        ]);
        exit;
    }
    
    // التحقق من كلمة المرور
    if (!password_verify($password, $user['password'])) {
        // تسجيل محاولة تسجيل دخول فاشلة
        $stmt = $pdo->prepare("INSERT INTO failed_logins (email, ip_address, attempt_time) VALUES (?, ?, NOW())");
        $stmt->execute([$email, $_SERVER['REMOTE_ADDR']]);
        
        http_response_code(401);
        echo json_encode([
            'success' => false,
            'message' => 'البريد الإلكتروني أو كلمة المرور غير صحيحة',
            'error_code' => 'INVALID_CREDENTIALS'
        ]);
        exit;
    }
    
    // التحقق من محاولات تسجيل الدخول الفاشلة
    $stmt = $pdo->prepare("
        SELECT COUNT(*) FROM failed_logins 
        WHERE email = ? AND attempt_time > DATE_SUB(NOW(), INTERVAL 15 MINUTE)
    ");
    $stmt->execute([$email]);
    $failedAttempts = $stmt->fetchColumn();
    
    if ($failedAttempts >= 5) {
        http_response_code(429);
        echo json_encode([
            'success' => false,
            'message' => 'تم تجاوز عدد محاولات تسجيل الدخول المسموحة. حاول مرة أخرى بعد 15 دقيقة',
            'error_code' => 'TOO_MANY_ATTEMPTS'
        ]);
        exit;
    }
    
    // إنشاء رمز الوصول (JWT بسيط)
    $payload = [
        'user_id' => $user['id'],
        'email' => $user['email'],
        'subscription_type' => $user['subscription_type'],
        'issued_at' => time(),
        'expires_at' => time() + (24 * 60 * 60) // 24 ساعة
    ];
    
    $token = base64_encode(json_encode($payload));
    
    // تحديث آخر تسجيل دخول
    $stmt = $pdo->prepare("UPDATE users SET last_login = NOW() WHERE id = ?");
    $stmt->execute([$user['id']]);
    
    // تسجيل النشاط
    $stmt = $pdo->prepare("
        INSERT INTO activity_logs (user_id, action, description, ip_address, created_at) 
        VALUES (?, 'login', 'تسجيل دخول عبر API', ?, NOW())
    ");
    $stmt->execute([$user['id'], $_SERVER['REMOTE_ADDR']]);
    
    // حذف محاولات تسجيل الدخول الفاشلة للمستخدم
    $stmt = $pdo->prepare("DELETE FROM failed_logins WHERE email = ?");
    $stmt->execute([$email]);
    
    // الحصول على اشتراك المستخدم الحالي
    $stmt = $pdo->prepare("
        SELECT us.*, s.name as plan_name, s.features 
        FROM user_subscriptions us 
        JOIN subscriptions s ON us.subscription_id = s.id 
        WHERE us.user_id = ? AND us.status = 'active' AND us.end_date > NOW() 
        ORDER BY us.end_date DESC 
        LIMIT 1
    ");
    $stmt->execute([$user['id']]);
    $subscription = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // إعداد الاستجابة
    $response = [
        'success' => true,
        'message' => 'تم تسجيل الدخول بنجاح',
        'data' => [
            'user' => [
                'id' => $user['id'],
                'username' => $user['username'],
                'email' => $user['email'],
                'full_name' => $user['full_name'],
                'subscription_type' => $user['subscription_type'],
                'profile_picture' => $user['profile_picture'],
                'created_at' => $user['created_at'],
                'last_login' => $user['last_login']
            ],
            'token' => $token,
            'subscription' => $subscription ? [
                'plan_name' => $subscription['plan_name'],
                'features' => json_decode($subscription['features'], true),
                'end_date' => $subscription['end_date']
            ] : null
        ]
    ];
    
    http_response_code(200);
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    
} catch (PDOException $e) {
    error_log("Database error in login API: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'خطأ في الخادم',
        'error_code' => 'DATABASE_ERROR'
    ]);
    
} catch (Exception $e) {
    error_log("General error in login API: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'خطأ غير متوقع',
        'error_code' => 'GENERAL_ERROR'
    ]);
}
?>
