class AppConfig {
  // App Information
  static const String appName = 'Shahid';
  static const String appVersion = '1.0.0';
  static const String appBuildNumber = '1';
  
  // API Configuration
  static const String baseUrl = 'https://your-domain.com/api';
  static const String websiteUrl = 'https://your-domain.com';
  
  // API Endpoints
  static const String authEndpoint = '/auth';
  static const String moviesEndpoint = '/movies';
  static const String seriesEndpoint = '/series';
  static const String episodesEndpoint = '/episodes';
  static const String userEndpoint = '/user';
  static const String searchEndpoint = '/search';
  static const String subscriptionsEndpoint = '/subscriptions';
  static const String streamEndpoint = '/stream';
  static const String downloadEndpoint = '/download';
  static const String analyticsEndpoint = '/analytics';
  
  // Storage Keys
  static const String tokenKey = 'auth_token';
  static const String userKey = 'user_data';
  static const String themeKey = 'theme_mode';
  static const String languageKey = 'language';
  static const String firstTimeKey = 'first_time';
  static const String biometricKey = 'biometric_enabled';
  static const String notificationsKey = 'notifications_enabled';
  static const String downloadQualityKey = 'download_quality';
  static const String streamingQualityKey = 'streaming_quality';
  static const String autoPlayKey = 'auto_play';
  static const String subtitlesKey = 'subtitles_enabled';
  
  // Cache Configuration
  static const int cacheMaxAge = 3600; // 1 hour
  static const int imageCacheMaxAge = 86400; // 24 hours
  static const int videoCacheMaxAge = 604800; // 7 days
  
  // Pagination
  static const int defaultPageSize = 20;
  static const int maxPageSize = 50;
  
  // Video Player Configuration
  static const int seekDuration = 10; // seconds
  static const int bufferDuration = 30; // seconds
  static const double defaultVolume = 1.0;
  static const double defaultPlaybackSpeed = 1.0;
  
  // Download Configuration
  static const int maxConcurrentDownloads = 3;
  static const int maxDownloadsPerMonth = 50;
  static const List<String> supportedVideoFormats = ['mp4', 'mkv', 'avi'];
  static const List<String> supportedSubtitleFormats = ['srt', 'vtt', 'ass'];
  
  // Quality Options
  static const List<String> videoQualities = [
    'auto',
    '360p',
    '480p',
    '720p',
    '1080p',
    '4K'
  ];
  
  // Supported Languages
  static const List<Map<String, String>> supportedLanguages = [
    {'code': 'ar', 'name': 'العربية', 'flag': '🇸🇦'},
    {'code': 'en', 'name': 'English', 'flag': '🇺🇸'},
    {'code': 'fr', 'name': 'Français', 'flag': '🇫🇷'},
    {'code': 'es', 'name': 'Español', 'flag': '🇪🇸'},
  ];
  
  // Social Media Links
  static const String facebookUrl = 'https://facebook.com/shahid';
  static const String twitterUrl = 'https://twitter.com/shahid';
  static const String instagramUrl = 'https://instagram.com/shahid';
  static const String youtubeUrl = 'https://youtube.com/shahid';
  
  // Support & Legal
  static const String supportEmail = '<EMAIL>';
  static const String privacyPolicyUrl = '$websiteUrl/privacy';
  static const String termsOfServiceUrl = '$websiteUrl/terms';
  static const String aboutUrl = '$websiteUrl/about';
  
  // Firebase Configuration
  static const String firebaseProjectId = 'shahid-streaming';
  static const String firebaseApiKey = 'your-firebase-api-key';
  static const String firebaseAppId = 'your-firebase-app-id';
  
  // Analytics Events
  static const String eventAppOpen = 'app_open';
  static const String eventLogin = 'login';
  static const String eventRegister = 'register';
  static const String eventVideoPlay = 'video_play';
  static const String eventVideoPause = 'video_pause';
  static const String eventVideoComplete = 'video_complete';
  static const String eventSearch = 'search';
  static const String eventDownload = 'download';
  static const String eventShare = 'share';
  static const String eventSubscribe = 'subscribe';
  
  // Error Messages
  static const String networkErrorMessage = 'تحقق من اتصال الإنترنت';
  static const String serverErrorMessage = 'حدث خطأ في الخادم';
  static const String authErrorMessage = 'خطأ في المصادقة';
  static const String notFoundErrorMessage = 'المحتوى غير موجود';
  static const String permissionErrorMessage = 'ليس لديك صلاحية للوصول';
  static const String subscriptionErrorMessage = 'يتطلب اشتراك نشط';
  
  // Success Messages
  static const String loginSuccessMessage = 'تم تسجيل الدخول بنجاح';
  static const String registerSuccessMessage = 'تم إنشاء الحساب بنجاح';
  static const String downloadSuccessMessage = 'تم التحميل بنجاح';
  static const String favoriteAddedMessage = 'تم إضافة للمفضلة';
  static const String favoriteRemovedMessage = 'تم إزالة من المفضلة';
  static const String watchlistAddedMessage = 'تم إضافة لقائمة المشاهدة';
  static const String watchlistRemovedMessage = 'تم إزالة من قائمة المشاهدة';
  
  // Validation Rules
  static const int minPasswordLength = 8;
  static const int maxPasswordLength = 50;
  static const int minNameLength = 2;
  static const int maxNameLength = 50;
  static const String emailPattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$';
  static const String phonePattern = r'^\+?[0-9]{10,15}$';
  
  // UI Configuration
  static const double borderRadius = 12.0;
  static const double cardElevation = 4.0;
  static const double iconSize = 24.0;
  static const double buttonHeight = 48.0;
  static const double inputHeight = 56.0;
  
  // Animation Durations
  static const Duration shortAnimation = Duration(milliseconds: 200);
  static const Duration mediumAnimation = Duration(milliseconds: 300);
  static const Duration longAnimation = Duration(milliseconds: 500);
  
  // Timeouts
  static const Duration connectionTimeout = Duration(seconds: 30);
  static const Duration receiveTimeout = Duration(seconds: 30);
  static const Duration sendTimeout = Duration(seconds: 30);
  
  // Development Configuration
  static const bool isDebugMode = true;
  static const bool enableLogging = true;
  static const bool enableAnalytics = true;
  static const bool enableCrashlytics = true;
  
  // Feature Flags
  static const bool enableDownloads = true;
  static const bool enableOfflineMode = true;
  static const bool enableBiometricAuth = true;
  static const bool enablePictureInPicture = true;
  static const bool enableChromecast = true;
  static const bool enableAirPlay = true;
  
  // Content Rating
  static const List<String> contentRatings = [
    'G',      // General Audiences
    'PG',     // Parental Guidance
    'PG-13',  // Parents Strongly Cautioned
    'R',      // Restricted
    'NC-17',  // Adults Only
  ];
  
  // Genres
  static const List<String> movieGenres = [
    'Action',
    'Adventure',
    'Animation',
    'Comedy',
    'Crime',
    'Documentary',
    'Drama',
    'Family',
    'Fantasy',
    'History',
    'Horror',
    'Music',
    'Mystery',
    'Romance',
    'Science Fiction',
    'Thriller',
    'War',
    'Western',
  ];
  
  // Helper Methods
  static String getFullUrl(String endpoint) {
    return '$baseUrl$endpoint';
  }
  
  static String getImageUrl(String imagePath) {
    if (imagePath.startsWith('http')) {
      return imagePath;
    }
    return '$websiteUrl$imagePath';
  }
  
  static String getVideoUrl(String videoPath) {
    if (videoPath.startsWith('http')) {
      return videoPath;
    }
    return '$websiteUrl$videoPath';
  }
  
  static bool isValidEmail(String email) {
    return RegExp(emailPattern).hasMatch(email);
  }
  
  static bool isValidPhone(String phone) {
    return RegExp(phonePattern).hasMatch(phone);
  }
  
  static bool isValidPassword(String password) {
    return password.length >= minPasswordLength && 
           password.length <= maxPasswordLength;
  }
}
