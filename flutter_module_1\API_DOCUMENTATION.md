# 🔌 دليل API - Shahid Platform

## 📋 نظرة عامة

توفر منصة **Shahid Platform** مجموعة شاملة من واجهات برمجة التطبيقات (APIs) لتطوير التطبيقات والخدمات المتكاملة.

**Base URL**: `http://localhost/amr2/flutter_module_1/backend/api/`

---

## 🔐 المصادقة والأمان

### نظام التوكن
جميع APIs المحمية تتطلب رمز وصول (JWT Token) في الهيدر:

```http
Authorization: Bearer YOUR_JWT_TOKEN
```

### الحصول على التوكن
```http
POST /auth/login.php
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**الاستجابة:**
```json
{
  "success": true,
  "message": "تم تسجيل الدخول بنجاح",
  "data": {
    "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "user": {
      "id": 1,
      "username": "user123",
      "email": "<EMAIL>",
      "subscription_type": "premium"
    }
  }
}
```

---

## 👤 إدارة المستخدمين

### 1. إنشاء حساب جديد
```http
POST /auth/register.php
Content-Type: application/json

{
  "username": "newuser",
  "email": "<EMAIL>",
  "password": "securepassword",
  "full_name": "الاسم الكامل",
  "phone": "966501234567"
}
```

### 2. الملف الشخصي
```http
GET /user/profile.php
Authorization: Bearer YOUR_TOKEN
```

### 3. تحديث الملف الشخصي
```http
PUT /user/profile.php
Authorization: Bearer YOUR_TOKEN
Content-Type: application/json

{
  "full_name": "الاسم الجديد",
  "phone": "966501234567",
  "current_password": "oldpass",
  "new_password": "newpass"
}
```

---

## 🎬 إدارة المحتوى

### 1. جلب المحتوى الأحدث
```http
GET /content/latest.php?limit=20&offset=0&type=movie
```

**المعاملات:**
- `limit`: عدد العناصر (افتراضي: 20، أقصى: 50)
- `offset`: نقطة البداية (افتراضي: 0)
- `type`: نوع المحتوى (`movie`, `series`, `all`)
- `category`: معرف التصنيف
- `featured`: المحتوى المميز (`true`/`false`)

**الاستجابة:**
```json
{
  "success": true,
  "data": {
    "content": [
      {
        "id": 1,
        "title": "عنوان الفيلم",
        "description": "وصف الفيلم",
        "poster": "path/to/poster.jpg",
        "rating": 8.5,
        "views": 15420,
        "content_type": "movie",
        "is_premium": false,
        "can_watch": true
      }
    ],
    "pagination": {
      "total": 150,
      "limit": 20,
      "offset": 0,
      "has_more": true
    }
  }
}
```

### 2. تفاصيل المحتوى
```http
GET /content/details.php?type=movie&id=1
```

### 3. حلقات المسلسل
```http
GET /series/episodes.php?series_id=1&season=1
Authorization: Bearer YOUR_TOKEN
```

---

## 🔍 البحث والفلترة

### البحث المتقدم
```http
GET /search/content.php?q=البحث&type=all&category=1&year=2023&rating_min=7&sort=relevance
```

**المعاملات:**
- `q`: نص البحث
- `type`: نوع المحتوى (`all`, `movie`, `series`)
- `category`: معرف التصنيف
- `year`: سنة الإنتاج
- `rating_min`: أقل تقييم
- `sort`: ترتيب النتائج (`relevance`, `rating`, `year`, `views`, `title`)

**الاستجابة:**
```json
{
  "success": true,
  "data": {
    "results": [...],
    "search_params": {
      "query": "البحث",
      "type": "all",
      "sort": "relevance"
    },
    "filters": {
      "categories": [...],
      "available_years": [2023, 2022, ...],
      "sort_options": [...]
    },
    "statistics": {
      "total_results": 25,
      "search_time": 0.045
    }
  }
}
```

---

## 🎥 المشاهدة والتتبع

### 1. بدء المشاهدة
```http
POST /watch/start.php
Authorization: Bearer YOUR_TOKEN
Content-Type: application/json

{
  "content_type": "movie",
  "content_id": 1,
  "watch_time": 0,
  "quality": "auto",
  "device": "mobile"
}
```

### 2. تحديث تقدم المشاهدة
```http
POST /watch/progress.php
Authorization: Bearer YOUR_TOKEN
Content-Type: application/json

{
  "content_type": "movie",
  "content_id": 1,
  "watch_time": 1800,
  "completed": false
}
```

### 3. سجل المشاهدة
```http
GET /watch/history.php?limit=20&offset=0
Authorization: Bearer YOUR_TOKEN
```

---

## ❤️ المفضلة

### 1. إضافة للمفضلة
```http
POST /favorites/add.php
Authorization: Bearer YOUR_TOKEN
Content-Type: application/json

{
  "content_type": "movie",
  "content_id": 1
}
```

### 2. إزالة من المفضلة
```http
DELETE /favorites/add.php
Authorization: Bearer YOUR_TOKEN
Content-Type: application/json

{
  "content_type": "movie",
  "content_id": 1
}
```

### 3. قائمة المفضلة
```http
GET /favorites/list.php?limit=20&offset=0
Authorization: Bearer YOUR_TOKEN
```

---

## 💳 الاشتراكات والدفع

### 1. خطط الاشتراك المتاحة
```http
GET /subscription/plans.php
```

### 2. الاشتراك في خطة
```http
POST /subscription/subscribe.php
Authorization: Bearer YOUR_TOKEN
Content-Type: application/json

{
  "subscription_id": 2,
  "payment_method": "credit_card",
  "promo_code": "DISCOUNT20"
}
```

### 3. حالة الاشتراك الحالي
```http
GET /subscription/status.php
Authorization: Bearer YOUR_TOKEN
```

### 4. إلغاء الاشتراك
```http
POST /subscription/cancel.php
Authorization: Bearer YOUR_TOKEN
Content-Type: application/json

{
  "reason": "سبب الإلغاء"
}
```

---

## 🤖 التوصيات

### الحصول على التوصيات
```http
GET /recommendations/get.php?type=mixed&limit=20&algorithm=hybrid
Authorization: Bearer YOUR_TOKEN
```

**المعاملات:**
- `type`: نوع المحتوى (`mixed`, `movie`, `series`)
- `limit`: عدد التوصيات (افتراضي: 20)
- `algorithm`: خوارزمية التوصية (`collaborative`, `content_based`, `hybrid`, `trending`)

**الاستجابة:**
```json
{
  "success": true,
  "data": {
    "recommendations": [...],
    "algorithm_used": "hybrid",
    "personalized": true,
    "total_count": 20
  }
}
```

---

## 📊 الإحصائيات والتقارير

### إحصائيات المستخدم
```http
GET /stats/user.php
Authorization: Bearer YOUR_TOKEN
```

**الاستجابة:**
```json
{
  "success": true,
  "data": {
    "watch_time": {
      "total_minutes": 2450,
      "this_week": 180,
      "this_month": 720
    },
    "content_stats": {
      "movies_watched": 25,
      "series_watched": 8,
      "episodes_watched": 156
    },
    "favorites_count": 12,
    "achievements": [...]
  }
}
```

---

## 🔔 الإشعارات

### 1. قائمة الإشعارات
```http
GET /notifications/list.php?limit=20&unread_only=false
Authorization: Bearer YOUR_TOKEN
```

### 2. تحديد إشعار كمقروء
```http
POST /notifications/mark_read.php
Authorization: Bearer YOUR_TOKEN
Content-Type: application/json

{
  "notification_id": 123
}
```

### 3. حذف إشعار
```http
DELETE /notifications/delete.php
Authorization: Bearer YOUR_TOKEN
Content-Type: application/json

{
  "notification_id": 123
}
```

---

## 📱 إعدادات التطبيق

### الحصول على الإعدادات
```http
GET /settings/app.php
```

**الاستجابة:**
```json
{
  "success": true,
  "data": {
    "app_version": "1.0.0",
    "min_supported_version": "1.0.0",
    "maintenance_mode": false,
    "features": {
      "offline_download": true,
      "live_streaming": false,
      "social_features": true
    },
    "cdn_urls": {
      "images": "https://cdn.shahidplatform.com/images/",
      "videos": "https://cdn.shahidplatform.com/videos/"
    }
  }
}
```

---

## ⚠️ معالجة الأخطاء

### رموز الحالة HTTP
- `200`: نجح الطلب
- `201`: تم إنشاء المورد بنجاح
- `400`: خطأ في البيانات المرسلة
- `401`: غير مصرح (رمز وصول مطلوب أو منتهي الصلاحية)
- `403`: ممنوع (لا توجد صلاحية)
- `404`: المورد غير موجود
- `409`: تعارض (مثل: محتوى موجود في المفضلة مسبقاً)
- `429`: تم تجاوز حد الطلبات
- `500`: خطأ في الخادم

### تنسيق الأخطاء
```json
{
  "success": false,
  "message": "رسالة الخطأ باللغة العربية",
  "error_code": "ERROR_CODE",
  "details": {
    "field": "اسم الحقل الذي يحتوي على خطأ",
    "validation_errors": [...]
  }
}
```

### أخطاء شائعة
- `INVALID_TOKEN`: رمز الوصول غير صالح
- `TOKEN_EXPIRED`: رمز الوصول منتهي الصلاحية
- `MISSING_REQUIRED_FIELD`: حقل مطلوب مفقود
- `INVALID_CREDENTIALS`: بيانات تسجيل الدخول خاطئة
- `SUBSCRIPTION_REQUIRED`: يتطلب اشتراك مدفوع
- `CONTENT_NOT_FOUND`: المحتوى غير موجود
- `RATE_LIMIT_EXCEEDED`: تم تجاوز حد الطلبات

---

## 🚀 حدود الاستخدام

### حدود الطلبات (Rate Limiting)
- **المستخدمين المجانيين**: 100 طلب/ساعة
- **المشتركين المدفوعين**: 1000 طلب/ساعة
- **APIs البحث**: 50 طلب/ساعة لجميع المستخدمين

### حجم البيانات
- **حد رفع الملفات**: 100 MB
- **حد البيانات المرسلة**: 10 MB لكل طلب
- **حد الاستجابة**: 50 MB

---

## 🔧 أمثلة عملية

### مثال: تطبيق بحث بسيط
```javascript
// البحث عن محتوى
async function searchContent(query) {
  const response = await fetch(`/api/search/content.php?q=${encodeURIComponent(query)}`, {
    headers: {
      'Authorization': `Bearer ${userToken}`
    }
  });
  
  const data = await response.json();
  
  if (data.success) {
    return data.data.results;
  } else {
    throw new Error(data.message);
  }
}

// استخدام الدالة
searchContent('أكشن')
  .then(results => console.log(results))
  .catch(error => console.error(error));
```

### مثال: تتبع تقدم المشاهدة
```javascript
// تحديث تقدم المشاهدة كل 30 ثانية
setInterval(async () => {
  const currentTime = videoPlayer.getCurrentTime();
  
  await fetch('/api/watch/progress.php', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${userToken}`
    },
    body: JSON.stringify({
      content_type: 'movie',
      content_id: currentMovieId,
      watch_time: Math.floor(currentTime),
      completed: currentTime >= videoDuration * 0.9
    })
  });
}, 30000);
```

---

## 📞 الدعم الفني للمطورين

### التواصل
- **البريد الإلكتروني**: <EMAIL>
- **Discord**: [رابط خادم Discord للمطورين]
- **GitHub**: [رابط مستودع GitHub]

### الموارد الإضافية
- **Postman Collection**: [رابط مجموعة Postman]
- **SDK للـ JavaScript**: [رابط SDK]
- **SDK للـ Flutter/Dart**: [رابط SDK]

---

**استمتع بتطوير تطبيقات رائعة مع APIs منصة شاهد!** 🚀
