<?php
/**
 * Shahid - Authentication Class
 * Professional Video Streaming Platform
 */

class Auth {
    private $db;
    private $config;
    
    public function __construct($db) {
        $this->db = $db;
        $this->config = include 'config/config.php';
    }
    
    public function login($email, $password, $remember = false) {
        // Check login attempts
        if ($this->isLockedOut($email)) {
            return ['success' => false, 'message' => 'Account temporarily locked due to too many failed attempts'];
        }
        
        $sql = "SELECT * FROM users WHERE email = :email AND status = 'active'";
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':email', $email);
        $stmt->execute();
        $user = $stmt->fetch();
        
        if ($user && password_verify($password, $user['password'])) {
            // Clear failed attempts
            $this->clearFailedAttempts($email);
            
            // Set session
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['user_email'] = $user['email'];
            $_SESSION['user_role'] = $user['role'];
            $_SESSION['logged_in'] = true;
            
            // Update last login
            $this->updateLastLogin($user['id']);
            
            // Set remember me cookie if requested
            if ($remember) {
                $this->setRememberToken($user['id']);
            }
            
            return ['success' => true, 'user' => $user];
        } else {
            // Record failed attempt
            $this->recordFailedAttempt($email);
            return ['success' => false, 'message' => 'Invalid email or password'];
        }
    }
    
    public function register($data) {
        // Validate input
        $errors = [];
        
        if (empty($data['name'])) {
            $errors[] = 'Name is required';
        }
        
        if (empty($data['email']) || !filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
            $errors[] = 'Valid email is required';
        }
        
        if (empty($data['password']) || strlen($data['password']) < $this->config['security']['password_min_length']) {
            $errors[] = 'Password must be at least ' . $this->config['security']['password_min_length'] . ' characters';
        }
        
        // Check if email already exists
        if ($this->emailExists($data['email'])) {
            $errors[] = 'Email already exists';
        }
        
        if (!empty($errors)) {
            return ['success' => false, 'errors' => $errors];
        }
        
        // Create user
        $hashedPassword = password_hash($data['password'], PASSWORD_DEFAULT);
        
        $sql = "INSERT INTO users (name, email, password, created_at) VALUES (:name, :email, :password, NOW())";
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':name', $data['name']);
        $stmt->bindParam(':email', $data['email']);
        $stmt->bindParam(':password', $hashedPassword);
        
        if ($stmt->execute()) {
            $userId = $this->db->lastInsertId();
            return ['success' => true, 'user_id' => $userId];
        } else {
            return ['success' => false, 'message' => 'Registration failed'];
        }
    }
    
    public function logout() {
        // Clear remember token if exists
        if (isset($_SESSION['user_id'])) {
            $this->clearRememberToken($_SESSION['user_id']);
        }
        
        // Destroy session
        session_destroy();
        
        // Clear remember me cookie
        if (isset($_COOKIE['remember_token'])) {
            setcookie('remember_token', '', time() - 3600, '/');
        }
    }
    
    public function isLoggedIn() {
        if (isset($_SESSION['logged_in']) && $_SESSION['logged_in'] === true) {
            return true;
        }
        
        // Check remember me token
        if (isset($_COOKIE['remember_token'])) {
            return $this->validateRememberToken($_COOKIE['remember_token']);
        }
        
        return false;
    }
    
    public function isAdmin() {
        return $this->isLoggedIn() && isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'admin';
    }
    
    public function getCurrentUser() {
        if (!$this->isLoggedIn()) {
            return null;
        }
        
        $sql = "SELECT * FROM users WHERE id = :id";
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':id', $_SESSION['user_id']);
        $stmt->execute();
        return $stmt->fetch();
    }
    
    private function emailExists($email) {
        $sql = "SELECT id FROM users WHERE email = :email";
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':email', $email);
        $stmt->execute();
        return $stmt->fetch() !== false;
    }
    
    private function recordFailedAttempt($email) {
        $sql = "INSERT INTO login_attempts (email, ip_address, attempted_at) VALUES (:email, :ip, NOW())";
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':email', $email);
        $stmt->bindParam(':ip', $_SERVER['REMOTE_ADDR']);
        $stmt->execute();
    }
    
    private function clearFailedAttempts($email) {
        $sql = "DELETE FROM login_attempts WHERE email = :email";
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':email', $email);
        $stmt->execute();
    }
    
    private function isLockedOut($email) {
        $lockoutDuration = $this->config['security']['lockout_duration'];
        $maxAttempts = $this->config['security']['max_login_attempts'];
        
        $sql = "SELECT COUNT(*) as attempts FROM login_attempts 
                WHERE email = :email AND attempted_at > DATE_SUB(NOW(), INTERVAL :duration SECOND)";
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':email', $email);
        $stmt->bindParam(':duration', $lockoutDuration);
        $stmt->execute();
        $result = $stmt->fetch();
        
        return $result['attempts'] >= $maxAttempts;
    }
    
    private function updateLastLogin($userId) {
        $sql = "UPDATE users SET last_login = NOW(), last_ip = :ip WHERE id = :id";
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':ip', $_SERVER['REMOTE_ADDR']);
        $stmt->bindParam(':id', $userId);
        $stmt->execute();
    }
    
    private function setRememberToken($userId) {
        $token = bin2hex(random_bytes(32));
        $hashedToken = hash('sha256', $token);
        
        $sql = "UPDATE users SET remember_token = :token WHERE id = :id";
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':token', $hashedToken);
        $stmt->bindParam(':id', $userId);
        $stmt->execute();
        
        // Set cookie for 30 days
        setcookie('remember_token', $token, time() + (30 * 24 * 60 * 60), '/');
    }
    
    private function clearRememberToken($userId) {
        $sql = "UPDATE users SET remember_token = NULL WHERE id = :id";
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':id', $userId);
        $stmt->execute();
    }
    
    private function validateRememberToken($token) {
        $hashedToken = hash('sha256', $token);
        
        $sql = "SELECT * FROM users WHERE remember_token = :token AND status = 'active'";
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':token', $hashedToken);
        $stmt->execute();
        $user = $stmt->fetch();
        
        if ($user) {
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['user_email'] = $user['email'];
            $_SESSION['user_role'] = $user['role'];
            $_SESSION['logged_in'] = true;
            return true;
        }
        
        return false;
    }
}
?>
