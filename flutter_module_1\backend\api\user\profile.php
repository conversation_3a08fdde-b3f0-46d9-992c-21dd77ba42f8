<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, PUT, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// معالجة طلبات OPTIONS
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// التحقق من طريقة الطلب
if (!in_array($_SERVER['REQUEST_METHOD'], ['GET', 'PUT'])) {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => 'طريقة الطلب غير مدعومة',
        'error_code' => 'METHOD_NOT_ALLOWED'
    ]);
    exit;
}

try {
    // الاتصال بقاعدة البيانات
    $pdo = new PDO("mysql:host=localhost;dbname=shahid_platform;charset=utf8mb4", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // التحقق من رمز الوصول
    if (!isset($_SERVER['HTTP_AUTHORIZATION'])) {
        http_response_code(401);
        echo json_encode([
            'success' => false,
            'message' => 'رمز الوصول مطلوب',
            'error_code' => 'MISSING_TOKEN'
        ]);
        exit;
    }
    
    $token = str_replace('Bearer ', '', $_SERVER['HTTP_AUTHORIZATION']);
    $payload = json_decode(base64_decode($token), true);
    
    if (!$payload || !isset($payload['user_id']) || $payload['expires_at'] <= time()) {
        http_response_code(401);
        echo json_encode([
            'success' => false,
            'message' => 'رمز الوصول غير صالح أو منتهي الصلاحية',
            'error_code' => 'INVALID_TOKEN'
        ]);
        exit;
    }
    
    $userId = $payload['user_id'];
    
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        // جلب معلومات الملف الشخصي
        
        // الحصول على بيانات المستخدم
        $stmt = $pdo->prepare("
            SELECT id, username, email, full_name, phone, profile_picture, 
                   subscription_type, created_at, last_login, is_active
            FROM users 
            WHERE id = ?
        ");
        $stmt->execute([$userId]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$user) {
            http_response_code(404);
            echo json_encode([
                'success' => false,
                'message' => 'المستخدم غير موجود',
                'error_code' => 'USER_NOT_FOUND'
            ]);
            exit;
        }
        
        // الحصول على الاشتراك الحالي
        $stmt = $pdo->prepare("
            SELECT us.*, s.name as plan_name, s.price, s.features, s.duration_days
            FROM user_subscriptions us 
            JOIN subscriptions s ON us.subscription_id = s.id 
            WHERE us.user_id = ? AND us.status = 'active' AND us.end_date > NOW() 
            ORDER BY us.end_date DESC 
            LIMIT 1
        ");
        $stmt->execute([$userId]);
        $subscription = $stmt->fetch(PDO::FETCH_ASSOC);
        
        // إحصائيات المشاهدة
        $stmt = $pdo->prepare("
            SELECT 
                COUNT(DISTINCT CASE WHEN content_type = 'movie' THEN content_id END) as movies_watched,
                COUNT(DISTINCT CASE WHEN content_type = 'episode' THEN content_id END) as episodes_watched,
                SUM(watch_time) as total_watch_time
            FROM watch_history 
            WHERE user_id = ?
        ");
        $stmt->execute([$userId]);
        $watchStats = $stmt->fetch(PDO::FETCH_ASSOC);
        
        // المفضلة
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM favorites WHERE user_id = ?");
        $stmt->execute([$userId]);
        $favoritesCount = $stmt->fetchColumn();
        
        // آخر المشاهدات
        $stmt = $pdo->prepare("
            SELECT wh.*, 
                   CASE 
                       WHEN wh.content_type = 'movie' THEN m.title
                       WHEN wh.content_type = 'episode' THEN CONCAT(s.title, ' - الحلقة ', e.episode_number)
                   END as content_title,
                   CASE 
                       WHEN wh.content_type = 'movie' THEN m.poster
                       WHEN wh.content_type = 'episode' THEN s.poster
                   END as content_poster
            FROM watch_history wh
            LEFT JOIN movies m ON wh.content_type = 'movie' AND wh.content_id = m.id
            LEFT JOIN episodes e ON wh.content_type = 'episode' AND wh.content_id = e.id
            LEFT JOIN series s ON e.series_id = s.id
            WHERE wh.user_id = ?
            ORDER BY wh.watched_at DESC
            LIMIT 10
        ");
        $stmt->execute([$userId]);
        $recentWatches = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // تنسيق البيانات
        foreach ($recentWatches as &$watch) {
            $watch['watched_at'] = date('Y-m-d H:i:s', strtotime($watch['watched_at']));
            $watch['watch_time_formatted'] = gmdate('H:i:s', $watch['watch_time']);
        }
        
        // إعداد الاستجابة
        $response = [
            'success' => true,
            'message' => 'تم جلب معلومات الملف الشخصي بنجاح',
            'data' => [
                'user' => [
                    'id' => intval($user['id']),
                    'username' => $user['username'],
                    'email' => $user['email'],
                    'full_name' => $user['full_name'],
                    'phone' => $user['phone'],
                    'profile_picture' => $user['profile_picture'],
                    'subscription_type' => $user['subscription_type'],
                    'created_at' => $user['created_at'],
                    'last_login' => $user['last_login'],
                    'is_active' => (bool)$user['is_active']
                ],
                'subscription' => $subscription ? [
                    'plan_name' => $subscription['plan_name'],
                    'price' => floatval($subscription['price']),
                    'features' => json_decode($subscription['features'], true),
                    'start_date' => $subscription['start_date'],
                    'end_date' => $subscription['end_date'],
                    'days_remaining' => max(0, ceil((strtotime($subscription['end_date']) - time()) / 86400))
                ] : null,
                'statistics' => [
                    'movies_watched' => intval($watchStats['movies_watched']),
                    'episodes_watched' => intval($watchStats['episodes_watched']),
                    'total_watch_time' => intval($watchStats['total_watch_time']),
                    'total_watch_time_formatted' => gmdate('H:i:s', $watchStats['total_watch_time']),
                    'favorites_count' => intval($favoritesCount)
                ],
                'recent_watches' => $recentWatches
            ]
        ];
        
        http_response_code(200);
        echo json_encode($response, JSON_UNESCAPED_UNICODE);
        
    } elseif ($_SERVER['REQUEST_METHOD'] === 'PUT') {
        // تحديث معلومات الملف الشخصي
        
        $input = json_decode(file_get_contents('php://input'), true);
        
        // الحقول القابلة للتحديث
        $allowedFields = ['full_name', 'phone', 'profile_picture'];
        $updateFields = [];
        $updateValues = [];
        
        foreach ($allowedFields as $field) {
            if (isset($input[$field])) {
                $updateFields[] = "$field = ?";
                $updateValues[] = $input[$field];
            }
        }
        
        // التحقق من وجود حقول للتحديث
        if (empty($updateFields)) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => 'لا توجد حقول للتحديث',
                'error_code' => 'NO_FIELDS_TO_UPDATE'
            ]);
            exit;
        }
        
        // تحديث كلمة المرور إذا تم توفيرها
        if (isset($input['current_password']) && isset($input['new_password'])) {
            // التحقق من كلمة المرور الحالية
            $stmt = $pdo->prepare("SELECT password FROM users WHERE id = ?");
            $stmt->execute([$userId]);
            $currentHash = $stmt->fetchColumn();
            
            if (!password_verify($input['current_password'], $currentHash)) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => 'كلمة المرور الحالية غير صحيحة',
                    'error_code' => 'INVALID_CURRENT_PASSWORD'
                ]);
                exit;
            }
            
            // التحقق من قوة كلمة المرور الجديدة
            if (strlen($input['new_password']) < 6) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => 'كلمة المرور الجديدة يجب أن تكون 6 أحرف على الأقل',
                    'error_code' => 'WEAK_PASSWORD'
                ]);
                exit;
            }
            
            $updateFields[] = "password = ?";
            $updateValues[] = password_hash($input['new_password'], PASSWORD_ARGON2ID);
        }
        
        // تنفيذ التحديث
        $updateValues[] = $userId;
        $sql = "UPDATE users SET " . implode(', ', $updateFields) . " WHERE id = ?";
        
        $stmt = $pdo->prepare($sql);
        
        if ($stmt->execute($updateValues)) {
            // تسجيل النشاط
            $stmt = $pdo->prepare("
                INSERT INTO activity_logs (user_id, action, description, ip_address, created_at) 
                VALUES (?, 'update_profile', 'تحديث معلومات الملف الشخصي', ?, NOW())
            ");
            $stmt->execute([$userId, $_SERVER['REMOTE_ADDR']]);
            
            // الحصول على البيانات المحدثة
            $stmt = $pdo->prepare("
                SELECT id, username, email, full_name, phone, profile_picture, 
                       subscription_type, created_at, last_login, is_active
                FROM users 
                WHERE id = ?
            ");
            $stmt->execute([$userId]);
            $updatedUser = $stmt->fetch(PDO::FETCH_ASSOC);
            
            http_response_code(200);
            echo json_encode([
                'success' => true,
                'message' => 'تم تحديث معلومات الملف الشخصي بنجاح',
                'data' => [
                    'user' => [
                        'id' => intval($updatedUser['id']),
                        'username' => $updatedUser['username'],
                        'email' => $updatedUser['email'],
                        'full_name' => $updatedUser['full_name'],
                        'phone' => $updatedUser['phone'],
                        'profile_picture' => $updatedUser['profile_picture'],
                        'subscription_type' => $updatedUser['subscription_type'],
                        'created_at' => $updatedUser['created_at'],
                        'last_login' => $updatedUser['last_login'],
                        'is_active' => (bool)$updatedUser['is_active']
                    ]
                ]
            ], JSON_UNESCAPED_UNICODE);
        } else {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => 'فشل في تحديث معلومات الملف الشخصي',
                'error_code' => 'UPDATE_FAILED'
            ]);
        }
    }
    
} catch (PDOException $e) {
    error_log("Database error in profile API: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'خطأ في الخادم',
        'error_code' => 'DATABASE_ERROR'
    ]);
    
} catch (Exception $e) {
    error_log("General error in profile API: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'خطأ غير متوقع',
        'error_code' => 'GENERAL_ERROR'
    ]);
}
?>
