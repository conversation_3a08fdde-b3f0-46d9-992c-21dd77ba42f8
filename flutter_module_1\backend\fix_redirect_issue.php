<?php
/**
 * إصلاح مشكلة إعادة التوجيه اللا نهائية
 * Fix Infinite Redirect Issue
 */

try {
    echo "<h1>🔧 إصلاح مشكلة إعادة التوجيه</h1>";
    
    $fixedIssues = [];
    $errors = [];
    
    // 1. فحص ملفات index.php المتضاربة
    echo "<h2>📁 فحص الملفات المتضاربة...</h2>";
    
    $conflictingFiles = [
        'assets/images/homepage.php',
        'assets/images/index.html',
        'assets/homepage.php',
        'assets/index.php'
    ];
    
    foreach ($conflictingFiles as $file) {
        if (file_exists($file)) {
            // نسخ احتياطي ثم حذف
            $backupFile = $file . '.backup.' . date('Y-m-d-H-i-s');
            if (copy($file, $backupFile)) {
                unlink($file);
                $fixedIssues[] = "تم حذف الملف المتضارب: $file (نسخة احتياطية: $backupFile)";
            } else {
                $errors[] = "فشل في حذف الملف المتضارب: $file";
            }
        }
    }
    
    // 2. إنشاء مجلد images إذا لم يكن موجوداً
    if (!is_dir('assets/images')) {
        if (mkdir('assets/images', 0755, true)) {
            $fixedIssues[] = "تم إنشاء مجلد assets/images";
        } else {
            $errors[] = "فشل في إنشاء مجلد assets/images";
        }
    }
    
    // 3. إضافة صور تجريبية
    echo "<h2>🖼️ إضافة صور تجريبية...</h2>";
    
    $sampleImages = [
        'logo.svg' => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><text y=".9em" font-size="90">🎬</text></svg>',
        'placeholder.svg' => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 300 200"><rect width="300" height="200" fill="#333"/><text x="150" y="100" text-anchor="middle" fill="white" font-size="16">صورة تجريبية</text></svg>',
        'hero-bg.svg' => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1920 1080"><defs><linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:#E50914;stop-opacity:0.8" /><stop offset="100%" style="stop-color:#B8070F;stop-opacity:0.9" /></linearGradient></defs><rect width="1920" height="1080" fill="url(#grad)"/></svg>'
    ];
    
    foreach ($sampleImages as $filename => $content) {
        $filepath = "assets/images/$filename";
        if (!file_exists($filepath)) {
            if (file_put_contents($filepath, $content)) {
                $fixedIssues[] = "تم إنشاء صورة تجريبية: $filename";
            } else {
                $errors[] = "فشل في إنشاء صورة: $filename";
            }
        }
    }
    
    // 4. فحص وإصلاح ملف .htaccess الرئيسي
    echo "<h2>⚙️ فحص إعدادات Apache...</h2>";
    
    $htaccessContent = file_get_contents('.htaccess');
    if (strpos($htaccessContent, 'DirectoryIndex') === false) {
        $newHtaccess = "# Shahid Platform - Fixed Configuration\n";
        $newHtaccess .= "DirectoryIndex final_homepage.php homepage.php index_simple.php index.php\n\n";
        $newHtaccess .= $htaccessContent;
        
        if (file_put_contents('.htaccess', $newHtaccess)) {
            $fixedIssues[] = "تم تحديث ملف .htaccess الرئيسي";
        } else {
            $errors[] = "فشل في تحديث ملف .htaccess";
        }
    }
    
    // 5. فحص ملفات الصفحة الرئيسية
    echo "<h2>🏠 فحص ملفات الصفحة الرئيسية...</h2>";
    
    $homepageFiles = [
        'final_homepage.php' => 'الصفحة الرئيسية النهائية',
        'homepage.php' => 'الصفحة الرئيسية المتطورة',
        'index_simple.php' => 'الصفحة البسيطة',
        'index.php' => 'نقطة الدخول الرئيسية'
    ];
    
    $workingHomepage = null;
    foreach ($homepageFiles as $file => $description) {
        if (file_exists($file)) {
            $size = filesize($file);
            if ($size > 1000) { // ملف يحتوي على محتوى حقيقي
                $workingHomepage = $file;
                $fixedIssues[] = "تم العثور على صفحة رئيسية صالحة: $file ($description)";
                break;
            }
        }
    }
    
    // 6. إنشاء ملف index.php بسيط إذا لم يكن هناك صفحة رئيسية صالحة
    if (!$workingHomepage) {
        $simpleIndex = '<?php
/**
 * Shahid Platform - Simple Redirect
 */

// Check for available homepage files
$homepageFiles = [
    "final_homepage.php",
    "homepage.php", 
    "index_simple.php"
];

foreach ($homepageFiles as $file) {
    if (file_exists($file) && filesize($file) > 1000) {
        header("Location: $file");
        exit;
    }
}

// If no homepage found, show simple message
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>🎬 Shahid Platform</title>
    <style>
        body { font-family: Arial; background: #0f0f0f; color: #fff; text-align: center; padding: 2rem; }
        .container { max-width: 600px; margin: 0 auto; background: #2f2f2f; padding: 2rem; border-radius: 15px; }
        h1 { color: #E50914; }
        .btn { background: #E50914; color: white; padding: 1rem 2rem; text-decoration: none; border-radius: 8px; margin: 0.5rem; display: inline-block; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎬 Shahid Platform</h1>
        <p>منصة البث الاحترافية</p>
        <a href="admin/dashboard.php" class="btn">🎛️ لوحة الإدارة</a>
        <a href="api/test.php" class="btn">🔗 اختبار API</a>
    </div>
</body>
</html>';
        
        if (file_put_contents('index.php', $simpleIndex)) {
            $fixedIssues[] = "تم إنشاء ملف index.php بسيط";
        } else {
            $errors[] = "فشل في إنشاء ملف index.php";
        }
    }
    
    // 7. تنظيف ملفات الكاش
    echo "<h2>🧹 تنظيف ملفات الكاش...</h2>";
    
    $cacheFiles = glob('*.tmp');
    $cacheFiles = array_merge($cacheFiles, glob('cache/*'));
    $cacheFiles = array_merge($cacheFiles, glob('temp/*'));
    
    foreach ($cacheFiles as $cacheFile) {
        if (is_file($cacheFile) && unlink($cacheFile)) {
            $fixedIssues[] = "تم حذف ملف كاش: $cacheFile";
        }
    }
    
    echo "<br><h2>🎉 تم إصلاح مشكلة إعادة التوجيه!</h2>";
    
    if (!empty($fixedIssues)) {
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h3>✅ المشاكل التي تم إصلاحها:</h3>";
        echo "<ul>";
        foreach ($fixedIssues as $issue) {
            echo "<li>$issue</li>";
        }
        echo "</ul>";
        echo "</div>";
    }
    
    if (!empty($errors)) {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h3>⚠️ مشاكل لم يتم حلها:</h3>";
        echo "<ul>";
        foreach ($errors as $error) {
            echo "<li>$error</li>";
        }
        echo "</ul>";
        echo "</div>";
    }
    
    echo "<div style='background: #cce5ff; border: 1px solid #99ccff; color: #004085; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>📋 الخطوات التالية:</h3>";
    echo "<ol>";
    echo "<li>امسح كاش المتصفح (Ctrl+F5)</li>";
    echo "<li>أعد تشغيل Apache في XAMPP</li>";
    echo "<li>جرب الوصول للصفحة الرئيسية مرة أخرى</li>";
    echo "<li>إذا استمرت المشكلة، استخدم الرابط المباشر للصفحة</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<div style='text-align: center; margin: 2rem 0;'>";
    echo "<a href='final_homepage.php' style='background: #E50914; color: white; padding: 1rem 2rem; text-decoration: none; border-radius: 8px; font-weight: bold; margin: 0.5rem;'>🏠 الصفحة الرئيسية النهائية</a>";
    echo "<a href='homepage.php' style='background: #28a745; color: white; padding: 1rem 2rem; text-decoration: none; border-radius: 8px; font-weight: bold; margin: 0.5rem;'>🎬 الصفحة المتطورة</a>";
    echo "<a href='admin/dashboard.php' style='background: #007bff; color: white; padding: 1rem 2rem; text-decoration: none; border-radius: 8px; font-weight: bold; margin: 0.5rem;'>🎛️ لوحة الإدارة</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px;'>";
    echo "<h3>❌ خطأ في إصلاح النظام:</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح مشكلة إعادة التوجيه</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #2d2d2d 100%);
            color: #fff;
            min-height: 100vh;
            padding: 2rem;
        }
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: rgba(47, 47, 47, 0.95);
            border-radius: 20px;
            padding: 3rem;
            border: 2px solid rgba(229, 9, 20, 0.3);
        }
        h1, h2, h3 { color: #E50914; }
        ul { margin: 1rem 0; padding-right: 2rem; }
        li { margin: 0.5rem 0; }
        ol { margin: 1rem 0; padding-right: 2rem; }
    </style>
</head>
<body>
    <div class="container">
        <!-- المحتوى يتم عرضه من PHP أعلاه -->
    </div>
</body>
</html>
