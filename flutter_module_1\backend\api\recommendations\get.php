<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// معالجة طلبات OPTIONS
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => 'طريقة الطلب غير مدعومة',
        'error_code' => 'METHOD_NOT_ALLOWED'
    ]);
    exit;
}

try {
    // الاتصال بقاعدة البيانات
    $pdo = new PDO("mysql:host=localhost;dbname=shahid_platform;charset=utf8mb4", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // معاملات الطلب
    $type = isset($_GET['type']) ? $_GET['type'] : 'mixed'; // mixed, movie, series
    $limit = isset($_GET['limit']) ? min(max(1, intval($_GET['limit'])), 50) : 20;
    $algorithm = isset($_GET['algorithm']) ? $_GET['algorithm'] : 'hybrid'; // collaborative, content_based, hybrid, trending
    
    // التحقق من رمز الوصول (اختياري)
    $userId = null;
    $userSubscription = 'free';
    
    if (isset($_SERVER['HTTP_AUTHORIZATION'])) {
        $token = str_replace('Bearer ', '', $_SERVER['HTTP_AUTHORIZATION']);
        $payload = json_decode(base64_decode($token), true);
        
        if ($payload && isset($payload['user_id']) && $payload['expires_at'] > time()) {
            $userId = $payload['user_id'];
            $userSubscription = $payload['subscription_type'] ?? 'free';
        }
    }
    
    $recommendations = [];
    
    if ($userId) {
        // توصيات مخصصة للمستخدم المسجل
        $recommendations = getPersonalizedRecommendations($pdo, $userId, $type, $limit, $algorithm);
    } else {
        // توصيات عامة للمستخدمين غير المسجلين
        $recommendations = getGeneralRecommendations($pdo, $type, $limit);
    }
    
    // معالجة النتائج وإضافة معلومات إضافية
    foreach ($recommendations as &$item) {
        // تحديد إمكانية المشاهدة
        $item['can_watch'] = !($item['is_premium'] ?? false) || $userSubscription !== 'free';
        
        // إضافة معلومات المشاهدة للمستخدم المسجل
        if ($userId) {
            if ($item['content_type'] === 'movie') {
                $stmt = $pdo->prepare("
                    SELECT watch_time, completed 
                    FROM watch_history 
                    WHERE user_id = ? AND content_type = 'movie' AND content_id = ? 
                    ORDER BY watched_at DESC 
                    LIMIT 1
                ");
                $stmt->execute([$userId, $item['id']]);
                $watchHistory = $stmt->fetch(PDO::FETCH_ASSOC);
                
                $item['watch_progress'] = $watchHistory ? [
                    'watch_time' => intval($watchHistory['watch_time']),
                    'completed' => (bool)$watchHistory['completed'],
                    'progress_percentage' => isset($item['duration']) && $item['duration'] > 0 ? 
                        min(100, ($watchHistory['watch_time'] / ($item['duration'] * 60)) * 100) : 0
                ] : null;
            }
            
            // التحقق من المفضلة
            $stmt = $pdo->prepare("
                SELECT id FROM favorites 
                WHERE user_id = ? AND content_type = ? AND content_id = ?
            ");
            $stmt->execute([$userId, $item['content_type'], $item['id']]);
            $item['is_favorite'] = (bool)$stmt->fetch();
        }
        
        // تنسيق البيانات
        $item['rating'] = floatval($item['rating']);
        $item['views'] = intval($item['views']);
        $item['release_year'] = isset($item['release_date']) ? date('Y', strtotime($item['release_date'])) : null;
    }
    
    // إعداد الاستجابة
    $response = [
        'success' => true,
        'message' => 'تم جلب التوصيات بنجاح',
        'data' => [
            'recommendations' => $recommendations,
            'algorithm_used' => $algorithm,
            'personalized' => (bool)$userId,
            'total_count' => count($recommendations),
            'user_info' => $userId ? [
                'user_id' => $userId,
                'subscription_type' => $userSubscription,
                'can_access_premium' => $userSubscription !== 'free'
            ] : null
        ]
    ];
    
    http_response_code(200);
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    
} catch (PDOException $e) {
    error_log("Database error in recommendations API: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'خطأ في الخادم',
        'error_code' => 'DATABASE_ERROR'
    ]);
    
} catch (Exception $e) {
    error_log("General error in recommendations API: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'خطأ غير متوقع',
        'error_code' => 'GENERAL_ERROR'
    ]);
}

function getPersonalizedRecommendations($pdo, $userId, $type, $limit, $algorithm) {
    switch ($algorithm) {
        case 'collaborative':
            return getCollaborativeRecommendations($pdo, $userId, $type, $limit);
        case 'content_based':
            return getContentBasedRecommendations($pdo, $userId, $type, $limit);
        case 'trending':
            return getTrendingRecommendations($pdo, $type, $limit);
        default:
            return getHybridRecommendations($pdo, $userId, $type, $limit);
    }
}

function getCollaborativeRecommendations($pdo, $userId, $type, $limit) {
    // العثور على مستخدمين مشابهين بناءً على تاريخ المشاهدة
    $stmt = $pdo->prepare("
        SELECT DISTINCT wh2.content_id, wh2.content_type,
               CASE 
                   WHEN wh2.content_type = 'movie' THEN m.title
                   WHEN wh2.content_type = 'episode' THEN s.title
               END as title,
               CASE 
                   WHEN wh2.content_type = 'movie' THEN m.poster
                   WHEN wh2.content_type = 'episode' THEN s.poster
               END as poster,
               CASE 
                   WHEN wh2.content_type = 'movie' THEN m.rating
                   WHEN wh2.content_type = 'episode' THEN s.rating
               END as rating,
               CASE 
                   WHEN wh2.content_type = 'movie' THEN m.views
                   WHEN wh2.content_type = 'episode' THEN s.views
               END as views,
               COUNT(*) as similarity_score
        FROM watch_history wh1
        JOIN watch_history wh2 ON wh1.user_id != wh2.user_id 
            AND wh1.content_id = wh2.content_id 
            AND wh1.content_type = wh2.content_type
        LEFT JOIN movies m ON wh2.content_type = 'movie' AND wh2.content_id = m.id
        LEFT JOIN episodes e ON wh2.content_type = 'episode' AND wh2.content_id = e.id
        LEFT JOIN series s ON e.series_id = s.id
        WHERE wh1.user_id = ?
        AND wh2.content_id NOT IN (
            SELECT content_id FROM watch_history WHERE user_id = ?
        )
        " . ($type !== 'mixed' ? "AND wh2.content_type = ?" : "") . "
        GROUP BY wh2.content_id, wh2.content_type
        ORDER BY similarity_score DESC, rating DESC
        LIMIT ?
    ");
    
    $params = [$userId, $userId];
    if ($type !== 'mixed') {
        $params[] = $type === 'series' ? 'episode' : $type;
    }
    $params[] = $limit;
    
    $stmt->execute($params);
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

function getContentBasedRecommendations($pdo, $userId, $type, $limit) {
    // التوصيات بناءً على التصنيفات المفضلة للمستخدم
    $stmt = $pdo->prepare("
        SELECT DISTINCT 
            CASE 
                WHEN ? = 'movie' OR ? = 'mixed' THEN m.id
                WHEN ? = 'series' THEN s.id
            END as id,
            CASE 
                WHEN ? = 'movie' OR ? = 'mixed' THEN m.title
                WHEN ? = 'series' THEN s.title
            END as title,
            CASE 
                WHEN ? = 'movie' OR ? = 'mixed' THEN m.poster
                WHEN ? = 'series' THEN s.poster
            END as poster,
            CASE 
                WHEN ? = 'movie' OR ? = 'mixed' THEN m.rating
                WHEN ? = 'series' THEN s.rating
            END as rating,
            CASE 
                WHEN ? = 'movie' OR ? = 'mixed' THEN m.views
                WHEN ? = 'series' THEN s.views
            END as views,
            CASE 
                WHEN ? = 'movie' OR ? = 'mixed' THEN 'movie'
                WHEN ? = 'series' THEN 'series'
            END as content_type
        FROM (
            SELECT DISTINCT category_id 
            FROM favorites f
            LEFT JOIN movies m ON f.content_type = 'movie' AND f.content_id = m.id
            LEFT JOIN series s ON f.content_type = 'series' AND f.content_id = s.id
            WHERE f.user_id = ?
        ) user_categories
        LEFT JOIN movies m ON user_categories.category_id = m.category_id 
            AND m.status = 'active'
            AND (? = 'movie' OR ? = 'mixed')
        LEFT JOIN series s ON user_categories.category_id = s.category_id 
            AND s.status = 'active'
            AND (? = 'series' OR ? = 'mixed')
        WHERE (m.id IS NOT NULL OR s.id IS NOT NULL)
        AND (
            (m.id IS NOT NULL AND m.id NOT IN (
                SELECT content_id FROM favorites WHERE user_id = ? AND content_type = 'movie'
            ))
            OR
            (s.id IS NOT NULL AND s.id NOT IN (
                SELECT content_id FROM favorites WHERE user_id = ? AND content_type = 'series'
            ))
        )
        ORDER BY rating DESC, views DESC
        LIMIT ?
    ");
    
    $params = array_fill(0, 16, $type);
    $params[] = $userId;
    $params[] = $type;
    $params[] = $type;
    $params[] = $type;
    $params[] = $type;
    $params[] = $userId;
    $params[] = $userId;
    $params[] = $limit;
    
    $stmt->execute($params);
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

function getTrendingRecommendations($pdo, $type, $limit) {
    // المحتوى الأكثر شعبية في الأسبوع الماضي
    $query = "
        SELECT id, title, poster, rating, views, 'movie' as content_type
        FROM movies 
        WHERE status = 'active' 
        AND created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
        " . ($type === 'movie' ? "" : ($type === 'series' ? "AND 1=0" : "")) . "
        
        UNION ALL
        
        SELECT id, title, poster, rating, views, 'series' as content_type
        FROM series 
        WHERE status = 'active' 
        AND created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
        " . ($type === 'series' ? "" : ($type === 'movie' ? "AND 1=0" : "")) . "
        
        ORDER BY views DESC, rating DESC
        LIMIT ?
    ";
    
    $stmt = $pdo->prepare($query);
    $stmt->execute([$limit]);
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

function getHybridRecommendations($pdo, $userId, $type, $limit) {
    // دمج عدة خوارزميات
    $collaborative = getCollaborativeRecommendations($pdo, $userId, $type, $limit / 2);
    $contentBased = getContentBasedRecommendations($pdo, $userId, $type, $limit / 2);
    $trending = getTrendingRecommendations($pdo, $type, $limit / 4);
    
    // دمج النتائج وإزالة المكررات
    $combined = array_merge($collaborative, $contentBased, $trending);
    $unique = [];
    $seen = [];
    
    foreach ($combined as $item) {
        $key = $item['content_type'] . '_' . $item['id'];
        if (!isset($seen[$key])) {
            $seen[$key] = true;
            $unique[] = $item;
        }
    }
    
    return array_slice($unique, 0, $limit);
}

function getGeneralRecommendations($pdo, $type, $limit) {
    // توصيات عامة للمستخدمين غير المسجلين
    $query = "
        SELECT id, title, poster, rating, views, 'movie' as content_type, 
               CASE WHEN rating >= 8.0 THEN 1 ELSE 0 END as is_premium
        FROM movies 
        WHERE status = 'active'
        " . ($type === 'movie' ? "" : ($type === 'series' ? "AND 1=0" : "")) . "
        
        UNION ALL
        
        SELECT id, title, poster, rating, views, 'series' as content_type, 1 as is_premium
        FROM series 
        WHERE status = 'active'
        " . ($type === 'series' ? "" : ($type === 'movie' ? "AND 1=0" : "")) . "
        
        ORDER BY rating DESC, views DESC
        LIMIT ?
    ";
    
    $stmt = $pdo->prepare($query);
    $stmt->execute([$limit]);
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}
?>
