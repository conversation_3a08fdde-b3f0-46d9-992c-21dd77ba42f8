<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shahid Platform - منصة البث الاحترافية</title>
    <link rel="icon" href="assets/images/favicon.ico" type="image/x-icon">
    <style>
        :root {
            --primary-color: #E50914;
            --secondary-color: #B8070F;
            --dark-bg: #0f0f0f;
            --card-bg: rgba(47, 47, 47, 0.9);
            --text-primary: #ffffff;
            --text-secondary: #cccccc;
            --success-color: #4CAF50;
            --warning-color: #FF9800;
            --error-color: #F44336;
            --info-color: #2196F3;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', 'Cairo', 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, var(--dark-bg) 0%, #1a1a1a 50%, #2d2d2d 100%);
            color: var(--text-primary);
            line-height: 1.6;
            overflow-x: hidden;
        }

        /* Header Styles */
        .header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            padding: 3rem 0;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }

        .header-content {
            position: relative;
            z-index: 2;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        .logo {
            font-size: 4rem;
            font-weight: 900;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
            letter-spacing: -2px;
        }

        .tagline {
            font-size: 1.5rem;
            margin-bottom: 2rem;
            opacity: 0.95;
            font-weight: 300;
        }

        .header-stats {
            display: flex;
            justify-content: center;
            gap: 3rem;
            margin-top: 2rem;
            flex-wrap: wrap;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            display: block;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
        }

        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        /* Navigation */
        .nav-container {
            background: rgba(0, 0, 0, 0.9);
            backdrop-filter: blur(10px);
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 100;
            border-bottom: 1px solid rgba(229, 9, 20, 0.3);
        }

        .nav-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .nav-logo {
            font-size: 1.8rem;
            font-weight: bold;
            color: var(--primary-color);
        }

        .nav-links {
            display: flex;
            gap: 2rem;
            list-style: none;
        }

        .nav-links a {
            color: var(--text-secondary);
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
            padding: 0.5rem 1rem;
            border-radius: 5px;
        }

        .nav-links a:hover {
            color: var(--primary-color);
            background: rgba(229, 9, 20, 0.1);
        }

        /* Main Container */
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 3rem 2rem;
        }

        /* System Status */
        .system-status {
            background: var(--card-bg);
            border-radius: 20px;
            padding: 2.5rem;
            margin-bottom: 3rem;
            border: 1px solid rgba(229, 9, 20, 0.2);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .status-header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .status-title {
            font-size: 2rem;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }

        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
        }

        .status-card {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 15px;
            padding: 1.5rem;
            border-left: 4px solid var(--info-color);
            transition: all 0.3s ease;
        }

        .status-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.4);
        }

        .status-card.success {
            border-left-color: var(--success-color);
            background: rgba(76, 175, 80, 0.1);
        }

        .status-card.error {
            border-left-color: var(--error-color);
            background: rgba(244, 67, 54, 0.1);
        }

        .status-card.warning {
            border-left-color: var(--warning-color);
            background: rgba(255, 152, 0, 0.1);
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .status-label {
            font-weight: 600;
            color: var(--text-secondary);
        }

        .status-value {
            font-weight: bold;
            padding: 0.3rem 0.8rem;
            border-radius: 15px;
            font-size: 0.9rem;
        }

        .status-value.success {
            background: rgba(76, 175, 80, 0.2);
            color: var(--success-color);
        }

        .status-value.error {
            background: rgba(244, 67, 54, 0.2);
            color: var(--error-color);
        }

        /* Quick Actions */
        .quick-actions {
            margin-bottom: 3rem;
        }

        .section-title {
            font-size: 2.5rem;
            text-align: center;
            margin-bottom: 2rem;
            color: var(--primary-color);
            font-weight: 700;
        }

        .actions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }

        .action-card {
            background: var(--card-bg);
            border-radius: 20px;
            padding: 2rem;
            text-align: center;
            border: 1px solid rgba(229, 9, 20, 0.2);
            transition: all 0.3s ease;
            text-decoration: none;
            color: inherit;
            position: relative;
            overflow: hidden;
        }

        .action-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
            transition: left 0.5s ease;
        }

        .action-card:hover::before {
            left: 100%;
        }

        .action-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(229, 9, 20, 0.3);
            color: inherit;
        }

        .action-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            display: block;
        }

        .action-title {
            font-size: 1.3rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
            color: var(--primary-color);
        }

        .action-description {
            color: var(--text-secondary);
            font-size: 0.95rem;
            line-height: 1.5;
        }

        /* Features Section */
        .features-section {
            margin-bottom: 3rem;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
        }

        .feature-card {
            background: var(--card-bg);
            border-radius: 20px;
            padding: 2.5rem;
            border: 1px solid rgba(229, 9, 20, 0.2);
            transition: all 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.4);
        }

        .feature-header {
            display: flex;
            align-items: center;
            margin-bottom: 1.5rem;
        }

        .feature-icon {
            font-size: 2.5rem;
            margin-left: 1rem;
            color: var(--primary-color);
        }

        .feature-title {
            font-size: 1.5rem;
            font-weight: bold;
            color: var(--primary-color);
        }

        .feature-list {
            list-style: none;
        }

        .feature-list li {
            padding: 0.5rem 0;
            display: flex;
            align-items: center;
            color: var(--text-secondary);
        }

        .feature-list li::before {
            content: '✅';
            margin-left: 0.5rem;
            font-size: 0.9rem;
        }

        /* Footer */
        .footer {
            background: rgba(0, 0, 0, 0.8);
            padding: 3rem 0;
            margin-top: 4rem;
            border-top: 1px solid rgba(229, 9, 20, 0.3);
        }

        .footer-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
            text-align: center;
        }

        .footer-logo {
            font-size: 2rem;
            font-weight: bold;
            color: var(--primary-color);
            margin-bottom: 1rem;
        }

        .footer-text {
            color: var(--text-secondary);
            margin-bottom: 2rem;
        }

        .footer-links {
            display: flex;
            justify-content: center;
            gap: 2rem;
            flex-wrap: wrap;
        }

        .footer-links a {
            color: var(--text-secondary);
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .footer-links a:hover {
            color: var(--primary-color);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .logo {
                font-size: 2.5rem;
            }

            .tagline {
                font-size: 1.2rem;
            }

            .header-stats {
                gap: 1.5rem;
            }

            .stat-number {
                font-size: 2rem;
            }

            .nav-content {
                flex-direction: column;
                gap: 1rem;
            }

            .nav-links {
                gap: 1rem;
            }

            .container {
                padding: 2rem 1rem;
            }

            .section-title {
                font-size: 2rem;
            }

            .actions-grid,
            .features-grid {
                grid-template-columns: 1fr;
            }

            .status-grid {
                grid-template-columns: 1fr;
            }
        }

        /* Loading Animation */
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: var(--primary-color);
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* Pulse Animation */
        .pulse {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <h1 class="logo">🎬 SHAHID</h1>
            <p class="tagline">منصة البث الاحترافية الشاملة</p>
            <div class="header-stats">
                <div class="stat-item">
                    <span class="stat-number">1000+</span>
                    <span class="stat-label">ساعة محتوى</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">50+</span>
                    <span class="stat-label">فيلم ومسلسل</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">24/7</span>
                    <span class="stat-label">خدمة مستمرة</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">4K</span>
                    <span class="stat-label">جودة عالية</span>
                </div>
            </div>
        </div>
    </header>

    <!-- Navigation -->
    <nav class="nav-container">
        <div class="nav-content">
            <div class="nav-logo">SHAHID</div>
            <ul class="nav-links">
                <li><a href="#home">الرئيسية</a></li>
                <li><a href="#features">الميزات</a></li>
                <li><a href="#system">حالة النظام</a></li>
                <li><a href="#admin">الإدارة</a></li>
                <li><a href="#api">API</a></li>
            </ul>
        </div>
    </nav>

    <!-- Main Container -->
    <div class="container">
        <!-- System Status Section -->
        <section id="system" class="system-status">
            <div class="status-header">
                <h2 class="status-title">🔍 حالة النظام المباشرة</h2>
                <p>مراقبة مستمرة لجميع مكونات المنصة</p>
            </div>
            <div class="status-grid" id="systemStatusGrid">
                <div class="status-card" id="dbCard">
                    <div class="status-item">
                        <span class="status-label">قاعدة البيانات</span>
                        <span class="status-value" id="dbStatus">
                            <span class="loading"></span> فحص...
                        </span>
                    </div>
                </div>
                <div class="status-card" id="tablesCard">
                    <div class="status-item">
                        <span class="status-label">الجداول</span>
                        <span class="status-value" id="tablesStatus">
                            <span class="loading"></span> فحص...
                        </span>
                    </div>
                </div>
                <div class="status-card" id="adminCard">
                    <div class="status-item">
                        <span class="status-label">حساب المدير</span>
                        <span class="status-value" id="adminStatus">
                            <span class="loading"></span> فحص...
                        </span>
                    </div>
                </div>
                <div class="status-card" id="apiCard">
                    <div class="status-item">
                        <span class="status-label">API</span>
                        <span class="status-value" id="apiStatus">
                            <span class="loading"></span> فحص...
                        </span>
                    </div>
                </div>
            </div>
        </section>

        <!-- Quick Actions Section -->
        <section class="quick-actions">
            <h2 class="section-title">⚡ إجراءات سريعة</h2>
            <div class="actions-grid">
                <a href="instant_fix.php" class="action-card pulse" style="border-color: var(--success-color);">
                    <span class="action-icon">⚡</span>
                    <h3 class="action-title">الإصلاح الفوري</h3>
                    <p class="action-description">إصلاح سريع لجميع مشاكل النظام في 30 ثانية</p>
                </a>

                <a href="admin/dashboard.php" class="action-card" style="border-color: var(--primary-color);">
                    <span class="action-icon">🎛️</span>
                    <h3 class="action-title">لوحة الإدارة</h3>
                    <p class="action-description">إدارة شاملة للمحتوى والمستخدمين والإعدادات</p>
                </a>

                <a href="api/test.php" class="action-card" style="border-color: var(--info-color);">
                    <span class="action-icon">🔗</span>
                    <h3 class="action-title">اختبار API</h3>
                    <p class="action-description">اختبار شامل لجميع endpoints والوظائف</p>
                </a>

                <a href="streaming/video_player.php?id=1&type=movie" class="action-card" style="border-color: var(--warning-color);">
                    <span class="action-icon">🎥</span>
                    <h3 class="action-title">مشغل الفيديو</h3>
                    <p class="action-description">مشغل فيديو متقدم مع جميع الميزات الاحترافية</p>
                </a>

                <a href="subscription/plans.php" class="action-card" style="border-color: #9C27B0;">
                    <span class="action-icon">💎</span>
                    <h3 class="action-title">خطط الاشتراك</h3>
                    <p class="action-description">خطط اشتراك متنوعة مع نظام دفع متكامل</p>
                </a>

                <a href="system_status.php" class="action-card" style="border-color: var(--info-color);">
                    <span class="action-icon">📊</span>
                    <h3 class="action-title">حالة النظام</h3>
                    <p class="action-description">مراقبة مفصلة لأداء النظام والإحصائيات</p>
                </a>
            </div>
        </section>
        <!-- Features Section -->
        <section id="features" class="features-section">
            <h2 class="section-title">🌟 الميزات الرئيسية</h2>
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-header">
                        <span class="feature-icon">🎬</span>
                        <h3 class="feature-title">إدارة المحتوى</h3>
                    </div>
                    <ul class="feature-list">
                        <li>رفع وإدارة الأفلام والمسلسلات</li>
                        <li>دعم جودات متعددة (4K, HD, SD)</li>
                        <li>نظام تصنيفات وتقييمات</li>
                        <li>ترجمات وصوتيات متعددة</li>
                        <li>معاينات وإعلانات تشويقية</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <div class="feature-header">
                        <span class="feature-icon">👥</span>
                        <h3 class="feature-title">إدارة المستخدمين</h3>
                    </div>
                    <ul class="feature-list">
                        <li>تسجيل دخول آمن مع JWT</li>
                        <li>ملفات شخصية متعددة</li>
                        <li>قوائم المفضلة والمشاهدة لاحقاً</li>
                        <li>تتبع تقدم المشاهدة</li>
                        <li>توصيات ذكية</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <div class="feature-header">
                        <span class="feature-icon">💳</span>
                        <h3 class="feature-title">نظام الاشتراكات</h3>
                    </div>
                    <ul class="feature-list">
                        <li>خطط اشتراك متعددة</li>
                        <li>دفع آمن مع Stripe/PayPal</li>
                        <li>فواتير وإيصالات تلقائية</li>
                        <li>إدارة الاشتراكات والتجديد</li>
                        <li>عروض وخصومات</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <div class="feature-header">
                        <span class="feature-icon">🎥</span>
                        <h3 class="feature-title">مشغل الفيديو</h3>
                    </div>
                    <ul class="feature-list">
                        <li>بث متكيف مع جودة الإنترنت</li>
                        <li>تحكم كامل في التشغيل</li>
                        <li>دعم الترجمات المدمجة</li>
                        <li>مشاهدة بدون اتصال</li>
                        <li>Chromecast و AirPlay</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <div class="feature-header">
                        <span class="feature-icon">📊</span>
                        <h3 class="feature-title">التحليلات</h3>
                    </div>
                    <ul class="feature-list">
                        <li>إحصائيات المشاهدة المفصلة</li>
                        <li>تحليل سلوك المستخدمين</li>
                        <li>تقارير الإيرادات</li>
                        <li>مراقبة الأداء المباشرة</li>
                        <li>رسوم بيانية تفاعلية</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <div class="feature-header">
                        <span class="feature-icon">🔒</span>
                        <h3 class="feature-title">الأمان</h3>
                    </div>
                    <ul class="feature-list">
                        <li>تشفير البيانات الحساسة</li>
                        <li>حماية من هجمات CSRF/XSS</li>
                        <li>Rate limiting متقدم</li>
                        <li>مراقبة الأنشطة المشبوهة</li>
                        <li>نسخ احتياطية تلقائية</li>
                    </ul>
                </div>
            </div>
        </section>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="footer-content">
            <div class="footer-logo">🎬 SHAHID PLATFORM</div>
            <p class="footer-text">منصة البث الاحترافية الشاملة - مطورة بأحدث التقنيات</p>
            <div class="footer-links">
                <a href="#home">الرئيسية</a>
                <a href="#features">الميزات</a>
                <a href="admin/dashboard.php">لوحة الإدارة</a>
                <a href="api/test.php">API</a>
                <a href="system_status.php">حالة النظام</a>
                <a href="subscription/plans.php">الاشتراكات</a>
            </div>
            <p style="margin-top: 2rem; color: var(--text-secondary); font-size: 0.9rem;">
                © 2024 Shahid Platform. جميع الحقوق محفوظة. مطور بـ ❤️ للمجتمع العربي
            </p>
        </div>
    </footer>

    <!-- JavaScript -->
    <script>
        // System Status Check
        async function checkSystemStatus() {
            try {
                const response = await fetch('system_status.php?status=1');
                const data = await response.json();

                updateStatus('db', data.database_connected, 'قاعدة البيانات');
                updateStatus('tables', data.tables_exist, 'الجداول');
                updateStatus('admin', data.admin_exists, 'حساب المدير');
                updateStatus('api', true, 'API');

                console.log('✅ System status updated:', data);
            } catch (error) {
                console.error('❌ Error checking system status:', error);
                ['db', 'tables', 'admin', 'api'].forEach(key => {
                    updateStatus(key, false, getStatusLabel(key));
                });
            }
        }

        function updateStatus(type, isOk, label) {
            const cardElement = document.getElementById(type + 'Card');
            const statusElement = document.getElementById(type + 'Status');

            if (cardElement && statusElement) {
                cardElement.className = 'status-card ' + (isOk ? 'success' : 'error');
                statusElement.className = 'status-value ' + (isOk ? 'success' : 'error');
                statusElement.innerHTML = isOk ? '✅ يعمل بشكل طبيعي' : '❌ يحتاج إصلاح';
            }
        }

        function getStatusLabel(type) {
            const labels = {
                db: 'قاعدة البيانات',
                tables: 'الجداول',
                admin: 'حساب المدير',
                api: 'API'
            };
            return labels[type] || type;
        }

        // Smooth scrolling
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({ behavior: 'smooth', block: 'start' });
                }
            });
        });

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🎬 Shahid Platform Final Homepage loaded successfully!');
            checkSystemStatus();
            setInterval(checkSystemStatus, 30000);

            document.querySelectorAll('.action-card').forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-10px) scale(1.02)';
                });
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });
        });

        window.addEventListener('load', function() {
            const loadTime = performance.now();
            console.log(`⚡ Page loaded in ${Math.round(loadTime)}ms`);
        });
    </script>
</body>
</html>
