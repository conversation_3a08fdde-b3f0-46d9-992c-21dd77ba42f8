<?php
session_start();

// تضمين الملفات المطلوبة
require_once 'includes/Security.php';
require_once 'includes/SettingsManager.php';
require_once 'includes/MenuManager.php';

try {
    $pdo = new PDO("mysql:host=localhost;dbname=shahid_platform;charset=utf8mb4", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $security = new Security($pdo);
    $settingsManager = new SettingsManager($pdo, $security);
    $menuManager = new MenuManager($pdo, $security);
    
    // الحصول على الإعدادات العامة
    $settings = $settingsManager->getPublicSettings();
    
    // الحصول على القائمة الرئيسية
    $mainMenu = $menuManager->getMainMenu();
    
    // الحصول على المحتوى المميز
    $featuredMovies = $pdo->query("
        SELECT * FROM movies 
        WHERE status = 'active' 
        ORDER BY rating DESC, views DESC 
        LIMIT 6
    ")->fetchAll(PDO::FETCH_ASSOC);
    
    $featuredSeries = $pdo->query("
        SELECT * FROM series 
        WHERE status = 'active' 
        ORDER BY rating DESC, views DESC 
        LIMIT 6
    ")->fetchAll(PDO::FETCH_ASSOC);
    
    // الحصول على التصنيفات
    $categories = $pdo->query("
        SELECT * FROM categories 
        WHERE status = 'active' 
        ORDER BY sort_order ASC, name ASC
    ")->fetchAll(PDO::FETCH_ASSOC);
    
    // الحصول على المحتوى الأحدث
    $latestContent = $pdo->query("
        (SELECT 'movie' as type, id, title, poster, release_year, rating, views, created_at FROM movies WHERE status = 'active' ORDER BY created_at DESC LIMIT 8)
        UNION ALL
        (SELECT 'series' as type, id, title, poster, release_year, rating, views, created_at FROM series WHERE status = 'active' ORDER BY created_at DESC LIMIT 8)
        ORDER BY created_at DESC
        LIMIT 12
    ")->fetchAll(PDO::FETCH_ASSOC);
    
} catch (Exception $e) {
    error_log("Database error: " . $e->getMessage());
    $settings = [];
    $mainMenu = [];
    $featuredMovies = [];
    $featuredSeries = [];
    $categories = [];
    $latestContent = [];
}

// الحصول على الإعدادات مع القيم الافتراضية
$siteName = $settings['site_name'] ?? 'Shahid Platform';
$siteDescription = $settings['site_description'] ?? 'منصة مشاهدة الأفلام والمسلسلات';
$primaryColor = $settings['primary_color'] ?? '#E50914';
$secondaryColor = $settings['secondary_color'] ?? '#FF6B35';
$siteLogo = $settings['site_logo'] ?? '/assets/images/logo.png';
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($siteName); ?> - <?php echo htmlspecialchars($siteDescription); ?></title>
    <meta name="description" content="<?php echo htmlspecialchars($siteDescription); ?>">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: <?php echo $primaryColor; ?>;
            --secondary-color: <?php echo $secondaryColor; ?>;
            --dark-bg: #0f0f0f;
            --card-bg: rgba(47, 47, 47, 0.8);
            --text-primary: #fff;
            --text-secondary: #ccc;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', 'Cairo', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, var(--dark-bg) 0%, #1a1a1a 50%, #2d2d2d 100%);
            color: var(--text-primary);
            min-height: 100vh;
        }
        
        .header {
            background: rgba(47, 47, 47, 0.95);
            padding: 1rem 0;
            border-bottom: 2px solid rgba(229, 9, 20, 0.3);
            position: sticky;
            top: 0;
            z-index: 1000;
            backdrop-filter: blur(10px);
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }
        
        .nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }
        
        .logo {
            color: var(--primary-color);
            font-size: 1.8rem;
            font-weight: bold;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .nav-menu {
            display: flex;
            list-style: none;
            gap: 2rem;
            align-items: center;
        }
        
        .nav-item {
            position: relative;
        }
        
        .nav-link {
            color: var(--text-primary);
            text-decoration: none;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .nav-link:hover {
            background: rgba(229, 9, 20, 0.2);
            color: var(--primary-color);
        }
        
        .dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            background: var(--card-bg);
            border-radius: 10px;
            padding: 1rem;
            min-width: 200px;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.3s ease;
            border: 1px solid rgba(229, 9, 20, 0.2);
            backdrop-filter: blur(10px);
        }
        
        .nav-item:hover .dropdown {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }
        
        .dropdown-item {
            display: block;
            color: var(--text-secondary);
            text-decoration: none;
            padding: 0.75rem;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .dropdown-item:hover {
            background: rgba(229, 9, 20, 0.1);
            color: var(--primary-color);
        }
        
        .user-menu {
            display: flex;
            gap: 1rem;
            align-items: center;
        }
        
        .btn {
            padding: 0.75rem 1.5rem;
            background: linear-gradient(45deg, var(--primary-color), #B8070F);
            color: white;
            border: none;
            border-radius: 8px;
            text-decoration: none;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(229, 9, 20, 0.3);
        }
        
        .btn-outline {
            background: transparent;
            border: 2px solid var(--primary-color);
            color: var(--primary-color);
        }
        
        .btn-outline:hover {
            background: var(--primary-color);
            color: white;
        }
        
        .hero {
            background: linear-gradient(rgba(0,0,0,0.6), rgba(0,0,0,0.6)), url('assets/images/hero-bg.jpg');
            background-size: cover;
            background-position: center;
            padding: 6rem 0;
            text-align: center;
        }
        
        .hero h1 {
            font-size: 3.5rem;
            margin-bottom: 1rem;
            background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .hero p {
            font-size: 1.3rem;
            color: var(--text-secondary);
            margin-bottom: 2rem;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }
        
        .hero-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .section {
            padding: 4rem 0;
        }
        
        .section-header {
            text-align: center;
            margin-bottom: 3rem;
        }
        
        .section-title {
            font-size: 2.5rem;
            color: var(--primary-color);
            margin-bottom: 1rem;
        }
        
        .section-subtitle {
            font-size: 1.1rem;
            color: var(--text-secondary);
        }
        
        .content-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }
        
        .content-card {
            background: var(--card-bg);
            border-radius: 15px;
            overflow: hidden;
            transition: all 0.3s ease;
            border: 2px solid rgba(229, 9, 20, 0.1);
            position: relative;
        }
        
        .content-card:hover {
            transform: translateY(-10px);
            border-color: rgba(229, 9, 20, 0.3);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }
        
        .card-image {
            width: 100%;
            height: 200px;
            background: linear-gradient(45deg, #333, #555);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            color: #666;
            position: relative;
            overflow: hidden;
        }
        
        .card-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .card-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .content-card:hover .card-overlay {
            opacity: 1;
        }
        
        .play-btn {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .play-btn:hover {
            transform: scale(1.1);
        }
        
        .card-content {
            padding: 1.5rem;
        }
        
        .card-title {
            font-size: 1.2rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
            color: var(--text-primary);
        }
        
        .card-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: var(--text-secondary);
            font-size: 0.9rem;
            margin-bottom: 1rem;
        }
        
        .card-rating {
            display: flex;
            align-items: center;
            gap: 0.25rem;
            color: #FFD700;
        }
        
        .card-description {
            color: var(--text-secondary);
            line-height: 1.6;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        
        .categories-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
        }
        
        .category-card {
            background: var(--card-bg);
            padding: 2rem;
            border-radius: 15px;
            text-align: center;
            transition: all 0.3s ease;
            border: 2px solid rgba(229, 9, 20, 0.1);
            cursor: pointer;
        }
        
        .category-card:hover {
            transform: translateY(-5px);
            border-color: rgba(229, 9, 20, 0.3);
        }
        
        .category-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            color: var(--primary-color);
        }
        
        .category-name {
            font-size: 1.2rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        
        .category-count {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }
        
        .footer {
            background: rgba(47, 47, 47, 0.95);
            padding: 3rem 0 1rem;
            border-top: 2px solid rgba(229, 9, 20, 0.3);
            margin-top: 4rem;
        }
        
        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }
        
        .footer-section h3 {
            color: var(--primary-color);
            margin-bottom: 1rem;
        }
        
        .footer-section p,
        .footer-section a {
            color: var(--text-secondary);
            text-decoration: none;
            line-height: 1.6;
        }
        
        .footer-section a:hover {
            color: var(--primary-color);
        }
        
        .footer-bottom {
            text-align: center;
            padding-top: 2rem;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            color: var(--text-secondary);
        }
        
        @media (max-width: 768px) {
            .nav {
                flex-direction: column;
            }
            
            .nav-menu {
                flex-direction: column;
                gap: 1rem;
            }
            
            .hero h1 {
                font-size: 2.5rem;
            }
            
            .hero-actions {
                flex-direction: column;
                align-items: center;
            }
            
            .content-grid {
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            }
            
            .categories-grid {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="container">
            <nav class="nav">
                <a href="dynamic_index.php" class="logo">
                    <i class="fas fa-play-circle"></i>
                    <?php echo htmlspecialchars($siteName); ?>
                </a>
                
                <ul class="nav-menu">
                    <?php foreach ($mainMenu as $menuItem): ?>
                        <li class="nav-item">
                            <a href="<?php echo htmlspecialchars($menuItem['url'] ?? '#'); ?>" class="nav-link">
                                <?php if (!empty($menuItem['icon'])): ?>
                                    <i class="<?php echo htmlspecialchars($menuItem['icon']); ?>"></i>
                                <?php endif; ?>
                                <?php echo htmlspecialchars($menuItem['name']); ?>
                                <?php if ($menuItem['has_children']): ?>
                                    <i class="fas fa-chevron-down"></i>
                                <?php endif; ?>
                            </a>
                            
                            <?php if ($menuItem['has_children']): ?>
                                <div class="dropdown">
                                    <?php foreach ($menuItem['children'] as $child): ?>
                                        <a href="<?php echo htmlspecialchars($child['url'] ?? '#'); ?>" class="dropdown-item">
                                            <?php if (!empty($child['icon'])): ?>
                                                <i class="<?php echo htmlspecialchars($child['icon']); ?>"></i>
                                            <?php endif; ?>
                                            <?php echo htmlspecialchars($child['name']); ?>
                                        </a>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
                        </li>
                    <?php endforeach; ?>
                </ul>
                
                <div class="user-menu">
                    <?php if (isset($_SESSION['user_id'])): ?>
                        <a href="profile.php" class="btn btn-outline">
                            <i class="fas fa-user"></i>
                            الملف الشخصي
                        </a>
                        <a href="logout.php" class="btn">
                            <i class="fas fa-sign-out-alt"></i>
                            تسجيل الخروج
                        </a>
                    <?php else: ?>
                        <a href="login.php" class="btn btn-outline">
                            <i class="fas fa-sign-in-alt"></i>
                            تسجيل الدخول
                        </a>
                        <a href="register.php" class="btn">
                            <i class="fas fa-user-plus"></i>
                            إنشاء حساب
                        </a>
                    <?php endif; ?>
                </div>
            </nav>
        </div>
    </header>
    
    <section class="hero">
        <div class="container">
            <h1>مرحباً بك في <?php echo htmlspecialchars($siteName); ?></h1>
            <p><?php echo htmlspecialchars($siteDescription); ?></p>
            <div class="hero-actions">
                <a href="movies.php" class="btn">
                    <i class="fas fa-film"></i>
                    تصفح الأفلام
                </a>
                <a href="series.php" class="btn btn-outline">
                    <i class="fas fa-tv"></i>
                    تصفح المسلسلات
                </a>
            </div>
        </div>
    </section>
    
    <?php if (!empty($featuredMovies)): ?>
    <section class="section">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">الأفلام المميزة</h2>
                <p class="section-subtitle">أحدث وأفضل الأفلام المتاحة على المنصة</p>
            </div>
            
            <div class="content-grid">
                <?php foreach ($featuredMovies as $movie): ?>
                    <div class="content-card">
                        <div class="card-image">
                            <?php if (!empty($movie['poster'])): ?>
                                <img src="<?php echo htmlspecialchars($movie['poster']); ?>" 
                                     alt="<?php echo htmlspecialchars($movie['title']); ?>">
                            <?php else: ?>
                                🎬
                            <?php endif; ?>
                            <div class="card-overlay">
                                <div class="play-btn">
                                    <i class="fas fa-play"></i>
                                </div>
                            </div>
                        </div>
                        <div class="card-content">
                            <h3 class="card-title"><?php echo htmlspecialchars($movie['title']); ?></h3>
                            <div class="card-meta">
                                <span><?php echo $movie['release_year'] ?? 'غير محدد'; ?></span>
                                <div class="card-rating">
                                    <i class="fas fa-star"></i>
                                    <?php echo number_format($movie['rating'] ?? 0, 1); ?>
                                </div>
                            </div>
                            <p class="card-description">
                                <?php echo htmlspecialchars($movie['description'] ?? 'لا يوجد وصف متاح'); ?>
                            </p>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </section>
    <?php endif; ?>
    
    <?php if (!empty($featuredSeries)): ?>
    <section class="section">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">المسلسلات المميزة</h2>
                <p class="section-subtitle">أحدث وأفضل المسلسلات المتاحة على المنصة</p>
            </div>
            
            <div class="content-grid">
                <?php foreach ($featuredSeries as $series): ?>
                    <div class="content-card">
                        <div class="card-image">
                            <?php if (!empty($series['poster'])): ?>
                                <img src="<?php echo htmlspecialchars($series['poster']); ?>" 
                                     alt="<?php echo htmlspecialchars($series['title']); ?>">
                            <?php else: ?>
                                📺
                            <?php endif; ?>
                            <div class="card-overlay">
                                <div class="play-btn">
                                    <i class="fas fa-play"></i>
                                </div>
                            </div>
                        </div>
                        <div class="card-content">
                            <h3 class="card-title"><?php echo htmlspecialchars($series['title']); ?></h3>
                            <div class="card-meta">
                                <span><?php echo $series['release_year'] ?? 'غير محدد'; ?></span>
                                <div class="card-rating">
                                    <i class="fas fa-star"></i>
                                    <?php echo number_format($series['rating'] ?? 0, 1); ?>
                                </div>
                            </div>
                            <p class="card-description">
                                <?php echo htmlspecialchars($series['description'] ?? 'لا يوجد وصف متاح'); ?>
                            </p>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </section>
    <?php endif; ?>
    
    <?php if (!empty($categories)): ?>
    <section class="section">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">التصنيفات</h2>
                <p class="section-subtitle">تصفح المحتوى حسب التصنيف</p>
            </div>
            
            <div class="categories-grid">
                <?php foreach ($categories as $category): ?>
                    <div class="category-card">
                        <div class="category-icon">
                            <?php echo $category['icon'] ?? '📂'; ?>
                        </div>
                        <h3 class="category-name"><?php echo htmlspecialchars($category['name']); ?></h3>
                        <p class="category-count">تصنيف متنوع</p>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </section>
    <?php endif; ?>
    
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3><?php echo htmlspecialchars($siteName); ?></h3>
                    <p><?php echo htmlspecialchars($siteDescription); ?></p>
                </div>
                
                <div class="footer-section">
                    <h3>روابط سريعة</h3>
                    <p><a href="movies.php">الأفلام</a></p>
                    <p><a href="series.php">المسلسلات</a></p>
                    <p><a href="search.php">البحث</a></p>
                </div>
                
                <div class="footer-section">
                    <h3>الحساب</h3>
                    <p><a href="profile.php">الملف الشخصي</a></p>
                    <p><a href="favorites.php">المفضلة</a></p>
                    <p><a href="settings.php">الإعدادات</a></p>
                </div>
                
                <div class="footer-section">
                    <h3>تواصل معنا</h3>
                    <p>البريد الإلكتروني: <EMAIL></p>
                    <p>الهاتف: +966 12 345 6789</p>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p>&copy; 2024 <?php echo htmlspecialchars($siteName); ?>. جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </footer>
    
    <script>
        // تأثيرات تفاعلية
        document.querySelectorAll('.content-card').forEach(card => {
            card.addEventListener('click', function() {
                // يمكن إضافة منطق فتح الفيديو هنا
                console.log('تم النقر على:', this.querySelector('.card-title').textContent);
            });
        });
        
        document.querySelectorAll('.category-card').forEach(card => {
            card.addEventListener('click', function() {
                // يمكن إضافة منطق فتح التصنيف هنا
                console.log('تم النقر على تصنيف:', this.querySelector('.category-name').textContent);
            });
        });
        
        console.log('🎬 الواجهة الديناميكية جاهزة!');
        console.log('✅ القوائم الديناميكية مفعلة');
        console.log('✅ الإعدادات الديناميكية مفعلة');
        console.log('✅ المحتوى الديناميكي مفعل');
    </script>
</body>
</html>
