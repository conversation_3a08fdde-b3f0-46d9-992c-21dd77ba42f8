<?php
/**
 * Shahid Analytics API
 * Professional Video Streaming Platform
 */

require_once '../core/Database.php';
require_once '../core/Security.php';
require_once '../core/ApiController.php';

class AnalyticsAPI extends ApiController {
    
    public function handleRequest() {
        $route = trim($_SERVER['REQUEST_URI'], '/');
        $segments = explode('/', $route);
        
        // Remove 'api/analytics' from segments
        $segments = array_slice($segments, 2);
        
        $action = $segments[0] ?? null;
        
        switch ($action) {
            case null:
            case '':
                $this->trackEvent();
                break;
            case 'video':
                $this->trackVideoEvent();
                break;
            case 'page':
                $this->trackPageView();
                break;
            case 'search':
                $this->trackSearchEvent();
                break;
            case 'user':
                $this->trackUserEvent();
                break;
            case 'error':
                $this->trackErrorEvent();
                break;
            default:
                $this->sendError('Invalid analytics endpoint', 404);
        }
    }
    
    private function trackEvent() {
        if (!$this->validateMethod(['POST'])) return;
        
        $eventType = $this->requestData['event_type'] ?? '';
        $eventData = $this->requestData['event_data'] ?? [];
        
        if (empty($eventType)) {
            $this->sendError('Event type is required', 400);
            return;
        }
        
        $user = $this->authenticateUser(false);
        $userId = $user ? $user['id'] : null;
        
        $this->logEvent($eventType, $eventData, $userId);
        
        $this->sendSuccess(null, 'Event tracked successfully');
    }
    
    private function trackVideoEvent() {
        if (!$this->validateMethod(['POST'])) return;
        
        $requiredFields = ['content_id', 'content_type', 'event_type'];
        if (!$this->validateRequired($requiredFields)) return;
        
        $contentId = intval($this->requestData['content_id']);
        $contentType = $this->requestData['content_type'];
        $eventType = $this->requestData['event_type'];
        $currentTime = intval($this->requestData['current_time'] ?? 0);
        $duration = intval($this->requestData['duration'] ?? 0);
        $quality = $this->requestData['quality'] ?? '';
        $deviceType = $this->requestData['device_type'] ?? '';
        
        $user = $this->authenticateUser(false);
        $userId = $user ? $user['id'] : null;
        
        $eventData = [
            'content_id' => $contentId,
            'content_type' => $contentType,
            'current_time' => $currentTime,
            'duration' => $duration,
            'quality' => $quality,
            'device_type' => $deviceType,
            'progress_percentage' => $duration > 0 ? round(($currentTime / $duration) * 100, 2) : 0
        ];
        
        $this->logVideoEvent($eventType, $eventData, $userId);
        
        $this->sendSuccess(null, 'Video event tracked successfully');
    }
    
    private function trackPageView() {
        if (!$this->validateMethod(['POST'])) return;
        
        $page = $this->requestData['page'] ?? '';
        $title = $this->requestData['title'] ?? '';
        $referrer = $this->requestData['referrer'] ?? '';
        
        if (empty($page)) {
            $this->sendError('Page is required', 400);
            return;
        }
        
        $user = $this->authenticateUser(false);
        $userId = $user ? $user['id'] : null;
        
        $eventData = [
            'page' => $page,
            'title' => $title,
            'referrer' => $referrer,
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'screen_resolution' => $this->requestData['screen_resolution'] ?? '',
            'viewport_size' => $this->requestData['viewport_size'] ?? ''
        ];
        
        $this->logEvent('page_view', $eventData, $userId);
        
        $this->sendSuccess(null, 'Page view tracked successfully');
    }
    
    private function trackSearchEvent() {
        if (!$this->validateMethod(['POST'])) return;
        
        $query = $this->requestData['query'] ?? '';
        $resultsCount = intval($this->requestData['results_count'] ?? 0);
        $searchType = $this->requestData['search_type'] ?? 'basic';
        $filters = $this->requestData['filters'] ?? [];
        
        if (empty($query)) {
            $this->sendError('Search query is required', 400);
            return;
        }
        
        $user = $this->authenticateUser(false);
        $userId = $user ? $user['id'] : null;
        
        $eventData = [
            'query' => $query,
            'results_count' => $resultsCount,
            'search_type' => $searchType,
            'filters' => $filters
        ];
        
        $this->logEvent('search', $eventData, $userId);
        
        $this->sendSuccess(null, 'Search event tracked successfully');
    }
    
    private function trackUserEvent() {
        if (!$this->validateMethod(['POST'])) return;
        
        $user = $this->authenticateUser();
        if (!$user) return;
        
        $eventType = $this->requestData['event_type'] ?? '';
        $eventData = $this->requestData['event_data'] ?? [];
        
        if (empty($eventType)) {
            $this->sendError('Event type is required', 400);
            return;
        }
        
        $this->logEvent("user_{$eventType}", $eventData, $user['id']);
        
        $this->sendSuccess(null, 'User event tracked successfully');
    }
    
    private function trackErrorEvent() {
        if (!$this->validateMethod(['POST'])) return;
        
        $errorType = $this->requestData['error_type'] ?? '';
        $errorMessage = $this->requestData['error_message'] ?? '';
        $errorStack = $this->requestData['error_stack'] ?? '';
        $page = $this->requestData['page'] ?? '';
        
        if (empty($errorType)) {
            $this->sendError('Error type is required', 400);
            return;
        }
        
        $user = $this->authenticateUser(false);
        $userId = $user ? $user['id'] : null;
        
        $eventData = [
            'error_type' => $errorType,
            'error_message' => $errorMessage,
            'error_stack' => $errorStack,
            'page' => $page,
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
        ];
        
        $this->logEvent('error', $eventData, $userId);
        
        $this->sendSuccess(null, 'Error event tracked successfully');
    }
    
    private function logEvent($eventType, $eventData, $userId = null) {
        try {
            $sql = "INSERT INTO analytics_events (
                        user_id, event_type, event_data, ip_address, 
                        user_agent, session_id, created_at
                    ) VALUES (
                        :user_id, :event_type, :event_data, :ip_address,
                        :user_agent, :session_id, NOW()
                    )";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute([
                ':user_id' => $userId,
                ':event_type' => $eventType,
                ':event_data' => json_encode($eventData),
                ':ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
                ':user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
                ':session_id' => session_id() ?: null
            ]);
            
        } catch (Exception $e) {
            error_log('Analytics Error: ' . $e->getMessage());
        }
    }
    
    private function logVideoEvent($eventType, $eventData, $userId = null) {
        try {
            // Log to general analytics
            $this->logEvent("video_{$eventType}", $eventData, $userId);
            
            // Log to specific video analytics table
            $sql = "INSERT INTO video_analytics (
                        user_id, content_id, content_type, event_type,
                        current_time, duration, quality, device_type,
                        progress_percentage, ip_address, created_at
                    ) VALUES (
                        :user_id, :content_id, :content_type, :event_type,
                        :current_time, :duration, :quality, :device_type,
                        :progress_percentage, :ip_address, NOW()
                    )";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute([
                ':user_id' => $userId,
                ':content_id' => $eventData['content_id'],
                ':content_type' => $eventData['content_type'],
                ':event_type' => $eventType,
                ':current_time' => $eventData['current_time'],
                ':duration' => $eventData['duration'],
                ':quality' => $eventData['quality'],
                ':device_type' => $eventData['device_type'],
                ':progress_percentage' => $eventData['progress_percentage'],
                ':ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
            ]);
            
        } catch (Exception $e) {
            error_log('Video Analytics Error: ' . $e->getMessage());
        }
    }
}

// Handle the request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

$api = new AnalyticsAPI();
$api->handleRequest();
?>
