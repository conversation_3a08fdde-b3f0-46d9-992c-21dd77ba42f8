<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// معالجة طلبات OPTIONS
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// التحقق من طريقة الطلب
if (!in_array($_SERVER['REQUEST_METHOD'], ['POST', 'DELETE'])) {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => 'طريقة الطلب غير مدعومة',
        'error_code' => 'METHOD_NOT_ALLOWED'
    ]);
    exit;
}

try {
    // الاتصال بقاعدة البيانات
    $pdo = new PDO("mysql:host=localhost;dbname=shahid_platform;charset=utf8mb4", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // التحقق من رمز الوصول
    if (!isset($_SERVER['HTTP_AUTHORIZATION'])) {
        http_response_code(401);
        echo json_encode([
            'success' => false,
            'message' => 'رمز الوصول مطلوب',
            'error_code' => 'MISSING_TOKEN'
        ]);
        exit;
    }
    
    $token = str_replace('Bearer ', '', $_SERVER['HTTP_AUTHORIZATION']);
    $payload = json_decode(base64_decode($token), true);
    
    if (!$payload || !isset($payload['user_id']) || $payload['expires_at'] <= time()) {
        http_response_code(401);
        echo json_encode([
            'success' => false,
            'message' => 'رمز الوصول غير صالح أو منتهي الصلاحية',
            'error_code' => 'INVALID_TOKEN'
        ]);
        exit;
    }
    
    $userId = $payload['user_id'];
    
    // قراءة البيانات المرسلة
    $input = json_decode(file_get_contents('php://input'), true);
    
    // التحقق من وجود البيانات المطلوبة
    if (!isset($input['content_type']) || !isset($input['content_id'])) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'message' => 'نوع المحتوى ومعرف المحتوى مطلوبان',
            'error_code' => 'MISSING_CONTENT_INFO'
        ]);
        exit;
    }
    
    $contentType = $input['content_type']; // movie, series
    $contentId = intval($input['content_id']);
    
    // التحقق من صحة نوع المحتوى
    if (!in_array($contentType, ['movie', 'series'])) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'message' => 'نوع المحتوى غير صحيح',
            'error_code' => 'INVALID_CONTENT_TYPE'
        ]);
        exit;
    }
    
    // التحقق من وجود المحتوى
    $table = $contentType === 'movie' ? 'movies' : 'series';
    $stmt = $pdo->prepare("SELECT id, title FROM $table WHERE id = ? AND status = 'active'");
    $stmt->execute([$contentId]);
    $content = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$content) {
        http_response_code(404);
        echo json_encode([
            'success' => false,
            'message' => 'المحتوى غير موجود أو غير متاح',
            'error_code' => 'CONTENT_NOT_FOUND'
        ]);
        exit;
    }
    
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // إضافة إلى المفضلة
        
        // التحقق من وجود المحتوى في المفضلة مسبقاً
        $stmt = $pdo->prepare("SELECT id FROM favorites WHERE user_id = ? AND content_type = ? AND content_id = ?");
        $stmt->execute([$userId, $contentType, $contentId]);
        $existing = $stmt->fetch();
        
        if ($existing) {
            http_response_code(409);
            echo json_encode([
                'success' => false,
                'message' => 'هذا المحتوى موجود في المفضلة مسبقاً',
                'error_code' => 'ALREADY_IN_FAVORITES'
            ]);
            exit;
        }
        
        // إضافة إلى المفضلة
        $stmt = $pdo->prepare("
            INSERT INTO favorites (user_id, content_type, content_id, added_at) 
            VALUES (?, ?, ?, NOW())
        ");
        
        if ($stmt->execute([$userId, $contentType, $contentId])) {
            // تسجيل النشاط
            $stmt = $pdo->prepare("
                INSERT INTO activity_logs (user_id, action, description, ip_address, created_at) 
                VALUES (?, 'add_favorite', ?, ?, NOW())
            ");
            
            $description = "إضافة إلى المفضلة: " . $content['title'];
            $stmt->execute([$userId, $description, $_SERVER['REMOTE_ADDR']]);
            
            // الحصول على عدد المفضلة الحالي
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM favorites WHERE user_id = ?");
            $stmt->execute([$userId]);
            $totalFavorites = $stmt->fetchColumn();
            
            http_response_code(201);
            echo json_encode([
                'success' => true,
                'message' => 'تم إضافة المحتوى إلى المفضلة بنجاح',
                'data' => [
                    'content' => [
                        'id' => $content['id'],
                        'title' => $content['title'],
                        'type' => $contentType
                    ],
                    'total_favorites' => intval($totalFavorites),
                    'added_at' => date('Y-m-d H:i:s')
                ]
            ], JSON_UNESCAPED_UNICODE);
        } else {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => 'فشل في إضافة المحتوى إلى المفضلة',
                'error_code' => 'ADD_FAILED'
            ]);
        }
        
    } elseif ($_SERVER['REQUEST_METHOD'] === 'DELETE') {
        // إزالة من المفضلة
        
        $stmt = $pdo->prepare("DELETE FROM favorites WHERE user_id = ? AND content_type = ? AND content_id = ?");
        
        if ($stmt->execute([$userId, $contentType, $contentId])) {
            $rowsAffected = $stmt->rowCount();
            
            if ($rowsAffected > 0) {
                // تسجيل النشاط
                $stmt = $pdo->prepare("
                    INSERT INTO activity_logs (user_id, action, description, ip_address, created_at) 
                    VALUES (?, 'remove_favorite', ?, ?, NOW())
                ");
                
                $description = "إزالة من المفضلة: " . $content['title'];
                $stmt->execute([$userId, $description, $_SERVER['REMOTE_ADDR']]);
                
                // الحصول على عدد المفضلة الحالي
                $stmt = $pdo->prepare("SELECT COUNT(*) FROM favorites WHERE user_id = ?");
                $stmt->execute([$userId]);
                $totalFavorites = $stmt->fetchColumn();
                
                http_response_code(200);
                echo json_encode([
                    'success' => true,
                    'message' => 'تم إزالة المحتوى من المفضلة بنجاح',
                    'data' => [
                        'content' => [
                            'id' => $content['id'],
                            'title' => $content['title'],
                            'type' => $contentType
                        ],
                        'total_favorites' => intval($totalFavorites)
                    ]
                ], JSON_UNESCAPED_UNICODE);
            } else {
                http_response_code(404);
                echo json_encode([
                    'success' => false,
                    'message' => 'المحتوى غير موجود في المفضلة',
                    'error_code' => 'NOT_IN_FAVORITES'
                ]);
            }
        } else {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => 'فشل في إزالة المحتوى من المفضلة',
                'error_code' => 'REMOVE_FAILED'
            ]);
        }
    }
    
} catch (PDOException $e) {
    error_log("Database error in favorites API: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'خطأ في الخادم',
        'error_code' => 'DATABASE_ERROR'
    ]);
    
} catch (Exception $e) {
    error_log("General error in favorites API: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'خطأ غير متوقع',
        'error_code' => 'GENERAL_ERROR'
    ]);
}
?>
