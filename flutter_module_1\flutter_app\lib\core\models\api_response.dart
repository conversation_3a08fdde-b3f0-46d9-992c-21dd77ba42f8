class ApiResponse<T> {
  final bool success;
  final T? data;
  final String? message;
  final String? error;
  final Pagination? pagination;
  final String? timestamp;

  ApiResponse({
    required this.success,
    this.data,
    this.message,
    this.error,
    this.pagination,
    this.timestamp,
  });

  factory ApiResponse.fromJson(Map<String, dynamic> json) {
    return ApiResponse<T>(
      success: json['success'] ?? false,
      data: json['data'],
      message: json['message'],
      error: json['error'],
      pagination: json['pagination'] != null 
          ? Pagination.fromJson(json['pagination'])
          : null,
      timestamp: json['timestamp'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'data': data,
      'message': message,
      'error': error,
      'pagination': pagination?.toJson(),
      'timestamp': timestamp,
    };
  }

  bool get isSuccess => success && error == null;
  bool get hasError => !success || error != null;
  String get errorMessage => error ?? message ?? 'Unknown error';
}

class Pagination {
  final int currentPage;
  final int perPage;
  final int total;
  final int totalPages;
  final bool hasNext;
  final bool hasPrev;

  Pagination({
    required this.currentPage,
    required this.perPage,
    required this.total,
    required this.totalPages,
    required this.hasNext,
    required this.hasPrev,
  });

  factory Pagination.fromJson(Map<String, dynamic> json) {
    return Pagination(
      currentPage: json['current_page'] ?? 1,
      perPage: json['per_page'] ?? 20,
      total: json['total'] ?? 0,
      totalPages: json['total_pages'] ?? 0,
      hasNext: json['has_next'] ?? false,
      hasPrev: json['has_prev'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'current_page': currentPage,
      'per_page': perPage,
      'total': total,
      'total_pages': totalPages,
      'has_next': hasNext,
      'has_prev': hasPrev,
    };
  }
}
