<?php
/**
 * مشغل الفيديو البسيط - يعمل بدون مشاكل
 */

session_start();

// تسجيل دخول تلقائي للاختبار
if (!isset($_SESSION["user_id"])) {
    $_SESSION["user_id"] = 1;
    $_SESSION["user_name"] = "مستخدم تجريبي";
}

$video_id = $_GET["id"] ?? 1;
$type = $_GET["type"] ?? "movie";

// فيديوهات تجريبية
$videos = [
    1 => [
        "title" => "فيلم الأكشن المثير",
        "description" => "فيلم أكشن مليء بالإثارة والتشويق",
        "poster" => "https://via.placeholder.com/300x450/E50914/FFFFFF?text=Action+Movie",
        "video_url" => "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4"
    ],
    2 => [
        "title" => "المسلسل الدرامي",
        "description" => "مسلسل درامي اجتماعي رائع",
        "poster" => "https://via.placeholder.com/300x450/2196F3/FFFFFF?text=Drama+Series",
        "video_url" => "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4"
    ]
];

$video = $videos[$video_id] ?? $videos[1];
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎬 <?php echo $video["title"]; ?> - Shahid Player</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
            background: #000;
            color: #fff;
            overflow-x: hidden;
        }
        .player-container {
            position: relative;
            width: 100%;
            height: 100vh;
            background: #000;
        }
        .video-player {
            width: 100%;
            height: 70vh;
            background: #000;
        }
        .video-player video {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }
        .video-info {
            padding: 2rem;
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
        }
        .video-title {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 1rem;
            color: #E50914;
        }
        .video-description {
            font-size: 1.1rem;
            line-height: 1.6;
            opacity: 0.9;
            margin-bottom: 2rem;
        }
        .controls {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }
        .btn {
            background: linear-gradient(45deg, #E50914, #B8070F);
            color: white;
            padding: 0.8rem 1.5rem;
            text-decoration: none;
            border-radius: 8px;
            font-weight: bold;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(229, 9, 20, 0.3);
        }
        .btn.secondary {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 1.5rem;
            color: #E50914;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .spinner {
            display: inline-block;
            animation: spin 1s linear infinite;
        }
    </style>
</head>
<body>
    <div class="player-container">
        <div class="video-player">
            <div class="loading" id="loading">
                <span class="spinner">⏳</span> جاري تحميل الفيديو...
            </div>
            <video id="videoElement" controls style="display: none;">
                <source src="<?php echo $video["video_url"]; ?>" type="video/mp4">
                متصفحك لا يدعم تشغيل الفيديو.
            </video>
        </div>
        
        <div class="video-info">
            <h1 class="video-title"><?php echo $video["title"]; ?></h1>
            <p class="video-description"><?php echo $video["description"]; ?></p>
            
            <div class="controls">
                <button class="btn" onclick="togglePlay()">▶️ تشغيل/إيقاف</button>
                <button class="btn" onclick="toggleFullscreen()">🔳 ملء الشاشة</button>
                <button class="btn secondary" onclick="changeQuality()">⚙️ الجودة</button>
                <a href="../admin/simple_dashboard.php" class="btn secondary">🎛️ لوحة الإدارة</a>
                <a href="../homepage.php" class="btn secondary">🏠 الصفحة الرئيسية</a>
            </div>
        </div>
    </div>
    
    <script>
        const video = document.getElementById("videoElement");
        const loading = document.getElementById("loading");
        
        // إخفاء شاشة التحميل وعرض الفيديو
        video.addEventListener("loadeddata", () => {
            loading.style.display = "none";
            video.style.display = "block";
            console.log("🎬 تم تحميل الفيديو بنجاح!");
        });
        
        // تشغيل/إيقاف الفيديو
        function togglePlay() {
            if (video.paused) {
                video.play();
            } else {
                video.pause();
            }
        }
        
        // ملء الشاشة
        function toggleFullscreen() {
            if (video.requestFullscreen) {
                video.requestFullscreen();
            } else if (video.webkitRequestFullscreen) {
                video.webkitRequestFullscreen();
            } else if (video.msRequestFullscreen) {
                video.msRequestFullscreen();
            }
        }
        
        // تغيير الجودة (وهمي)
        function changeQuality() {
            alert("🎥 خيارات الجودة: 720p, 1080p, 4K");
        }
        
        // اختصارات لوحة المفاتيح
        document.addEventListener("keydown", (e) => {
            switch(e.code) {
                case "Space":
                    e.preventDefault();
                    togglePlay();
                    break;
                case "KeyF":
                    toggleFullscreen();
                    break;
            }
        });
        
        console.log("🎬 مشغل الفيديو البسيط يعمل بنجاح!");
    </script>
</body>
</html>