<?php
/**
 * Shahid Platform - Advanced Analytics Dashboard
 * Complete Analytics and Reporting System
 */

session_start();
require_once '../config/database.php';
require_once '../config/auth.php';

// Check admin authentication
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../auth/login.php');
    exit;
}

// Database connection
try {
    $pdo = new PDO("mysql:host=localhost;dbname=shahid_platform;charset=utf8mb4", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    die("Database connection failed: " . $e->getMessage());
}

// Get date range from request
$start_date = $_GET['start_date'] ?? date('Y-m-d', strtotime('-30 days'));
$end_date = $_GET['end_date'] ?? date('Y-m-d');

// Analytics Functions
function getOverviewStats($pdo, $start_date, $end_date) {
    $stats = [];
    
    // Total users
    $stmt = $pdo->query("SELECT COUNT(*) FROM users WHERE status = 'active'");
    $stats['total_users'] = $stmt->fetchColumn();
    
    // New users in period
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE DATE(created_at) BETWEEN ? AND ? AND status = 'active'");
    $stmt->execute([$start_date, $end_date]);
    $stats['new_users'] = $stmt->fetchColumn();
    
    // Active users (watched something in period)
    $stmt = $pdo->prepare("SELECT COUNT(DISTINCT user_id) FROM watch_history WHERE DATE(created_at) BETWEEN ? AND ?");
    $stmt->execute([$start_date, $end_date]);
    $stats['active_users'] = $stmt->fetchColumn();
    
    // Total content
    $stmt = $pdo->query("SELECT COUNT(*) FROM movies WHERE status = 'active'");
    $stats['total_movies'] = $stmt->fetchColumn();
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM series WHERE status IN ('active', 'ongoing', 'completed')");
    $stats['total_series'] = $stmt->fetchColumn();
    
    // Total views in period
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM watch_history WHERE DATE(created_at) BETWEEN ? AND ?");
    $stmt->execute([$start_date, $end_date]);
    $stats['total_views'] = $stmt->fetchColumn();
    
    // Revenue in period
    $stmt = $pdo->prepare("SELECT COALESCE(SUM(amount), 0) FROM payments WHERE DATE(created_at) BETWEEN ? AND ? AND status = 'completed'");
    $stmt->execute([$start_date, $end_date]);
    $stats['revenue'] = $stmt->fetchColumn();
    
    // Subscription breakdown
    $stmt = $pdo->query("SELECT subscription_type, COUNT(*) as count FROM users WHERE status = 'active' GROUP BY subscription_type");
    $subscriptions = $stmt->fetchAll(PDO::FETCH_ASSOC);
    foreach ($subscriptions as $sub) {
        $stats['subscription_' . $sub['subscription_type']] = $sub['count'];
    }
    
    return $stats;
}

function getDailyStats($pdo, $start_date, $end_date) {
    $stmt = $pdo->prepare("
        SELECT 
            DATE(created_at) as date,
            COUNT(DISTINCT user_id) as active_users,
            COUNT(*) as total_views
        FROM watch_history 
        WHERE DATE(created_at) BETWEEN ? AND ?
        GROUP BY DATE(created_at)
        ORDER BY date
    ");
    $stmt->execute([$start_date, $end_date]);
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

function getTopContent($pdo, $start_date, $end_date) {
    // Top movies
    $stmt = $pdo->prepare("
        SELECT 
            m.title, m.title_ar, 
            COUNT(wh.id) as views,
            AVG(r.rating) as avg_rating
        FROM movies m
        LEFT JOIN watch_history wh ON m.id = wh.movie_id AND DATE(wh.created_at) BETWEEN ? AND ?
        LEFT JOIN ratings r ON m.id = r.movie_id
        WHERE m.status = 'active'
        GROUP BY m.id
        HAVING views > 0
        ORDER BY views DESC
        LIMIT 10
    ");
    $stmt->execute([$start_date, $end_date]);
    $top_movies = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Top series
    $stmt = $pdo->prepare("
        SELECT 
            s.title, s.title_ar, 
            COUNT(wh.id) as views,
            AVG(r.rating) as avg_rating
        FROM series s
        LEFT JOIN watch_history wh ON s.id = wh.series_id AND DATE(wh.created_at) BETWEEN ? AND ?
        LEFT JOIN ratings r ON s.id = r.series_id
        WHERE s.status IN ('active', 'ongoing', 'completed')
        GROUP BY s.id
        HAVING views > 0
        ORDER BY views DESC
        LIMIT 10
    ");
    $stmt->execute([$start_date, $end_date]);
    $top_series = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    return ['movies' => $top_movies, 'series' => $top_series];
}

function getRevenueStats($pdo, $start_date, $end_date) {
    // Daily revenue
    $stmt = $pdo->prepare("
        SELECT 
            DATE(created_at) as date,
            SUM(amount) as revenue,
            COUNT(*) as transactions
        FROM payments 
        WHERE DATE(created_at) BETWEEN ? AND ? AND status = 'completed'
        GROUP BY DATE(created_at)
        ORDER BY date
    ");
    $stmt->execute([$start_date, $end_date]);
    $daily_revenue = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Revenue by plan
    $stmt = $pdo->prepare("
        SELECT 
            s.plan_type,
            SUM(p.amount) as revenue,
            COUNT(p.id) as transactions
        FROM payments p
        JOIN subscriptions s ON p.subscription_id = s.id
        WHERE DATE(p.created_at) BETWEEN ? AND ? AND p.status = 'completed'
        GROUP BY s.plan_type
    ");
    $stmt->execute([$start_date, $end_date]);
    $revenue_by_plan = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    return ['daily' => $daily_revenue, 'by_plan' => $revenue_by_plan];
}

function getUserEngagement($pdo, $start_date, $end_date) {
    // Average watch time
    $stmt = $pdo->prepare("
        SELECT 
            AVG(watch_time) as avg_watch_time,
            AVG(duration) as avg_duration,
            COUNT(*) as total_sessions
        FROM watch_history 
        WHERE DATE(created_at) BETWEEN ? AND ?
    ");
    $stmt->execute([$start_date, $end_date]);
    $engagement = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // User retention (users who watched in both periods)
    $stmt = $pdo->prepare("
        SELECT COUNT(DISTINCT user_id) as returning_users
        FROM watch_history 
        WHERE DATE(created_at) BETWEEN ? AND ?
        AND user_id IN (
            SELECT DISTINCT user_id 
            FROM watch_history 
            WHERE DATE(created_at) BETWEEN DATE_SUB(?, INTERVAL 30 DAY) AND ?
        )
    ");
    $stmt->execute([$start_date, $end_date, $start_date, $start_date]);
    $retention = $stmt->fetch(PDO::FETCH_ASSOC);
    
    return array_merge($engagement, $retention);
}

// Get all analytics data
$overview = getOverviewStats($pdo, $start_date, $end_date);
$daily_stats = getDailyStats($pdo, $start_date, $end_date);
$top_content = getTopContent($pdo, $start_date, $end_date);
$revenue_stats = getRevenueStats($pdo, $start_date, $end_date);
$engagement = getUserEngagement($pdo, $start_date, $end_date);
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التحليلات المتقدمة - Shahid Platform</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', 'Cairo', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #2d2d2d 100%);
            color: #fff;
            line-height: 1.6;
            min-height: 100vh;
        }
        
        .header {
            background: linear-gradient(135deg, #E50914 0%, #B8070F 100%);
            padding: 2rem 0;
            text-align: center;
            box-shadow: 0 4px 20px rgba(229, 9, 20, 0.3);
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .date-filter {
            background: rgba(47, 47, 47, 0.9);
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            border: 1px solid rgba(229, 9, 20, 0.2);
            display: flex;
            align-items: center;
            gap: 1rem;
            flex-wrap: wrap;
        }
        
        .date-filter label {
            color: #ccc;
            font-weight: bold;
        }
        
        .date-filter input {
            padding: 0.5rem;
            border: 1px solid #555;
            border-radius: 5px;
            background: rgba(0, 0, 0, 0.3);
            color: #fff;
        }
        
        .date-filter button {
            background: linear-gradient(45deg, #E50914, #B8070F);
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
        }
        
        .stats-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 3rem;
        }
        
        .stat-card {
            background: rgba(47, 47, 47, 0.9);
            border-radius: 15px;
            padding: 2rem;
            text-align: center;
            border: 1px solid rgba(229, 9, 20, 0.2);
            transition: all 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(229, 9, 20, 0.3);
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: #E50914;
            display: block;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            color: #ccc;
            font-size: 1rem;
        }
        
        .stat-change {
            font-size: 0.9rem;
            margin-top: 0.5rem;
        }
        
        .stat-change.positive {
            color: #4CAF50;
        }
        
        .stat-change.negative {
            color: #F44336;
        }
        
        .charts-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }
        
        .chart-card {
            background: rgba(47, 47, 47, 0.9);
            border-radius: 15px;
            padding: 2rem;
            border: 1px solid rgba(229, 9, 20, 0.2);
        }
        
        .chart-card h3 {
            color: #E50914;
            margin-bottom: 1.5rem;
            text-align: center;
        }
        
        .chart-container {
            position: relative;
            height: 300px;
        }
        
        .tables-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }
        
        .table-card {
            background: rgba(47, 47, 47, 0.9);
            border-radius: 15px;
            padding: 2rem;
            border: 1px solid rgba(229, 9, 20, 0.2);
        }
        
        .table-card h3 {
            color: #E50914;
            margin-bottom: 1.5rem;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
        }
        
        th, td {
            padding: 1rem;
            text-align: right;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        th {
            background: rgba(229, 9, 20, 0.2);
            color: #E50914;
            font-weight: bold;
        }
        
        tr:hover {
            background: rgba(229, 9, 20, 0.1);
        }
        
        .back-link {
            display: inline-block;
            background: linear-gradient(45deg, #555, #333);
            color: white;
            padding: 0.8rem 1.5rem;
            border-radius: 8px;
            text-decoration: none;
            margin-bottom: 2rem;
            transition: all 0.3s ease;
        }
        
        .back-link:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
            color: white;
        }
        
        .engagement-metrics {
            background: rgba(47, 47, 47, 0.9);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            border: 1px solid rgba(229, 9, 20, 0.2);
        }
        
        .engagement-metrics h3 {
            color: #E50914;
            margin-bottom: 1.5rem;
            text-align: center;
        }
        
        .engagement-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
        }
        
        .engagement-item {
            text-align: center;
            background: rgba(0, 0, 0, 0.3);
            padding: 1.5rem;
            border-radius: 10px;
        }
        
        .engagement-value {
            font-size: 2rem;
            font-weight: bold;
            color: #E50914;
            display: block;
        }
        
        .engagement-label {
            color: #ccc;
            margin-top: 0.5rem;
        }
        
        @media (max-width: 768px) {
            .charts-section {
                grid-template-columns: 1fr;
            }
            
            .tables-section {
                grid-template-columns: 1fr;
            }
            
            .date-filter {
                flex-direction: column;
                align-items: stretch;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>📊 التحليلات المتقدمة</h1>
        <p>تحليل شامل لأداء المنصة والمستخدمين</p>
    </div>

    <div class="container">
        <a href="dashboard.php" class="back-link">← العودة إلى لوحة التحكم</a>
        
        <!-- Date Filter -->
        <div class="date-filter">
            <label>من تاريخ:</label>
            <input type="date" id="startDate" value="<?php echo $start_date; ?>">
            <label>إلى تاريخ:</label>
            <input type="date" id="endDate" value="<?php echo $end_date; ?>">
            <button onclick="updateAnalytics()">تحديث التحليلات</button>
        </div>

        <!-- Overview Stats -->
        <div class="stats-overview">
            <div class="stat-card">
                <span class="stat-number"><?php echo number_format($overview['total_users']); ?></span>
                <div class="stat-label">إجمالي المستخدمين</div>
                <div class="stat-change positive">+<?php echo $overview['new_users']; ?> جديد</div>
            </div>
            <div class="stat-card">
                <span class="stat-number"><?php echo number_format($overview['active_users']); ?></span>
                <div class="stat-label">مستخدمين نشطين</div>
                <div class="stat-change">في الفترة المحددة</div>
            </div>
            <div class="stat-card">
                <span class="stat-number"><?php echo number_format($overview['total_views']); ?></span>
                <div class="stat-label">إجمالي المشاهدات</div>
                <div class="stat-change">في الفترة المحددة</div>
            </div>
            <div class="stat-card">
                <span class="stat-number">$<?php echo number_format($overview['revenue'], 2); ?></span>
                <div class="stat-label">الإيرادات</div>
                <div class="stat-change positive">في الفترة المحددة</div>
            </div>
            <div class="stat-card">
                <span class="stat-number"><?php echo number_format($overview['total_movies']); ?></span>
                <div class="stat-label">إجمالي الأفلام</div>
            </div>
            <div class="stat-card">
                <span class="stat-number"><?php echo number_format($overview['total_series']); ?></span>
                <div class="stat-label">إجمالي المسلسلات</div>
            </div>
        </div>

        <!-- Engagement Metrics -->
        <div class="engagement-metrics">
            <h3>مؤشرات التفاعل</h3>
            <div class="engagement-grid">
                <div class="engagement-item">
                    <span class="engagement-value"><?php echo round($engagement['avg_watch_time'] ?? 0); ?></span>
                    <div class="engagement-label">متوسط وقت المشاهدة (دقيقة)</div>
                </div>
                <div class="engagement-item">
                    <span class="engagement-value"><?php echo round(($engagement['avg_watch_time'] ?? 0) / ($engagement['avg_duration'] ?? 1) * 100); ?>%</span>
                    <div class="engagement-label">معدل إكمال المشاهدة</div>
                </div>
                <div class="engagement-item">
                    <span class="engagement-value"><?php echo number_format($engagement['total_sessions'] ?? 0); ?></span>
                    <div class="engagement-label">جلسات المشاهدة</div>
                </div>
                <div class="engagement-item">
                    <span class="engagement-value"><?php echo number_format($engagement['returning_users'] ?? 0); ?></span>
                    <div class="engagement-label">مستخدمين عائدين</div>
                </div>
            </div>
        </div>

        <!-- Charts Section -->
        <div class="charts-section">
            <div class="chart-card">
                <h3>المشاهدات اليومية</h3>
                <div class="chart-container">
                    <canvas id="dailyViewsChart"></canvas>
                </div>
            </div>
            <div class="chart-card">
                <h3>توزيع الاشتراكات</h3>
                <div class="chart-container">
                    <canvas id="subscriptionChart"></canvas>
                </div>
            </div>
        </div>

        <!-- Tables Section -->
        <div class="tables-section">
            <div class="table-card">
                <h3>أفضل الأفلام</h3>
                <table>
                    <thead>
                        <tr>
                            <th>الفيلم</th>
                            <th>المشاهدات</th>
                            <th>التقييم</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($top_content['movies'] as $movie): ?>
                        <tr>
                            <td><?php echo htmlspecialchars($movie['title_ar'] ?? $movie['title']); ?></td>
                            <td><?php echo number_format($movie['views']); ?></td>
                            <td><?php echo round($movie['avg_rating'] ?? 0, 1); ?>/10</td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            
            <div class="table-card">
                <h3>أفضل المسلسلات</h3>
                <table>
                    <thead>
                        <tr>
                            <th>المسلسل</th>
                            <th>المشاهدات</th>
                            <th>التقييم</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($top_content['series'] as $series): ?>
                        <tr>
                            <td><?php echo htmlspecialchars($series['title_ar'] ?? $series['title']); ?></td>
                            <td><?php echo number_format($series['views']); ?></td>
                            <td><?php echo round($series['avg_rating'] ?? 0, 1); ?>/10</td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script>
        // Daily Views Chart
        const dailyViewsCtx = document.getElementById('dailyViewsChart').getContext('2d');
        const dailyViewsChart = new Chart(dailyViewsCtx, {
            type: 'line',
            data: {
                labels: <?php echo json_encode(array_column($daily_stats, 'date')); ?>,
                datasets: [{
                    label: 'المشاهدات اليومية',
                    data: <?php echo json_encode(array_column($daily_stats, 'total_views')); ?>,
                    borderColor: '#E50914',
                    backgroundColor: 'rgba(229, 9, 20, 0.1)',
                    tension: 0.4
                }, {
                    label: 'المستخدمين النشطين',
                    data: <?php echo json_encode(array_column($daily_stats, 'active_users')); ?>,
                    borderColor: '#4CAF50',
                    backgroundColor: 'rgba(76, 175, 80, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        labels: {
                            color: '#fff'
                        }
                    }
                },
                scales: {
                    x: {
                        ticks: {
                            color: '#ccc'
                        },
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        }
                    },
                    y: {
                        ticks: {
                            color: '#ccc'
                        },
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        }
                    }
                }
            }
        });

        // Subscription Distribution Chart
        const subscriptionCtx = document.getElementById('subscriptionChart').getContext('2d');
        const subscriptionChart = new Chart(subscriptionCtx, {
            type: 'doughnut',
            data: {
                labels: ['مجاني', 'أساسي', 'مميز'],
                datasets: [{
                    data: [
                        <?php echo $overview['subscription_free'] ?? 0; ?>,
                        <?php echo $overview['subscription_basic'] ?? 0; ?>,
                        <?php echo $overview['subscription_premium'] ?? 0; ?>
                    ],
                    backgroundColor: [
                        '#9E9E9E',
                        '#2196F3',
                        '#E50914'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        labels: {
                            color: '#fff'
                        }
                    }
                }
            }
        });

        function updateAnalytics() {
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;
            
            if (startDate && endDate) {
                window.location.href = `analytics.php?start_date=${startDate}&end_date=${endDate}`;
            }
        }

        console.log('📊 Advanced Analytics Dashboard loaded successfully!');
    </script>
</body>
</html>
