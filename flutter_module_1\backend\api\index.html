<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shahid API - منصة البث الاحترافية</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            color: #fff;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            text-align: center;
            max-width: 800px;
            padding: 2rem;
        }
        
        .logo {
            font-size: 3rem;
            font-weight: bold;
            color: #E50914;
            margin-bottom: 1rem;
        }
        
        h1 {
            font-size: 2rem;
            margin-bottom: 1rem;
            color: #fff;
        }
        
        p {
            font-size: 1.1rem;
            margin-bottom: 2rem;
            color: #ccc;
            line-height: 1.6;
        }
        
        .api-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin: 2rem 0;
        }
        
        .api-link {
            background: #2d2d2d;
            padding: 1.5rem;
            border-radius: 10px;
            text-decoration: none;
            color: #fff;
            transition: all 0.3s;
            border: 2px solid transparent;
        }
        
        .api-link:hover {
            background: #E50914;
            border-color: #E50914;
            transform: translateY(-5px);
        }
        
        .api-link h3 {
            font-size: 1.2rem;
            margin-bottom: 0.5rem;
            color: #E50914;
        }
        
        .api-link:hover h3 {
            color: #fff;
        }
        
        .api-link p {
            font-size: 0.9rem;
            margin: 0;
            color: #ccc;
        }
        
        .api-link:hover p {
            color: #fff;
        }
        
        .status {
            background: #28a745;
            color: #fff;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.9rem;
            display: inline-block;
            margin-bottom: 2rem;
        }
        
        .endpoints {
            background: #2d2d2d;
            padding: 2rem;
            border-radius: 10px;
            margin: 2rem 0;
            text-align: right;
        }
        
        .endpoints h3 {
            color: #E50914;
            margin-bottom: 1rem;
        }
        
        .endpoint {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid #444;
        }
        
        .endpoint:last-child {
            border-bottom: none;
        }
        
        .method {
            background: #E50914;
            color: #fff;
            padding: 0.2rem 0.5rem;
            border-radius: 3px;
            font-size: 0.8rem;
            font-weight: bold;
        }
        
        .method.get {
            background: #28a745;
        }
        
        .method.post {
            background: #007bff;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }
            
            .logo {
                font-size: 2rem;
            }
            
            h1 {
                font-size: 1.5rem;
            }
            
            .api-links {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">🎬 Shahid</div>
        <h1>API منصة البث الاحترافية</h1>
        <div class="status">✅ النظام يعمل بشكل طبيعي</div>
        <p>مرحباً بك في API منصة Shahid للبث الاحترافي. يمكنك الوصول لجميع البيانات والخدمات من خلال النقاط التالية.</p>
        
        <div class="api-links">
            <a href="test_api.php" class="api-link">
                <h3>🧪 اختبار API</h3>
                <p>صفحة اختبار شاملة لجميع endpoints</p>
            </a>

            <a href="api.php?endpoint=status" class="api-link">
                <h3>📊 حالة النظام</h3>
                <p>فحص حالة الخادم وقاعدة البيانات</p>
            </a>

            <a href="api.php?endpoint=movies" class="api-link">
                <h3>🎬 الأفلام</h3>
                <p>قائمة الأفلام المتاحة مع التفاصيل</p>
            </a>

            <a href="api.php?endpoint=series" class="api-link">
                <h3>📺 المسلسلات</h3>
                <p>قائمة المسلسلات والحلقات</p>
            </a>

            <a href="api.php?endpoint=search&q=test" class="api-link">
                <h3>🔍 البحث</h3>
                <p>البحث في المحتوى المتاح</p>
            </a>

            <a href="?endpoint=categories" class="api-link">
                <h3>📂 التصنيفات</h3>
                <p>تصنيفات المحتوى المختلفة</p>
            </a>

            <a href="?endpoint=episodes&series_id=1" class="api-link">
                <h3>📺 الحلقات</h3>
                <p>حلقات المسلسلات</p>
            </a>

            <a href="test_api.php" class="api-link">
                <h3>🧪 اختبار API</h3>
                <p>صفحة اختبار شاملة</p>
            </a>

            <a href="../admin/" class="api-link">
                <h3>🎛️ لوحة الإدارة</h3>
                <p>إدارة المحتوى والمستخدمين</p>
            </a>
        </div>
        
        <div class="endpoints">
            <h3>📋 نقاط النهاية المتاحة (API Endpoints)</h3>
            
            <div class="endpoint">
                <span>حالة النظام</span>
                <div>
                    <span class="method get">GET</span>
                    <code>/api/status</code>
                </div>
            </div>
            
            <div class="endpoint">
                <span>قائمة الأفلام</span>
                <div>
                    <span class="method get">GET</span>
                    <code>/api/movies</code>
                </div>
            </div>
            
            <div class="endpoint">
                <span>تفاصيل فيلم</span>
                <div>
                    <span class="method get">GET</span>
                    <code>/api/movies/{id}</code>
                </div>
            </div>
            
            <div class="endpoint">
                <span>قائمة المسلسلات</span>
                <div>
                    <span class="method get">GET</span>
                    <code>/api/series</code>
                </div>
            </div>
            
            <div class="endpoint">
                <span>تفاصيل مسلسل</span>
                <div>
                    <span class="method get">GET</span>
                    <code>/api/series/{id}</code>
                </div>
            </div>
            
            <div class="endpoint">
                <span>البحث</span>
                <div>
                    <span class="method get">GET</span>
                    <code>/api/search?q={query}</code>
                </div>
            </div>
            
            <div class="endpoint">
                <span>تسجيل الدخول</span>
                <div>
                    <span class="method post">POST</span>
                    <code>/api/auth/login</code>
                </div>
            </div>
            
            <div class="endpoint">
                <span>التسجيل</span>
                <div>
                    <span class="method post">POST</span>
                    <code>/api/auth/register</code>
                </div>
            </div>
        </div>
        
        <p style="margin-top: 2rem; font-size: 0.9rem; color: #666;">
            📚 للمزيد من التوثيق والأمثلة، قم بزيارة 
            <a href="test_api.php" style="color: #E50914;">صفحة اختبار API</a>
        </p>
    </div>
    
    <script>
        // Auto-refresh status every 30 seconds
        setInterval(() => {
            fetch('api.php?endpoint=status')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        document.querySelector('.status').textContent = '✅ النظام يعمل بشكل طبيعي';
                        document.querySelector('.status').style.background = '#28a745';
                    } else {
                        document.querySelector('.status').textContent = '⚠️ مشكلة في النظام';
                        document.querySelector('.status').style.background = '#dc3545';
                    }
                })
                .catch(() => {
                    document.querySelector('.status').textContent = '❌ لا يمكن الوصول للخادم';
                    document.querySelector('.status').style.background = '#dc3545';
                });
        }, 30000);
    </script>
</body>
</html>
