<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// معالجة طلبات OPTIONS
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => 'طريقة الطلب غير مدعومة',
        'error_code' => 'METHOD_NOT_ALLOWED'
    ]);
    exit;
}

try {
    // الاتصال بقاعدة البيانات
    $pdo = new PDO("mysql:host=localhost;dbname=shahid_platform;charset=utf8mb4", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // التحقق من رمز الوصول
    if (!isset($_SERVER['HTTP_AUTHORIZATION'])) {
        http_response_code(401);
        echo json_encode([
            'success' => false,
            'message' => 'رمز الوصول مطلوب',
            'error_code' => 'MISSING_TOKEN'
        ]);
        exit;
    }
    
    $token = str_replace('Bearer ', '', $_SERVER['HTTP_AUTHORIZATION']);
    $payload = json_decode(base64_decode($token), true);
    
    if (!$payload || !isset($payload['user_id']) || $payload['expires_at'] <= time()) {
        http_response_code(401);
        echo json_encode([
            'success' => false,
            'message' => 'رمز الوصول غير صالح أو منتهي الصلاحية',
            'error_code' => 'INVALID_TOKEN'
        ]);
        exit;
    }
    
    $userId = $payload['user_id'];
    
    // قراءة البيانات المرسلة
    $input = json_decode(file_get_contents('php://input'), true);
    
    // التحقق من وجود البيانات المطلوبة
    if (!isset($input['subscription_id']) || !isset($input['payment_method'])) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'message' => 'معرف الاشتراك وطريقة الدفع مطلوبان',
            'error_code' => 'MISSING_REQUIRED_DATA'
        ]);
        exit;
    }
    
    $subscriptionId = intval($input['subscription_id']);
    $paymentMethod = $input['payment_method'];
    $promoCode = isset($input['promo_code']) ? trim($input['promo_code']) : '';
    
    // التحقق من صحة طريقة الدفع
    $validPaymentMethods = ['credit_card', 'paypal', 'bank_transfer', 'apple_pay', 'google_pay'];
    if (!in_array($paymentMethod, $validPaymentMethods)) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'message' => 'طريقة الدفع غير صحيحة',
            'error_code' => 'INVALID_PAYMENT_METHOD'
        ]);
        exit;
    }
    
    // الحصول على معلومات المستخدم
    $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->execute([$userId]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$user) {
        http_response_code(404);
        echo json_encode([
            'success' => false,
            'message' => 'المستخدم غير موجود',
            'error_code' => 'USER_NOT_FOUND'
        ]);
        exit;
    }
    
    // الحصول على معلومات خطة الاشتراك
    $stmt = $pdo->prepare("SELECT * FROM subscriptions WHERE id = ? AND is_active = 1");
    $stmt->execute([$subscriptionId]);
    $subscription = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$subscription) {
        http_response_code(404);
        echo json_encode([
            'success' => false,
            'message' => 'خطة الاشتراك غير موجودة أو غير نشطة',
            'error_code' => 'SUBSCRIPTION_NOT_FOUND'
        ]);
        exit;
    }
    
    // التحقق من وجود اشتراك نشط
    $stmt = $pdo->prepare("
        SELECT * FROM user_subscriptions 
        WHERE user_id = ? AND status = 'active' AND end_date > NOW()
    ");
    $stmt->execute([$userId]);
    $activeSubscription = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($activeSubscription) {
        http_response_code(409);
        echo json_encode([
            'success' => false,
            'message' => 'لديك اشتراك نشط بالفعل',
            'error_code' => 'ACTIVE_SUBSCRIPTION_EXISTS',
            'data' => [
                'current_subscription' => [
                    'id' => $activeSubscription['id'],
                    'end_date' => $activeSubscription['end_date']
                ]
            ]
        ]);
        exit;
    }
    
    // حساب السعر النهائي
    $finalPrice = floatval($subscription['price']);
    $discount = 0;
    $promoCodeData = null;
    
    // التحقق من كود الخصم
    if ($promoCode) {
        $stmt = $pdo->prepare("
            SELECT * FROM promo_codes 
            WHERE code = ? AND is_active = 1 
            AND (expiry_date IS NULL OR expiry_date > NOW())
            AND (usage_limit IS NULL OR usage_count < usage_limit)
        ");
        $stmt->execute([$promoCode]);
        $promoCodeData = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($promoCodeData) {
            if ($promoCodeData['discount_type'] === 'percentage') {
                $discount = ($finalPrice * $promoCodeData['discount_value']) / 100;
            } else {
                $discount = min($promoCodeData['discount_value'], $finalPrice);
            }
            $finalPrice -= $discount;
        }
    }
    
    // بدء المعاملة
    $pdo->beginTransaction();
    
    try {
        // إنشاء معاملة الدفع
        $transactionId = uniqid('txn_', true);
        
        $stmt = $pdo->prepare("
            INSERT INTO payments (
                user_id, subscription_id, amount, discount_amount, final_amount,
                payment_method, transaction_id, promo_code, status, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'pending', NOW())
        ");
        
        $stmt->execute([
            $userId, $subscriptionId, $subscription['price'], $discount, $finalPrice,
            $paymentMethod, $transactionId, $promoCode
        ]);
        
        $paymentId = $pdo->lastInsertId();
        
        // محاكاة معالجة الدفع (في التطبيق الحقيقي، هنا يتم الاتصال بمعالج الدفع)
        $paymentSuccess = true; // محاكاة نجاح الدفع
        
        if ($paymentSuccess) {
            // تحديث حالة الدفع
            $stmt = $pdo->prepare("
                UPDATE payments 
                SET status = 'completed', processed_at = NOW() 
                WHERE id = ?
            ");
            $stmt->execute([$paymentId]);
            
            // إنشاء اشتراك المستخدم
            $startDate = date('Y-m-d H:i:s');
            $endDate = date('Y-m-d H:i:s', strtotime("+{$subscription['duration_days']} days"));
            
            $stmt = $pdo->prepare("
                INSERT INTO user_subscriptions (
                    user_id, subscription_id, payment_id, start_date, end_date, status, created_at
                ) VALUES (?, ?, ?, ?, ?, 'active', NOW())
            ");
            
            $stmt->execute([$userId, $subscriptionId, $paymentId, $startDate, $endDate]);
            $userSubscriptionId = $pdo->lastInsertId();
            
            // تحديث نوع اشتراك المستخدم
            $newSubscriptionType = match($subscription['name']) {
                'خطة مميزة' => 'premium',
                'خطة VIP' => 'vip',
                default => 'premium'
            };
            
            $stmt = $pdo->prepare("UPDATE users SET subscription_type = ? WHERE id = ?");
            $stmt->execute([$newSubscriptionType, $userId]);
            
            // تحديث استخدام كود الخصم
            if ($promoCodeData) {
                $stmt = $pdo->prepare("
                    UPDATE promo_codes 
                    SET usage_count = usage_count + 1 
                    WHERE id = ?
                ");
                $stmt->execute([$promoCodeData['id']]);
            }
            
            // تسجيل النشاط
            $stmt = $pdo->prepare("
                INSERT INTO activity_logs (user_id, action, description, ip_address, created_at) 
                VALUES (?, 'subscription_purchase', ?, ?, NOW())
            ");
            
            $description = "اشتراك في خطة: " . $subscription['name'] . " - المبلغ: " . $finalPrice . " ر.س";
            $stmt->execute([$userId, $description, $_SERVER['REMOTE_ADDR']]);
            
            // إرسال إشعار للمستخدم
            $stmt = $pdo->prepare("
                INSERT INTO notifications (user_id, title, message, type, created_at) 
                VALUES (?, 'تم تفعيل الاشتراك', ?, 'success', NOW())
            ");
            
            $notificationMessage = "تم تفعيل اشتراكك في خطة {$subscription['name']} بنجاح. يمكنك الآن الاستمتاع بجميع المحتويات المميزة.";
            $stmt->execute([$userId, $notificationMessage]);
            
            // تأكيد المعاملة
            $pdo->commit();
            
            // إعداد الاستجابة
            $response = [
                'success' => true,
                'message' => 'تم تفعيل الاشتراك بنجاح',
                'data' => [
                    'subscription' => [
                        'id' => $userSubscriptionId,
                        'plan_name' => $subscription['name'],
                        'plan_description' => $subscription['description'],
                        'features' => json_decode($subscription['features'], true),
                        'start_date' => $startDate,
                        'end_date' => $endDate,
                        'duration_days' => intval($subscription['duration_days'])
                    ],
                    'payment' => [
                        'id' => $paymentId,
                        'transaction_id' => $transactionId,
                        'amount' => floatval($subscription['price']),
                        'discount' => $discount,
                        'final_amount' => $finalPrice,
                        'payment_method' => $paymentMethod,
                        'promo_code' => $promoCode
                    ],
                    'user' => [
                        'id' => $userId,
                        'subscription_type' => $newSubscriptionType,
                        'can_access_premium' => true
                    ]
                ]
            ];
            
            http_response_code(201);
            echo json_encode($response, JSON_UNESCAPED_UNICODE);
            
        } else {
            // فشل الدفع
            $stmt = $pdo->prepare("
                UPDATE payments 
                SET status = 'failed', processed_at = NOW() 
                WHERE id = ?
            ");
            $stmt->execute([$paymentId]);
            
            $pdo->commit();
            
            http_response_code(402);
            echo json_encode([
                'success' => false,
                'message' => 'فشل في معالجة الدفع',
                'error_code' => 'PAYMENT_FAILED',
                'data' => [
                    'payment_id' => $paymentId,
                    'transaction_id' => $transactionId
                ]
            ]);
        }
        
    } catch (Exception $e) {
        $pdo->rollback();
        throw $e;
    }
    
} catch (PDOException $e) {
    error_log("Database error in subscription API: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'خطأ في الخادم',
        'error_code' => 'DATABASE_ERROR'
    ]);
    
} catch (Exception $e) {
    error_log("General error in subscription API: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'خطأ غير متوقع',
        'error_code' => 'GENERAL_ERROR'
    ]);
}
?>
