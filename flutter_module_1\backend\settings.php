<?php
session_start();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

$user_id = $_SESSION['user_id'];
$user = null;
$error = '';
$success = '';

try {
    $pdo = new PDO("mysql:host=localhost;dbname=shahid_platform;charset=utf8mb4", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // جلب بيانات المستخدم
    $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->execute([$user_id]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$user) {
        session_destroy();
        header('Location: login.php');
        exit;
    }
    
} catch (Exception $e) {
    $error = 'حدث خطأ في تحميل البيانات';
}

// معالجة تحديث الإعدادات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['update_profile'])) {
            // تحديث الملف الشخصي
            $full_name = trim($_POST['full_name'] ?? '');
            $email = trim($_POST['email'] ?? '');
            $phone = trim($_POST['phone'] ?? '');
            $country = trim($_POST['country'] ?? '');
            $birth_date = $_POST['birth_date'] ?? '';
            $gender = $_POST['gender'] ?? '';
            $language = $_POST['language'] ?? 'ar';
            
            if (empty($full_name) || empty($email)) {
                $error = 'الاسم والبريد الإلكتروني مطلوبان';
            } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                $error = 'البريد الإلكتروني غير صحيح';
            } else {
                // التحقق من عدم وجود البريد الإلكتروني لمستخدم آخر
                $checkStmt = $pdo->prepare("SELECT id FROM users WHERE email = ? AND id != ?");
                $checkStmt->execute([$email, $user_id]);
                
                if ($checkStmt->fetch()) {
                    $error = 'البريد الإلكتروني مستخدم من قبل مستخدم آخر';
                } else {
                    // تحديث البيانات
                    $updateStmt = $pdo->prepare("
                        UPDATE users 
                        SET full_name = ?, email = ?, phone = ?, country = ?, birth_date = ?, gender = ?, language = ?, updated_at = NOW()
                        WHERE id = ?
                    ");
                    
                    $updateStmt->execute([
                        $full_name, $email, $phone, $country, 
                        $birth_date ?: null, $gender ?: null, $language, $user_id
                    ]);
                    
                    // تحديث بيانات الجلسة
                    $_SESSION['email'] = $email;
                    $_SESSION['full_name'] = $full_name;
                    
                    $success = 'تم تحديث الملف الشخصي بنجاح';
                    
                    // إعادة جلب البيانات المحدثة
                    $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
                    $stmt->execute([$user_id]);
                    $user = $stmt->fetch(PDO::FETCH_ASSOC);
                }
            }
            
        } elseif (isset($_POST['change_password'])) {
            // تغيير كلمة المرور
            $current_password = $_POST['current_password'] ?? '';
            $new_password = $_POST['new_password'] ?? '';
            $confirm_password = $_POST['confirm_password'] ?? '';
            
            if (empty($current_password) || empty($new_password) || empty($confirm_password)) {
                $error = 'جميع حقول كلمة المرور مطلوبة';
            } elseif (strlen($new_password) < 6) {
                $error = 'كلمة المرور الجديدة يجب أن تكون 6 أحرف على الأقل';
            } elseif ($new_password !== $confirm_password) {
                $error = 'كلمة المرور الجديدة وتأكيدها غير متطابقتين';
            } elseif (!password_verify($current_password, $user['password'] ?? '')) {
                $error = 'كلمة المرور الحالية غير صحيحة';
            } else {
                // تحديث كلمة المرور
                $hashedPassword = password_hash($new_password, PASSWORD_DEFAULT);
                $updateStmt = $pdo->prepare("UPDATE users SET password = ?, updated_at = NOW() WHERE id = ?");
                $updateStmt->execute([$hashedPassword, $user_id]);
                
                $success = 'تم تغيير كلمة المرور بنجاح';
            }
        }
        
    } catch (Exception $e) {
        $error = 'حدث خطأ أثناء التحديث';
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الإعدادات - Shahid Platform</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', 'Cairo', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #2d2d2d 100%);
            color: #fff;
            min-height: 100vh;
        }
        
        .header {
            background: rgba(47, 47, 47, 0.95);
            padding: 1rem 0;
            border-bottom: 2px solid rgba(229, 9, 20, 0.3);
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 0 2rem;
        }
        
        .nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }
        
        .logo {
            color: #E50914;
            font-size: 1.8rem;
            font-weight: bold;
            text-decoration: none;
        }
        
        .nav-links {
            display: flex;
            gap: 2rem;
            align-items: center;
        }
        
        .nav-links a {
            color: #fff;
            text-decoration: none;
            transition: color 0.3s ease;
        }
        
        .nav-links a:hover, .nav-links a.active {
            color: #E50914;
        }
        
        .settings-section {
            padding: 3rem 0;
        }
        
        .page-header {
            text-align: center;
            margin-bottom: 3rem;
        }
        
        .page-title {
            font-size: 2.5rem;
            color: #E50914;
            margin-bottom: 0.5rem;
        }
        
        .page-subtitle {
            color: #ccc;
            font-size: 1.1rem;
        }
        
        .settings-tabs {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .tab-btn {
            padding: 1rem 2rem;
            background: rgba(47, 47, 47, 0.8);
            color: #ccc;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1rem;
        }
        
        .tab-btn.active {
            background: linear-gradient(45deg, #E50914, #B8070F);
            color: #fff;
        }
        
        .tab-btn:hover {
            background: rgba(229, 9, 20, 0.2);
            color: #fff;
        }
        
        .settings-content {
            background: rgba(47, 47, 47, 0.8);
            border-radius: 20px;
            padding: 3rem;
            border: 2px solid rgba(229, 9, 20, 0.1);
        }
        
        .tab-panel {
            display: none;
        }
        
        .tab-panel.active {
            display: block;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: #ccc;
            font-weight: 500;
        }
        
        .form-group input,
        .form-group select {
            width: 100%;
            padding: 1rem;
            border: 2px solid rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.05);
            color: #fff;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        
        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #E50914;
            background: rgba(255, 255, 255, 0.1);
        }
        
        .btn {
            padding: 1rem 2rem;
            background: linear-gradient(45deg, #E50914, #B8070F);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(229, 9, 20, 0.3);
        }
        
        .error {
            background: rgba(244, 67, 54, 0.1);
            border: 1px solid rgba(244, 67, 54, 0.3);
            color: #F44336;
            padding: 1rem;
            border-radius: 10px;
            margin-bottom: 1rem;
            text-align: center;
        }
        
        .success {
            background: rgba(76, 175, 80, 0.1);
            border: 1px solid rgba(76, 175, 80, 0.3);
            color: #4CAF50;
            padding: 1rem;
            border-radius: 10px;
            margin-bottom: 1rem;
            text-align: center;
        }
        
        .info-box {
            background: rgba(33, 150, 243, 0.1);
            border: 1px solid rgba(33, 150, 243, 0.3);
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .info-box h4 {
            color: #2196F3;
            margin-bottom: 0.5rem;
        }
        
        .info-box p {
            color: #ccc;
            line-height: 1.6;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 0 1rem;
            }
            
            .nav {
                flex-direction: column;
                gap: 1rem;
            }
            
            .settings-content {
                padding: 2rem;
            }
            
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .settings-tabs {
                flex-direction: column;
                align-items: center;
            }
            
            .tab-btn {
                width: 100%;
                max-width: 300px;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="container">
            <nav class="nav">
                <a href="index.php" class="logo">🎬 شاهد</a>
                <div class="nav-links">
                    <a href="index.php">الرئيسية</a>
                    <a href="movies.php">الأفلام</a>
                    <a href="series.php">المسلسلات</a>
                    <a href="search.php">البحث</a>
                    <a href="profile.php">الملف الشخصي</a>
                    <a href="favorites.php">المفضلة</a>
                    <a href="settings.php" class="active">الإعدادات</a>
                    <a href="logout.php">تسجيل الخروج</a>
                </div>
            </nav>
        </div>
    </header>
    
    <section class="settings-section">
        <div class="container">
            <div class="page-header">
                <h1 class="page-title">⚙️ الإعدادات</h1>
                <p class="page-subtitle">إدارة حسابك وتفضيلاتك</p>
            </div>
            
            <?php if ($error): ?>
                <div class="error">
                    <strong>خطأ:</strong> <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="success">
                    <?php echo htmlspecialchars($success); ?>
                </div>
            <?php endif; ?>
            
            <div class="settings-tabs">
                <button class="tab-btn active" onclick="showTab('profile')">👤 الملف الشخصي</button>
                <button class="tab-btn" onclick="showTab('password')">🔒 كلمة المرور</button>
                <button class="tab-btn" onclick="showTab('preferences')">🎛️ التفضيلات</button>
            </div>
            
            <div class="settings-content">
                <!-- تبويب الملف الشخصي -->
                <div id="profile-tab" class="tab-panel active">
                    <h3 style="color: #E50914; margin-bottom: 2rem;">👤 معلومات الملف الشخصي</h3>
                    
                    <form method="POST" action="">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="full_name">الاسم الكامل</label>
                                <input type="text" id="full_name" name="full_name" required
                                       value="<?php echo htmlspecialchars($user['full_name'] ?? $user['username'] ?? ''); ?>">
                            </div>
                            
                            <div class="form-group">
                                <label for="email">البريد الإلكتروني</label>
                                <input type="email" id="email" name="email" required
                                       value="<?php echo htmlspecialchars($user['email'] ?? ''); ?>">
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="phone">رقم الهاتف</label>
                                <input type="tel" id="phone" name="phone"
                                       value="<?php echo htmlspecialchars($user['phone'] ?? ''); ?>">
                            </div>
                            
                            <div class="form-group">
                                <label for="country">البلد</label>
                                <select id="country" name="country">
                                    <option value="">اختر البلد</option>
                                    <option value="SA" <?php echo ($user['country'] ?? '') === 'SA' ? 'selected' : ''; ?>>السعودية</option>
                                    <option value="AE" <?php echo ($user['country'] ?? '') === 'AE' ? 'selected' : ''; ?>>الإمارات</option>
                                    <option value="EG" <?php echo ($user['country'] ?? '') === 'EG' ? 'selected' : ''; ?>>مصر</option>
                                    <option value="JO" <?php echo ($user['country'] ?? '') === 'JO' ? 'selected' : ''; ?>>الأردن</option>
                                    <option value="LB" <?php echo ($user['country'] ?? '') === 'LB' ? 'selected' : ''; ?>>لبنان</option>
                                    <option value="KW" <?php echo ($user['country'] ?? '') === 'KW' ? 'selected' : ''; ?>>الكويت</option>
                                    <option value="QA" <?php echo ($user['country'] ?? '') === 'QA' ? 'selected' : ''; ?>>قطر</option>
                                    <option value="BH" <?php echo ($user['country'] ?? '') === 'BH' ? 'selected' : ''; ?>>البحرين</option>
                                    <option value="OM" <?php echo ($user['country'] ?? '') === 'OM' ? 'selected' : ''; ?>>عمان</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="birth_date">تاريخ الميلاد</label>
                                <input type="date" id="birth_date" name="birth_date"
                                       value="<?php echo $user['birth_date'] ?? ''; ?>">
                            </div>
                            
                            <div class="form-group">
                                <label for="gender">الجنس</label>
                                <select id="gender" name="gender">
                                    <option value="">اختر الجنس</option>
                                    <option value="male" <?php echo ($user['gender'] ?? '') === 'male' ? 'selected' : ''; ?>>ذكر</option>
                                    <option value="female" <?php echo ($user['gender'] ?? '') === 'female' ? 'selected' : ''; ?>>أنثى</option>
                                    <option value="other" <?php echo ($user['gender'] ?? '') === 'other' ? 'selected' : ''; ?>>آخر</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="language">اللغة المفضلة</label>
                            <select id="language" name="language">
                                <option value="ar" <?php echo ($user['language'] ?? 'ar') === 'ar' ? 'selected' : ''; ?>>العربية</option>
                                <option value="en" <?php echo ($user['language'] ?? 'ar') === 'en' ? 'selected' : ''; ?>>English</option>
                            </select>
                        </div>
                        
                        <button type="submit" name="update_profile" class="btn">
                            💾 حفظ التغييرات
                        </button>
                    </form>
                </div>
                
                <!-- تبويب كلمة المرور -->
                <div id="password-tab" class="tab-panel">
                    <h3 style="color: #E50914; margin-bottom: 2rem;">🔒 تغيير كلمة المرور</h3>
                    
                    <div class="info-box">
                        <h4>🛡️ نصائح الأمان</h4>
                        <p>استخدم كلمة مرور قوية تحتوي على أحرف كبيرة وصغيرة وأرقام ورموز. تجنب استخدام معلومات شخصية.</p>
                    </div>
                    
                    <form method="POST" action="">
                        <div class="form-group">
                            <label for="current_password">كلمة المرور الحالية</label>
                            <input type="password" id="current_password" name="current_password" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="new_password">كلمة المرور الجديدة</label>
                            <input type="password" id="new_password" name="new_password" required 
                                   minlength="6" placeholder="6 أحرف على الأقل">
                        </div>
                        
                        <div class="form-group">
                            <label for="confirm_password">تأكيد كلمة المرور الجديدة</label>
                            <input type="password" id="confirm_password" name="confirm_password" required>
                        </div>
                        
                        <button type="submit" name="change_password" class="btn">
                            🔐 تغيير كلمة المرور
                        </button>
                    </form>
                </div>
                
                <!-- تبويب التفضيلات -->
                <div id="preferences-tab" class="tab-panel">
                    <h3 style="color: #E50914; margin-bottom: 2rem;">🎛️ التفضيلات</h3>
                    
                    <div class="info-box">
                        <h4>📊 معلومات الاشتراك</h4>
                        <p>نوع الاشتراك الحالي: <strong><?php echo strtoupper($user['subscription_type'] ?? 'free'); ?></strong></p>
                        <p>تاريخ التسجيل: <?php echo date('Y-m-d', strtotime($user['created_at'] ?? 'now')); ?></p>
                    </div>
                    
                    <div style="text-align: center; margin-top: 2rem;">
                        <a href="subscriptions.php" class="btn" style="display: inline-block; text-decoration: none; max-width: 300px;">
                            💳 إدارة الاشتراك
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>
    
    <script>
        function showTab(tabName) {
            // إخفاء جميع التبويبات
            document.querySelectorAll('.tab-panel').forEach(panel => {
                panel.classList.remove('active');
            });
            
            // إزالة الفئة النشطة من جميع الأزرار
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            
            // إظهار التبويب المحدد
            document.getElementById(tabName + '-tab').classList.add('active');
            
            // إضافة الفئة النشطة للزر المحدد
            event.target.classList.add('active');
        }
        
        // التحقق من تطابق كلمة المرور
        document.getElementById('confirm_password').addEventListener('input', function() {
            const newPassword = document.getElementById('new_password').value;
            const confirmPassword = this.value;
            
            if (confirmPassword && newPassword !== confirmPassword) {
                this.setCustomValidity('كلمات المرور غير متطابقة');
            } else {
                this.setCustomValidity('');
            }
        });
    </script>
</body>
</html>
