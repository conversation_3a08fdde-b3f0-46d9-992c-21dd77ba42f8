<?php
/**
 * Shahid - Authentication Controller
 * Professional Video Streaming Platform
 */

class AuthController extends Controller {
    
    public function login() {
        // If already logged in, redirect to home
        if ($this->auth->isLoggedIn()) {
            $this->redirect('/');
            return;
        }
        
        if ($_POST) {
            $this->validateCSRF();
            
            $email = $this->sanitizeInput($_POST['email'] ?? '');
            $password = $_POST['password'] ?? '';
            $remember = isset($_POST['remember']);
            
            $validation = $this->validateInput($_POST, [
                'email' => ['required' => true, 'email' => true],
                'password' => ['required' => true, 'min_length' => 6]
            ]);
            
            if (empty($validation)) {
                $result = $this->auth->login($email, $password, $remember);
                
                if ($result['success']) {
                    $redirectUrl = $_SESSION['redirect_after_login'] ?? '/';
                    unset($_SESSION['redirect_after_login']);
                    $this->redirect($redirectUrl);
                    return;
                } else {
                    $error = $result['message'];
                }
            } else {
                $error = 'Please check your input and try again.';
            }
        }
        
        $data = [
            'page_title' => 'Login - ' . $this->config['site']['name'],
            'error' => $error ?? null,
            'csrf_token' => $this->security->generateCSRFToken()
        ];
        
        $this->view('auth/login', $data);
    }
    
    public function register() {
        // If already logged in, redirect to home
        if ($this->auth->isLoggedIn()) {
            $this->redirect('/');
            return;
        }
        
        // Check if registration is enabled
        if (!$this->getSetting('registration_enabled', true)) {
            $this->view('errors/403', ['message' => 'Registration is currently disabled']);
            return;
        }
        
        if ($_POST) {
            $this->validateCSRF();
            
            $data = [
                'name' => $this->sanitizeInput($_POST['name'] ?? ''),
                'email' => $this->sanitizeInput($_POST['email'] ?? ''),
                'password' => $_POST['password'] ?? '',
                'confirm_password' => $_POST['confirm_password'] ?? ''
            ];
            
            $validation = $this->validateInput($data, [
                'name' => ['required' => true, 'min_length' => 2, 'max_length' => 255],
                'email' => ['required' => true, 'email' => true],
                'password' => ['required' => true, 'min_length' => 8],
                'confirm_password' => ['required' => true]
            ]);
            
            // Check password confirmation
            if ($data['password'] !== $data['confirm_password']) {
                $validation['confirm_password'][] = 'Passwords do not match';
            }
            
            if (empty($validation)) {
                $result = $this->auth->register($data);
                
                if ($result['success']) {
                    // Auto login after registration
                    $loginResult = $this->auth->login($data['email'], $data['password']);
                    
                    if ($loginResult['success']) {
                        $this->redirect('/');
                        return;
                    } else {
                        $success = 'Account created successfully! Please login.';
                        $this->redirect('/login');
                        return;
                    }
                } else {
                    $errors = $result['errors'] ?? [$result['message']];
                }
            } else {
                $errors = array_merge(...array_values($validation));
            }
        }
        
        $data = [
            'page_title' => 'Register - ' . $this->config['site']['name'],
            'errors' => $errors ?? null,
            'csrf_token' => $this->security->generateCSRFToken(),
            'form_data' => $_POST ?? []
        ];
        
        $this->view('auth/register', $data);
    }
    
    public function logout() {
        $this->auth->logout();
        $this->redirect('/');
    }
    
    public function forgotPassword() {
        if ($_POST) {
            $this->validateCSRF();
            
            $email = $this->sanitizeInput($_POST['email'] ?? '');
            
            if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
                $error = 'Please enter a valid email address';
            } else {
                // Check if email exists
                $userModel = new User();
                $user = $userModel->findByEmail($email);
                
                if ($user) {
                    // Generate reset token
                    $token = $this->security->generateToken();
                    $expires = date('Y-m-d H:i:s', strtotime('+1 hour'));
                    
                    // Save reset token
                    $userModel->update($user[0]['id'], [
                        'reset_token' => hash('sha256', $token),
                        'reset_expires' => $expires
                    ]);
                    
                    // Send reset email (implement email service)
                    $this->sendPasswordResetEmail($email, $token);
                }
                
                // Always show success message for security
                $success = 'If an account with that email exists, we\'ve sent password reset instructions.';
            }
        }
        
        $data = [
            'page_title' => 'Forgot Password - ' . $this->config['site']['name'],
            'error' => $error ?? null,
            'success' => $success ?? null,
            'csrf_token' => $this->security->generateCSRFToken()
        ];
        
        $this->view('auth/forgot-password', $data);
    }
    
    public function resetPassword() {
        $token = $_GET['token'] ?? '';
        
        if (empty($token)) {
            $this->redirect('/login');
            return;
        }
        
        // Verify token
        $userModel = new User();
        $hashedToken = hash('sha256', $token);
        
        $sql = "SELECT * FROM users 
                WHERE reset_token = :token 
                AND reset_expires > NOW() 
                AND status = 'active'";
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':token', $hashedToken);
        $stmt->execute();
        $user = $stmt->fetch();
        
        if (!$user) {
            $error = 'Invalid or expired reset token';
            $this->view('auth/reset-password', ['error' => $error]);
            return;
        }
        
        if ($_POST) {
            $this->validateCSRF();
            
            $password = $_POST['password'] ?? '';
            $confirmPassword = $_POST['confirm_password'] ?? '';
            
            $validation = $this->validateInput($_POST, [
                'password' => ['required' => true, 'min_length' => 8],
                'confirm_password' => ['required' => true]
            ]);
            
            if ($password !== $confirmPassword) {
                $validation['confirm_password'][] = 'Passwords do not match';
            }
            
            if (empty($validation)) {
                // Update password and clear reset token
                $userModel->update($user['id'], [
                    'password' => password_hash($password, PASSWORD_DEFAULT),
                    'reset_token' => null,
                    'reset_expires' => null
                ]);
                
                $success = 'Password reset successfully! Please login with your new password.';
                $this->redirect('/login');
                return;
            } else {
                $errors = array_merge(...array_values($validation));
            }
        }
        
        $data = [
            'page_title' => 'Reset Password - ' . $this->config['site']['name'],
            'token' => $token,
            'errors' => $errors ?? null,
            'csrf_token' => $this->security->generateCSRFToken()
        ];
        
        $this->view('auth/reset-password', $data);
    }
    
    private function sendPasswordResetEmail($email, $token) {
        // Implement email sending logic here
        // This would typically use a service like PHPMailer or similar
        
        $resetUrl = $this->config['site']['url'] . '/reset-password?token=' . $token;
        $subject = 'Password Reset - ' . $this->config['site']['name'];
        $message = "Click the following link to reset your password: " . $resetUrl;
        
        // For now, just log it (implement proper email service)
        error_log("Password reset email for $email: $resetUrl");
        
        return true;
    }
    
    private function getSetting($key, $default = null) {
        try {
            $sql = "SELECT value FROM settings WHERE `key` = :key";
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':key', $key);
            $stmt->execute();
            $result = $stmt->fetch();
            
            return $result ? $result['value'] : $default;
        } catch (Exception $e) {
            return $default;
        }
    }
}
?>
