# 🔍 دليل نظام المراقبة المباشرة

## نظرة عامة

تم إنشاء نظام مراقبة مباشر متطور لمراقبة جميع مكونات منصة شاهد في الوقت الفعلي.

## 📁 الملفات المضافة

### 1. ملفات المراقبة الرئيسية
- `dashboard_live.php` - لوحة المراقبة المباشرة الجديدة (محسنة)
- `live_monitoring.php` - نسخة أساسية من المراقبة المباشرة
- `system_status.php` - تم تحديثه لإضافة مراقبة API

### 2. ملفات الأصول (Assets)
- `assets/monitoring-style.css` - تصميم متطور للوحة المراقبة
- `assets/monitoring-script.js` - سكريبت JavaScript للتحديث التلقائي

## 🎯 المكونات المراقبة

### 1. **API** ✅
- مراقبة جميع نقاط API
- قياس وقت الاستجابة
- نسبة الجاهزية
- حالة الملفات

### 2. **قاعدة البيانات** 💾
- حالة الاتصال
- عدد الجداول
- حجم قاعدة البيانات
- زمن الاستجابة (ping)

### 3. **الجداول** 📊
- فحص الجداول المطلوبة
- عدد السجلات في كل جدول
- الجداول المفقودة
- تحليل مفصل

### 4. **حساب المدير** 👤
- وجود حسابات المديرين
- الجلسات النشطة
- آخر نشاط
- عدد المديرين

## 🚀 كيفية الاستخدام

### الوصول للوحة المراقبة

```
# اللوحة المحسنة (موصى بها)
http://localhost/amr2/flutter_module_1/backend/dashboard_live.php

# اللوحة الأساسية
http://localhost/amr2/flutter_module_1/backend/live_monitoring.php

# حالة النظام المحدثة
http://localhost/amr2/flutter_module_1/backend/system_status.php
```

### الحصول على البيانات عبر API

```javascript
// الحصول على جميع البيانات
fetch('dashboard_live.php?ajax=1')
  .then(response => response.json())
  .then(data => console.log(data));

// البيانات المتاحة:
// - data.api (حالة API)
// - data.database (حالة قاعدة البيانات)
// - data.admin (حالة حساب المدير)
// - data.tables (حالة الجداول)
// - data.overall (الحالة العامة)
```

## ⚙️ الميزات المتقدمة

### 1. التحديث التلقائي
- تحديث كل 5 ثوانٍ
- إيقاف التحديث عند إخفاء التبويب
- إعادة المحاولة التلقائية عند الأخطاء

### 2. التفاعل
- النقر على أي مكون لتحديثه فوراً
- اختصارات لوحة المفاتيح (F5, Ctrl+R)
- رسائل خطأ تفاعلية

### 3. التصميم المتجاوب
- يعمل على جميع الأجهزة
- تصميم داكن احترافي
- رسوم متحركة سلسة

## 🎨 التخصيص

### تغيير فترة التحديث
```javascript
// في وحدة التحكم (Console)
setUpdateInterval(10000); // 10 ثوانٍ
```

### إجبار التحديث
```javascript
forceUpdate(); // تحديث فوري
```

### فحص حالة النظام
```javascript
getMonitorStatus(); // معلومات النظام
```

## 📊 مؤشرات الحالة

### ألوان المؤشرات
- 🟢 **أخضر**: يعمل بشكل طبيعي
- 🟡 **أصفر**: تحذير - يحتاج انتباه
- 🔴 **أحمر**: خطأ - يحتاج إصلاح فوري

### نقاط الصحة العامة
- **90-100%**: ممتاز 🟢
- **70-89%**: جيد 🟡
- **50-69%**: تحذير 🟠
- **أقل من 50%**: حرج 🔴

## 🔧 استكشاف الأخطاء

### مشاكل شائعة

1. **عدم تحديث البيانات**
   - تأكد من تشغيل الخادم
   - فحص وحدة التحكم للأخطاء
   - إعادة تحميل الصفحة

2. **خطأ في قاعدة البيانات**
   - تأكد من تشغيل MySQL
   - فحص بيانات الاتصال
   - تشغيل `fix_database.php`

3. **مشاكل API**
   - تأكد من وجود ملفات API
   - فحص صلاحيات الملفات
   - مراجعة سجل الأخطاء

## 📝 ملاحظات مهمة

1. **الأمان**: النظام مصمم للاستخدام الداخلي فقط
2. **الأداء**: التحديث التلقائي قد يؤثر على الأداء مع عدد كبير من المستخدمين
3. **التوافق**: يتطلب PHP 7.4+ و MySQL 5.7+

## 🔄 التحديثات المستقبلية

- [ ] إضافة مراقبة استخدام الذاكرة
- [ ] مراقبة مساحة القرص الصلب
- [ ] تنبيهات عبر البريد الإلكتروني
- [ ] تصدير التقارير
- [ ] مراقبة الأداء التفصيلية

## 📞 الدعم

في حالة وجود مشاكل أو اقتراحات، يرجى:
1. فحص وحدة التحكم للأخطاء
2. مراجعة سجل أخطاء PHP
3. التأكد من تشغيل جميع الخدمات المطلوبة

---

**تم إنشاؤه بواسطة**: نظام المراقبة المباشرة لمنصة شاهد  
**التاريخ**: <?php echo date('Y-m-d'); ?>  
**الإصدار**: 1.0.0
