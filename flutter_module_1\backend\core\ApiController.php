<?php
/**
 * Shahid - API Controller Base Class
 * Professional Video Streaming Platform
 */

abstract class ApiController {
    protected $db;
    protected $security;
    protected $user;
    protected $requestMethod;
    protected $requestData;
    
    public function __construct() {
        $this->db = Database::getInstance()->getConnection();
        $this->security = new Security();
        $this->requestMethod = $_SERVER['REQUEST_METHOD'];
        $this->requestData = $this->getRequestData();
        
        // Set JSON headers
        header('Content-Type: application/json');
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
        header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
    }
    
    /**
     * Authenticate user from JWT token
     */
    protected function authenticateUser($required = true) {
        $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
        
        if (!$authHeader || !preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
            if ($required) {
                $this->sendError('Authorization token required', 401);
                return false;
            }
            return null;
        }
        
        $token = $matches[1];
        $payload = $this->security->validateJWT($token);
        
        if (!$payload) {
            if ($required) {
                $this->sendError('Invalid or expired token', 401);
                return false;
            }
            return null;
        }
        
        $userModel = new User();
        $user = $userModel->findById($payload['user_id']);
        
        if (!$user || $user['status'] !== 'active') {
            if ($required) {
                $this->sendError('User not found or inactive', 401);
                return false;
            }
            return null;
        }
        
        $this->user = $user;
        return $user;
    }
    
    /**
     * Require admin role
     */
    protected function requireAdmin() {
        $user = $this->authenticateUser();
        if (!$user) return false;
        
        if ($user['role'] !== 'admin') {
            $this->sendError('Admin access required', 403);
            return false;
        }
        
        return true;
    }
    
    /**
     * Check if user has active subscription
     */
    protected function requireSubscription() {
        $user = $this->authenticateUser();
        if (!$user) return false;
        
        $userModel = new User();
        if (!$userModel->hasActiveSubscription($user['id'])) {
            $this->sendError('Active subscription required', 403);
            return false;
        }
        
        return true;
    }
    
    /**
     * Validate request method
     */
    protected function validateMethod($allowedMethods) {
        if (!in_array($this->requestMethod, $allowedMethods)) {
            $this->sendError('Method not allowed', 405);
            return false;
        }
        return true;
    }
    
    /**
     * Validate required fields
     */
    protected function validateRequired($fields) {
        $missing = [];
        
        foreach ($fields as $field) {
            if (!isset($this->requestData[$field]) || empty($this->requestData[$field])) {
                $missing[] = $field;
            }
        }
        
        if (!empty($missing)) {
            $this->sendError('Missing required fields: ' . implode(', ', $missing), 400);
            return false;
        }
        
        return true;
    }
    
    /**
     * Sanitize input data
     */
    protected function sanitizeInput($data) {
        if (is_array($data)) {
            return array_map([$this, 'sanitizeInput'], $data);
        }
        
        return htmlspecialchars(trim($data), ENT_QUOTES, 'UTF-8');
    }
    
    /**
     * Validate pagination parameters
     */
    protected function getPaginationParams($defaultLimit = 20, $maxLimit = 100) {
        $page = max(1, intval($_GET['page'] ?? 1));
        $limit = min($maxLimit, max(1, intval($_GET['limit'] ?? $defaultLimit)));
        $offset = ($page - 1) * $limit;
        
        return [
            'page' => $page,
            'limit' => $limit,
            'offset' => $offset
        ];
    }
    
    /**
     * Get request data from JSON or POST
     */
    protected function getRequestData() {
        $input = file_get_contents('php://input');
        $data = json_decode($input, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            return $_POST; // Fallback to POST data
        }
        
        return $data ?: [];
    }
    
    /**
     * Send JSON response
     */
    protected function sendResponse($data, $statusCode = 200) {
        http_response_code($statusCode);
        echo json_encode($data, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
        exit;
    }
    
    /**
     * Send error response
     */
    protected function sendError($message, $statusCode = 400, $details = null) {
        http_response_code($statusCode);
        
        $response = [
            'success' => false,
            'error' => $message,
            'timestamp' => date('c')
        ];
        
        if ($details) {
            $response['details'] = $details;
        }
        
        echo json_encode($response, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
        exit;
    }
    
    /**
     * Send success response
     */
    protected function sendSuccess($data = null, $message = null, $statusCode = 200) {
        $response = [
            'success' => true,
            'timestamp' => date('c')
        ];
        
        if ($message) {
            $response['message'] = $message;
        }
        
        if ($data !== null) {
            $response['data'] = $data;
        }
        
        $this->sendResponse($response, $statusCode);
    }
    
    /**
     * Send paginated response
     */
    protected function sendPaginatedResponse($data, $total, $page, $limit) {
        $response = [
            'success' => true,
            'data' => $data,
            'pagination' => [
                'current_page' => $page,
                'per_page' => $limit,
                'total' => $total,
                'total_pages' => ceil($total / $limit),
                'has_next' => $page < ceil($total / $limit),
                'has_prev' => $page > 1
            ],
            'timestamp' => date('c')
        ];
        
        $this->sendResponse($response);
    }
    
    /**
     * Log API request
     */
    protected function logRequest($action = null, $details = null) {
        $logData = [
            'timestamp' => date('Y-m-d H:i:s'),
            'method' => $this->requestMethod,
            'uri' => $_SERVER['REQUEST_URI'],
            'user_id' => $this->user['id'] ?? null,
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            'action' => $action,
            'details' => $details
        ];
        
        // Log to database or file
        error_log('API Request: ' . json_encode($logData));
    }
    
    /**
     * Rate limiting check
     */
    protected function checkRateLimit($identifier = null, $maxRequests = 100, $timeWindow = 3600) {
        if (!$identifier) {
            $identifier = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        }
        
        $cacheKey = "rate_limit:{$identifier}";
        
        // Simple file-based rate limiting (in production, use Redis or Memcached)
        $rateLimitFile = sys_get_temp_dir() . "/shahid_rate_limit_" . md5($cacheKey);
        
        $currentTime = time();
        $requests = [];
        
        if (file_exists($rateLimitFile)) {
            $data = json_decode(file_get_contents($rateLimitFile), true);
            $requests = $data['requests'] ?? [];
        }
        
        // Remove old requests outside the time window
        $requests = array_filter($requests, function($timestamp) use ($currentTime, $timeWindow) {
            return ($currentTime - $timestamp) < $timeWindow;
        });
        
        // Check if limit exceeded
        if (count($requests) >= $maxRequests) {
            $this->sendError('Rate limit exceeded', 429);
            return false;
        }
        
        // Add current request
        $requests[] = $currentTime;
        
        // Save updated requests
        file_put_contents($rateLimitFile, json_encode(['requests' => $requests]));
        
        return true;
    }
    
    /**
     * Validate email format
     */
    protected function validateEmail($email) {
        return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
    }
    
    /**
     * Validate password strength
     */
    protected function validatePassword($password) {
        if (strlen($password) < 8) {
            return 'Password must be at least 8 characters long';
        }
        
        if (!preg_match('/[A-Z]/', $password)) {
            return 'Password must contain at least one uppercase letter';
        }
        
        if (!preg_match('/[a-z]/', $password)) {
            return 'Password must contain at least one lowercase letter';
        }
        
        if (!preg_match('/[0-9]/', $password)) {
            return 'Password must contain at least one number';
        }
        
        return null; // Valid password
    }
    
    /**
     * Generate video streaming token
     */
    protected function generateVideoToken($contentId, $userId, $expiresIn = 3600) {
        $payload = [
            'content_id' => $contentId,
            'user_id' => $userId,
            'type' => 'video_stream',
            'iat' => time(),
            'exp' => time() + $expiresIn
        ];
        
        return $this->security->generateJWT($payload);
    }
    
    /**
     * Check content access permissions
     */
    protected function checkContentAccess($contentId, $contentType = 'movie') {
        $user = $this->authenticateUser();
        if (!$user) return false;
        
        // Get content info
        if ($contentType === 'movie') {
            $movieModel = new Movie();
            $content = $movieModel->findById($contentId);
        } else {
            $seriesModel = new Series();
            $content = $seriesModel->findById($contentId);
        }
        
        if (!$content) {
            $this->sendError('Content not found', 404);
            return false;
        }
        
        // Check if content is premium and user has subscription
        if ($content['premium']) {
            $userModel = new User();
            if (!$userModel->hasActiveSubscription($user['id'])) {
                $this->sendError('Premium subscription required', 403);
                return false;
            }
        }
        
        return $content;
    }
    
    /**
     * Handle file uploads
     */
    protected function handleFileUpload($fileKey, $allowedTypes = [], $maxSize = 10485760) {
        if (!isset($_FILES[$fileKey])) {
            return null;
        }
        
        $file = $_FILES[$fileKey];
        
        if ($file['error'] !== UPLOAD_ERR_OK) {
            $this->sendError('File upload error', 400);
            return false;
        }
        
        if ($file['size'] > $maxSize) {
            $this->sendError('File too large', 400);
            return false;
        }
        
        if (!empty($allowedTypes)) {
            $fileType = mime_content_type($file['tmp_name']);
            if (!in_array($fileType, $allowedTypes)) {
                $this->sendError('Invalid file type', 400);
                return false;
            }
        }
        
        return $file;
    }
}
?>
