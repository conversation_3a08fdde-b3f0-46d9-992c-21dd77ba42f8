<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة الإدارة الشاملة - Shahid Platform</title>
    <link rel="icon" href="../assets/images/favicon.ico" type="image/x-icon">
    <style>
        :root {
            --primary-color: #E50914;
            --secondary-color: #B8070F;
            --dark-bg: #0f0f0f;
            --sidebar-bg: #1a1a1a;
            --card-bg: rgba(47, 47, 47, 0.9);
            --text-primary: #ffffff;
            --text-secondary: #cccccc;
            --success-color: #4CAF50;
            --warning-color: #FF9800;
            --error-color: #F44336;
            --info-color: #2196F3;
            --border-color: rgba(255, 255, 255, 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', 'Cairo', 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, var(--dark-bg) 0%, #1a1a1a 50%, #2d2d2d 100%);
            color: var(--text-primary);
            line-height: 1.6;
            overflow-x: hidden;
        }

        /* Layout */
        .dashboard-container {
            display: flex;
            min-height: 100vh;
        }

        /* Sidebar */
        .sidebar {
            width: 280px;
            background: var(--sidebar-bg);
            border-left: 1px solid var(--border-color);
            position: fixed;
            height: 100vh;
            overflow-y: auto;
            z-index: 1000;
            transition: transform 0.3s ease;
        }

        .sidebar-header {
            padding: 2rem 1.5rem;
            border-bottom: 1px solid var(--border-color);
            text-align: center;
        }

        .sidebar-logo {
            font-size: 1.8rem;
            font-weight: bold;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }

        .sidebar-subtitle {
            font-size: 0.9rem;
            color: var(--text-secondary);
        }

        .sidebar-nav {
            padding: 1rem 0;
        }

        .nav-section {
            margin-bottom: 2rem;
        }

        .nav-section-title {
            padding: 0.5rem 1.5rem;
            font-size: 0.8rem;
            font-weight: bold;
            color: var(--text-secondary);
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .nav-item {
            display: block;
            padding: 1rem 1.5rem;
            color: var(--text-secondary);
            text-decoration: none;
            transition: all 0.3s ease;
            border-right: 3px solid transparent;
        }

        .nav-item:hover,
        .nav-item.active {
            background: rgba(229, 9, 20, 0.1);
            color: var(--primary-color);
            border-right-color: var(--primary-color);
        }

        .nav-item i {
            margin-left: 0.75rem;
            width: 20px;
            text-align: center;
        }

        /* Main Content */
        .main-content {
            flex: 1;
            margin-right: 280px;
            padding: 2rem;
        }

        /* Header */
        .content-header {
            background: var(--card-bg);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            border: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .header-title {
            font-size: 2rem;
            color: var(--primary-color);
            font-weight: bold;
        }

        .header-actions {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .btn-primary {
            background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
            color: white;
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.1);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        }

        /* Stats Grid */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: var(--card-bg);
            border-radius: 15px;
            padding: 2rem;
            border: 1px solid var(--border-color);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.4);
        }

        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .stat-title {
            font-size: 0.9rem;
            color: var(--text-secondary);
            font-weight: 600;
        }

        .stat-icon {
            font-size: 2rem;
            opacity: 0.7;
        }

        .stat-value {
            font-size: 2.5rem;
            font-weight: bold;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }

        .stat-change {
            font-size: 0.8rem;
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }

        .stat-change.positive {
            color: var(--success-color);
        }

        .stat-change.negative {
            color: var(--error-color);
        }

        /* Content Grid */
        .content-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .content-card {
            background: var(--card-bg);
            border-radius: 15px;
            padding: 2rem;
            border: 1px solid var(--border-color);
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid var(--border-color);
        }

        .card-title {
            font-size: 1.3rem;
            font-weight: bold;
            color: var(--primary-color);
        }

        /* Table */
        .table-container {
            overflow-x: auto;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th,
        .table td {
            padding: 1rem;
            text-align: right;
            border-bottom: 1px solid var(--border-color);
        }

        .table th {
            background: rgba(229, 9, 20, 0.1);
            color: var(--primary-color);
            font-weight: 600;
            font-size: 0.9rem;
        }

        .table td {
            color: var(--text-secondary);
        }

        .table tr:hover {
            background: rgba(255, 255, 255, 0.05);
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <aside class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <div class="sidebar-logo">🎬 SHAHID</div>
                <div class="sidebar-subtitle">لوحة الإدارة الشاملة</div>
            </div>

            <nav class="sidebar-nav">
                <div class="nav-section">
                    <div class="nav-section-title">الرئيسية</div>
                    <a href="#dashboard" class="nav-item active" data-section="dashboard">
                        <i>📊</i> لوحة المعلومات
                    </a>
                    <a href="#analytics" class="nav-item" data-section="analytics">
                        <i>📈</i> التحليلات
                    </a>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">المحتوى</div>
                    <a href="#movies" class="nav-item" data-section="movies">
                        <i>🎬</i> الأفلام
                    </a>
                    <a href="#series" class="nav-item" data-section="series">
                        <i>📺</i> المسلسلات
                    </a>
                    <a href="#categories" class="nav-item" data-section="categories">
                        <i>🏷️</i> التصنيفات
                    </a>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">المستخدمون</div>
                    <a href="#users" class="nav-item" data-section="users">
                        <i>👥</i> المستخدمون
                    </a>
                    <a href="#subscriptions" class="nav-item" data-section="subscriptions">
                        <i>💎</i> الاشتراكات
                    </a>
                    <a href="#payments" class="nav-item" data-section="payments">
                        <i>💳</i> المدفوعات
                    </a>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">النظام</div>
                    <a href="#settings" class="nav-item" data-section="settings">
                        <i>⚙️</i> الإعدادات
                    </a>
                    <a href="#security" class="nav-item" data-section="security">
                        <i>🔒</i> الأمان
                    </a>
                    <a href="#logs" class="nav-item" data-section="logs">
                        <i>📋</i> السجلات
                    </a>
                </div>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Header -->
            <div class="content-header">
                <div>
                    <h1 class="header-title">لوحة المعلومات</h1>
                    <p style="color: var(--text-secondary); margin-top: 0.5rem;">
                        مرحباً بك في لوحة الإدارة الشاملة
                    </p>
                </div>
                <div class="header-actions">
                    <button class="btn btn-secondary" onclick="refreshData()">
                        <i>🔄</i> تحديث البيانات
                    </button>
                    <a href="../final_homepage.php" class="btn btn-primary">
                        <i>🏠</i> الصفحة الرئيسية
                    </a>
                </div>
            </div>

            <!-- Stats Grid -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-header">
                        <span class="stat-title">إجمالي المستخدمين</span>
                        <span class="stat-icon">👥</span>
                    </div>
                    <div class="stat-value" id="totalUsers">
                        <span class="loading"></span>
                    </div>
                    <div class="stat-change positive">
                        <span>↗</span> +12% هذا الشهر
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-header">
                        <span class="stat-title">إجمالي المحتوى</span>
                        <span class="stat-icon">🎬</span>
                    </div>
                    <div class="stat-value" id="totalContent">
                        <span class="loading"></span>
                    </div>
                    <div class="stat-change positive">
                        <span>↗</span> +5 هذا الأسبوع
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-header">
                        <span class="stat-title">الاشتراكات النشطة</span>
                        <span class="stat-icon">💎</span>
                    </div>
                    <div class="stat-value" id="activeSubscriptions">
                        <span class="loading"></span>
                    </div>
                    <div class="stat-change positive">
                        <span>↗</span> +8% هذا الشهر
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-header">
                        <span class="stat-title">الإيرادات الشهرية</span>
                        <span class="stat-icon">💰</span>
                    </div>
                    <div class="stat-value" id="monthlyRevenue">
                        <span class="loading"></span>
                    </div>
                    <div class="stat-change positive">
                        <span>↗</span> +15% هذا الشهر
                    </div>
                </div>
            </div>

            <!-- Content Grid -->
            <div class="content-grid">
                <!-- Recent Activity -->
                <div class="content-card">
                    <div class="card-header">
                        <h3 class="card-title">النشاط الأخير</h3>
                        <button class="btn btn-secondary" style="padding: 0.5rem 1rem; font-size: 0.8rem;">
                            عرض الكل
                        </button>
                    </div>
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>المستخدم</th>
                                    <th>النشاط</th>
                                    <th>الوقت</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody id="recentActivity">
                                <tr>
                                    <td>أحمد محمد</td>
                                    <td>مشاهدة فيلم "The Dark Knight"</td>
                                    <td>منذ 5 دقائق</td>
                                    <td><span class="status-badge status-active">نشط</span></td>
                                </tr>
                                <tr>
                                    <td>فاطمة علي</td>
                                    <td>اشتراك في الخطة المميزة</td>
                                    <td>منذ 15 دقيقة</td>
                                    <td><span class="status-badge status-active">مكتمل</span></td>
                                </tr>
                                <tr>
                                    <td>محمد حسن</td>
                                    <td>إضافة فيلم للمفضلة</td>
                                    <td>منذ 30 دقيقة</td>
                                    <td><span class="status-badge status-active">نشط</span></td>
                                </tr>
                                <tr>
                                    <td>سارة أحمد</td>
                                    <td>تسجيل حساب جديد</td>
                                    <td>منذ ساعة</td>
                                    <td><span class="status-badge status-pending">معلق</span></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="content-card">
                    <div class="card-header">
                        <h3 class="card-title">إجراءات سريعة</h3>
                    </div>
                    <div class="quick-actions">
                        <a href="#" class="quick-action" onclick="showSection('movies')">
                            <div class="quick-action-icon">🎬</div>
                            <div class="quick-action-title">إضافة فيلم</div>
                            <div class="quick-action-desc">رفع محتوى جديد</div>
                        </a>

                        <a href="#" class="quick-action" onclick="showSection('users')">
                            <div class="quick-action-icon">👤</div>
                            <div class="quick-action-title">إدارة المستخدمين</div>
                            <div class="quick-action-desc">عرض وتعديل الحسابات</div>
                        </a>

                        <a href="#" class="quick-action" onclick="showSection('analytics')">
                            <div class="quick-action-icon">📊</div>
                            <div class="quick-action-title">التقارير</div>
                            <div class="quick-action-desc">إحصائيات مفصلة</div>
                        </a>

                        <a href="#" class="quick-action" onclick="showSection('settings')">
                            <div class="quick-action-icon">⚙️</div>
                            <div class="quick-action-title">الإعدادات</div>
                            <div class="quick-action-desc">تخصيص النظام</div>
                        </a>

                        <a href="../system_status.php" class="quick-action" target="_blank">
                            <div class="quick-action-icon">🔍</div>
                            <div class="quick-action-title">حالة النظام</div>
                            <div class="quick-action-desc">مراقبة الأداء</div>
                        </a>

                        <a href="../api/test.php" class="quick-action" target="_blank">
                            <div class="quick-action-icon">🔗</div>
                            <div class="quick-action-title">اختبار API</div>
                            <div class="quick-action-desc">فحص الواجهات</div>
                        </a>
                    </div>
                </div>
            </div>

            <!-- System Status -->
            <div class="content-card">
                <div class="card-header">
                    <h3 class="card-title">حالة النظام المباشرة</h3>
                    <div style="display: flex; gap: 0.5rem; align-items: center;">
                        <span id="lastUpdate" style="font-size: 0.8rem; color: var(--text-secondary);">
                            آخر تحديث: جاري التحميل...
                        </span>
                        <button class="btn btn-secondary" style="padding: 0.5rem 1rem; font-size: 0.8rem;" onclick="checkSystemStatus()">
                            🔄 تحديث
                        </button>
                    </div>
                </div>
                <div class="stats-grid" style="margin-bottom: 0;">
                    <div class="stat-card" id="dbStatusCard">
                        <div class="stat-header">
                            <span class="stat-title">قاعدة البيانات</span>
                            <span class="stat-icon">🗄️</span>
                        </div>
                        <div class="stat-value" id="dbStatusValue">
                            <span class="loading"></span>
                        </div>
                        <div class="stat-change" id="dbStatusChange">
                            فحص الاتصال...
                        </div>
                    </div>

                    <div class="stat-card" id="apiStatusCard">
                        <div class="stat-header">
                            <span class="stat-title">API</span>
                            <span class="stat-icon">🔗</span>
                        </div>
                        <div class="stat-value" id="apiStatusValue">
                            <span class="loading"></span>
                        </div>
                        <div class="stat-change" id="apiStatusChange">
                            فحص الخدمات...
                        </div>
                    </div>

                    <div class="stat-card" id="serverStatusCard">
                        <div class="stat-header">
                            <span class="stat-title">الخادم</span>
                            <span class="stat-icon">🖥️</span>
                        </div>
                        <div class="stat-value" id="serverStatusValue">
                            <span class="loading"></span>
                        </div>
                        <div class="stat-change" id="serverStatusChange">
                            فحص الأداء...
                        </div>
                    </div>

                    <div class="stat-card" id="securityStatusCard">
                        <div class="stat-header">
                            <span class="stat-title">الأمان</span>
                            <span class="stat-icon">🔒</span>
                        </div>
                        <div class="stat-value" id="securityStatusValue">
                            <span class="loading"></span>
                        </div>
                        <div class="stat-change" id="securityStatusChange">
                            فحص الحماية...
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Notification -->
    <div id="notification" class="notification"></div>

    <!-- JavaScript -->
    <script>
        // Global variables
        let currentSection = 'dashboard';
        let refreshInterval;

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🎛️ Admin Dashboard loaded successfully!');
            initializeDashboard();
        });

        function initializeDashboard() {
            // Load initial data
            loadDashboardData();
            checkSystemStatus();

            // Set up auto-refresh
            refreshInterval = setInterval(() => {
                loadDashboardData();
                checkSystemStatus();
            }, 30000); // Refresh every 30 seconds

            // Set up navigation
            setupNavigation();
        }

        function setupNavigation() {
            document.querySelectorAll('.nav-item').forEach(item => {
                item.addEventListener('click', function(e) {
                    e.preventDefault();
                    const section = this.dataset.section;
                    if (section) {
                        showSection(section);
                    }
                });
            });
        }

        function showSection(section) {
            // Update active nav item
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });
            document.querySelector(`[data-section="${section}"]`).classList.add('active');

            // Update content based on section
            currentSection = section;
            updateHeaderTitle(section);

            showNotification(`تم التبديل إلى قسم ${getSectionName(section)}`, 'success');
        }

        function getSectionName(section) {
            const names = {
                dashboard: 'لوحة المعلومات',
                analytics: 'التحليلات',
                movies: 'الأفلام',
                series: 'المسلسلات',
                categories: 'التصنيفات',
                users: 'المستخدمون',
                subscriptions: 'الاشتراكات',
                payments: 'المدفوعات',
                settings: 'الإعدادات',
                security: 'الأمان',
                logs: 'السجلات'
            };
            return names[section] || section;
        }

        function updateHeaderTitle(section) {
            const title = document.querySelector('.header-title');
            title.textContent = getSectionName(section);
        }

        async function loadDashboardData() {
            try {
                // Simulate API calls for demo
                await new Promise(resolve => setTimeout(resolve, 1000));

                // Update stats with demo data
                document.getElementById('totalUsers').textContent = '1,247';
                document.getElementById('totalContent').textContent = '856';
                document.getElementById('activeSubscriptions').textContent = '423';
                document.getElementById('monthlyRevenue').textContent = '$12,450';

                console.log('✅ Dashboard data loaded');
            } catch (error) {
                console.error('❌ Error loading dashboard data:', error);
                showNotification('خطأ في تحميل البيانات', 'error');
            }
        }

        async function checkSystemStatus() {
            try {
                const response = await fetch('../system_status.php?status=1');
                const data = await response.json();

                // Update database status
                updateSystemStatus('db', data.database_connected, 'قاعدة البيانات');
                updateSystemStatus('api', true, 'API'); // API working if we got response
                updateSystemStatus('server', true, 'الخادم'); // Server working if page loads
                updateSystemStatus('security', true, 'الأمان'); // Security active

                // Update last update time
                document.getElementById('lastUpdate').textContent =
                    `آخر تحديث: ${new Date().toLocaleTimeString('ar-EG')}`;

                console.log('✅ System status updated');
            } catch (error) {
                console.error('❌ Error checking system status:', error);

                // Show error status for all
                ['db', 'api', 'server', 'security'].forEach(type => {
                    updateSystemStatus(type, false, getSystemLabel(type));
                });
            }
        }

        function updateSystemStatus(type, isOk, label) {
            const card = document.getElementById(type + 'StatusCard');
            const value = document.getElementById(type + 'StatusValue');
            const change = document.getElementById(type + 'StatusChange');

            if (card && value && change) {
                // Update card styling
                card.style.borderTopColor = isOk ? 'var(--success-color)' : 'var(--error-color)';

                // Update value
                value.textContent = isOk ? '✅' : '❌';
                value.style.color = isOk ? 'var(--success-color)' : 'var(--error-color)';

                // Update change text
                change.textContent = isOk ? 'يعمل بشكل طبيعي' : 'يحتاج إصلاح';
                change.className = 'stat-change ' + (isOk ? 'positive' : 'negative');
            }
        }

        function getSystemLabel(type) {
            const labels = {
                db: 'قاعدة البيانات',
                api: 'API',
                server: 'الخادم',
                security: 'الأمان'
            };
            return labels[type] || type;
        }

        function refreshData() {
            showNotification('جاري تحديث البيانات...', 'info');
            loadDashboardData();
            checkSystemStatus();
        }

        function showNotification(message, type = 'success') {
            const notification = document.getElementById('notification');
            notification.textContent = message;
            notification.className = `notification ${type} show`;

            setTimeout(() => {
                notification.classList.remove('show');
            }, 3000);
        }

        // Performance monitoring
        window.addEventListener('load', function() {
            const loadTime = performance.now();
            console.log(`⚡ Dashboard loaded in ${Math.round(loadTime)}ms`);
        });

        // Cleanup on page unload
        window.addEventListener('beforeunload', function() {
            if (refreshInterval) {
                clearInterval(refreshInterval);
            }
        });
    </script>
</body>
</html>
        }

        .table tr:hover {
            background: rgba(255, 255, 255, 0.05);
        }

        /* Status Badge */
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .status-active {
            background: rgba(76, 175, 80, 0.2);
            color: var(--success-color);
        }

        .status-inactive {
            background: rgba(244, 67, 54, 0.2);
            color: var(--error-color);
        }

        .status-pending {
            background: rgba(255, 152, 0, 0.2);
            color: var(--warning-color);
        }

        /* Quick Actions */
        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }

        .quick-action {
            background: var(--card-bg);
            border-radius: 10px;
            padding: 1.5rem;
            text-align: center;
            border: 1px solid var(--border-color);
            transition: all 0.3s ease;
            text-decoration: none;
            color: inherit;
        }

        .quick-action:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
            color: inherit;
        }

        .quick-action-icon {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            color: var(--primary-color);
        }

        .quick-action-title {
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .quick-action-desc {
            font-size: 0.8rem;
            color: var(--text-secondary);
        }

        /* Responsive */
        @media (max-width: 1024px) {
            .sidebar {
                transform: translateX(-100%);
            }

            .sidebar.open {
                transform: translateX(0);
            }

            .main-content {
                margin-right: 0;
            }

            .content-grid {
                grid-template-columns: 1fr;
            }

            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            }
        }

        @media (max-width: 768px) {
            .main-content {
                padding: 1rem;
            }

            .content-header {
                flex-direction: column;
                text-align: center;
            }

            .header-actions {
                width: 100%;
                justify-content: center;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .quick-actions {
                grid-template-columns: 1fr;
            }
        }

        /* Loading Animation */
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: var(--primary-color);
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* Notification */
        .notification {
            position: fixed;
            top: 2rem;
            left: 50%;
            transform: translateX(-50%);
            background: var(--success-color);
            color: white;
            padding: 1rem 2rem;
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
            z-index: 10000;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .notification.show {
            opacity: 1;
        }

        .notification.error {
            background: var(--error-color);
        }

        .notification.warning {
            background: var(--warning-color);
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <aside class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <div class="sidebar-logo">🎬 SHAHID</div>
                <div class="sidebar-subtitle">لوحة الإدارة الشاملة</div>
            </div>

            <nav class="sidebar-nav">
                <div class="nav-section">
                    <div class="nav-section-title">الرئيسية</div>
                    <a href="#dashboard" class="nav-item active" data-section="dashboard">
                        <i>📊</i> لوحة المعلومات
                    </a>
                    <a href="#analytics" class="nav-item" data-section="analytics">
                        <i>📈</i> التحليلات
                    </a>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">المحتوى</div>
                    <a href="#movies" class="nav-item" data-section="movies">
                        <i>🎬</i> الأفلام
                    </a>
                    <a href="#series" class="nav-item" data-section="series">
                        <i>📺</i> المسلسلات
                    </a>
                    <a href="#categories" class="nav-item" data-section="categories">
                        <i>🏷️</i> التصنيفات
                    </a>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">المستخدمون</div>
                    <a href="#users" class="nav-item" data-section="users">
                        <i>👥</i> المستخدمون
                    </a>
                    <a href="#subscriptions" class="nav-item" data-section="subscriptions">
                        <i>💎</i> الاشتراكات
                    </a>
                    <a href="#payments" class="nav-item" data-section="payments">
                        <i>💳</i> المدفوعات
                    </a>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">النظام</div>
                    <a href="#settings" class="nav-item" data-section="settings">
                        <i>⚙️</i> الإعدادات
                    </a>
                    <a href="#security" class="nav-item" data-section="security">
                        <i>🔒</i> الأمان
                    </a>
                    <a href="#logs" class="nav-item" data-section="logs">
                        <i>📋</i> السجلات
                    </a>
                </div>
            </nav>
        </aside>
