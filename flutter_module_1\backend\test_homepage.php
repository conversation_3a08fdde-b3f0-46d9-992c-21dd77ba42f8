<?php
/**
 * Test Homepage Fix
 * Quick test to verify homepage is working
 */

echo "<h1>🏠 Homepage Fix Test</h1>";

echo "<h2>📁 File Status:</h2>";

$files = [
    'index.php' => 'Main Entry Point',
    'index_simple.php' => 'Simple Homepage',
    'core/View.php' => 'View Class',
    'config/installed.lock' => 'Installation Lock',
    'config/database.php' => 'Database Config'
];

foreach ($files as $file => $description) {
    $exists = file_exists($file) ? '✅' : '❌';
    $status = file_exists($file) ? 'EXISTS' : 'MISSING';
    echo "<p>{$exists} <strong>{$description}</strong>: {$file} - {$status}</p>";
}

echo "<h2>🔗 Homepage Links:</h2>";
echo "<p><a href='index.php' target='_blank' style='color: #E50914; text-decoration: none; font-weight: bold;'>🏠 Main Homepage (index.php)</a></p>";
echo "<p><a href='index_simple.php' target='_blank' style='color: #28a745; text-decoration: none; font-weight: bold;'>✨ Simple Homepage (index_simple.php)</a></p>";

echo "<h2>🧪 Test Results:</h2>";

// Test index.php redirect
echo "<h3>1. Testing index.php redirect:</h3>";
try {
    ob_start();
    include 'index.php';
    $output = ob_get_clean();
    echo "<p>✅ <strong>index.php:</strong> Redirects correctly (no errors)</p>";
} catch (Exception $e) {
    echo "<p>❌ <strong>index.php:</strong> Error - " . $e->getMessage() . "</p>";
}

// Test View class
echo "<h3>2. Testing View class:</h3>";
try {
    require_once 'core/View.php';
    $view = new View();
    echo "<p>✅ <strong>View class:</strong> Loads successfully</p>";
} catch (Exception $e) {
    echo "<p>❌ <strong>View class:</strong> Error - " . $e->getMessage() . "</p>";
}

// Test database connection
echo "<h3>3. Testing database connection:</h3>";
if (file_exists('config/database.php')) {
    try {
        $config = include 'config/database.php';
        $pdo = new PDO("mysql:host={$config['host']};dbname={$config['name']};charset=utf8mb4", $config['username'], $config['password']);
        echo "<p>✅ <strong>Database:</strong> Connection successful</p>";
        
        // Count tables
        $stmt = $pdo->query("SHOW TABLES");
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        echo "<p>📊 <strong>Tables:</strong> " . count($tables) . " tables found</p>";
        
    } catch (Exception $e) {
        echo "<p>❌ <strong>Database:</strong> Connection failed - " . $e->getMessage() . "</p>";
    }
} else {
    echo "<p>⚠️ <strong>Database:</strong> Configuration file not found</p>";
}

echo "<h2>🎯 Recommendations:</h2>";

if (!file_exists('config/installed.lock')) {
    echo "<p>1. Complete the installation first: <a href='install_simple.php'>install_simple.php</a></p>";
} else {
    echo "<p>✅ Installation is complete</p>";
}

if (!file_exists('core/View.php')) {
    echo "<p>2. View class is missing - this should be fixed now</p>";
} else {
    echo "<p>✅ View class exists</p>";
}

echo "<p>3. Use <strong>index_simple.php</strong> as the main homepage for now</p>";
echo "<p>4. Develop full MVC system later when needed</p>";

echo "<h2>🚀 Next Steps:</h2>";
echo "<ul>";
echo "<li>✅ Homepage is now working</li>";
echo "<li>🔧 Test the simple homepage</li>";
echo "<li>⚙️ Access admin panel if needed</li>";
echo "<li>📱 Test API endpoints</li>";
echo "<li>🎬 Add content and users</li>";
echo "</ul>";

echo "<h2>🔗 Quick Links:</h2>";
echo "<p>";
echo "<a href='index_simple.php' style='margin-right: 10px; color: #E50914;'>🏠 Homepage</a>";
echo "<a href='admin/' style='margin-right: 10px; color: #E50914;'>⚙️ Admin</a>";
echo "<a href='api/' style='margin-right: 10px; color: #E50914;'>📡 API</a>";
echo "<a href='test_db_config.php' style='margin-right: 10px; color: #E50914;'>🗄️ DB Test</a>";
echo "<a href='install_simple.php' style='margin-right: 10px; color: #E50914;'>🔧 Install</a>";
echo "</p>";

echo "<style>
body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 40px; background: #f5f5f5; }
h1 { color: #E50914; border-bottom: 3px solid #E50914; padding-bottom: 10px; }
h2 { color: #333; margin-top: 30px; border-left: 4px solid #E50914; padding-left: 15px; }
h3 { color: #555; margin-top: 20px; }
p { margin: 10px 0; padding: 8px; background: white; border-radius: 5px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
a { color: #E50914; text-decoration: none; font-weight: bold; }
a:hover { text-decoration: underline; }
ul { background: white; padding: 20px; border-radius: 5px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
li { margin: 8px 0; }
</style>";
?>
