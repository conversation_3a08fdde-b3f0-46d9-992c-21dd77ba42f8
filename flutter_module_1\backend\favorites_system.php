<?php
/**
 * نظام المفضلة وسجل المشاهدة
 */

require_once 'config/database.php';

class FavoritesSystem {
    private $pdo;
    
    public function __construct() {
        try {
            $this->pdo = new PDO("mysql:host=localhost;dbname=shahid_platform;charset=utf8mb4", "root", "");
            $this->pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        } catch (PDOException $e) {
            throw new Exception("خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage());
        }
    }
    
    /**
     * إضافة/إزالة من المفضلة
     */
    public function toggleFavorite($userId, $contentId, $contentType) {
        try {
            // التحقق من وجود العنصر في المفضلة
            $stmt = $this->pdo->prepare("
                SELECT id FROM favorites 
                WHERE user_id = ? AND content_id = ? AND content_type = ?
            ");
            $stmt->execute([$userId, $contentId, $contentType]);
            
            if ($stmt->fetch()) {
                // إزالة من المفضلة
                $stmt = $this->pdo->prepare("
                    DELETE FROM favorites 
                    WHERE user_id = ? AND content_id = ? AND content_type = ?
                ");
                $stmt->execute([$userId, $contentId, $contentType]);
                
                return [
                    'success' => true,
                    'action' => 'removed',
                    'message' => 'تم إزالة العنصر من المفضلة'
                ];
            } else {
                // إضافة للمفضلة
                $stmt = $this->pdo->prepare("
                    INSERT INTO favorites (user_id, content_id, content_type, added_at)
                    VALUES (?, ?, ?, NOW())
                ");
                $stmt->execute([$userId, $contentId, $contentType]);
                
                return [
                    'success' => true,
                    'action' => 'added',
                    'message' => 'تم إضافة العنصر للمفضلة'
                ];
            }
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * الحصول على قائمة المفضلة للمستخدم
     */
    public function getUserFavorites($userId, $contentType = null, $limit = 50, $offset = 0) {
        try {
            $sql = "
                SELECT f.*, 
                       CASE 
                           WHEN f.content_type = 'movie' THEN m.title
                           WHEN f.content_type = 'series' THEN s.title
                       END as title,
                       CASE 
                           WHEN f.content_type = 'movie' THEN m.poster
                           WHEN f.content_type = 'series' THEN s.poster
                       END as poster,
                       CASE 
                           WHEN f.content_type = 'movie' THEN m.year
                           WHEN f.content_type = 'series' THEN s.year
                       END as year,
                       CASE 
                           WHEN f.content_type = 'movie' THEN m.rating
                           WHEN f.content_type = 'series' THEN s.rating
                       END as rating
                FROM favorites f
                LEFT JOIN movies m ON f.content_id = m.id AND f.content_type = 'movie'
                LEFT JOIN series s ON f.content_id = s.id AND f.content_type = 'series'
                WHERE f.user_id = ?
            ";
            
            $params = [$userId];
            
            if ($contentType) {
                $sql .= " AND f.content_type = ?";
                $params[] = $contentType;
            }
            
            $sql .= " ORDER BY f.added_at DESC LIMIT ? OFFSET ?";
            $params[] = $limit;
            $params[] = $offset;
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            return [];
        }
    }
    
    /**
     * التحقق من وجود عنصر في المفضلة
     */
    public function isFavorite($userId, $contentId, $contentType) {
        try {
            $stmt = $this->pdo->prepare("
                SELECT id FROM favorites 
                WHERE user_id = ? AND content_id = ? AND content_type = ?
            ");
            $stmt->execute([$userId, $contentId, $contentType]);
            
            return $stmt->fetch() !== false;
            
        } catch (Exception $e) {
            return false;
        }
    }
    
    /**
     * إضافة/تحديث سجل المشاهدة
     */
    public function updateWatchHistory($userId, $contentId, $contentType, $watchTime, $totalTime, $episodeId = null) {
        try {
            $completed = ($watchTime >= $totalTime * 0.9); // اعتبار المحتوى مكتمل إذا شوهد 90% منه
            
            // التحقق من وجود سجل سابق
            $stmt = $this->pdo->prepare("
                SELECT id FROM watch_history 
                WHERE user_id = ? AND content_id = ? AND content_type = ? AND episode_id = ?
            ");
            $stmt->execute([$userId, $contentId, $contentType, $episodeId]);
            
            if ($stmt->fetch()) {
                // تحديث السجل الموجود
                $stmt = $this->pdo->prepare("
                    UPDATE watch_history 
                    SET watch_time = ?, total_time = ?, completed = ?, last_watched = NOW()
                    WHERE user_id = ? AND content_id = ? AND content_type = ? AND episode_id = ?
                ");
                $stmt->execute([$watchTime, $totalTime, $completed, $userId, $contentId, $contentType, $episodeId]);
            } else {
                // إضافة سجل جديد
                $stmt = $this->pdo->prepare("
                    INSERT INTO watch_history (user_id, content_id, content_type, episode_id, watch_time, total_time, completed, last_watched)
                    VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
                ");
                $stmt->execute([$userId, $contentId, $contentType, $episodeId, $watchTime, $totalTime, $completed]);
            }
            
            return [
                'success' => true,
                'completed' => $completed,
                'progress' => round(($watchTime / $totalTime) * 100, 1)
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * الحصول على سجل المشاهدة للمستخدم
     */
    public function getUserWatchHistory($userId, $limit = 50, $offset = 0) {
        try {
            $stmt = $this->pdo->prepare("
                SELECT wh.*, 
                       CASE 
                           WHEN wh.content_type = 'movie' THEN m.title
                           WHEN wh.content_type = 'series' THEN s.title
                           WHEN wh.content_type = 'episode' THEN e.title
                       END as title,
                       CASE 
                           WHEN wh.content_type = 'movie' THEN m.poster
                           WHEN wh.content_type = 'series' THEN s.poster
                           WHEN wh.content_type = 'episode' THEN s2.poster
                       END as poster,
                       CASE 
                           WHEN wh.content_type = 'episode' THEN CONCAT('الموسم ', e.season, ' - الحلقة ', e.episode)
                           ELSE NULL
                       END as episode_info
                FROM watch_history wh
                LEFT JOIN movies m ON wh.content_id = m.id AND wh.content_type = 'movie'
                LEFT JOIN series s ON wh.content_id = s.id AND wh.content_type = 'series'
                LEFT JOIN episodes e ON wh.episode_id = e.id AND wh.content_type = 'episode'
                LEFT JOIN series s2 ON e.series_id = s2.id AND wh.content_type = 'episode'
                WHERE wh.user_id = ?
                ORDER BY wh.last_watched DESC
                LIMIT ? OFFSET ?
            ");
            
            $stmt->execute([$userId, $limit, $offset]);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            return [];
        }
    }
    
    /**
     * الحصول على المحتوى المستمر (لم يكتمل بعد)
     */
    public function getContinueWatching($userId, $limit = 10) {
        try {
            $stmt = $this->pdo->prepare("
                SELECT wh.*, 
                       CASE 
                           WHEN wh.content_type = 'movie' THEN m.title
                           WHEN wh.content_type = 'series' THEN s.title
                           WHEN wh.content_type = 'episode' THEN e.title
                       END as title,
                       CASE 
                           WHEN wh.content_type = 'movie' THEN m.poster
                           WHEN wh.content_type = 'series' THEN s.poster
                           WHEN wh.content_type = 'episode' THEN s2.poster
                       END as poster,
                       ROUND((wh.watch_time / wh.total_time) * 100, 1) as progress_percent
                FROM watch_history wh
                LEFT JOIN movies m ON wh.content_id = m.id AND wh.content_type = 'movie'
                LEFT JOIN series s ON wh.content_id = s.id AND wh.content_type = 'series'
                LEFT JOIN episodes e ON wh.episode_id = e.id AND wh.content_type = 'episode'
                LEFT JOIN series s2 ON e.series_id = s2.id AND wh.content_type = 'episode'
                WHERE wh.user_id = ? AND wh.completed = FALSE AND wh.watch_time > 0
                ORDER BY wh.last_watched DESC
                LIMIT ?
            ");
            
            $stmt->execute([$userId, $limit]);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            return [];
        }
    }
    
    /**
     * إحصائيات المشاهدة للمستخدم
     */
    public function getUserStats($userId) {
        try {
            // إجمالي وقت المشاهدة
            $stmt = $this->pdo->prepare("
                SELECT SUM(watch_time) as total_watch_time,
                       COUNT(*) as total_items,
                       COUNT(CASE WHEN completed = TRUE THEN 1 END) as completed_items
                FROM watch_history 
                WHERE user_id = ?
            ");
            $stmt->execute([$userId]);
            $watchStats = $stmt->fetch(PDO::FETCH_ASSOC);
            
            // عدد المفضلة
            $stmt = $this->pdo->prepare("
                SELECT COUNT(*) as total_favorites,
                       COUNT(CASE WHEN content_type = 'movie' THEN 1 END) as favorite_movies,
                       COUNT(CASE WHEN content_type = 'series' THEN 1 END) as favorite_series
                FROM favorites 
                WHERE user_id = ?
            ");
            $stmt->execute([$userId]);
            $favoriteStats = $stmt->fetch(PDO::FETCH_ASSOC);
            
            // النوع المفضل
            $stmt = $this->pdo->prepare("
                SELECT 
                    CASE 
                        WHEN wh.content_type = 'movie' THEN m.genre
                        WHEN wh.content_type = 'series' THEN s.genre
                    END as genre,
                    COUNT(*) as count
                FROM watch_history wh
                LEFT JOIN movies m ON wh.content_id = m.id AND wh.content_type = 'movie'
                LEFT JOIN series s ON wh.content_id = s.id AND wh.content_type = 'series'
                WHERE wh.user_id = ? AND (m.genre IS NOT NULL OR s.genre IS NOT NULL)
                GROUP BY genre
                ORDER BY count DESC
                LIMIT 1
            ");
            $stmt->execute([$userId]);
            $favoriteGenre = $stmt->fetch(PDO::FETCH_ASSOC);
            
            return [
                'total_watch_time' => $this->formatWatchTime($watchStats['total_watch_time'] ?? 0),
                'total_items' => $watchStats['total_items'] ?? 0,
                'completed_items' => $watchStats['completed_items'] ?? 0,
                'total_favorites' => $favoriteStats['total_favorites'] ?? 0,
                'favorite_movies' => $favoriteStats['favorite_movies'] ?? 0,
                'favorite_series' => $favoriteStats['favorite_series'] ?? 0,
                'favorite_genre' => $favoriteGenre['genre'] ?? 'غير محدد'
            ];
            
        } catch (Exception $e) {
            return [
                'total_watch_time' => '0 دقيقة',
                'total_items' => 0,
                'completed_items' => 0,
                'total_favorites' => 0,
                'favorite_movies' => 0,
                'favorite_series' => 0,
                'favorite_genre' => 'غير محدد'
            ];
        }
    }
    
    /**
     * تنسيق وقت المشاهدة
     */
    private function formatWatchTime($seconds) {
        if ($seconds < 60) {
            return $seconds . ' ثانية';
        } elseif ($seconds < 3600) {
            return floor($seconds / 60) . ' دقيقة';
        } else {
            $hours = floor($seconds / 3600);
            $minutes = floor(($seconds % 3600) / 60);
            return $hours . ' ساعة' . ($minutes > 0 ? ' و ' . $minutes . ' دقيقة' : '');
        }
    }
    
    /**
     * حذف عنصر من سجل المشاهدة
     */
    public function removeFromWatchHistory($userId, $historyId) {
        try {
            $stmt = $this->pdo->prepare("
                DELETE FROM watch_history 
                WHERE id = ? AND user_id = ?
            ");
            $stmt->execute([$historyId, $userId]);
            
            return [
                'success' => true,
                'message' => 'تم حذف العنصر من سجل المشاهدة'
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * مسح سجل المشاهدة بالكامل
     */
    public function clearWatchHistory($userId) {
        try {
            $stmt = $this->pdo->prepare("DELETE FROM watch_history WHERE user_id = ?");
            $stmt->execute([$userId]);
            
            return [
                'success' => true,
                'message' => 'تم مسح سجل المشاهدة بالكامل'
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
}

// معالجة طلبات API
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    header('Content-Type: application/json');
    
    $favoritesSystem = new FavoritesSystem();
    $action = $_POST['action'] ?? '';
    
    // محاكاة معرف المستخدم
    $userId = $_POST['user_id'] ?? 1;
    
    switch ($action) {
        case 'toggle_favorite':
            $contentId = $_POST['content_id'] ?? 0;
            $contentType = $_POST['content_type'] ?? 'movie';
            echo json_encode($favoritesSystem->toggleFavorite($userId, $contentId, $contentType));
            break;
            
        case 'get_favorites':
            $contentType = $_POST['content_type'] ?? null;
            $limit = $_POST['limit'] ?? 50;
            $offset = $_POST['offset'] ?? 0;
            
            $favorites = $favoritesSystem->getUserFavorites($userId, $contentType, $limit, $offset);
            echo json_encode([
                'success' => true,
                'favorites' => $favorites
            ]);
            break;
            
        case 'is_favorite':
            $contentId = $_POST['content_id'] ?? 0;
            $contentType = $_POST['content_type'] ?? 'movie';
            
            echo json_encode([
                'success' => true,
                'is_favorite' => $favoritesSystem->isFavorite($userId, $contentId, $contentType)
            ]);
            break;
            
        case 'update_watch_history':
            $contentId = $_POST['content_id'] ?? 0;
            $contentType = $_POST['content_type'] ?? 'movie';
            $watchTime = $_POST['watch_time'] ?? 0;
            $totalTime = $_POST['total_time'] ?? 0;
            $episodeId = $_POST['episode_id'] ?? null;
            
            echo json_encode($favoritesSystem->updateWatchHistory($userId, $contentId, $contentType, $watchTime, $totalTime, $episodeId));
            break;
            
        case 'get_watch_history':
            $limit = $_POST['limit'] ?? 50;
            $offset = $_POST['offset'] ?? 0;
            
            $history = $favoritesSystem->getUserWatchHistory($userId, $limit, $offset);
            echo json_encode([
                'success' => true,
                'history' => $history
            ]);
            break;
            
        case 'get_continue_watching':
            $limit = $_POST['limit'] ?? 10;
            
            $continueWatching = $favoritesSystem->getContinueWatching($userId, $limit);
            echo json_encode([
                'success' => true,
                'continue_watching' => $continueWatching
            ]);
            break;
            
        case 'get_user_stats':
            echo json_encode([
                'success' => true,
                'stats' => $favoritesSystem->getUserStats($userId)
            ]);
            break;
            
        case 'remove_from_history':
            $historyId = $_POST['history_id'] ?? 0;
            echo json_encode($favoritesSystem->removeFromWatchHistory($userId, $historyId));
            break;
            
        case 'clear_history':
            echo json_encode($favoritesSystem->clearWatchHistory($userId));
            break;
            
        default:
            echo json_encode(['success' => false, 'error' => 'إجراء غير صالح']);
    }
    exit;
}
?>
