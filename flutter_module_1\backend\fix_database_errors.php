<?php
/**
 * إصلاح أخطاء قاعدة البيانات
 * Fix Database Errors
 */

echo "<h1>🔧 إصلاح أخطاء قاعدة البيانات</h1>";

try {
    $pdo = new PDO("mysql:host=localhost;dbname=shahid_platform;charset=utf8mb4", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>✅ تم الاتصال بقاعدة البيانات بنجاح</h3>";
    echo "</div>";
    
    // 1. إصلاح جدول المستخدمين - التحقق من الأعمدة الموجودة
    echo "<h2>👥 فحص وإصلاح جدول المستخدمين</h2>";
    
    try {
        // فحص هيكل جدول المستخدمين
        $stmt = $pdo->query("DESCRIBE users");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        echo "<p style='color: #2196F3;'>الأعمدة الموجودة في جدول users:</p>";
        echo "<ul style='color: #ccc; margin-right: 2rem;'>";
        foreach ($columns as $column) {
            echo "<li>$column</li>";
        }
        echo "</ul>";
        
        // التحقق من وجود عمود username
        if (!in_array('username', $columns)) {
            echo "<p style='color: #FF9800;'>⚠️ عمود username مفقود، سيتم إضافته...</p>";
            
            $pdo->exec("ALTER TABLE users ADD COLUMN username varchar(50) UNIQUE AFTER id");
            
            // تحديث البيانات الموجودة بإنشاء username من email
            $pdo->exec("UPDATE users SET username = SUBSTRING_INDEX(email, '@', 1) WHERE username IS NULL");
            
            echo "<p style='color: #4CAF50;'>✅ تم إضافة عمود username بنجاح</p>";
        } else {
            echo "<p style='color: #4CAF50;'>✅ عمود username موجود</p>";
        }
        
        // التحقق من باقي الأعمدة المطلوبة وإضافتها إذا لزم الأمر
        $requiredColumns = [
            'phone' => 'varchar(20) DEFAULT NULL',
            'avatar' => 'varchar(255) DEFAULT NULL',
            'birth_date' => 'date DEFAULT NULL',
            'gender' => "enum('male','female','other') DEFAULT NULL",
            'country' => 'varchar(50) DEFAULT NULL',
            'language' => "varchar(10) DEFAULT 'ar'",
            'subscription_type' => "enum('free','premium','vip') DEFAULT 'free'",
            'subscription_start' => 'datetime DEFAULT NULL',
            'subscription_end' => 'datetime DEFAULT NULL',
            'is_active' => 'tinyint(1) DEFAULT 1',
            'is_verified' => 'tinyint(1) DEFAULT 0',
            'verification_token' => 'varchar(255) DEFAULT NULL',
            'reset_token' => 'varchar(255) DEFAULT NULL',
            'last_login' => 'datetime DEFAULT NULL',
            'login_attempts' => 'int(11) DEFAULT 0'
        ];
        
        foreach ($requiredColumns as $columnName => $columnDef) {
            if (!in_array($columnName, $columns)) {
                try {
                    $pdo->exec("ALTER TABLE users ADD COLUMN $columnName $columnDef");
                    echo "<p style='color: #4CAF50;'>✅ تم إضافة عمود $columnName</p>";
                } catch (Exception $e) {
                    echo "<p style='color: #F44336;'>❌ خطأ في إضافة عمود $columnName: " . htmlspecialchars($e->getMessage()) . "</p>";
                }
            }
        }
        
    } catch (Exception $e) {
        echo "<p style='color: #F44336;'>❌ خطأ في فحص جدول المستخدمين: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
    
    // 2. إصلاح جدول api_keys - حذف وإعادة إنشاء
    echo "<h2>🔑 إصلاح جدول api_keys</h2>";
    
    try {
        // حذف الجدول إذا كان موجوداً
        $pdo->exec("DROP TABLE IF EXISTS api_keys");
        echo "<p style='color: #FF9800;'>⚠️ تم حذف جدول api_keys القديم</p>";
        
        // إنشاء الجدول من جديد بدون تكرار المفاتيح
        $apiKeysSQL = "
            CREATE TABLE `api_keys` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `name` varchar(100) NOT NULL,
                `api_key` varchar(255) NOT NULL,
                `secret_key` varchar(255) DEFAULT NULL,
                `permissions` json DEFAULT NULL,
                `rate_limit` int(11) DEFAULT 1000,
                `allowed_ips` text DEFAULT NULL,
                `user_id` int(11) DEFAULT NULL,
                `is_active` tinyint(1) DEFAULT 1,
                `last_used` datetime DEFAULT NULL,
                `usage_count` int(11) DEFAULT 0,
                `expires_at` datetime DEFAULT NULL,
                `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                UNIQUE KEY `unique_api_key` (`api_key`),
                KEY `idx_user` (`user_id`),
                KEY `idx_active` (`is_active`),
                KEY `idx_expires` (`expires_at`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        
        $pdo->exec($apiKeysSQL);
        echo "<p style='color: #4CAF50;'>✅ تم إنشاء جدول api_keys بنجاح</p>";
        
        // إدراج مفتاح API تجريبي
        $apiKey = 'sk_' . bin2hex(random_bytes(16));
        $secretKey = 'sk_secret_' . bin2hex(random_bytes(20));
        
        $stmt = $pdo->prepare("INSERT INTO api_keys (name, api_key, secret_key, permissions, is_active) VALUES (?, ?, ?, ?, ?)");
        $permissions = json_encode(['read', 'write']);
        $stmt->execute(['Default API Key', $apiKey, $secretKey, $permissions, 1]);
        
        echo "<p style='color: #2196F3;'>🔑 تم إنشاء مفتاح API تجريبي: <strong>$apiKey</strong></p>";
        
    } catch (Exception $e) {
        echo "<p style='color: #F44336;'>❌ خطأ في إنشاء جدول api_keys: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
    
    // 3. التحقق من باقي الجداول وإصلاحها
    echo "<h2>🗄️ فحص باقي الجداول</h2>";
    
    $tablesToCheck = [
        'movies' => "SELECT COUNT(*) FROM movies",
        'series' => "SELECT COUNT(*) FROM series", 
        'categories' => "SELECT COUNT(*) FROM categories",
        'favorites' => "SELECT COUNT(*) FROM favorites",
        'watch_history' => "SELECT COUNT(*) FROM watch_history",
        'ratings' => "SELECT COUNT(*) FROM ratings",
        'subtitles' => "SELECT COUNT(*) FROM subtitles",
        'audio_tracks' => "SELECT COUNT(*) FROM audio_tracks",
        'subscriptions' => "SELECT COUNT(*) FROM subscriptions",
        'user_subscriptions' => "SELECT COUNT(*) FROM user_subscriptions",
        'payments' => "SELECT COUNT(*) FROM payments",
        'admins' => "SELECT COUNT(*) FROM admins",
        'reports' => "SELECT COUNT(*) FROM reports",
        'settings' => "SELECT COUNT(*) FROM settings",
        'notifications' => "SELECT COUNT(*) FROM notifications",
        'comments' => "SELECT COUNT(*) FROM comments",
        'playlists' => "SELECT COUNT(*) FROM playlists",
        'playlist_items' => "SELECT COUNT(*) FROM playlist_items",
        'logs' => "SELECT COUNT(*) FROM logs"
    ];
    
    echo "<table style='width: 100%; border-collapse: collapse; margin: 20px 0; background: rgba(0,0,0,0.3); border-radius: 10px; overflow: hidden;'>";
    echo "<tr style='background: rgba(229, 9, 20, 0.2);'>";
    echo "<th style='padding: 15px; border: 1px solid #ddd; text-align: right; color: #fff;'>الجدول</th>";
    echo "<th style='padding: 15px; border: 1px solid #ddd; text-align: center; color: #fff;'>الحالة</th>";
    echo "<th style='padding: 15px; border: 1px solid #ddd; text-align: center; color: #fff;'>عدد السجلات</th>";
    echo "</tr>";
    
    $existingTables = 0;
    $missingTables = 0;
    
    foreach ($tablesToCheck as $table => $query) {
        try {
            $stmt = $pdo->query($query);
            $count = $stmt->fetchColumn();
            $existingTables++;
            
            echo "<tr style='background: rgba(255,255,255,0.05);'>";
            echo "<td style='padding: 12px; border: 1px solid #ddd; color: #fff;'><strong>$table</strong></td>";
            echo "<td style='padding: 12px; border: 1px solid #ddd; text-align: center; color: #4CAF50; font-weight: bold;'>✅ موجود</td>";
            echo "<td style='padding: 12px; border: 1px solid #ddd; text-align: center; color: #ccc;'>$count</td>";
            echo "</tr>";
            
        } catch (Exception $e) {
            $missingTables++;
            
            echo "<tr style='background: rgba(255,255,255,0.05);'>";
            echo "<td style='padding: 12px; border: 1px solid #ddd; color: #fff;'><strong>$table</strong></td>";
            echo "<td style='padding: 12px; border: 1px solid #ddd; text-align: center; color: #F44336; font-weight: bold;'>❌ مفقود</td>";
            echo "<td style='padding: 12px; border: 1px solid #ddd; text-align: center; color: #ccc;'>-</td>";
            echo "</tr>";
        }
    }
    
    echo "</table>";
    
    // 4. إنشاء مستخدم تجريبي إذا لم يكن موجوداً
    echo "<h2>👤 فحص المستخدمين التجريبيين</h2>";
    
    try {
        // التحقق من وجود مستخدم تجريبي
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE email = ?");
        $stmt->execute(['<EMAIL>']);
        $adminExists = $stmt->fetchColumn();
        
        if ($adminExists == 0) {
            $adminPassword = password_hash('admin123', PASSWORD_DEFAULT);
            $stmt = $pdo->prepare("
                INSERT INTO users (username, email, password, full_name, subscription_type, is_active, is_verified) 
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute(['admin', '<EMAIL>', $adminPassword, 'مدير النظام', 'vip', 1, 1]);
            echo "<p style='color: #4CAF50;'>✅ تم إنشاء مستخدم إداري: <EMAIL> / admin123</p>";
        } else {
            echo "<p style='color: #2196F3;'>ℹ️ المستخدم الإداري موجود مسبقاً</p>";
        }
        
        // إنشاء مستخدم عادي للاختبار
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE email = ?");
        $stmt->execute(['<EMAIL>']);
        $userExists = $stmt->fetchColumn();
        
        if ($userExists == 0) {
            $userPassword = password_hash('123456', PASSWORD_DEFAULT);
            $stmt = $pdo->prepare("
                INSERT INTO users (username, email, password, full_name, subscription_type, is_active, is_verified) 
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute(['testuser', '<EMAIL>', $userPassword, 'مستخدم تجريبي', 'free', 1, 1]);
            echo "<p style='color: #4CAF50;'>✅ تم إنشاء مستخدم تجريبي: <EMAIL> / 123456</p>";
        } else {
            echo "<p style='color: #2196F3;'>ℹ️ المستخدم التجريبي موجود مسبقاً</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: #F44336;'>❌ خطأ في إنشاء المستخدمين: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
    
    // 5. ملخص الإصلاحات
    echo "<h2>📊 ملخص الإصلاحات</h2>";
    
    $totalTables = count($tablesToCheck);
    $completionRate = round(($existingTables / $totalTables) * 100, 1);
    
    echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin: 2rem 0;'>";
    
    // بطاقة الجداول الموجودة
    echo "<div style='background: rgba(76, 175, 80, 0.1); border: 1px solid rgba(76, 175, 80, 0.3); border-radius: 10px; padding: 1.5rem; text-align: center;'>";
    echo "<h3 style='color: #4CAF50; margin-bottom: 1rem;'>✅ جداول موجودة</h3>";
    echo "<div style='font-size: 2.5rem; font-weight: bold; color: #4CAF50; margin-bottom: 0.5rem;'>$existingTables</div>";
    echo "<div style='opacity: 0.8; color: #ccc;'>من $totalTables جدول</div>";
    echo "</div>";
    
    // بطاقة الجداول المفقودة
    echo "<div style='background: rgba(244, 67, 54, 0.1); border: 1px solid rgba(244, 67, 54, 0.3); border-radius: 10px; padding: 1.5rem; text-align: center;'>";
    echo "<h3 style='color: #F44336; margin-bottom: 1rem;'>❌ جداول مفقودة</h3>";
    echo "<div style='font-size: 2.5rem; font-weight: bold; color: #F44336; margin-bottom: 0.5rem;'>$missingTables</div>";
    echo "<div style='opacity: 0.8; color: #ccc;'>تحتاج إنشاء</div>";
    echo "</div>";
    
    // بطاقة نسبة الإكمال
    $completionColor = $completionRate >= 90 ? '#4CAF50' : ($completionRate >= 70 ? '#FF9800' : '#F44336');
    echo "<div style='background: rgba(229, 9, 20, 0.1); border: 1px solid rgba(229, 9, 20, 0.3); border-radius: 10px; padding: 1.5rem; text-align: center;'>";
    echo "<h3 style='color: #E50914; margin-bottom: 1rem;'>🎯 نسبة الإكمال</h3>";
    echo "<div style='font-size: 2.5rem; font-weight: bold; color: $completionColor; margin-bottom: 0.5rem;'>$completionRate%</div>";
    echo "<div style='opacity: 0.8; color: #ccc;'>مكتمل</div>";
    echo "</div>";
    
    echo "</div>";
    
    if ($completionRate >= 95) {
        echo "<div style='background: rgba(76, 175, 80, 0.1); border: 1px solid rgba(76, 175, 80, 0.3); border-radius: 10px; padding: 2rem; margin: 2rem 0; text-align: center;'>";
        echo "<h3 style='color: #4CAF50; margin-bottom: 1rem;'>🎉 تم إصلاح جميع المشاكل!</h3>";
        echo "<p style='color: #ccc;'>قاعدة البيانات جاهزة للاستخدام بنجاح.</p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>❌ خطأ في الاتصال بقاعدة البيانات:</h3>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

// روابط سريعة
echo "<div style='text-align: center; margin: 3rem 0;'>";
echo "<h3 style='color: #E50914; margin-bottom: 2rem;'>🔗 الخطوات التالية</h3>";

$next_steps = [
    ['url' => 'create_missing_database_tables.php', 'title' => '🔧 إنشاء الجداول المفقودة', 'color' => '#4CAF50'],
    ['url' => 'dashboard_live.php', 'title' => '📊 لوحة المراقبة', 'color' => '#E50914'],
    ['url' => 'check_database_tables.php', 'title' => '🔍 فحص الجداول', 'color' => '#2196F3'],
    ['url' => 'login.php', 'title' => '🔐 اختبار تسجيل الدخول', 'color' => '#FF9800'],
    ['url' => 'index.php', 'title' => '🏠 الصفحة الرئيسية', 'color' => '#9C27B0']
];

foreach ($next_steps as $step) {
    echo "<a href='{$step['url']}' style='background: {$step['color']}; color: white; padding: 1rem 2rem; text-decoration: none; border-radius: 8px; font-weight: bold; margin: 0.5rem; display: inline-block; transition: transform 0.3s ease;' onmouseover='this.style.transform=\"translateY(-3px)\"' onmouseout='this.style.transform=\"translateY(0)\"'>{$step['title']}</a>";
}

echo "</div>";
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 إصلاح أخطاء قاعدة البيانات - Shahid Platform</title>
    <style>
        body {
            font-family: 'Segoe UI', 'Cairo', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #2d2d2d 100%);
            color: #fff;
            min-height: 100vh;
            padding: 2rem;
            margin: 0;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(47, 47, 47, 0.95);
            border-radius: 20px;
            padding: 3rem;
            border: 2px solid rgba(229, 9, 20, 0.3);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
        }
        h1, h2, h3 { 
            color: #E50914; 
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
        }
        h1 {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 2rem;
            background: linear-gradient(45deg, #E50914, #FF6B35);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        p {
            line-height: 1.6;
            margin: 0.5rem 0;
        }
        ul {
            margin: 1rem 0;
        }
        table {
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        }
        th {
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        tr:hover {
            background: rgba(229, 9, 20, 0.1) !important;
        }
        .btn {
            transition: all 0.3s ease;
        }
        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
        }
        @media (max-width: 768px) {
            .container {
                padding: 1rem;
                margin: 1rem;
            }
            h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- المحتوى يتم عرضه من PHP أعلاه -->
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔧 أداة إصلاح أخطاء قاعدة البيانات جاهزة!');
        });
    </script>
</body>
</html>
