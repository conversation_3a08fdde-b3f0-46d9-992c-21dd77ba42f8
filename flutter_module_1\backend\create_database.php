<?php
/**
 * Create Database and Tables - Shahid Platform
 * This script creates the database and all required tables
 */

// Get database config
$config = include 'config/database.php';

echo "<h1>🗄️ إنشاء قاعدة البيانات - Shahid Platform</h1>";

try {
    // Connect to MySQL without database name to create it
    $pdo = new PDO("mysql:host={$config['host']};charset=utf8mb4", $config['username'], $config['password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<p>✅ <strong>اتصال MySQL ناجح</strong></p>";
    
    // Create database
    $dbName = $config['name'];
    $pdo->exec("CREATE DATABASE IF NOT EXISTS `$dbName` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "<p>✅ <strong>تم إنشاء قاعدة البيانات:</strong> $dbName</p>";
    
    // Connect to the new database
    $pdo = new PDO("mysql:host={$config['host']};dbname={$config['name']};charset=utf8mb4", 
                   $config['username'], $config['password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<p>✅ <strong>اتصال بقاعدة البيانات ناجح</strong></p>";
    
    // Create tables
    echo "<h2>📋 إنشاء الجداول:</h2>";
    
    // Users table
    $pdo->exec("CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        email VARCHAR(255) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        role ENUM('user', 'admin') DEFAULT 'user',
        status ENUM('active', 'inactive', 'pending') DEFAULT 'active',
        avatar VARCHAR(500) NULL,
        phone VARCHAR(50) NULL,
        birth_date DATE NULL,
        country VARCHAR(100) NULL,
        language VARCHAR(10) DEFAULT 'ar',
        email_verified_at TIMESTAMP NULL,
        last_login TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )");
    echo "<p>✅ جدول المستخدمين (users)</p>";
    
    // Movies table
    $pdo->exec("CREATE TABLE IF NOT EXISTS movies (
        id INT AUTO_INCREMENT PRIMARY KEY,
        title VARCHAR(500) NOT NULL,
        title_en VARCHAR(500) NULL,
        description TEXT NULL,
        year INT NULL,
        duration INT NULL COMMENT 'Duration in minutes',
        genre VARCHAR(255) NULL,
        rating DECIMAL(3,1) NULL,
        poster VARCHAR(500) NULL,
        backdrop VARCHAR(500) NULL,
        trailer VARCHAR(500) NULL,
        video_url VARCHAR(500) NULL,
        quality VARCHAR(50) DEFAULT 'HD',
        language VARCHAR(100) DEFAULT 'Arabic',
        subtitles JSON NULL,
        director VARCHAR(255) NULL,
        cast TEXT NULL,
        country VARCHAR(100) NULL,
        age_rating VARCHAR(20) NULL,
        status ENUM('draft', 'published', 'archived') DEFAULT 'published',
        featured BOOLEAN DEFAULT FALSE,
        trending BOOLEAN DEFAULT FALSE,
        views_count INT DEFAULT 0,
        likes_count INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_status (status),
        INDEX idx_year (year),
        INDEX idx_rating (rating),
        INDEX idx_featured (featured),
        INDEX idx_trending (trending)
    )");
    echo "<p>✅ جدول الأفلام (movies)</p>";
    
    // Series table
    $pdo->exec("CREATE TABLE IF NOT EXISTS series (
        id INT AUTO_INCREMENT PRIMARY KEY,
        title VARCHAR(500) NOT NULL,
        title_en VARCHAR(500) NULL,
        description TEXT NULL,
        year INT NULL,
        seasons INT DEFAULT 1,
        genre VARCHAR(255) NULL,
        rating DECIMAL(3,1) NULL,
        poster VARCHAR(500) NULL,
        backdrop VARCHAR(500) NULL,
        trailer VARCHAR(500) NULL,
        language VARCHAR(100) DEFAULT 'Arabic',
        director VARCHAR(255) NULL,
        cast TEXT NULL,
        country VARCHAR(100) NULL,
        age_rating VARCHAR(20) NULL,
        status ENUM('draft', 'published', 'archived') DEFAULT 'published',
        featured BOOLEAN DEFAULT FALSE,
        trending BOOLEAN DEFAULT FALSE,
        views_count INT DEFAULT 0,
        likes_count INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_status (status),
        INDEX idx_year (year),
        INDEX idx_rating (rating),
        INDEX idx_featured (featured),
        INDEX idx_trending (trending)
    )");
    echo "<p>✅ جدول المسلسلات (series)</p>";
    
    // Episodes table
    $pdo->exec("CREATE TABLE IF NOT EXISTS episodes (
        id INT AUTO_INCREMENT PRIMARY KEY,
        series_id INT NOT NULL,
        title VARCHAR(500) NOT NULL,
        description TEXT NULL,
        season_number INT NOT NULL,
        episode_number INT NOT NULL,
        duration INT NULL COMMENT 'Duration in minutes',
        video_url VARCHAR(500) NULL,
        thumbnail VARCHAR(500) NULL,
        quality VARCHAR(50) DEFAULT 'HD',
        subtitles JSON NULL,
        air_date DATE NULL,
        status ENUM('draft', 'published', 'archived') DEFAULT 'published',
        views_count INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (series_id) REFERENCES series(id) ON DELETE CASCADE,
        INDEX idx_series (series_id),
        INDEX idx_season (season_number),
        INDEX idx_episode (episode_number),
        INDEX idx_status (status),
        UNIQUE KEY unique_episode (series_id, season_number, episode_number)
    )");
    echo "<p>✅ جدول الحلقات (episodes)</p>";
    
    // Categories table
    $pdo->exec("CREATE TABLE IF NOT EXISTS categories (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        name_en VARCHAR(255) NULL,
        description TEXT NULL,
        icon VARCHAR(255) NULL,
        color VARCHAR(7) NULL,
        sort_order INT DEFAULT 0,
        status ENUM('active', 'inactive') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )");
    echo "<p>✅ جدول التصنيفات (categories)</p>";
    
    // User favorites table
    $pdo->exec("CREATE TABLE IF NOT EXISTS user_favorites (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        content_type ENUM('movie', 'series') NOT NULL,
        content_id INT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        UNIQUE KEY unique_favorite (user_id, content_type, content_id),
        INDEX idx_user (user_id),
        INDEX idx_content (content_type, content_id)
    )");
    echo "<p>✅ جدول المفضلة (user_favorites)</p>";
    
    // User watchlist table
    $pdo->exec("CREATE TABLE IF NOT EXISTS user_watchlist (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        content_type ENUM('movie', 'series') NOT NULL,
        content_id INT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        UNIQUE KEY unique_watchlist (user_id, content_type, content_id),
        INDEX idx_user (user_id),
        INDEX idx_content (content_type, content_id)
    )");
    echo "<p>✅ جدول قائمة المشاهدة (user_watchlist)</p>";
    
    // Watch history table
    $pdo->exec("CREATE TABLE IF NOT EXISTS watch_history (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        content_type ENUM('movie', 'series', 'episode') NOT NULL,
        content_id INT NOT NULL,
        episode_id INT NULL,
        progress_seconds INT DEFAULT 0,
        total_seconds INT DEFAULT 0,
        completed BOOLEAN DEFAULT FALSE,
        last_watched TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        INDEX idx_user (user_id),
        INDEX idx_content (content_type, content_id),
        INDEX idx_last_watched (last_watched)
    )");
    echo "<p>✅ جدول تاريخ المشاهدة (watch_history)</p>";
    
    // Ratings table
    $pdo->exec("CREATE TABLE IF NOT EXISTS ratings (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        content_type ENUM('movie', 'series') NOT NULL,
        content_id INT NOT NULL,
        rating DECIMAL(3,1) NOT NULL,
        review TEXT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        UNIQUE KEY unique_rating (user_id, content_type, content_id),
        INDEX idx_content (content_type, content_id),
        INDEX idx_rating (rating)
    )");
    echo "<p>✅ جدول التقييمات (ratings)</p>";
    
    // Subscriptions table
    $pdo->exec("CREATE TABLE IF NOT EXISTS subscription_plans (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        name_en VARCHAR(255) NULL,
        description TEXT NULL,
        price DECIMAL(10,2) NOT NULL,
        currency VARCHAR(3) DEFAULT 'USD',
        duration_days INT NOT NULL,
        features JSON NULL,
        max_devices INT DEFAULT 1,
        max_downloads INT DEFAULT 0,
        quality VARCHAR(50) DEFAULT 'HD',
        ads_free BOOLEAN DEFAULT TRUE,
        status ENUM('active', 'inactive') DEFAULT 'active',
        sort_order INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )");
    echo "<p>✅ جدول خطط الاشتراك (subscription_plans)</p>";
    
    // User subscriptions table
    $pdo->exec("CREATE TABLE IF NOT EXISTS user_subscriptions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        plan_id INT NOT NULL,
        status ENUM('active', 'expired', 'cancelled', 'pending') DEFAULT 'pending',
        start_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        end_date TIMESTAMP NULL,
        auto_renew BOOLEAN DEFAULT TRUE,
        payment_method VARCHAR(100) NULL,
        transaction_id VARCHAR(255) NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (plan_id) REFERENCES subscription_plans(id),
        INDEX idx_user (user_id),
        INDEX idx_status (status),
        INDEX idx_dates (start_date, end_date)
    )");
    echo "<p>✅ جدول اشتراكات المستخدمين (user_subscriptions)</p>";
    
    // Settings table
    $pdo->exec("CREATE TABLE IF NOT EXISTS settings (
        id INT AUTO_INCREMENT PRIMARY KEY,
        setting_key VARCHAR(255) UNIQUE NOT NULL,
        setting_value TEXT NULL,
        setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
        description TEXT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )");
    echo "<p>✅ جدول الإعدادات (settings)</p>";
    
    // Insert default admin user
    $adminPassword = password_hash('admin123', PASSWORD_DEFAULT);
    $pdo->exec("INSERT IGNORE INTO users (name, email, password, role, status) VALUES 
                ('Administrator', '<EMAIL>', '$adminPassword', 'admin', 'active')");
    echo "<p>✅ تم إنشاء حساب المدير الافتراضي</p>";
    
    // Insert default subscription plans
    $pdo->exec("INSERT IGNORE INTO subscription_plans (id, name, name_en, description, price, duration_days, features, max_devices) VALUES 
                (1, 'الخطة الأساسية', 'Basic Plan', 'مشاهدة محدودة بجودة عادية', 9.99, 30, '[\"HD Quality\", \"1 Device\", \"Limited Downloads\"]', 1),
                (2, 'الخطة المتقدمة', 'Premium Plan', 'مشاهدة غير محدودة بجودة عالية', 19.99, 30, '[\"Full HD Quality\", \"3 Devices\", \"Unlimited Downloads\", \"No Ads\"]', 3),
                (3, 'الخطة العائلية', 'Family Plan', 'مشاهدة عائلية بجودة فائقة', 29.99, 30, '[\"4K Quality\", \"5 Devices\", \"Unlimited Downloads\", \"No Ads\", \"Family Profiles\"]', 5)");
    echo "<p>✅ تم إنشاء خطط الاشتراك الافتراضية</p>";
    
    // Insert default categories
    $pdo->exec("INSERT IGNORE INTO categories (name, name_en, icon, color) VALUES 
                ('أكشن', 'Action', '⚔️', '#FF6B6B'),
                ('دراما', 'Drama', '🎭', '#4ECDC4'),
                ('كوميديا', 'Comedy', '😂', '#45B7D1'),
                ('رومانسي', 'Romance', '💕', '#F7DC6F'),
                ('إثارة', 'Thriller', '🔥', '#BB8FCE'),
                ('خيال علمي', 'Sci-Fi', '🚀', '#85C1E9'),
                ('رعب', 'Horror', '👻', '#EC7063'),
                ('مغامرة', 'Adventure', '🗺️', '#58D68D'),
                ('جريمة', 'Crime', '🕵️', '#F8C471'),
                ('وثائقي', 'Documentary', '📹', '#AED6F1')");
    echo "<p>✅ تم إنشاء التصنيفات الافتراضية</p>";
    
    // Insert default settings
    $pdo->exec("INSERT IGNORE INTO settings (setting_key, setting_value, setting_type, description) VALUES 
                ('site_name', 'Shahid', 'string', 'اسم الموقع'),
                ('site_description', 'منصة البث الاحترافية', 'string', 'وصف الموقع'),
                ('admin_email', '<EMAIL>', 'string', 'بريد المدير'),
                ('maintenance_mode', '0', 'boolean', 'وضع الصيانة'),
                ('allow_registration', '1', 'boolean', 'السماح بالتسجيل الجديد'),
                ('max_devices_per_user', '3', 'number', 'أقصى عدد أجهزة لكل مستخدم'),
                ('video_quality_default', 'HD', 'string', 'جودة الفيديو الافتراضية'),
                ('supported_languages', '[\"ar\", \"en\"]', 'json', 'اللغات المدعومة')");
    echo "<p>✅ تم إنشاء الإعدادات الافتراضية</p>";
    
    // Create installed.lock file
    file_put_contents('config/installed.lock', date('Y-m-d H:i:s'));
    echo "<p>✅ تم إنشاء ملف التثبيت</p>";
    
    echo "<h2>🎉 تم إنشاء قاعدة البيانات بنجاح!</h2>";
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>✅ تم إنشاء:</h3>";
    echo "<ul>";
    echo "<li>قاعدة البيانات: <strong>$dbName</strong></li>";
    echo "<li>12 جدول رئيسي مع العلاقات</li>";
    echo "<li>حساب المدير: <EMAIL> / admin123</li>";
    echo "<li>3 خطط اشتراك افتراضية</li>";
    echo "<li>10 تصنيفات افتراضية</li>";
    echo "<li>إعدادات النظام الأساسية</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h3>🔗 الخطوات التالية:</h3>";
    echo "<p><a href='index_simple.php' style='background: #E50914; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🏠 الصفحة الرئيسية</a></p>";
    echo "<p><a href='admin/' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🎛️ لوحة الإدارة</a></p>";
    echo "<p><a href='api/test_api.php' style='background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🔗 اختبار API</a></p>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>❌ خطأ في إنشاء قاعدة البيانات:</h3>";
    echo "<p><strong>الخطأ:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<h4>🔧 الحلول المقترحة:</h4>";
    echo "<ul>";
    echo "<li>تأكد من تشغيل خادم MySQL</li>";
    echo "<li>تحقق من صحة بيانات الاتصال في config/database.php</li>";
    echo "<li>تأكد من وجود صلاحيات إنشاء قواعد البيانات</li>";
    echo "<li>جرب تغيير كلمة المرور في ملف الإعدادات</li>";
    echo "</ul>";
    echo "</div>";
}

echo "<style>
body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 40px; background: #f8f9fa; }
h1 { color: #E50914; border-bottom: 3px solid #E50914; padding-bottom: 10px; }
h2 { color: #333; margin-top: 30px; }
h3 { color: #555; }
p { margin: 5px 0; }
ul { margin: 10px 0; padding-left: 20px; }
a { color: #E50914; text-decoration: none; }
a:hover { text-decoration: underline; }
</style>";
?>
