<?php
/**
 * Series Management - Shahid Admin Panel
 */

session_start();

// Check admin login
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: index.php');
    exit();
}

// Database connection
try {
    $config = include '../config/database.php';
    $pdo = new PDO("mysql:host={$config['host']};dbname={$config['name']};charset=utf8mb4", 
                   $config['username'], $config['password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (Exception $e) {
    die('خطأ في الاتصال بقاعدة البيانات: ' . $e->getMessage());
}

// Handle actions
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['add_series'])) {
        // Add new series
        $title = $_POST['title'] ?? '';
        $description = $_POST['description'] ?? '';
        $year = $_POST['year'] ?? '';
        $genre = $_POST['genre'] ?? '';
        $rating = $_POST['rating'] ?? '';
        $poster = $_POST['poster'] ?? '';
        $trailer = $_POST['trailer'] ?? '';
        $seasons = $_POST['seasons'] ?? '';
        
        if (!empty($title)) {
            try {
                $stmt = $pdo->prepare("INSERT INTO series (title, description, year, genre, rating, poster, trailer, seasons, status, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'published', NOW())");
                $stmt->execute([$title, $description, $year, $genre, $rating, $poster, $trailer, $seasons]);
                $message = 'تم إضافة المسلسل بنجاح';
                $messageType = 'success';
            } catch (Exception $e) {
                $message = 'خطأ في إضافة المسلسل: ' . $e->getMessage();
                $messageType = 'error';
            }
        } else {
            $message = 'عنوان المسلسل مطلوب';
            $messageType = 'error';
        }
    } elseif (isset($_POST['delete_series'])) {
        // Delete series
        $seriesId = $_POST['series_id'] ?? '';
        if (!empty($seriesId)) {
            try {
                // Delete episodes first
                $stmt = $pdo->prepare("DELETE FROM episodes WHERE series_id = ?");
                $stmt->execute([$seriesId]);
                
                // Delete series
                $stmt = $pdo->prepare("DELETE FROM series WHERE id = ?");
                $stmt->execute([$seriesId]);
                
                $message = 'تم حذف المسلسل وجميع حلقاته بنجاح';
                $messageType = 'success';
            } catch (Exception $e) {
                $message = 'خطأ في حذف المسلسل: ' . $e->getMessage();
                $messageType = 'error';
            }
        }
    } elseif (isset($_POST['add_episode'])) {
        // Add episode
        $seriesId = $_POST['series_id'] ?? '';
        $title = $_POST['episode_title'] ?? '';
        $description = $_POST['episode_description'] ?? '';
        $season = $_POST['season_number'] ?? '';
        $episode = $_POST['episode_number'] ?? '';
        $duration = $_POST['episode_duration'] ?? '';
        
        if (!empty($seriesId) && !empty($title) && !empty($season) && !empty($episode)) {
            try {
                $stmt = $pdo->prepare("INSERT INTO episodes (series_id, title, description, season_number, episode_number, duration, status, created_at) VALUES (?, ?, ?, ?, ?, ?, 'published', NOW())");
                $stmt->execute([$seriesId, $title, $description, $season, $episode, $duration]);
                $message = 'تم إضافة الحلقة بنجاح';
                $messageType = 'success';
            } catch (Exception $e) {
                $message = 'خطأ في إضافة الحلقة: ' . $e->getMessage();
                $messageType = 'error';
            }
        } else {
            $message = 'جميع بيانات الحلقة مطلوبة';
            $messageType = 'error';
        }
    }
}

// Get series
$page = (int)($_GET['page'] ?? 1);
$limit = 10;
$offset = ($page - 1) * $limit;

try {
    $stmt = $pdo->prepare("SELECT s.*, COUNT(e.id) as episodes_count FROM series s LEFT JOIN episodes e ON s.id = e.series_id GROUP BY s.id ORDER BY s.created_at DESC LIMIT $limit OFFSET $offset");
    $stmt->execute();
    $series = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get total count
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM series");
    $total = $stmt->fetch()['total'];
    $totalPages = ceil($total / $limit);
} catch (Exception $e) {
    $series = [];
    $total = 0;
    $totalPages = 0;
    $message = 'خطأ في جلب المسلسلات: ' . $e->getMessage();
    $messageType = 'error';
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المسلسلات - Shahid Admin</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            color: #fff;
            min-height: 100vh;
        }
        
        .header {
            background: #E50914;
            padding: 1rem 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .header h1 {
            font-size: 1.8rem;
        }
        
        .header a {
            color: #fff;
            text-decoration: none;
            padding: 0.5rem 1rem;
            border: 1px solid #fff;
            border-radius: 5px;
            transition: all 0.3s;
        }
        
        .header a:hover {
            background: #fff;
            color: #E50914;
        }
        
        .container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 2rem;
        }
        
        .message {
            padding: 1rem;
            border-radius: 5px;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .message.success {
            background: #28a745;
            color: #fff;
        }
        
        .message.error {
            background: #dc3545;
            color: #fff;
        }
        
        .add-form {
            background: #2d2d2d;
            padding: 2rem;
            border-radius: 10px;
            margin-bottom: 3rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }
        
        .add-form h2 {
            color: #E50914;
            margin-bottom: 2rem;
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin-bottom: 1rem;
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        .form-group.full-width {
            grid-column: 1 / -1;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: bold;
        }
        
        .form-group input,
        .form-group textarea {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #555;
            border-radius: 5px;
            background: #1a1a1a;
            color: #fff;
            font-size: 1rem;
        }
        
        .form-group textarea {
            height: 100px;
            resize: vertical;
        }
        
        .form-group input:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #E50914;
        }
        
        .btn {
            padding: 0.75rem 1.5rem;
            background: #E50914;
            color: #fff;
            border: none;
            border-radius: 5px;
            font-size: 1rem;
            cursor: pointer;
            transition: background 0.3s;
            margin-left: 0.5rem;
        }
        
        .btn:hover {
            background: #b8070f;
        }
        
        .btn.danger {
            background: #dc3545;
        }
        
        .btn.danger:hover {
            background: #c82333;
        }
        
        .btn.secondary {
            background: #6c757d;
        }
        
        .btn.secondary:hover {
            background: #5a6268;
        }
        
        .series-table {
            background: #2d2d2d;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }
        
        .table-header {
            background: #E50914;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .table-header h2 {
            font-size: 1.3rem;
        }
        
        .table-content {
            overflow-x: auto;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
        }
        
        th, td {
            padding: 1rem;
            text-align: right;
            border-bottom: 1px solid #555;
        }
        
        th {
            background: #3d3d3d;
            font-weight: bold;
        }
        
        tr:hover {
            background: #3d3d3d;
        }
        
        .poster-img {
            width: 50px;
            height: 75px;
            object-fit: cover;
            border-radius: 5px;
        }
        
        .actions {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 2rem;
            gap: 1rem;
        }
        
        .pagination a {
            color: #fff;
            text-decoration: none;
            padding: 0.5rem 1rem;
            border: 1px solid #555;
            border-radius: 5px;
            transition: all 0.3s;
        }
        
        .pagination a:hover,
        .pagination a.active {
            background: #E50914;
            border-color: #E50914;
        }
        
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.8);
        }
        
        .modal-content {
            background-color: #2d2d2d;
            margin: 5% auto;
            padding: 2rem;
            border-radius: 10px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }
        
        .close {
            color: #aaa;
            float: left;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }
        
        .close:hover {
            color: #E50914;
        }
        
        @media (max-width: 768px) {
            .form-grid {
                grid-template-columns: 1fr;
            }
            
            .container {
                padding: 0 1rem;
            }
            
            .header {
                flex-direction: column;
                gap: 1rem;
            }
            
            .actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>📺 إدارة المسلسلات</h1>
        <a href="index.php">← العودة للوحة الرئيسية</a>
    </div>
    
    <div class="container">
        <?php if (!empty($message)): ?>
            <div class="message <?php echo $messageType; ?>">
                <?php echo htmlspecialchars($message); ?>
            </div>
        <?php endif; ?>
        
        <!-- Add Series Form -->
        <div class="add-form">
            <h2>➕ إضافة مسلسل جديد</h2>
            <form method="POST">
                <div class="form-grid">
                    <div class="form-group">
                        <label for="title">عنوان المسلسل *</label>
                        <input type="text" id="title" name="title" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="year">سنة الإنتاج</label>
                        <input type="number" id="year" name="year" min="1900" max="2030">
                    </div>
                    
                    <div class="form-group">
                        <label for="seasons">عدد المواسم</label>
                        <input type="number" id="seasons" name="seasons" min="1">
                    </div>
                    
                    <div class="form-group">
                        <label for="genre">النوع</label>
                        <input type="text" id="genre" name="genre" placeholder="دراما، كوميديا، إثارة">
                    </div>
                    
                    <div class="form-group">
                        <label for="rating">التقييم</label>
                        <input type="number" id="rating" name="rating" min="0" max="10" step="0.1">
                    </div>
                    
                    <div class="form-group">
                        <label for="poster">رابط الملصق</label>
                        <input type="url" id="poster" name="poster" placeholder="https://example.com/poster.jpg">
                    </div>
                    
                    <div class="form-group full-width">
                        <label for="description">الوصف</label>
                        <textarea id="description" name="description" placeholder="وصف المسلسل..."></textarea>
                    </div>
                    
                    <div class="form-group full-width">
                        <label for="trailer">رابط الإعلان</label>
                        <input type="url" id="trailer" name="trailer" placeholder="https://youtube.com/watch?v=...">
                    </div>
                </div>
                
                <button type="submit" name="add_series" class="btn">إضافة المسلسل</button>
            </form>
        </div>
        
        <!-- Series Table -->
        <div class="series-table">
            <div class="table-header">
                <h2>📋 قائمة المسلسلات (<?php echo number_format($total); ?> مسلسل)</h2>
            </div>
            
            <div class="table-content">
                <table>
                    <thead>
                        <tr>
                            <th>الملصق</th>
                            <th>العنوان</th>
                            <th>السنة</th>
                            <th>المواسم</th>
                            <th>الحلقات</th>
                            <th>النوع</th>
                            <th>التقييم</th>
                            <th>تاريخ الإضافة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (!empty($series)): ?>
                            <?php foreach ($series as $show): ?>
                                <tr>
                                    <td>
                                        <?php if (!empty($show['poster'])): ?>
                                            <img src="<?php echo htmlspecialchars($show['poster']); ?>" 
                                                 alt="<?php echo htmlspecialchars($show['title']); ?>" 
                                                 class="poster-img">
                                        <?php else: ?>
                                            <div style="width: 50px; height: 75px; background: #555; border-radius: 5px; display: flex; align-items: center; justify-content: center; font-size: 0.8rem;">
                                                لا توجد صورة
                                            </div>
                                        <?php endif; ?>
                                    </td>
                                    <td><strong><?php echo htmlspecialchars($show['title']); ?></strong></td>
                                    <td><?php echo htmlspecialchars($show['year'] ?: 'غير محدد'); ?></td>
                                    <td><?php echo htmlspecialchars($show['seasons'] ?: 'غير محدد'); ?></td>
                                    <td><?php echo number_format($show['episodes_count']); ?></td>
                                    <td><?php echo htmlspecialchars($show['genre'] ?: 'غير محدد'); ?></td>
                                    <td><?php echo htmlspecialchars($show['rating'] ? $show['rating'] . '/10' : 'غير مقيم'); ?></td>
                                    <td><?php echo date('Y-m-d', strtotime($show['created_at'])); ?></td>
                                    <td>
                                        <div class="actions">
                                            <button onclick="openEpisodeModal(<?php echo $show['id']; ?>, '<?php echo htmlspecialchars($show['title']); ?>')" class="btn secondary">إضافة حلقة</button>
                                            <form method="POST" style="display: inline;" onsubmit="return confirm('هل أنت متأكد من حذف هذا المسلسل وجميع حلقاته؟')">
                                                <input type="hidden" name="series_id" value="<?php echo $show['id']; ?>">
                                                <button type="submit" name="delete_series" class="btn danger">حذف</button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="9" style="text-align: center; color: #ccc; padding: 2rem;">
                                    لا توجد مسلسلات. قم بإضافة مسلسل جديد أعلاه.
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
        
        <!-- Pagination -->
        <?php if ($totalPages > 1): ?>
            <div class="pagination">
                <?php if ($page > 1): ?>
                    <a href="?page=<?php echo $page - 1; ?>">← السابق</a>
                <?php endif; ?>
                
                <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                    <a href="?page=<?php echo $i; ?>" <?php echo $i === $page ? 'class="active"' : ''; ?>>
                        <?php echo $i; ?>
                    </a>
                <?php endfor; ?>
                
                <?php if ($page < $totalPages): ?>
                    <a href="?page=<?php echo $page + 1; ?>">التالي →</a>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>
    
    <!-- Episode Modal -->
    <div id="episodeModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeEpisodeModal()">&times;</span>
            <h2 id="modalTitle">إضافة حلقة جديدة</h2>
            <form method="POST">
                <input type="hidden" id="modalSeriesId" name="series_id">
                
                <div class="form-group">
                    <label for="episode_title">عنوان الحلقة *</label>
                    <input type="text" id="episode_title" name="episode_title" required>
                </div>
                
                <div class="form-grid">
                    <div class="form-group">
                        <label for="season_number">رقم الموسم *</label>
                        <input type="number" id="season_number" name="season_number" min="1" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="episode_number">رقم الحلقة *</label>
                        <input type="number" id="episode_number" name="episode_number" min="1" required>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="episode_duration">المدة (بالدقائق)</label>
                    <input type="number" id="episode_duration" name="episode_duration" min="1">
                </div>
                
                <div class="form-group">
                    <label for="episode_description">وصف الحلقة</label>
                    <textarea id="episode_description" name="episode_description" placeholder="وصف الحلقة..."></textarea>
                </div>
                
                <button type="submit" name="add_episode" class="btn">إضافة الحلقة</button>
                <button type="button" onclick="closeEpisodeModal()" class="btn secondary">إلغاء</button>
            </form>
        </div>
    </div>
    
    <script>
        function openEpisodeModal(seriesId, seriesTitle) {
            document.getElementById('modalSeriesId').value = seriesId;
            document.getElementById('modalTitle').textContent = 'إضافة حلقة جديدة - ' + seriesTitle;
            document.getElementById('episodeModal').style.display = 'block';
        }
        
        function closeEpisodeModal() {
            document.getElementById('episodeModal').style.display = 'none';
            // Reset form
            document.getElementById('episode_title').value = '';
            document.getElementById('season_number').value = '';
            document.getElementById('episode_number').value = '';
            document.getElementById('episode_duration').value = '';
            document.getElementById('episode_description').value = '';
        }
        
        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('episodeModal');
            if (event.target === modal) {
                closeEpisodeModal();
            }
        }
    </script>
</body>
</html>
