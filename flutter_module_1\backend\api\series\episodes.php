<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// معالجة طلبات OPTIONS
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => 'طريقة الطلب غير مدعومة',
        'error_code' => 'METHOD_NOT_ALLOWED'
    ]);
    exit;
}

try {
    // الاتصال بقاعدة البيانات
    $pdo = new PDO("mysql:host=localhost;dbname=shahid_platform;charset=utf8mb4", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // الحصول على معرف المسلسل
    $series_id = isset($_GET['series_id']) ? intval($_GET['series_id']) : 0;
    $season = isset($_GET['season']) ? intval($_GET['season']) : null;
    $limit = isset($_GET['limit']) ? min(max(1, intval($_GET['limit'])), 100) : 50;
    $offset = isset($_GET['offset']) ? max(0, intval($_GET['offset'])) : 0;
    
    if (!$series_id) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'message' => 'معرف المسلسل مطلوب',
            'error_code' => 'MISSING_SERIES_ID'
        ]);
        exit;
    }
    
    // التحقق من وجود المسلسل
    $stmt = $pdo->prepare("SELECT * FROM series WHERE id = ? AND status = 'active'");
    $stmt->execute([$series_id]);
    $series = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$series) {
        http_response_code(404);
        echo json_encode([
            'success' => false,
            'message' => 'المسلسل غير موجود أو غير نشط',
            'error_code' => 'SERIES_NOT_FOUND'
        ]);
        exit;
    }
    
    // التحقق من رمز الوصول (اختياري)
    $userId = null;
    $userSubscription = 'free';
    
    if (isset($_SERVER['HTTP_AUTHORIZATION'])) {
        $token = str_replace('Bearer ', '', $_SERVER['HTTP_AUTHORIZATION']);
        $payload = json_decode(base64_decode($token), true);
        
        if ($payload && isset($payload['user_id']) && $payload['expires_at'] > time()) {
            $userId = $payload['user_id'];
            $userSubscription = $payload['subscription_type'] ?? 'free';
        }
    }
    
    // بناء الاستعلام للحلقات
    $episodesQuery = "
        SELECT 
            e.id,
            e.title,
            e.description,
            e.episode_number,
            e.season_number,
            e.video_url,
            e.thumbnail,
            e.duration,
            e.views,
            e.rating,
            e.created_at,
            e.is_premium
        FROM episodes e
        WHERE e.series_id = ? AND e.status = 'active'
    ";
    
    $params = [$series_id];
    
    if ($season !== null) {
        $episodesQuery .= " AND e.season_number = ?";
        $params[] = $season;
    }
    
    $episodesQuery .= " ORDER BY e.season_number ASC, e.episode_number ASC LIMIT ? OFFSET ?";
    $params[] = $limit;
    $params[] = $offset;
    
    $stmt = $pdo->prepare($episodesQuery);
    $stmt->execute($params);
    $episodes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // معالجة الحلقات وإضافة معلومات إضافية
    foreach ($episodes as &$episode) {
        // تحديد ما إذا كان المستخدم يمكنه مشاهدة الحلقة
        $episode['can_watch'] = !$episode['is_premium'] || $userSubscription !== 'free';
        
        // إخفاء رابط الفيديو للمحتوى المدفوع
        if ($episode['is_premium'] && $userSubscription === 'free') {
            $episode['video_url'] = null;
        }
        
        // تنسيق المدة
        if ($episode['duration']) {
            $minutes = floor($episode['duration'] / 60);
            $seconds = $episode['duration'] % 60;
            $episode['duration_formatted'] = sprintf('%02d:%02d', $minutes, $seconds);
        }
        
        // تنسيق التاريخ
        $episode['created_at'] = date('Y-m-d H:i:s', strtotime($episode['created_at']));
        
        // إضافة معلومات المشاهدة للمستخدم المسجل
        if ($userId) {
            $stmt = $pdo->prepare("
                SELECT watch_time, completed 
                FROM watch_history 
                WHERE user_id = ? AND content_type = 'episode' AND content_id = ? 
                ORDER BY watched_at DESC 
                LIMIT 1
            ");
            $stmt->execute([$userId, $episode['id']]);
            $watchHistory = $stmt->fetch(PDO::FETCH_ASSOC);
            
            $episode['watch_progress'] = $watchHistory ? [
                'watch_time' => intval($watchHistory['watch_time']),
                'completed' => (bool)$watchHistory['completed'],
                'progress_percentage' => $episode['duration'] > 0 ? 
                    min(100, ($watchHistory['watch_time'] / $episode['duration']) * 100) : 0
            ] : null;
        }
    }
    
    // الحصول على إجمالي عدد الحلقات
    $countQuery = "SELECT COUNT(*) FROM episodes WHERE series_id = ? AND status = 'active'";
    $countParams = [$series_id];
    
    if ($season !== null) {
        $countQuery .= " AND season_number = ?";
        $countParams[] = $season;
    }
    
    $stmt = $pdo->prepare($countQuery);
    $stmt->execute($countParams);
    $totalEpisodes = $stmt->fetchColumn();
    
    // الحصول على معلومات المواسم
    $seasonsQuery = "
        SELECT 
            season_number,
            COUNT(*) as episodes_count,
            MIN(created_at) as first_episode_date,
            MAX(created_at) as last_episode_date
        FROM episodes 
        WHERE series_id = ? AND status = 'active' 
        GROUP BY season_number 
        ORDER BY season_number ASC
    ";
    
    $stmt = $pdo->prepare($seasonsQuery);
    $stmt->execute([$series_id]);
    $seasons = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // إضافة معلومات التقدم لكل موسم (للمستخدمين المسجلين)
    if ($userId) {
        foreach ($seasons as &$seasonInfo) {
            $stmt = $pdo->prepare("
                SELECT 
                    COUNT(*) as total_episodes,
                    SUM(CASE WHEN wh.completed = 1 THEN 1 ELSE 0 END) as completed_episodes
                FROM episodes e
                LEFT JOIN watch_history wh ON e.id = wh.content_id 
                    AND wh.content_type = 'episode' 
                    AND wh.user_id = ?
                WHERE e.series_id = ? AND e.season_number = ? AND e.status = 'active'
            ");
            $stmt->execute([$userId, $series_id, $seasonInfo['season_number']]);
            $progress = $stmt->fetch(PDO::FETCH_ASSOC);
            
            $seasonInfo['progress'] = [
                'total_episodes' => intval($progress['total_episodes']),
                'completed_episodes' => intval($progress['completed_episodes']),
                'completion_percentage' => $progress['total_episodes'] > 0 ? 
                    ($progress['completed_episodes'] / $progress['total_episodes']) * 100 : 0
            ];
        }
    }
    
    // إعداد الاستجابة
    $response = [
        'success' => true,
        'message' => 'تم جلب الحلقات بنجاح',
        'data' => [
            'series' => [
                'id' => $series['id'],
                'title' => $series['title'],
                'description' => $series['description'],
                'poster' => $series['poster'],
                'total_seasons' => $series['total_seasons'],
                'rating' => floatval($series['rating']),
                'views' => intval($series['views'])
            ],
            'episodes' => $episodes,
            'seasons' => $seasons,
            'pagination' => [
                'total' => intval($totalEpisodes),
                'limit' => $limit,
                'offset' => $offset,
                'has_more' => ($offset + $limit) < $totalEpisodes
            ],
            'filters' => [
                'current_season' => $season,
                'available_seasons' => array_column($seasons, 'season_number')
            ],
            'user_info' => $userId ? [
                'user_id' => $userId,
                'subscription_type' => $userSubscription,
                'can_access_premium' => $userSubscription !== 'free'
            ] : null
        ]
    ];
    
    http_response_code(200);
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    
} catch (PDOException $e) {
    error_log("Database error in episodes API: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'خطأ في الخادم',
        'error_code' => 'DATABASE_ERROR'
    ]);
    
} catch (Exception $e) {
    error_log("General error in episodes API: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'خطأ غير متوقع',
        'error_code' => 'GENERAL_ERROR'
    ]);
}
?>
