@echo off
chcp 65001 >nul
title 🗄️ إعداد قاعدة البيانات على XAMPP

echo.
echo ========================================
echo 🗄️ إعداد قاعدة البيانات على XAMPP
echo ========================================
echo.

REM Check if XAMPP is installed
if not exist "C:\xampp\xampp-control.exe" (
    echo ❌ XAMPP غير مثبت في المسار الافتراضي
    echo    يرجى تثبيت XAMPP من: https://www.apachefriends.org/
    echo.
    pause
    exit /b 1
)

echo ✅ تم العثور على XAMPP
echo.

REM Check if XAMPP is running
echo 🔍 فحص حالة XAMPP...
tasklist /FI "IMAGENAME eq httpd.exe" 2>NUL | find /I /N "httpd.exe">NUL
if "%ERRORLEVEL%"=="0" (
    echo ✅ Apache يعمل
) else (
    echo ⚠️ Apache غير مشغل
    echo 🚀 تشغيل Apache...
    start "" "C:\xampp\apache\bin\httpd.exe"
    timeout /t 3 >nul
)

tasklist /FI "IMAGENAME eq mysqld.exe" 2>NUL | find /I /N "mysqld.exe">NUL
if "%ERRORLEVEL%"=="0" (
    echo ✅ MySQL يعمل
) else (
    echo ⚠️ MySQL غير مشغل
    echo 🚀 تشغيل MySQL...
    start "" "C:\xampp\mysql\bin\mysqld.exe" --defaults-file="C:\xampp\mysql\bin\my.ini"
    timeout /t 5 >nul
)

echo.
echo 🌐 فتح أداة إعداد قاعدة البيانات...
echo.

REM Open the setup page in browser
start http://localhost/amr2/flutter_module_1/backend/setup_xampp_database.php

echo ========================================
echo 📋 الخطوات التالية:
echo ========================================
echo.
echo 1. ستفتح صفحة الإعداد في المتصفح
echo 2. اتبع التعليمات المعروضة
echo 3. اختر "الإعداد التلقائي" للسهولة
echo.
echo 🔗 روابط مفيدة:
echo   📊 phpMyAdmin: http://localhost/phpmyadmin/
echo   🏠 الموقع: http://localhost/amr2/flutter_module_1/backend/homepage.php
echo   🎛️ لوحة الإدارة: http://localhost/amr2/flutter_module_1/backend/admin/dashboard.php
echo.

echo اضغط أي مفتاح لفتح phpMyAdmin...
pause >nul

start http://localhost/phpmyadmin/

echo.
echo ✅ تم! قاعدة البيانات جاهزة للاستخدام
echo.
pause
