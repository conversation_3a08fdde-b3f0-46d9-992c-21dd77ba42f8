name: shahid_app
description: Shahid - Professional Video Streaming Platform

# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter

  # UI & Design
  cupertino_icons: ^1.0.6
  google_fonts: ^6.1.0
  flutter_svg: ^2.0.9
  cached_network_image: ^3.3.0
  shimmer: ^3.0.0
  lottie: ^2.7.0
  flutter_staggered_grid_view: ^0.7.0
  carousel_slider: ^4.2.1
  smooth_page_indicator: ^1.1.0

  # State Management
  provider: ^6.1.1
  get: ^4.6.6

  # Networking
  http: ^1.1.0
  dio: ^5.3.2
  connectivity_plus: ^5.0.1

  # Storage & Caching
  shared_preferences: ^2.2.2
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  path_provider: ^2.1.1

  # Authentication & Security
  jwt_decoder: ^2.0.1
  crypto: ^3.0.3
  local_auth: ^2.1.6
  flutter_secure_storage: ^9.0.0

  # Video Player
  video_player: ^2.8.1
  chewie: ^1.7.4
  wakelock_plus: ^1.1.4
  screen_brightness: ^0.2.2+1

  # Media & Files
  image_picker: ^1.0.4
  file_picker: ^6.1.1
  permission_handler: ^11.0.1
  downloads_path_provider_28: ^0.1.2

  # Utilities
  intl: ^0.18.1
  url_launcher: ^6.2.1
  share_plus: ^7.2.1
  device_info_plus: ^9.1.0
  package_info_plus: ^4.2.0
  flutter_local_notifications: ^16.1.0
  firebase_messaging: ^14.7.6

  # UI Components
  flutter_rating_bar: ^4.0.1
  expandable: ^5.0.1
  pull_to_refresh: ^2.0.0
  infinite_scroll_pagination: ^4.0.0
  flutter_spinkit: ^5.2.0

  # Analytics & Crash Reporting
  firebase_analytics: ^10.7.4
  firebase_crashlytics: ^3.4.6
  firebase_core: ^2.24.2

dev_dependencies:
  flutter_test:
    sdk: flutter

  flutter_lints: ^3.0.0
  hive_generator: ^2.0.1
  build_runner: ^2.4.7

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  uses-material-design: true

  # Assets
  assets:
    - assets/images/
    - assets/icons/
    - assets/animations/
    - assets/fonts/

  # Fonts
  fonts:
    - family: Cairo
      fonts:
        - asset: assets/fonts/Cairo-Regular.ttf
        - asset: assets/fonts/Cairo-Bold.ttf
          weight: 700
        - asset: assets/fonts/Cairo-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/Cairo-Light.ttf
          weight: 300

    - family: Roboto
      fonts:
        - asset: assets/fonts/Roboto-Regular.ttf
        - asset: assets/fonts/Roboto-Bold.ttf
          weight: 700
        - asset: assets/fonts/Roboto-Medium.ttf
          weight: 500
