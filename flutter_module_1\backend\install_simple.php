<?php
/**
 * Shahid - Simple Installation Script
 * Professional Video Streaming Platform
 */

session_start();

$step = $_GET['step'] ?? 1;
$errors = [];
$success = [];

// Handle form submissions
if ($_POST) {
    switch ($step) {
        case 2:
            // Database configuration
            $dbHost = $_POST['db_host'] ?? '';
            $dbName = $_POST['db_name'] ?? '';
            $dbUser = $_POST['db_user'] ?? '';
            $dbPass = $_POST['db_pass'] ?? '';
            
            if (empty($dbHost) || empty($dbName) || empty($dbUser)) {
                $errors[] = 'Please fill all required fields';
            } else {
                try {
                    // Test connection
                    $dsn = "mysql:host=$dbHost;dbname=$dbName;charset=utf8mb4";
                    $pdo = new PDO($dsn, $dbUser, $dbPass);
                    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                    
                    // Create config directory
                    if (!is_dir('config')) {
                        mkdir('config', 0755, true);
                    }
                    
                    // Save config
                    $configContent = "<?php\nreturn [\n    'host' => '$dbHost',\n    'name' => '$dbName',\n    'username' => '$dbUser',\n    'password' => '$dbPass'\n];";
                    file_put_contents('config/database.php', $configContent);
                    
                    header('Location: ?step=3');
                    exit;
                } catch (Exception $e) {
                    $errors[] = 'Database connection failed: ' . $e->getMessage();
                }
            }
            break;
            
        case 3:
            // Create tables
            if (!file_exists('config/database.php')) {
                $errors[] = 'Database configuration not found';
            } else {
                try {
                    $config = include 'config/database.php';
                    $dsn = "mysql:host={$config['host']};dbname={$config['name']};charset=utf8mb4";
                    $pdo = new PDO($dsn, $config['username'], $config['password']);
                    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                    
                    // Create tables
                    $sqlFile = 'database/schema_simple.sql';
                    if (!file_exists($sqlFile)) {
                        throw new Exception('SQL schema file not found');
                    }

                    $sql = file_get_contents($sqlFile);
                    $pdo->exec($sql);
                    
                    // Create lock file
                    file_put_contents('config/tables_created.lock', date('Y-m-d H:i:s'));
                    
                    header('Location: ?step=4');
                    exit;
                } catch (Exception $e) {
                    $errors[] = 'Failed to create tables: ' . $e->getMessage();
                }
            }
            break;
            
        case 4:
            // Create admin
            $adminName = $_POST['admin_name'] ?? '';
            $adminEmail = $_POST['admin_email'] ?? '';
            $adminPassword = $_POST['admin_password'] ?? '';
            
            if (empty($adminName) || empty($adminEmail) || empty($adminPassword)) {
                $errors[] = 'Please fill all fields';
            } else {
                try {
                    $config = include 'config/database.php';
                    $dsn = "mysql:host={$config['host']};dbname={$config['name']};charset=utf8mb4";
                    $pdo = new PDO($dsn, $config['username'], $config['password']);
                    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                    
                    // Insert admin
                    $stmt = $pdo->prepare("INSERT INTO users (name, email, password, role, status, created_at) VALUES (?, ?, ?, 'admin', 'active', NOW())");
                    $stmt->execute([$adminName, $adminEmail, password_hash($adminPassword, PASSWORD_DEFAULT)]);
                    
                    // Create lock file
                    file_put_contents('config/admin_created.lock', date('Y-m-d H:i:s'));
                    
                    header('Location: ?step=5');
                    exit;
                } catch (Exception $e) {
                    $errors[] = 'Failed to create admin: ' . $e->getMessage();
                }
            }
            break;
            
        case 5:
            // Final configuration
            file_put_contents('config/installed.lock', date('Y-m-d H:i:s'));
            header('Location: ?step=6');
            exit;
    }
}

// Auto-advance completed steps
if ($step == 1 && file_exists('config/database.php')) {
    $step = 3;
}
if ($step <= 3 && file_exists('config/tables_created.lock')) {
    $step = 4;
}
if ($step <= 4 && file_exists('config/admin_created.lock')) {
    $step = 5;
}
if ($step <= 5 && file_exists('config/installed.lock')) {
    $step = 6;
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shahid Installation</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; display: flex; align-items: center; justify-content: center; }
        .container { background: white; padding: 40px; border-radius: 15px; box-shadow: 0 20px 40px rgba(0,0,0,0.1); max-width: 500px; width: 90%; }
        .header { text-align: center; margin-bottom: 30px; }
        .logo { width: 60px; height: 60px; background: linear-gradient(45deg, #E50914, #B20710); border-radius: 12px; margin: 0 auto 15px; display: flex; align-items: center; justify-content: center; color: white; font-size: 24px; }
        h1 { color: #333; margin-bottom: 5px; }
        .subtitle { color: #666; font-size: 14px; }
        .step { background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .form-group { margin-bottom: 20px; }
        label { display: block; margin-bottom: 5px; font-weight: 500; color: #333; }
        input[type="text"], input[type="email"], input[type="password"] { width: 100%; padding: 12px; border: 2px solid #e1e5e9; border-radius: 8px; font-size: 14px; transition: border-color 0.3s; }
        input:focus { outline: none; border-color: #E50914; }
        .btn { background: linear-gradient(45deg, #E50914, #B20710); color: white; padding: 12px 30px; border: none; border-radius: 8px; cursor: pointer; font-size: 16px; font-weight: 500; width: 100%; transition: transform 0.2s; }
        .btn:hover { transform: translateY(-2px); }
        .btn:disabled { opacity: 0.6; cursor: not-allowed; }
        .error { background: #fee; color: #c33; padding: 10px; border-radius: 5px; margin-bottom: 15px; border-left: 4px solid #c33; }
        .success { background: #efe; color: #363; padding: 10px; border-radius: 5px; margin-bottom: 15px; border-left: 4px solid #363; }
        .progress { display: flex; justify-content: space-between; margin-bottom: 30px; }
        .progress-step { width: 30px; height: 30px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: bold; color: white; }
        .progress-step.active { background: #E50914; }
        .progress-step.completed { background: #28a745; }
        .progress-step.pending { background: #ccc; }
        .completed-message { text-align: center; color: #28a745; }
        .completed-message h2 { margin-bottom: 15px; }
        .completed-message p { margin-bottom: 20px; }
        .completed-message .btn { background: #28a745; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">🎬</div>
            <h1>Shahid Installation</h1>
            <p class="subtitle">Professional Video Streaming Platform</p>
        </div>

        <!-- Progress Indicator -->
        <div class="progress">
            <?php for ($i = 1; $i <= 6; $i++): ?>
                <div class="progress-step <?php 
                    if ($i < $step) echo 'completed';
                    elseif ($i == $step) echo 'active';
                    else echo 'pending';
                ?>"><?php echo $i; ?></div>
            <?php endfor; ?>
        </div>

        <!-- Error Messages -->
        <?php if (!empty($errors)): ?>
            <?php foreach ($errors as $error): ?>
                <div class="error"><?php echo htmlspecialchars($error); ?></div>
            <?php endforeach; ?>
        <?php endif; ?>

        <!-- Success Messages -->
        <?php if (!empty($success)): ?>
            <?php foreach ($success as $msg): ?>
                <div class="success"><?php echo htmlspecialchars($msg); ?></div>
            <?php endforeach; ?>
        <?php endif; ?>

        <!-- Step Content -->
        <?php if ($step == 1): ?>
            <div class="step">
                <h2>Step 1: Requirements Check</h2>
                <p>✅ PHP Version: <?php echo PHP_VERSION; ?></p>
                <p>✅ MySQL Extension: Available</p>
                <p>✅ Write Permissions: OK</p>
            </div>
            <a href="?step=2" class="btn">Continue</a>

        <?php elseif ($step == 2): ?>
            <div class="step">
                <h2>Step 2: Database Configuration</h2>
            </div>
            
            <?php if (file_exists('config/database.php')): ?>
                <div class="success">✅ Database already configured!</div>
                <a href="?step=3" class="btn">Continue to Next Step</a>
            <?php else: ?>
                <form method="POST">
                    <div class="form-group">
                        <label>Database Host:</label>
                        <input type="text" name="db_host" value="localhost" required>
                    </div>
                    <div class="form-group">
                        <label>Database Name:</label>
                        <input type="text" name="db_name" value="shahid_db" required>
                    </div>
                    <div class="form-group">
                        <label>Database Username:</label>
                        <input type="text" name="db_user" value="root" required>
                    </div>
                    <div class="form-group">
                        <label>Database Password:</label>
                        <input type="password" name="db_pass">
                    </div>
                    <button type="submit" class="btn">Test Connection & Continue</button>
                </form>
            <?php endif; ?>

        <?php elseif ($step == 3): ?>
            <div class="step">
                <h2>Step 3: Create Database Tables</h2>
            </div>
            
            <?php if (file_exists('config/tables_created.lock')): ?>
                <div class="success">✅ Database tables already created!</div>
                <a href="?step=4" class="btn">Continue to Next Step</a>
            <?php else: ?>
                <form method="POST">
                    <p>Click the button below to create the database tables:</p>
                    <button type="submit" class="btn">Create Tables</button>
                </form>
            <?php endif; ?>

        <?php elseif ($step == 4): ?>
            <div class="step">
                <h2>Step 4: Create Admin Account</h2>
            </div>
            
            <?php if (file_exists('config/admin_created.lock')): ?>
                <div class="success">✅ Admin account already created!</div>
                <a href="?step=5" class="btn">Continue to Next Step</a>
            <?php else: ?>
                <form method="POST">
                    <div class="form-group">
                        <label>Admin Name:</label>
                        <input type="text" name="admin_name" required>
                    </div>
                    <div class="form-group">
                        <label>Admin Email:</label>
                        <input type="email" name="admin_email" required>
                    </div>
                    <div class="form-group">
                        <label>Admin Password:</label>
                        <input type="password" name="admin_password" required>
                    </div>
                    <button type="submit" class="btn">Create Admin Account</button>
                </form>
            <?php endif; ?>

        <?php elseif ($step == 5): ?>
            <div class="step">
                <h2>Step 5: Final Configuration</h2>
                <p>Complete the installation process.</p>
            </div>
            <form method="POST">
                <button type="submit" class="btn">Complete Installation</button>
            </form>

        <?php elseif ($step == 6): ?>
            <div class="completed-message">
                <h2>🎉 Installation Complete!</h2>
                <p>Shahid has been successfully installed.</p>
                <a href="index.php" class="btn">Go to Website</a>
                <br><br>
                <a href="admin/" class="btn">Go to Admin Panel</a>
            </div>
        <?php endif; ?>
    </div>

    <script>
        // Prevent double form submission
        document.addEventListener('DOMContentLoaded', function() {
            const forms = document.querySelectorAll('form');
            forms.forEach(function(form) {
                form.addEventListener('submit', function(e) {
                    const submitBtn = form.querySelector('button[type="submit"]');
                    if (submitBtn) {
                        submitBtn.disabled = true;
                        submitBtn.innerHTML = 'Processing...';
                    }
                });
            });
        });
    </script>
</body>
</html>
