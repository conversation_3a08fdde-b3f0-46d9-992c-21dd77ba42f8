<?php
/**
 * نظام المصادقة والأمان
 * Authentication and Security System
 */

// بدء الجلسة إذا لم تكن مبدوءة
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// إعدادات الأمان
ini_set('session.cookie_httponly', 1);
ini_set('session.use_only_cookies', 1);
ini_set('session.cookie_secure', 0); // تغيير إلى 1 في HTTPS

// تضمين إعدادات قاعدة البيانات
require_once __DIR__ . '/database.php';

/**
 * فحص تسجيل الدخول
 */
function isLoggedIn() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

/**
 * فحص صلاحيات المدير
 */
function isAdmin() {
    return isLoggedIn() && isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'admin';
}

/**
 * الحصول على معلومات المستخدم الحالي
 */
function getCurrentUser() {
    if (!isLoggedIn()) {
        return null;
    }
    
    try {
        $config = include __DIR__ . '/database.php';
        $pdo = new PDO(
            "mysql:host={$config['host']};dbname={$config['name']};charset=utf8mb4",
            $config['username'],
            $config['password']
        );
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
        $stmt->execute([$_SESSION['user_id']]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
        
    } catch (Exception $e) {
        return null;
    }
}

/**
 * تسجيل الدخول
 */
function login($email, $password) {
    try {
        $config = include __DIR__ . '/database.php';
        $pdo = new PDO(
            "mysql:host={$config['host']};dbname={$config['name']};charset=utf8mb4",
            $config['username'],
            $config['password']
        );
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        $stmt = $pdo->prepare("SELECT * FROM users WHERE email = ? AND status = 'active'");
        $stmt->execute([$email]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($user && password_verify($password, $user['password'])) {
            // تسجيل الدخول ناجح
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['user_name'] = $user['name'];
            $_SESSION['user_email'] = $user['email'];
            $_SESSION['user_role'] = $user['role'];
            $_SESSION['user_subscription'] = $user['subscription_type'] ?? 'free';
            
            // تحديث آخر تسجيل دخول
            $updateStmt = $pdo->prepare("UPDATE users SET updated_at = NOW() WHERE id = ?");
            $updateStmt->execute([$user['id']]);
            
            return true;
        }
        
        return false;
        
    } catch (Exception $e) {
        error_log("Login error: " . $e->getMessage());
        return false;
    }
}

/**
 * تسجيل الخروج
 */
function logout() {
    // مسح جميع متغيرات الجلسة
    $_SESSION = array();
    
    // حذف كوكيز الجلسة
    if (ini_get("session.use_cookies")) {
        $params = session_get_cookie_params();
        setcookie(session_name(), '', time() - 42000,
            $params["path"], $params["domain"],
            $params["secure"], $params["httponly"]
        );
    }
    
    // تدمير الجلسة
    session_destroy();
}

/**
 * التحقق من صلاحية الوصول للمحتوى
 */
function canAccessContent($contentType = 'free') {
    if (!isLoggedIn()) {
        return $contentType === 'free';
    }
    
    $userSubscription = $_SESSION['user_subscription'] ?? 'free';
    
    switch ($contentType) {
        case 'free':
            return true;
        case 'basic':
            return in_array($userSubscription, ['basic', 'premium']);
        case 'premium':
            return $userSubscription === 'premium';
        default:
            return false;
    }
}

/**
 * حماية الصفحات - يتطلب تسجيل الدخول
 */
function requireLogin($redirectTo = '/login.php') {
    if (!isLoggedIn()) {
        header("Location: $redirectTo");
        exit;
    }
}

/**
 * حماية صفحات المدير
 */
function requireAdmin($redirectTo = '/') {
    requireLogin();
    if (!isAdmin()) {
        header("Location: $redirectTo");
        exit;
    }
}

/**
 * تنظيف وتأمين البيانات
 */
function sanitizeInput($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
    return $data;
}

/**
 * توليد رمز CSRF
 */
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

/**
 * التحقق من رمز CSRF
 */
function verifyCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

/**
 * تسجيل محاولات تسجيل الدخول
 */
function logLoginAttempt($email, $success = false, $ip = null) {
    try {
        $config = include __DIR__ . '/database.php';
        $pdo = new PDO(
            "mysql:host={$config['host']};dbname={$config['name']};charset=utf8mb4",
            $config['username'],
            $config['password']
        );
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // إنشاء جدول سجل تسجيل الدخول إذا لم يكن موجوداً
        $pdo->exec("CREATE TABLE IF NOT EXISTS login_attempts (
            id INT AUTO_INCREMENT PRIMARY KEY,
            email VARCHAR(255) NOT NULL,
            ip_address VARCHAR(45),
            success BOOLEAN DEFAULT FALSE,
            attempted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_email (email),
            INDEX idx_ip (ip_address),
            INDEX idx_attempted_at (attempted_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
        
        $ip = $ip ?: $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        
        $stmt = $pdo->prepare("INSERT INTO login_attempts (email, ip_address, success) VALUES (?, ?, ?)");
        $stmt->execute([$email, $ip, $success ? 1 : 0]);
        
    } catch (Exception $e) {
        error_log("Login attempt logging error: " . $e->getMessage());
    }
}

/**
 * فحص محاولات تسجيل الدخول المتكررة
 */
function isRateLimited($email, $maxAttempts = 5, $timeWindow = 900) { // 15 دقيقة
    try {
        $config = include __DIR__ . '/database.php';
        $pdo = new PDO(
            "mysql:host={$config['host']};dbname={$config['name']};charset=utf8mb4",
            $config['username'],
            $config['password']
        );
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        $stmt = $pdo->prepare("
            SELECT COUNT(*) FROM login_attempts 
            WHERE email = ? AND success = FALSE 
            AND attempted_at > DATE_SUB(NOW(), INTERVAL ? SECOND)
        ");
        $stmt->execute([$email, $timeWindow]);
        
        return $stmt->fetchColumn() >= $maxAttempts;
        
    } catch (Exception $e) {
        error_log("Rate limiting check error: " . $e->getMessage());
        return false;
    }
}

/**
 * إنشاء جلسة آمنة
 */
function createSecureSession() {
    // تجديد معرف الجلسة لمنع session fixation
    session_regenerate_id(true);
    
    // إضافة معلومات أمان إضافية
    $_SESSION['user_agent'] = $_SERVER['HTTP_USER_AGENT'] ?? '';
    $_SESSION['ip_address'] = $_SERVER['REMOTE_ADDR'] ?? '';
    $_SESSION['login_time'] = time();
}

/**
 * التحقق من صحة الجلسة
 */
function validateSession() {
    if (!isLoggedIn()) {
        return false;
    }
    
    // التحقق من User Agent
    if (isset($_SESSION['user_agent']) && $_SESSION['user_agent'] !== ($_SERVER['HTTP_USER_AGENT'] ?? '')) {
        logout();
        return false;
    }
    
    // التحقق من انتهاء صلاحية الجلسة (24 ساعة)
    if (isset($_SESSION['login_time']) && (time() - $_SESSION['login_time']) > 86400) {
        logout();
        return false;
    }
    
    return true;
}

// التحقق التلقائي من صحة الجلسة
if (isLoggedIn()) {
    validateSession();
}

/**
 * دوال مساعدة للعرض
 */

// عرض اسم المستخدم
function getUserName() {
    return $_SESSION['user_name'] ?? 'ضيف';
}

// عرض نوع الاشتراك
function getSubscriptionType() {
    return $_SESSION['user_subscription'] ?? 'free';
}

// فحص انتهاء الاشتراك
function isSubscriptionActive() {
    if (!isLoggedIn()) {
        return false;
    }
    
    $user = getCurrentUser();
    if (!$user || !isset($user['subscription_end'])) {
        return false;
    }
    
    return strtotime($user['subscription_end']) > time();
}

/**
 * رسائل النظام
 */
function setFlashMessage($type, $message) {
    $_SESSION['flash_messages'][] = [
        'type' => $type,
        'message' => $message
    ];
}

function getFlashMessages() {
    $messages = $_SESSION['flash_messages'] ?? [];
    unset($_SESSION['flash_messages']);
    return $messages;
}

// تعيين المنطقة الزمنية
date_default_timezone_set('Asia/Riyadh');

// إعدادات الأمان الإضافية
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');
header('X-XSS-Protection: 1; mode=block');

// تنظيف الجلسات المنتهية الصلاحية (يتم تشغيله أحياناً)
if (rand(1, 100) === 1) {
    try {
        $config = include __DIR__ . '/database.php';
        $pdo = new PDO(
            "mysql:host={$config['host']};dbname={$config['name']};charset=utf8mb4",
            $config['username'],
            $config['password']
        );
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // حذف محاولات تسجيل الدخول القديمة (أكثر من أسبوع)
        $pdo->exec("DELETE FROM login_attempts WHERE attempted_at < DATE_SUB(NOW(), INTERVAL 7 DAY)");
        
    } catch (Exception $e) {
        error_log("Session cleanup error: " . $e->getMessage());
    }
}
?>
