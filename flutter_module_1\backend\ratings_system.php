<?php
/**
 * نظام التقييمات والتعليقات المتقدم
 * يدعم تقييم الأفلام والمسلسلات مع التعليقات والردود
 */

require_once 'config/database.php';

class RatingsSystem {
    private $db;

    public function __construct() {
        $this->db = Database::getInstance()->getConnection();
    }

    /**
     * إضافة تقييم جديد
     */
    public function addRating($userId, $contentId, $contentType, $rating, $comment = '') {
        try {
            // التحقق من صحة التقييم
            if ($rating < 1 || $rating > 10) {
                throw new Exception('التقييم يجب أن يكون بين 1 و 10');
            }

            // التحقق من وجود تقييم سابق
            $stmt = $this->db->prepare("
                SELECT id FROM ratings
                WHERE user_id = ? AND content_id = ? AND content_type = ?
            ");
            $stmt->execute([$userId, $contentId, $contentType]);

            if ($stmt->fetch()) {
                // تحديث التقييم الموجود
                $stmt = $this->db->prepare("
                    UPDATE ratings
                    SET rating = ?, comment = ?, updated_at = NOW()
                    WHERE user_id = ? AND content_id = ? AND content_type = ?
                ");
                $stmt->execute([$rating, $comment, $userId, $contentId, $contentType]);
                $message = 'تم تحديث التقييم بنجاح';
            } else {
                // إضافة تقييم جديد
                $stmt = $this->db->prepare("
                    INSERT INTO ratings (user_id, content_id, content_type, rating, comment, created_at)
                    VALUES (?, ?, ?, ?, ?, NOW())
                ");
                $stmt->execute([$userId, $contentId, $contentType, $rating, $comment]);
                $message = 'تم إضافة التقييم بنجاح';
            }

            // تحديث متوسط التقييم
            $this->updateAverageRating($contentId, $contentType);

            return [
                'success' => true,
                'message' => $message,
                'average_rating' => $this->getAverageRating($contentId, $contentType)
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * الحصول على تقييمات المحتوى
     */
    public function getContentRatings($contentId, $contentType, $limit = 20, $offset = 0) {
        $stmt = $this->db->prepare("
            SELECT r.*, u.name as user_name, u.avatar
            FROM ratings r
            LEFT JOIN users u ON r.user_id = u.id
            WHERE r.content_id = ? AND r.content_type = ?
            ORDER BY r.created_at DESC
            LIMIT ? OFFSET ?
        ");

        $stmt->execute([$contentId, $contentType, $limit, $offset]);
        $ratings = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // تنسيق التواريخ
        foreach ($ratings as &$rating) {
            $rating['created_at_formatted'] = $this->formatDate($rating['created_at']);
            $rating['updated_at_formatted'] = $this->formatDate($rating['updated_at']);
        }

        return $ratings;
    }

    /**
     * الحصول على متوسط التقييم
     */
    public function getAverageRating($contentId, $contentType) {
        $stmt = $this->db->prepare("
            SELECT AVG(rating) as average, COUNT(*) as count
            FROM ratings
            WHERE content_id = ? AND content_type = ?
        ");

        $stmt->execute([$contentId, $contentType]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);

        return [
            'average' => round($result['average'], 1),
            'count' => $result['count']
        ];
    }

    /**
     * تحديث متوسط التقييم في جدول المحتوى
     */
    private function updateAverageRating($contentId, $contentType) {
        $ratingData = $this->getAverageRating($contentId, $contentType);

        $table = ($contentType === 'movie') ? 'movies' : 'series';

        $stmt = $this->db->prepare("
            UPDATE {$table}
            SET rating = ?, rating_count = ?
            WHERE id = ?
        ");

        $stmt->execute([
            $ratingData['average'],
            $ratingData['count'],
            $contentId
        ]);
    }

    /**
     * حذف تقييم
     */
    public function deleteRating($userId, $ratingId) {
        try {
            // التحقق من ملكية التقييم
            $stmt = $this->db->prepare("
                SELECT content_id, content_type FROM ratings
                WHERE id = ? AND user_id = ?
            ");
            $stmt->execute([$ratingId, $userId]);
            $rating = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$rating) {
                throw new Exception('التقييم غير موجود أو غير مسموح بحذفه');
            }

            // حذف التقييم
            $stmt = $this->db->prepare("DELETE FROM ratings WHERE id = ?");
            $stmt->execute([$ratingId]);

            // تحديث متوسط التقييم
            $this->updateAverageRating($rating['content_id'], $rating['content_type']);

            return [
                'success' => true,
                'message' => 'تم حذف التقييم بنجاح'
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * الحصول على تقييم المستخدم للمحتوى
     */
    public function getUserRating($userId, $contentId, $contentType) {
        $stmt = $this->db->prepare("
            SELECT * FROM ratings
            WHERE user_id = ? AND content_id = ? AND content_type = ?
        ");

        $stmt->execute([$userId, $contentId, $contentType]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * الحصول على أفضل المحتوى حسب التقييم
     */
    public function getTopRatedContent($contentType, $limit = 10) {
        $table = ($contentType === 'movie') ? 'movies' : 'series';

        $stmt = $this->db->prepare("
            SELECT * FROM {$table}
            WHERE rating_count >= 5
            ORDER BY rating DESC, rating_count DESC
            LIMIT ?
        ");

        $stmt->execute([$limit]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * إضافة تعليق على تقييم
     */
    public function addComment($userId, $ratingId, $comment) {
        try {
            if (empty(trim($comment))) {
                throw new Exception('التعليق لا يمكن أن يكون فارغاً');
            }

            $stmt = $this->db->prepare("
                INSERT INTO rating_comments (rating_id, user_id, comment, created_at)
                VALUES (?, ?, ?, NOW())
            ");

            $stmt->execute([$ratingId, $userId, $comment]);

            return [
                'success' => true,
                'message' => 'تم إضافة التعليق بنجاح',
                'comment_id' => $this->db->lastInsertId()
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * الحصول على تعليقات التقييم
     */
    public function getRatingComments($ratingId) {
        $stmt = $this->db->prepare("
            SELECT rc.*, u.name as user_name, u.avatar
            FROM rating_comments rc
            LEFT JOIN users u ON rc.user_id = u.id
            WHERE rc.rating_id = ?
            ORDER BY rc.created_at ASC
        ");

        $stmt->execute([$ratingId]);
        $comments = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // تنسيق التواريخ
        foreach ($comments as &$comment) {
            $comment['created_at_formatted'] = $this->formatDate($comment['created_at']);
        }

        return $comments;
    }

    /**
     * تنسيق التاريخ
     */
    private function formatDate($date) {
        if (!$date) return '';

        $timestamp = strtotime($date);
        $now = time();
        $diff = $now - $timestamp;

        if ($diff < 60) {
            return 'الآن';
        } elseif ($diff < 3600) {
            $minutes = floor($diff / 60);
            return "منذ {$minutes} دقيقة";
        } elseif ($diff < 86400) {
            $hours = floor($diff / 3600);
            return "منذ {$hours} ساعة";
        } elseif ($diff < 2592000) {
            $days = floor($diff / 86400);
            return "منذ {$days} يوم";
        } else {
            return date('Y/m/d', $timestamp);
        }
    }

    /**
     * إحصائيات التقييمات
     */
    public function getRatingStats($contentId, $contentType) {
        // توزيع التقييمات
        $stmt = $this->db->prepare("
            SELECT rating, COUNT(*) as count
            FROM ratings
            WHERE content_id = ? AND content_type = ?
            GROUP BY rating
            ORDER BY rating DESC
        ");

        $stmt->execute([$contentId, $contentType]);
        $distribution = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // إحصائيات عامة
        $averageData = $this->getAverageRating($contentId, $contentType);

        return [
            'average' => $averageData['average'],
            'count' => $averageData['count'],
            'distribution' => $distribution
        ];
    }
}

// معالجة طلبات API
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    header('Content-Type: application/json');

    $ratingsSystem = new RatingsSystem();
    $action = $_POST['action'] ?? '';

    // محاكاة معرف المستخدم (في التطبيق الحقيقي سيأتي من الجلسة)
    $userId = $_POST['user_id'] ?? 1;

    switch ($action) {
        case 'add_rating':
            $contentId = $_POST['content_id'] ?? 0;
            $contentType = $_POST['content_type'] ?? 'movie';
            $rating = $_POST['rating'] ?? 0;
            $comment = $_POST['comment'] ?? '';

            echo json_encode($ratingsSystem->addRating($userId, $contentId, $contentType, $rating, $comment));
            break;

        case 'get_ratings':
            $contentId = $_POST['content_id'] ?? 0;
            $contentType = $_POST['content_type'] ?? 'movie';
            $limit = $_POST['limit'] ?? 20;
            $offset = $_POST['offset'] ?? 0;

            $ratings = $ratingsSystem->getContentRatings($contentId, $contentType, $limit, $offset);
            echo json_encode([
                'success' => true,
                'ratings' => $ratings
            ]);
            break;

        case 'get_average':
            $contentId = $_POST['content_id'] ?? 0;
            $contentType = $_POST['content_type'] ?? 'movie';

            echo json_encode([
                'success' => true,
                'data' => $ratingsSystem->getAverageRating($contentId, $contentType)
            ]);
            break;

        case 'delete_rating':
            $ratingId = $_POST['rating_id'] ?? 0;
            echo json_encode($ratingsSystem->deleteRating($userId, $ratingId));
            break;

        case 'get_user_rating':
            $contentId = $_POST['content_id'] ?? 0;
            $contentType = $_POST['content_type'] ?? 'movie';

            $userRating = $ratingsSystem->getUserRating($userId, $contentId, $contentType);
            echo json_encode([
                'success' => true,
                'rating' => $userRating
            ]);
            break;

        case 'get_top_rated':
            $contentType = $_POST['content_type'] ?? 'movie';
            $limit = $_POST['limit'] ?? 10;

            $topRated = $ratingsSystem->getTopRatedContent($contentType, $limit);
            echo json_encode([
                'success' => true,
                'content' => $topRated
            ]);
            break;

        case 'add_comment':
            $ratingId = $_POST['rating_id'] ?? 0;
            $comment = $_POST['comment'] ?? '';

            echo json_encode($ratingsSystem->addComment($userId, $ratingId, $comment));
            break;

        case 'get_comments':
            $ratingId = $_POST['rating_id'] ?? 0;

            $comments = $ratingsSystem->getRatingComments($ratingId);
            echo json_encode([
                'success' => true,
                'comments' => $comments
            ]);
            break;

        case 'get_stats':
            $contentId = $_POST['content_id'] ?? 0;
            $contentType = $_POST['content_type'] ?? 'movie';

            echo json_encode([
                'success' => true,
                'stats' => $ratingsSystem->getRatingStats($contentId, $contentType)
            ]);
            break;

        default:
            echo json_encode(['success' => false, 'error' => 'إجراء غير صالح']);
    }
    exit;
}
?>