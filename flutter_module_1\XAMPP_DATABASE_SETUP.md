# 🗄️ دليل إضافة قاعدة البيانات إلى XAMPP

## نظرة عامة

هذا الدليل يوضح كيفية إضافة قاعدة بيانات منصة شاهد إلى XAMPP بطرق مختلفة.

## 📋 المتطلبات

- ✅ XAMPP مثبت ومشغل
- ✅ Apache Server يعمل على المنفذ 80
- ✅ MySQL Server يعمل على المنفذ 3306
- ✅ PHP 7.4 أو أحدث

## 🚀 الطرق المتاحة

### 1. الطريقة الأسرع: الإعداد التلقائي ⚡

**الأسهل والأسرع - موصى بها للمبتدئين**

```
http://localhost/amr2/flutter_module_1/backend/quick_database_setup.php
```

**الخطوات:**
1. افتح الرابط أعلاه
2. اضغط "إعداد قاعدة البيانات الآن"
3. انتظر حتى اكتمال العملية
4. ستحصل على قاعدة بيانات كاملة مع بيانات تجريبية

**ما يتم إنشاؤه:**
- قاعدة البيانات `shahid_platform`
- 5 جداول أساسية
- حساب مدير: `<EMAIL>` / `admin123`
- بيانات تجريبية (أفلام، تصنيفات)

---

### 2. الطريقة الشاملة: دليل الإعداد المفصل 📋

**للمستخدمين المتقدمين الذين يريدون التحكم الكامل**

```
http://localhost/amr2/flutter_module_1/backend/setup_xampp_database.php
```

**الميزات:**
- فحص شامل لحالة XAMPP
- خيارات متعددة للإعداد
- دليل خطوة بخطوة
- روابط مفيدة لجميع الأدوات

---

### 3. الطريقة التقليدية: ملفات Batch 🖥️

**للمستخدمين الذين يفضلون سطر الأوامر**

#### Windows:
```bash
# انقر نقراً مزدوجاً على الملف
setup_database_xampp.bat
```

#### أو استخدم ملف التحديث الموجود:
```bash
update_database.bat
```

---

### 4. الطريقة اليدوية: phpMyAdmin 🔧

**للمستخدمين المتقدمين**

1. **افتح phpMyAdmin:**
   ```
   http://localhost/phpmyadmin/
   ```

2. **إنشاء قاعدة البيانات:**
   - اضغط "New"
   - اسم القاعدة: `shahid_platform`
   - Collation: `utf8mb4_unicode_ci`
   - اضغط "Create"

3. **استيراد الجداول:**
   - اختر قاعدة البيانات
   - تبويب "Import"
   - اختر `backend/database/schema.sql`
   - اضغط "Go"

4. **استيراد البيانات:**
   - كرر العملية مع `backend/database/production_data.sql`

---

## 🔍 التحقق من النجاح

### أدوات المراقبة:

```
# مراقبة مباشرة (الأفضل)
http://localhost/amr2/flutter_module_1/backend/dashboard_live.php

# حالة النظام
http://localhost/amr2/flutter_module_1/backend/system_status.php

# فهرس أدوات المراقبة
http://localhost/amr2/flutter_module_1/backend/monitoring_index.php
```

### فحص قاعدة البيانات:

```sql
-- في phpMyAdmin
USE shahid_platform;
SHOW TABLES;
SELECT COUNT(*) FROM users;
SELECT COUNT(*) FROM movies;
```

### النتيجة المتوقعة:
- ✅ قاعدة البيانات `shahid_platform` موجودة
- ✅ 5+ جداول أساسية
- ✅ حساب مدير واحد على الأقل
- ✅ بيانات تجريبية

---

## 🎯 الروابط الرئيسية بعد الإعداد

### الواجهات الأساسية:
```
🏠 الصفحة الرئيسية:
http://localhost/amr2/flutter_module_1/backend/homepage.php

🎛️ لوحة الإدارة:
http://localhost/amr2/flutter_module_1/backend/admin/dashboard.php

🔗 اختبار API:
http://localhost/amr2/flutter_module_1/backend/api/test.php
```

### أدوات المراقبة:
```
📊 مراقبة مباشرة:
http://localhost/amr2/flutter_module_1/backend/dashboard_live.php

🔍 فهرس أدوات المراقبة:
http://localhost/amr2/flutter_module_1/backend/monitoring_index.php
```

### أدوات الإدارة:
```
📊 phpMyAdmin:
http://localhost/phpmyadmin/

🎛️ XAMPP Control:
http://localhost/xampp/
```

---

## 👤 بيانات تسجيل الدخول الافتراضية

### حساب المدير:
```
البريد الإلكتروني: <EMAIL>
كلمة المرور: admin123
الدور: مدير
الاشتراك: Premium (10 سنوات)
```

### حساب تجريبي (إذا كان متوفراً):
```
البريد الإلكتروني: <EMAIL>
كلمة المرور: password123
الدور: مستخدم
الاشتراك: Free
```

---

## 🔧 استكشاف الأخطاء

### مشاكل شائعة:

#### 1. XAMPP غير مشغل
```
الحل:
- افتح XAMPP Control Panel
- اضغط Start بجانب Apache
- اضغط Start بجانب MySQL
- تأكد من اللون الأخضر
```

#### 2. خطأ في الاتصال بقاعدة البيانات
```
الحل:
- تأكد من تشغيل MySQL
- تحقق من المنفذ 3306
- استخدم أداة الإصلاح السريع
```

#### 3. الجداول مفقودة
```
الحل:
http://localhost/amr2/flutter_module_1/backend/fix_database.php
```

#### 4. حساب المدير مفقود
```
الحل:
http://localhost/amr2/flutter_module_1/backend/create_admin.php
```

---

## 📁 هيكل الملفات

```
flutter_module_1/
├── backend/
│   ├── database/
│   │   ├── schema.sql              # جداول قاعدة البيانات
│   │   ├── production_data.sql     # البيانات التجريبية
│   │   └── update_database.php     # أداة التحديث
│   ├── config/
│   │   └── database.php           # إعدادات الاتصال
│   ├── setup_xampp_database.php   # دليل الإعداد المفصل
│   ├── quick_database_setup.php   # الإعداد السريع
│   ├── fix_database.php          # إصلاح قاعدة البيانات
│   └── create_admin.php          # إنشاء حساب مدير
├── setup_database_xampp.bat      # ملف Batch للإعداد
└── update_database.bat           # ملف Batch للتحديث
```

---

## 🎉 الخطوات التالية

بعد إعداد قاعدة البيانات بنجاح:

1. **تصفح الموقع:** افتح الصفحة الرئيسية
2. **سجل دخول كمدير:** استخدم بيانات المدير
3. **استكشف لوحة الإدارة:** أضف محتوى جديد
4. **راقب النظام:** استخدم أدوات المراقبة المباشرة
5. **اختبر API:** تأكد من عمل جميع النقاط

---

## 📞 الدعم

في حالة وجود مشاكل:

1. **تحقق من أدوات المراقبة** للحصول على معلومات مفصلة
2. **استخدم أدوات الإصلاح** المتوفرة في النظام
3. **راجع سجل أخطاء PHP** في XAMPP
4. **تأكد من تشغيل جميع خدمات XAMPP**

---

**تم إنشاؤه بواسطة:** نظام إدارة قواعد البيانات لمنصة شاهد  
**التاريخ:** 2025-01-16  
**الإصدار:** 1.0.0
