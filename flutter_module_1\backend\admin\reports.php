<?php
session_start();

// التحقق من تسجيل الدخول والصلاحيات
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit;
}

try {
    $pdo = new PDO("mysql:host=localhost;dbname=shahid_platform;charset=utf8mb4", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // التحقق من صلاحيات الإدارة
    $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ? AND (subscription_type = 'vip' OR email LIKE '%admin%')");
    $stmt->execute([$_SESSION['user_id']]);
    $admin = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$admin) {
        header('Location: ../index.php');
        exit;
    }
    
    // معاملات التقرير
    $report_type = isset($_GET['type']) ? $_GET['type'] : 'overview';
    $date_from = isset($_GET['date_from']) ? $_GET['date_from'] : date('Y-m-01');
    $date_to = isset($_GET['date_to']) ? $_GET['date_to'] : date('Y-m-d');
    
    // تقرير عام
    $overview_stats = [
        'total_users' => $pdo->query("SELECT COUNT(*) FROM users")->fetchColumn(),
        'new_users_month' => $pdo->query("SELECT COUNT(*) FROM users WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)")->fetchColumn(),
        'active_subscriptions' => $pdo->query("SELECT COUNT(*) FROM user_subscriptions WHERE status = 'active'")->fetchColumn(),
        'total_content' => $pdo->query("SELECT COUNT(*) FROM movies")->fetchColumn() + $pdo->query("SELECT COUNT(*) FROM series")->fetchColumn(),
        'total_views' => $pdo->query("SELECT SUM(views) FROM movies")->fetchColumn() + $pdo->query("SELECT SUM(views) FROM series")->fetchColumn(),
        'monthly_revenue' => $pdo->query("SELECT SUM(amount) FROM payments WHERE status = 'completed' AND created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)")->fetchColumn(),
        'total_revenue' => $pdo->query("SELECT SUM(amount) FROM payments WHERE status = 'completed'")->fetchColumn(),
        'avg_session_time' => rand(15, 45) // محاكاة - يمكن تطويرها لاحقاً
    ];
    
    // تقرير المستخدمين
    $users_by_month = $pdo->query("
        SELECT DATE_FORMAT(created_at, '%Y-%m') as month, COUNT(*) as count 
        FROM users 
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 12 MONTH) 
        GROUP BY DATE_FORMAT(created_at, '%Y-%m') 
        ORDER BY month
    ")->fetchAll(PDO::FETCH_ASSOC);
    
    $users_by_subscription = $pdo->query("
        SELECT subscription_type, COUNT(*) as count 
        FROM users 
        GROUP BY subscription_type
    ")->fetchAll(PDO::FETCH_ASSOC);
    
    // تقرير المحتوى
    $content_stats = [
        'most_viewed_movies' => $pdo->query("SELECT title, views FROM movies ORDER BY views DESC LIMIT 10")->fetchAll(PDO::FETCH_ASSOC),
        'most_viewed_series' => $pdo->query("SELECT title, views FROM series ORDER BY views DESC LIMIT 10")->fetchAll(PDO::FETCH_ASSOC),
        'content_by_category' => $pdo->query("
            SELECT c.name, 
                   (SELECT COUNT(*) FROM movies WHERE category_id = c.id) + 
                   (SELECT COUNT(*) FROM series WHERE category_id = c.id) as total_content
            FROM categories c 
            ORDER BY total_content DESC
        ")->fetchAll(PDO::FETCH_ASSOC)
    ];
    
    // تقرير الإيرادات
    $revenue_by_month = $pdo->query("
        SELECT DATE_FORMAT(created_at, '%Y-%m') as month, SUM(amount) as revenue 
        FROM payments 
        WHERE status = 'completed' AND created_at >= DATE_SUB(NOW(), INTERVAL 12 MONTH) 
        GROUP BY DATE_FORMAT(created_at, '%Y-%m') 
        ORDER BY month
    ")->fetchAll(PDO::FETCH_ASSOC);
    
    $revenue_by_plan = $pdo->query("
        SELECT s.name, SUM(p.amount) as revenue, COUNT(p.id) as transactions 
        FROM payments p 
        JOIN subscriptions s ON p.subscription_id = s.id 
        WHERE p.status = 'completed' 
        GROUP BY s.id, s.name 
        ORDER BY revenue DESC
    ")->fetchAll(PDO::FETCH_ASSOC);
    
    // تقرير النشاط
    $activity_stats = $pdo->query("
        SELECT action, COUNT(*) as count 
        FROM activity_logs 
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) 
        GROUP BY action 
        ORDER BY count DESC 
        LIMIT 10
    ")->fetchAll(PDO::FETCH_ASSOC);
    
} catch (Exception $e) {
    $error = "خطأ في قاعدة البيانات: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التقارير والإحصائيات - Shahid Platform</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', 'Cairo', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #2d2d2d 100%);
            color: #fff;
            min-height: 100vh;
        }
        
        .admin-container {
            display: flex;
            min-height: 100vh;
        }
        
        .sidebar {
            width: 280px;
            background: rgba(47, 47, 47, 0.95);
            border-left: 2px solid rgba(229, 9, 20, 0.3);
            padding: 2rem 0;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
        }
        
        .sidebar-header {
            padding: 0 2rem 2rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            margin-bottom: 2rem;
        }
        
        .logo {
            color: #E50914;
            font-size: 1.8rem;
            font-weight: bold;
            text-decoration: none;
        }
        
        .sidebar-menu {
            list-style: none;
            padding: 0 1rem;
        }
        
        .sidebar-menu li {
            margin-bottom: 0.5rem;
        }
        
        .sidebar-menu a {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            color: #ccc;
            text-decoration: none;
            border-radius: 10px;
            transition: all 0.3s ease;
        }
        
        .sidebar-menu a:hover,
        .sidebar-menu a.active {
            background: rgba(229, 9, 20, 0.2);
            color: #fff;
        }
        
        .main-content {
            flex: 1;
            margin-right: 280px;
            padding: 2rem;
        }
        
        .header {
            background: rgba(47, 47, 47, 0.8);
            padding: 1.5rem 2rem;
            border-radius: 15px;
            margin-bottom: 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .header h1 {
            color: #E50914;
            font-size: 2rem;
        }
        
        .report-tabs {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .tab-btn {
            padding: 1rem 2rem;
            background: rgba(47, 47, 47, 0.8);
            color: #ccc;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .tab-btn:hover,
        .tab-btn.active {
            background: rgba(229, 9, 20, 0.2);
            color: #E50914;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: rgba(47, 47, 47, 0.8);
            padding: 2rem;
            border-radius: 15px;
            border: 2px solid rgba(229, 9, 20, 0.1);
            text-align: center;
            transition: all 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
            border-color: rgba(229, 9, 20, 0.3);
        }
        
        .stat-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            color: #E50914;
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: #E50914;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            color: #ccc;
            font-size: 1.1rem;
        }
        
        .chart-section {
            background: rgba(47, 47, 47, 0.8);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            border: 2px solid rgba(229, 9, 20, 0.1);
        }
        
        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
        }
        
        .chart-title {
            color: #E50914;
            font-size: 1.5rem;
        }
        
        .chart-container {
            position: relative;
            height: 400px;
            margin-bottom: 2rem;
        }
        
        .table-section {
            background: rgba(47, 47, 47, 0.8);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            border: 2px solid rgba(229, 9, 20, 0.1);
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }
        
        .data-table th,
        .data-table td {
            padding: 1rem;
            text-align: right;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .data-table th {
            background: rgba(229, 9, 20, 0.2);
            color: #E50914;
            font-weight: bold;
        }
        
        .data-table tr:hover {
            background: rgba(255, 255, 255, 0.05);
        }
        
        .export-section {
            background: rgba(47, 47, 47, 0.8);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            border: 2px solid rgba(229, 9, 20, 0.1);
            text-align: center;
        }
        
        .btn {
            padding: 0.75rem 1.5rem;
            background: linear-gradient(45deg, #E50914, #B8070F);
            color: white;
            border: none;
            border-radius: 8px;
            text-decoration: none;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            margin: 0.5rem;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(229, 9, 20, 0.3);
        }
        
        .btn-secondary {
            background: linear-gradient(45deg, #2196F3, #1976D2);
        }
        
        .btn-success {
            background: linear-gradient(45deg, #4CAF50, #45a049);
        }
        
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }
            
            .main-content {
                margin-right: 0;
                padding: 1rem;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .report-tabs {
                flex-direction: column;
            }
            
            .chart-container {
                height: 300px;
            }
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <aside class="sidebar">
            <div class="sidebar-header">
                <a href="../index.php" class="logo">
                    <i class="fas fa-play-circle"></i> شاهد
                </a>
            </div>

            <ul class="sidebar-menu">
                <li><a href="dashboard.php"><i class="fas fa-tachometer-alt"></i> لوحة التحكم</a></li>
                <li><a href="content_manager.php"><i class="fas fa-film"></i> مدير المحتوى</a></li>
                <li><a href="upload_center.php"><i class="fas fa-upload"></i> مركز الرفع</a></li>
                <li><a href="users.php"><i class="fas fa-users"></i> إدارة المستخدمين</a></li>
                <li><a href="subscription_manager.php"><i class="fas fa-crown"></i> مدير الاشتراكات</a></li>
                <li><a href="payments.php"><i class="fas fa-credit-card"></i> المدفوعات</a></li>
                <li><a href="reports.php" class="active"><i class="fas fa-chart-bar"></i> التقارير</a></li>
                <li><a href="settings.php"><i class="fas fa-cog"></i> الإعدادات</a></li>
                <li><a href="../logout.php"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a></li>
            </ul>
        </aside>

        <main class="main-content">
            <div class="header">
                <h1><i class="fas fa-chart-bar"></i> التقارير والإحصائيات</h1>
            </div>

            <!-- تبويبات التقارير -->
            <div class="report-tabs">
                <a href="?type=overview" class="tab-btn <?php echo $report_type === 'overview' ? 'active' : ''; ?>">
                    <i class="fas fa-chart-pie"></i> نظرة عامة
                </a>
                <a href="?type=users" class="tab-btn <?php echo $report_type === 'users' ? 'active' : ''; ?>">
                    <i class="fas fa-users"></i> المستخدمين
                </a>
                <a href="?type=content" class="tab-btn <?php echo $report_type === 'content' ? 'active' : ''; ?>">
                    <i class="fas fa-film"></i> المحتوى
                </a>
                <a href="?type=revenue" class="tab-btn <?php echo $report_type === 'revenue' ? 'active' : ''; ?>">
                    <i class="fas fa-dollar-sign"></i> الإيرادات
                </a>
            </div>

            <?php if ($report_type === 'overview'): ?>
                <!-- تقرير عام -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon"><i class="fas fa-users"></i></div>
                        <div class="stat-number"><?php echo number_format($overview_stats['total_users']); ?></div>
                        <div class="stat-label">إجمالي المستخدمين</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon"><i class="fas fa-user-plus"></i></div>
                        <div class="stat-number"><?php echo number_format($overview_stats['new_users_month']); ?></div>
                        <div class="stat-label">مستخدمين جدد هذا الشهر</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon"><i class="fas fa-crown"></i></div>
                        <div class="stat-number"><?php echo number_format($overview_stats['active_subscriptions']); ?></div>
                        <div class="stat-label">اشتراكات نشطة</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon"><i class="fas fa-film"></i></div>
                        <div class="stat-number"><?php echo number_format($overview_stats['total_content']); ?></div>
                        <div class="stat-label">إجمالي المحتوى</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon"><i class="fas fa-eye"></i></div>
                        <div class="stat-number"><?php echo number_format($overview_stats['total_views']); ?></div>
                        <div class="stat-label">إجمالي المشاهدات</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon"><i class="fas fa-dollar-sign"></i></div>
                        <div class="stat-number"><?php echo number_format($overview_stats['total_revenue'], 2); ?> ر.س</div>
                        <div class="stat-label">إجمالي الإيرادات</div>
                    </div>
                </div>

                <!-- رسم بياني للمستخدمين الجدد -->
                <div class="chart-section">
                    <div class="chart-header">
                        <h3 class="chart-title">المستخدمين الجدد (آخر 12 شهر)</h3>
                    </div>
                    <div class="chart-container">
                        <canvas id="usersChart"></canvas>
                    </div>
                </div>

            <?php elseif ($report_type === 'users'): ?>
                <!-- تقرير المستخدمين -->
                <div class="chart-section">
                    <div class="chart-header">
                        <h3 class="chart-title">توزيع المستخدمين حسب نوع الاشتراك</h3>
                    </div>
                    <div class="chart-container">
                        <canvas id="subscriptionChart"></canvas>
                    </div>
                </div>

                <div class="table-section">
                    <h3 class="chart-title">المستخدمين حسب نوع الاشتراك</h3>
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>نوع الاشتراك</th>
                                <th>عدد المستخدمين</th>
                                <th>النسبة</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            $total_users = array_sum(array_column($users_by_subscription, 'count'));
                            foreach ($users_by_subscription as $sub):
                                $percentage = $total_users > 0 ? ($sub['count'] / $total_users) * 100 : 0;
                            ?>
                                <tr>
                                    <td><?php echo ucfirst($sub['subscription_type']); ?></td>
                                    <td><?php echo number_format($sub['count']); ?></td>
                                    <td><?php echo number_format($percentage, 1); ?>%</td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

            <?php elseif ($report_type === 'content'): ?>
                <!-- تقرير المحتوى -->
                <div class="table-section">
                    <h3 class="chart-title">الأفلام الأكثر مشاهدة</h3>
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>العنوان</th>
                                <th>عدد المشاهدات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($content_stats['most_viewed_movies'] as $movie): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($movie['title']); ?></td>
                                    <td><?php echo number_format($movie['views']); ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

                <div class="table-section">
                    <h3 class="chart-title">المسلسلات الأكثر مشاهدة</h3>
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>العنوان</th>
                                <th>عدد المشاهدات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($content_stats['most_viewed_series'] as $series): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($series['title']); ?></td>
                                    <td><?php echo number_format($series['views']); ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

            <?php elseif ($report_type === 'revenue'): ?>
                <!-- تقرير الإيرادات -->
                <div class="chart-section">
                    <div class="chart-header">
                        <h3 class="chart-title">الإيرادات الشهرية (آخر 12 شهر)</h3>
                    </div>
                    <div class="chart-container">
                        <canvas id="revenueChart"></canvas>
                    </div>
                </div>

                <div class="table-section">
                    <h3 class="chart-title">الإيرادات حسب خطة الاشتراك</h3>
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>خطة الاشتراك</th>
                                <th>عدد المعاملات</th>
                                <th>إجمالي الإيرادات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($revenue_by_plan as $plan): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($plan['name']); ?></td>
                                    <td><?php echo number_format($plan['transactions']); ?></td>
                                    <td><?php echo number_format($plan['revenue'], 2); ?> ر.س</td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>

            <!-- قسم التصدير -->
            <div class="export-section">
                <h3 style="color: #E50914; margin-bottom: 1rem;">
                    <i class="fas fa-download"></i> تصدير التقارير
                </h3>
                <p style="color: #ccc; margin-bottom: 2rem;">يمكنك تصدير التقارير بصيغ مختلفة</p>

                <button class="btn" onclick="exportToPDF()">
                    <i class="fas fa-file-pdf"></i> تصدير PDF
                </button>
                <button class="btn btn-secondary" onclick="exportToExcel()">
                    <i class="fas fa-file-excel"></i> تصدير Excel
                </button>
                <button class="btn btn-success" onclick="printReport()">
                    <i class="fas fa-print"></i> طباعة
                </button>
            </div>
        </main>
    </div>

    <script>
        // رسم بياني للمستخدمين الجدد
        <?php if ($report_type === 'overview'): ?>
        const usersCtx = document.getElementById('usersChart').getContext('2d');
        new Chart(usersCtx, {
            type: 'line',
            data: {
                labels: <?php echo json_encode(array_column($users_by_month, 'month')); ?>,
                datasets: [{
                    label: 'مستخدمين جدد',
                    data: <?php echo json_encode(array_column($users_by_month, 'count')); ?>,
                    borderColor: '#E50914',
                    backgroundColor: 'rgba(229, 9, 20, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        labels: {
                            color: '#fff'
                        }
                    }
                },
                scales: {
                    x: {
                        ticks: {
                            color: '#ccc'
                        },
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        }
                    },
                    y: {
                        ticks: {
                            color: '#ccc'
                        },
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        }
                    }
                }
            }
        });
        <?php endif; ?>

        // رسم بياني لتوزيع الاشتراكات
        <?php if ($report_type === 'users'): ?>
        const subscriptionCtx = document.getElementById('subscriptionChart').getContext('2d');
        new Chart(subscriptionCtx, {
            type: 'doughnut',
            data: {
                labels: <?php echo json_encode(array_column($users_by_subscription, 'subscription_type')); ?>,
                datasets: [{
                    data: <?php echo json_encode(array_column($users_by_subscription, 'count')); ?>,
                    backgroundColor: ['#E50914', '#FF6B35', '#4CAF50', '#2196F3']
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        labels: {
                            color: '#fff'
                        }
                    }
                }
            }
        });
        <?php endif; ?>

        // رسم بياني للإيرادات
        <?php if ($report_type === 'revenue'): ?>
        const revenueCtx = document.getElementById('revenueChart').getContext('2d');
        new Chart(revenueCtx, {
            type: 'bar',
            data: {
                labels: <?php echo json_encode(array_column($revenue_by_month, 'month')); ?>,
                datasets: [{
                    label: 'الإيرادات (ر.س)',
                    data: <?php echo json_encode(array_column($revenue_by_month, 'revenue')); ?>,
                    backgroundColor: 'rgba(229, 9, 20, 0.8)',
                    borderColor: '#E50914',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        labels: {
                            color: '#fff'
                        }
                    }
                },
                scales: {
                    x: {
                        ticks: {
                            color: '#ccc'
                        },
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        }
                    },
                    y: {
                        ticks: {
                            color: '#ccc'
                        },
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        }
                    }
                }
            }
        });
        <?php endif; ?>

        // وظائف التصدير
        function exportToPDF() {
            alert('ميزة تصدير PDF قيد التطوير');
        }

        function exportToExcel() {
            alert('ميزة تصدير Excel قيد التطوير');
        }

        function printReport() {
            window.print();
        }

        console.log('نظام التقارير جاهز!');
    </script>
</body>
</html>
