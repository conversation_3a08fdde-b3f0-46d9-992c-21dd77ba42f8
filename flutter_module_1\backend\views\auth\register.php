<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= htmlspecialchars($page_title ?? 'إنشاء حساب - Shahid') ?></title>
    
    <!-- CSS -->
    <link rel="stylesheet" href="/assets/css/bootstrap.min.css">
    <link rel="stylesheet" href="/assets/css/auth.css">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body class="auth-body">
    
    <!-- Background Video -->
    <div class="auth-background">
        <video autoplay muted loop>
            <source src="/assets/videos/auth-bg.mp4" type="video/mp4">
        </video>
        <div class="auth-overlay"></div>
    </div>
    
    <!-- Navigation -->
    <nav class="auth-navbar">
        <div class="container">
            <a href="/" class="navbar-brand">
                <img src="/assets/images/logo.png" alt="Shahid" height="40">
            </a>
        </div>
    </nav>
    
    <!-- Main Content -->
    <div class="auth-container">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-6 col-lg-5">
                    <div class="auth-card">
                        <div class="auth-header">
                            <h2>إنشاء حساب جديد</h2>
                            <p>انضم إلى Shahid واستمتع بأفضل المحتوى</p>
                        </div>
                        
                        <?php if (isset($errors) && !empty($errors)): ?>
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-circle me-2"></i>
                                <ul class="mb-0">
                                    <?php foreach ($errors as $error): ?>
                                        <li><?= htmlspecialchars($error) ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        <?php endif; ?>
                        
                        <form method="POST" class="auth-form">
                            <input type="hidden" name="csrf_token" value="<?= $csrf_token ?>">
                            
                            <div class="form-group">
                                <label for="name">الاسم الكامل</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-user"></i>
                                    </span>
                                    <input type="text" 
                                           class="form-control" 
                                           id="name" 
                                           name="name" 
                                           placeholder="أدخل اسمك الكامل"
                                           value="<?= htmlspecialchars($form_data['name'] ?? '') ?>"
                                           required>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label for="email">البريد الإلكتروني</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-envelope"></i>
                                    </span>
                                    <input type="email" 
                                           class="form-control" 
                                           id="email" 
                                           name="email" 
                                           placeholder="أدخل بريدك الإلكتروني"
                                           value="<?= htmlspecialchars($form_data['email'] ?? '') ?>"
                                           required>
                                </div>
                                <div class="form-text">سنرسل لك رسائل تأكيد على هذا البريد</div>
                            </div>
                            
                            <div class="form-group">
                                <label for="password">كلمة المرور</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-lock"></i>
                                    </span>
                                    <input type="password" 
                                           class="form-control" 
                                           id="password" 
                                           name="password" 
                                           placeholder="أدخل كلمة مرور قوية"
                                           required>
                                    <button type="button" class="btn btn-outline-secondary toggle-password" data-target="password">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                                <div class="password-strength">
                                    <div class="strength-bar">
                                        <div class="strength-fill"></div>
                                    </div>
                                    <div class="strength-text">قوة كلمة المرور</div>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label for="confirm_password">تأكيد كلمة المرور</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-lock"></i>
                                    </span>
                                    <input type="password" 
                                           class="form-control" 
                                           id="confirm_password" 
                                           name="confirm_password" 
                                           placeholder="أعد إدخال كلمة المرور"
                                           required>
                                    <button type="button" class="btn btn-outline-secondary toggle-password" data-target="confirm_password">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <div class="form-check">
                                    <input type="checkbox" class="form-check-input" id="terms" name="terms" required>
                                    <label class="form-check-label" for="terms">
                                        أوافق على <a href="/terms" target="_blank">شروط الاستخدام</a> و 
                                        <a href="/privacy" target="_blank">سياسة الخصوصية</a>
                                    </label>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <div class="form-check">
                                    <input type="checkbox" class="form-check-input" id="newsletter" name="newsletter">
                                    <label class="form-check-label" for="newsletter">
                                        أريد تلقي النشرة الإخبارية والعروض الخاصة
                                    </label>
                                </div>
                            </div>
                            
                            <button type="submit" class="btn btn-primary btn-lg w-100">
                                <i class="fas fa-user-plus me-2"></i>
                                إنشاء حساب
                            </button>
                        </form>
                        
                        <div class="auth-divider">
                            <span>أو</span>
                        </div>
                        
                        <div class="social-login">
                            <button class="btn btn-social btn-google">
                                <i class="fab fa-google me-2"></i>
                                التسجيل بـ Google
                            </button>
                            <button class="btn btn-social btn-facebook">
                                <i class="fab fa-facebook-f me-2"></i>
                                التسجيل بـ Facebook
                            </button>
                        </div>
                        
                        <div class="auth-footer">
                            <div class="text-center">
                                <span>لديك حساب بالفعل؟</span>
                                <a href="/login" class="login-link">تسجيل الدخول</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/assets/js/jquery.min.js"></script>
    
    <script>
        // Toggle password visibility
        $('.toggle-password').click(function() {
            const target = $(this).data('target');
            const passwordInput = $('#' + target);
            const icon = $(this).find('i');
            
            if (passwordInput.attr('type') === 'password') {
                passwordInput.attr('type', 'text');
                icon.removeClass('fa-eye').addClass('fa-eye-slash');
            } else {
                passwordInput.attr('type', 'password');
                icon.removeClass('fa-eye-slash').addClass('fa-eye');
            }
        });
        
        // Password strength checker
        $('#password').on('input', function() {
            const password = $(this).val();
            const strength = checkPasswordStrength(password);
            updatePasswordStrength(strength);
        });
        
        // Password confirmation checker
        $('#confirm_password').on('input', function() {
            const password = $('#password').val();
            const confirmPassword = $(this).val();
            
            if (confirmPassword && password !== confirmPassword) {
                $(this).addClass('is-invalid');
                $(this).siblings('.invalid-feedback').remove();
                $(this).parent().after('<div class="invalid-feedback">كلمات المرور غير متطابقة</div>');
            } else {
                $(this).removeClass('is-invalid');
                $(this).siblings('.invalid-feedback').remove();
            }
        });
        
        // Form validation
        $('.auth-form').on('submit', function(e) {
            const name = $('#name').val().trim();
            const email = $('#email').val().trim();
            const password = $('#password').val();
            const confirmPassword = $('#confirm_password').val();
            const terms = $('#terms').is(':checked');
            
            let isValid = true;
            let errors = [];
            
            if (!name || name.length < 2) {
                errors.push('يجب أن يكون الاسم أكثر من حرفين');
                isValid = false;
            }
            
            if (!email || !isValidEmail(email)) {
                errors.push('يرجى إدخال بريد إلكتروني صحيح');
                isValid = false;
            }
            
            if (!password || password.length < 8) {
                errors.push('يجب أن تكون كلمة المرور 8 أحرف على الأقل');
                isValid = false;
            }
            
            if (password !== confirmPassword) {
                errors.push('كلمات المرور غير متطابقة');
                isValid = false;
            }
            
            if (!terms) {
                errors.push('يجب الموافقة على شروط الاستخدام');
                isValid = false;
            }
            
            if (!isValid) {
                e.preventDefault();
                showAlert(errors.join('<br>'), 'danger');
                return false;
            }
            
            // Show loading
            const submitBtn = $(this).find('button[type="submit"]');
            submitBtn.prop('disabled', true);
            submitBtn.html('<i class="fas fa-spinner fa-spin me-2"></i>جاري إنشاء الحساب...');
        });
        
        // Social login handlers
        $('.btn-google').click(function() {
            window.location.href = '/auth/google';
        });
        
        $('.btn-facebook').click(function() {
            window.location.href = '/auth/facebook';
        });
        
        // Helper functions
        function checkPasswordStrength(password) {
            let score = 0;
            
            if (password.length >= 8) score++;
            if (password.match(/[a-z]/)) score++;
            if (password.match(/[A-Z]/)) score++;
            if (password.match(/[0-9]/)) score++;
            if (password.match(/[^a-zA-Z0-9]/)) score++;
            
            return score;
        }
        
        function updatePasswordStrength(strength) {
            const strengthBar = $('.strength-fill');
            const strengthText = $('.strength-text');
            
            const levels = ['ضعيف جداً', 'ضعيف', 'متوسط', 'قوي', 'قوي جداً'];
            const colors = ['#dc3545', '#fd7e14', '#ffc107', '#28a745', '#20c997'];
            const widths = ['20%', '40%', '60%', '80%', '100%'];
            
            if (strength > 0) {
                strengthBar.css({
                    'width': widths[strength - 1],
                    'background-color': colors[strength - 1]
                });
                strengthText.text('قوة كلمة المرور: ' + levels[strength - 1]);
            } else {
                strengthBar.css('width', '0%');
                strengthText.text('قوة كلمة المرور');
            }
        }
        
        function isValidEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        }
        
        function showAlert(message, type) {
            const alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            
            $('.auth-form').before(alertHtml);
            
            setTimeout(function() {
                $('.alert').fadeOut();
            }, 8000);
        }
        
        // Auto-focus first input
        $(document).ready(function() {
            $('#name').focus();
        });
    </script>
    
</body>
</html>
