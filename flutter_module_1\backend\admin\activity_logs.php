<?php
session_start();

// التحقق من تسجيل الدخول والصلاحيات
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit;
}

try {
    $pdo = new PDO("mysql:host=localhost;dbname=shahid_platform;charset=utf8mb4", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // التحقق من صلاحيات الإدارة
    $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ? AND (subscription_type = 'vip' OR email LIKE '%admin%')");
    $stmt->execute([$_SESSION['user_id']]);
    $admin = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$admin) {
        header('Location: ../index.php');
        exit;
    }
    
    $message = '';
    $error = '';
    
    // معالجة العمليات
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'clear_old_logs':
                    $days = intval($_POST['days']);
                    if ($days > 0) {
                        $stmt = $pdo->prepare("DELETE FROM activity_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL ? DAY)");
                        $stmt->execute([$days]);
                        $deletedCount = $stmt->rowCount();
                        $message = "تم حذف $deletedCount سجل قديم";
                    }
                    break;
                    
                case 'export_logs':
                    $startDate = $_POST['start_date'];
                    $endDate = $_POST['end_date'];
                    
                    // تصدير السجلات (محاكاة)
                    $message = "تم تصدير السجلات من $startDate إلى $endDate";
                    break;
            }
        }
    }
    
    // معاملات الاستعلام
    $limit = 50;
    $offset = isset($_GET['page']) ? (max(1, intval($_GET['page'])) - 1) * $limit : 0;
    $action_filter = isset($_GET['action']) ? $_GET['action'] : '';
    $user_filter = isset($_GET['user_id']) ? intval($_GET['user_id']) : 0;
    $date_filter = isset($_GET['date']) ? $_GET['date'] : '';
    
    // بناء الاستعلام
    $where_conditions = [];
    $params = [];
    
    if ($action_filter) {
        $where_conditions[] = "al.action = ?";
        $params[] = $action_filter;
    }
    
    if ($user_filter) {
        $where_conditions[] = "al.user_id = ?";
        $params[] = $user_filter;
    }
    
    if ($date_filter) {
        $where_conditions[] = "DATE(al.created_at) = ?";
        $params[] = $date_filter;
    }
    
    $where_clause = $where_conditions ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
    
    // الحصول على السجلات
    $logs_query = "
        SELECT al.*, u.username, u.email, u.full_name
        FROM activity_logs al 
        LEFT JOIN users u ON al.user_id = u.id 
        $where_clause
        ORDER BY al.created_at DESC 
        LIMIT ? OFFSET ?
    ";
    
    $params[] = $limit;
    $params[] = $offset;
    
    $stmt = $pdo->prepare($logs_query);
    $stmt->execute($params);
    $logs = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // الحصول على إجمالي العدد
    $count_params = array_slice($params, 0, -2);
    $count_query = "
        SELECT COUNT(*) 
        FROM activity_logs al 
        LEFT JOIN users u ON al.user_id = u.id 
        $where_clause
    ";
    
    $stmt = $pdo->prepare($count_query);
    $stmt->execute($count_params);
    $total_logs = $stmt->fetchColumn();
    
    // إحصائيات
    $stats = [
        'total_logs' => $pdo->query("SELECT COUNT(*) FROM activity_logs")->fetchColumn(),
        'today_logs' => $pdo->query("SELECT COUNT(*) FROM activity_logs WHERE DATE(created_at) = CURDATE()")->fetchColumn(),
        'week_logs' => $pdo->query("SELECT COUNT(*) FROM activity_logs WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)")->fetchColumn(),
        'unique_users' => $pdo->query("SELECT COUNT(DISTINCT user_id) FROM activity_logs WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)")->fetchColumn()
    ];
    
    // أكثر الأنشطة شيوعاً
    $popular_actions = $pdo->query("
        SELECT action, COUNT(*) as count 
        FROM activity_logs 
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
        GROUP BY action 
        ORDER BY count DESC 
        LIMIT 10
    ")->fetchAll(PDO::FETCH_ASSOC);
    
    // المستخدمين الأكثر نشاطاً
    $active_users = $pdo->query("
        SELECT u.username, u.full_name, COUNT(*) as activity_count
        FROM activity_logs al
        JOIN users u ON al.user_id = u.id
        WHERE al.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
        GROUP BY al.user_id
        ORDER BY activity_count DESC
        LIMIT 10
    ")->fetchAll(PDO::FETCH_ASSOC);
    
    // الحصول على قائمة المستخدمين للفلترة
    $users = $pdo->query("
        SELECT DISTINCT u.id, u.username, u.full_name 
        FROM users u
        JOIN activity_logs al ON u.id = al.user_id
        ORDER BY u.full_name ASC 
        LIMIT 100
    ")->fetchAll(PDO::FETCH_ASSOC);
    
} catch (Exception $e) {
    $error = "خطأ في قاعدة البيانات: " . $e->getMessage();
}

function getActionText($action) {
    $actions = [
        'login' => 'تسجيل دخول',
        'logout' => 'تسجيل خروج',
        'register' => 'إنشاء حساب',
        'watch_start' => 'بدء مشاهدة',
        'watch_complete' => 'إكمال مشاهدة',
        'add_favorite' => 'إضافة للمفضلة',
        'remove_favorite' => 'إزالة من المفضلة',
        'subscription_purchase' => 'شراء اشتراك',
        'subscription_cancel' => 'إلغاء اشتراك',
        'profile_update' => 'تحديث الملف الشخصي',
        'password_change' => 'تغيير كلمة المرور',
        'content_upload' => 'رفع محتوى',
        'content_edit' => 'تعديل محتوى',
        'content_delete' => 'حذف محتوى',
        'payment_success' => 'دفع ناجح',
        'payment_failed' => 'فشل في الدفع'
    ];
    return $actions[$action] ?? $action;
}

function getActionIcon($action) {
    $icons = [
        'login' => 'fas fa-sign-in-alt',
        'logout' => 'fas fa-sign-out-alt',
        'register' => 'fas fa-user-plus',
        'watch_start' => 'fas fa-play',
        'watch_complete' => 'fas fa-check-circle',
        'add_favorite' => 'fas fa-heart',
        'remove_favorite' => 'far fa-heart',
        'subscription_purchase' => 'fas fa-crown',
        'subscription_cancel' => 'fas fa-times-circle',
        'profile_update' => 'fas fa-user-edit',
        'password_change' => 'fas fa-key',
        'content_upload' => 'fas fa-upload',
        'content_edit' => 'fas fa-edit',
        'content_delete' => 'fas fa-trash',
        'payment_success' => 'fas fa-credit-card',
        'payment_failed' => 'fas fa-exclamation-triangle'
    ];
    return $icons[$action] ?? 'fas fa-info-circle';
}

function getActionColor($action) {
    $colors = [
        'login' => '#4CAF50',
        'logout' => '#FF9800',
        'register' => '#2196F3',
        'watch_start' => '#E50914',
        'watch_complete' => '#4CAF50',
        'add_favorite' => '#E91E63',
        'remove_favorite' => '#9E9E9E',
        'subscription_purchase' => '#FF9800',
        'subscription_cancel' => '#F44336',
        'profile_update' => '#2196F3',
        'password_change' => '#9C27B0',
        'content_upload' => '#4CAF50',
        'content_edit' => '#FF9800',
        'content_delete' => '#F44336',
        'payment_success' => '#4CAF50',
        'payment_failed' => '#F44336'
    ];
    return $colors[$action] ?? '#666';
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>سجلات النشاط - Shahid Platform</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', 'Cairo', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #2d2d2d 100%);
            color: #fff;
            min-height: 100vh;
        }
        
        .admin-container {
            display: flex;
            min-height: 100vh;
        }
        
        .sidebar {
            width: 280px;
            background: rgba(47, 47, 47, 0.95);
            border-left: 2px solid rgba(229, 9, 20, 0.3);
            padding: 2rem 0;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
        }
        
        .sidebar-header {
            padding: 0 2rem 2rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            margin-bottom: 2rem;
        }
        
        .logo {
            color: #E50914;
            font-size: 1.8rem;
            font-weight: bold;
            text-decoration: none;
        }
        
        .sidebar-menu {
            list-style: none;
            padding: 0 1rem;
        }
        
        .sidebar-menu li {
            margin-bottom: 0.5rem;
        }
        
        .sidebar-menu a {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            color: #ccc;
            text-decoration: none;
            border-radius: 10px;
            transition: all 0.3s ease;
        }
        
        .sidebar-menu a:hover,
        .sidebar-menu a.active {
            background: rgba(229, 9, 20, 0.2);
            color: #fff;
        }
        
        .main-content {
            flex: 1;
            margin-right: 280px;
            padding: 2rem;
        }
        
        .header {
            background: rgba(47, 47, 47, 0.8);
            padding: 1.5rem 2rem;
            border-radius: 15px;
            margin-bottom: 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .header h1 {
            color: #E50914;
            font-size: 2rem;
        }
        
        .btn {
            padding: 0.75rem 1.5rem;
            background: linear-gradient(45deg, #E50914, #B8070F);
            color: white;
            border: none;
            border-radius: 8px;
            text-decoration: none;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(229, 9, 20, 0.3);
        }
        
        .btn-secondary {
            background: linear-gradient(45deg, #2196F3, #1976D2);
        }
        
        .btn-danger {
            background: linear-gradient(45deg, #F44336, #D32F2F);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: rgba(47, 47, 47, 0.8);
            padding: 1.5rem;
            border-radius: 15px;
            border: 2px solid rgba(229, 9, 20, 0.1);
            text-align: center;
            transition: all 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
            border-color: rgba(229, 9, 20, 0.3);
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #E50914;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            color: #ccc;
        }
        
        .filters-section {
            background: rgba(47, 47, 47, 0.8);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            border: 2px solid rgba(229, 9, 20, 0.1);
        }
        
        .filters-form {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            align-items: end;
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
        }
        
        .form-group label {
            margin-bottom: 0.5rem;
            color: #E50914;
            font-weight: bold;
        }
        
        .form-group input,
        .form-group select {
            padding: 0.75rem;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.1);
            color: #fff;
            font-size: 1rem;
        }
        
        .logs-section {
            background: rgba(47, 47, 47, 0.8);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            border: 2px solid rgba(229, 9, 20, 0.1);
        }
        
        .logs-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }
        
        .logs-table th,
        .logs-table td {
            padding: 1rem;
            text-align: right;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .logs-table th {
            background: rgba(229, 9, 20, 0.2);
            color: #E50914;
            font-weight: bold;
        }
        
        .logs-table tr:hover {
            background: rgba(255, 255, 255, 0.05);
        }
        
        .action-badge {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: bold;
            color: white;
        }
        
        .charts-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }
        
        .chart-card {
            background: rgba(47, 47, 47, 0.8);
            border-radius: 15px;
            padding: 2rem;
            border: 2px solid rgba(229, 9, 20, 0.1);
        }
        
        .chart-title {
            color: #E50914;
            font-size: 1.2rem;
            font-weight: bold;
            margin-bottom: 1rem;
        }
        
        .list-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem;
            margin-bottom: 0.5rem;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
        }
        
        .alert {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 2rem;
        }
        
        .alert-success {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid rgba(76, 175, 80, 0.5);
            color: #4CAF50;
        }
        
        .alert-error {
            background: rgba(244, 67, 54, 0.2);
            border: 1px solid rgba(244, 67, 54, 0.5);
            color: #F44336;
        }
        
        .management-section {
            background: rgba(47, 47, 47, 0.8);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            border: 2px solid rgba(229, 9, 20, 0.1);
        }
        
        .management-form {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            align-items: end;
        }
        
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }
            
            .main-content {
                margin-right: 0;
                padding: 1rem;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .filters-form {
                grid-template-columns: 1fr;
            }
            
            .charts-section {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <aside class="sidebar">
            <div class="sidebar-header">
                <a href="../index.php" class="logo">
                    <i class="fas fa-play-circle"></i> شاهد
                </a>
            </div>

            <ul class="sidebar-menu">
                <li><a href="dashboard.php"><i class="fas fa-tachometer-alt"></i> لوحة التحكم</a></li>
                <li><a href="content_manager.php"><i class="fas fa-film"></i> مدير المحتوى</a></li>
                <li><a href="upload_center.php"><i class="fas fa-upload"></i> مركز الرفع</a></li>
                <li><a href="users.php"><i class="fas fa-users"></i> إدارة المستخدمين</a></li>
                <li><a href="subscription_manager.php"><i class="fas fa-crown"></i> مدير الاشتراكات</a></li>
                <li><a href="payments.php"><i class="fas fa-credit-card"></i> المدفوعات</a></li>
                <li><a href="reports.php"><i class="fas fa-chart-bar"></i> التقارير</a></li>
                <li><a href="notifications.php"><i class="fas fa-bell"></i> الإشعارات</a></li>
                <li><a href="activity_logs.php" class="active"><i class="fas fa-history"></i> سجلات النشاط</a></li>
                <li><a href="settings.php"><i class="fas fa-cog"></i> الإعدادات</a></li>
                <li><a href="../logout.php"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a></li>
            </ul>
        </aside>

        <main class="main-content">
            <div class="header">
                <h1><i class="fas fa-history"></i> سجلات النشاط</h1>
            </div>

            <?php if ($message): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i> <?php echo $message; ?>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-circle"></i> <?php echo $error; ?>
                </div>
            <?php endif; ?>

            <!-- إحصائيات -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number"><?php echo number_format($stats['total_logs']); ?></div>
                    <div class="stat-label">إجمالي السجلات</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo number_format($stats['today_logs']); ?></div>
                    <div class="stat-label">سجلات اليوم</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo number_format($stats['week_logs']); ?></div>
                    <div class="stat-label">سجلات الأسبوع</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo number_format($stats['unique_users']); ?></div>
                    <div class="stat-label">مستخدمين نشطين</div>
                </div>
            </div>

            <!-- الرسوم البيانية -->
            <div class="charts-section">
                <div class="chart-card">
                    <h3 class="chart-title">الأنشطة الأكثر شيوعاً</h3>
                    <?php foreach ($popular_actions as $action): ?>
                        <div class="list-item">
                            <span>
                                <i class="<?php echo getActionIcon($action['action']); ?>" style="color: <?php echo getActionColor($action['action']); ?>"></i>
                                <?php echo getActionText($action['action']); ?>
                            </span>
                            <span style="color: #E50914; font-weight: bold;"><?php echo number_format($action['count']); ?></span>
                        </div>
                    <?php endforeach; ?>
                </div>

                <div class="chart-card">
                    <h3 class="chart-title">المستخدمين الأكثر نشاطاً</h3>
                    <?php foreach ($active_users as $user): ?>
                        <div class="list-item">
                            <span>
                                <i class="fas fa-user" style="color: #2196F3;"></i>
                                <?php echo htmlspecialchars($user['full_name'] ?? $user['username']); ?>
                            </span>
                            <span style="color: #E50914; font-weight: bold;"><?php echo number_format($user['activity_count']); ?></span>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>

            <!-- فلاتر البحث -->
            <div class="filters-section">
                <h3 style="color: #E50914; margin-bottom: 1rem;">
                    <i class="fas fa-filter"></i> فلاتر البحث
                </h3>

                <form method="GET" class="filters-form">
                    <div class="form-group">
                        <label>نوع النشاط</label>
                        <select name="action">
                            <option value="">جميع الأنشطة</option>
                            <option value="login" <?php echo $action_filter === 'login' ? 'selected' : ''; ?>>تسجيل دخول</option>
                            <option value="logout" <?php echo $action_filter === 'logout' ? 'selected' : ''; ?>>تسجيل خروج</option>
                            <option value="register" <?php echo $action_filter === 'register' ? 'selected' : ''; ?>>إنشاء حساب</option>
                            <option value="watch_start" <?php echo $action_filter === 'watch_start' ? 'selected' : ''; ?>>بدء مشاهدة</option>
                            <option value="subscription_purchase" <?php echo $action_filter === 'subscription_purchase' ? 'selected' : ''; ?>>شراء اشتراك</option>
                            <option value="add_favorite" <?php echo $action_filter === 'add_favorite' ? 'selected' : ''; ?>>إضافة للمفضلة</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label>المستخدم</label>
                        <select name="user_id">
                            <option value="">جميع المستخدمين</option>
                            <?php foreach ($users as $user): ?>
                                <option value="<?php echo $user['id']; ?>" <?php echo $user_filter === $user['id'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($user['full_name'] ?? $user['username']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="form-group">
                        <label>التاريخ</label>
                        <input type="date" name="date" value="<?php echo htmlspecialchars($date_filter); ?>">
                    </div>

                    <div class="form-group">
                        <button type="submit" class="btn">
                            <i class="fas fa-search"></i> بحث
                        </button>
                    </div>
                </form>
            </div>

            <!-- إدارة السجلات -->
            <div class="management-section">
                <h3 style="color: #E50914; margin-bottom: 1rem;">
                    <i class="fas fa-cogs"></i> إدارة السجلات
                </h3>

                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem;">
                    <form method="POST" class="management-form" onsubmit="return confirm('هل أنت متأكد من حذف السجلات القديمة؟')">
                        <input type="hidden" name="action" value="clear_old_logs">
                        <div class="form-group">
                            <label>حذف السجلات الأقدم من</label>
                            <select name="days" required>
                                <option value="30">30 يوم</option>
                                <option value="60">60 يوم</option>
                                <option value="90">90 يوم</option>
                                <option value="180">6 أشهر</option>
                                <option value="365">سنة</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-trash"></i> حذف السجلات القديمة
                            </button>
                        </div>
                    </form>

                    <form method="POST" class="management-form">
                        <input type="hidden" name="action" value="export_logs">
                        <div class="form-group">
                            <label>من تاريخ</label>
                            <input type="date" name="start_date" required>
                        </div>
                        <div class="form-group">
                            <label>إلى تاريخ</label>
                            <input type="date" name="end_date" required>
                        </div>
                        <div class="form-group">
                            <button type="submit" class="btn btn-secondary">
                                <i class="fas fa-download"></i> تصدير السجلات
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- جدول السجلات -->
            <div class="logs-section">
                <h3 style="color: #E50914; margin-bottom: 1rem;">
                    <i class="fas fa-list"></i> سجلات النشاط
                </h3>

                <div style="overflow-x: auto;">
                    <table class="logs-table">
                        <thead>
                            <tr>
                                <th>التاريخ والوقت</th>
                                <th>المستخدم</th>
                                <th>النشاط</th>
                                <th>الوصف</th>
                                <th>عنوان IP</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($logs as $log): ?>
                                <tr>
                                    <td><?php echo date('Y-m-d H:i:s', strtotime($log['created_at'])); ?></td>
                                    <td>
                                        <div>
                                            <strong><?php echo htmlspecialchars($log['full_name'] ?? $log['username']); ?></strong>
                                            <br>
                                            <small style="color: #ccc;"><?php echo htmlspecialchars($log['email']); ?></small>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="action-badge" style="background-color: <?php echo getActionColor($log['action']); ?>">
                                            <i class="<?php echo getActionIcon($log['action']); ?>"></i>
                                            <?php echo getActionText($log['action']); ?>
                                        </span>
                                    </td>
                                    <td><?php echo htmlspecialchars($log['description']); ?></td>
                                    <td>
                                        <code style="background: rgba(255,255,255,0.1); padding: 0.25rem 0.5rem; border-radius: 4px;">
                                            <?php echo htmlspecialchars($log['ip_address']); ?>
                                        </code>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

                <?php if (empty($logs)): ?>
                    <div style="text-align: center; padding: 3rem; color: #ccc;">
                        <i class="fas fa-inbox" style="font-size: 3rem; margin-bottom: 1rem;"></i>
                        <p>لا توجد سجلات نشاط</p>
                    </div>
                <?php endif; ?>
            </div>
        </main>
    </div>

    <script>
        // تحديث الصفحة كل 30 ثانية للسجلات الجديدة
        setInterval(function() {
            if (!document.querySelector('form:focus-within')) {
                window.location.reload();
            }
        }, 30000);

        // تأكيد العمليات الحساسة
        document.querySelectorAll('form[onsubmit]').forEach(form => {
            form.addEventListener('submit', function(e) {
                if (!confirm(this.getAttribute('onsubmit').match(/'([^']+)'/)[1])) {
                    e.preventDefault();
                }
            });
        });

        console.log('نظام سجلات النشاط جاهز!');
    </script>
</body>
</html>
