<?php include 'views/admin/layout/header.php'; ?>

<!-- Page Header -->
<div class="page-header">
    <div class="row align-items-center">
        <div class="col">
            <h1 class="page-title">لوحة التحكم</h1>
            <p class="page-subtitle">نظرة عامة على إحصائيات المنصة</p>
        </div>
        <div class="col-auto">
            <div class="btn-group">
                <button class="btn btn-primary" onclick="refreshDashboard()">
                    <i class="fas fa-sync-alt me-2"></i>تحديث
                </button>
                <button class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown">
                    تصدير التقرير
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item btn-export" data-format="pdf" data-type="dashboard">PDF</a></li>
                    <li><a class="dropdown-item btn-export" data-format="excel" data-type="dashboard">Excel</a></li>
                </ul>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card bg-primary">
            <div class="stats-icon">
                <i class="fas fa-users"></i>
            </div>
            <div class="stats-content">
                <h3><?= number_format($stats['total_users']) ?></h3>
                <p>إجمالي المستخدمين</p>
                <div class="stats-trend">
                    <i class="fas fa-arrow-up"></i>
                    <span>+12% من الشهر الماضي</span>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card bg-success">
            <div class="stats-icon">
                <i class="fas fa-film"></i>
            </div>
            <div class="stats-content">
                <h3><?= number_format($stats['total_movies']) ?></h3>
                <p>إجمالي الأفلام</p>
                <div class="stats-trend">
                    <i class="fas fa-arrow-up"></i>
                    <span>+5 هذا الأسبوع</span>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card bg-info">
            <div class="stats-icon">
                <i class="fas fa-tv"></i>
            </div>
            <div class="stats-content">
                <h3><?= number_format($stats['total_series']) ?></h3>
                <p>إجمالي المسلسلات</p>
                <div class="stats-trend">
                    <i class="fas fa-arrow-up"></i>
                    <span>+2 هذا الأسبوع</span>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card bg-warning">
            <div class="stats-icon">
                <i class="fas fa-crown"></i>
            </div>
            <div class="stats-content">
                <h3><?= number_format($stats['active_subscriptions']) ?></h3>
                <p>الاشتراكات النشطة</p>
                <div class="stats-trend">
                    <i class="fas fa-arrow-up"></i>
                    <span>+8% من الشهر الماضي</span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Revenue and Views Row -->
<div class="row mb-4">
    <div class="col-xl-6 col-md-6 mb-4">
        <div class="stats-card bg-gradient-primary">
            <div class="stats-icon">
                <i class="fas fa-dollar-sign"></i>
            </div>
            <div class="stats-content">
                <h3>$<?= number_format($stats['monthly_revenue'], 2) ?></h3>
                <p>إيرادات هذا الشهر</p>
                <div class="stats-trend">
                    <i class="fas fa-arrow-up"></i>
                    <span>+15% من الشهر الماضي</span>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-6 col-md-6 mb-4">
        <div class="stats-card bg-gradient-success">
            <div class="stats-icon">
                <i class="fas fa-eye"></i>
            </div>
            <div class="stats-content">
                <h3><?= number_format($stats['total_views']) ?></h3>
                <p>إجمالي المشاهدات</p>
                <div class="stats-trend">
                    <i class="fas fa-arrow-up"></i>
                    <span>+25% من الشهر الماضي</span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts Row -->
<div class="row mb-4">
    <!-- Revenue Chart -->
    <div class="col-xl-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">الإيرادات الشهرية</h5>
                <div class="card-tools">
                    <select class="form-select form-select-sm" id="revenueChartPeriod">
                        <option value="12">آخر 12 شهر</option>
                        <option value="6">آخر 6 أشهر</option>
                        <option value="3">آخر 3 أشهر</option>
                    </select>
                </div>
            </div>
            <div class="card-body">
                <canvas id="revenueChart" height="300"></canvas>
            </div>
        </div>
    </div>
    
    <!-- Subscription Distribution -->
    <div class="col-xl-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">توزيع الاشتراكات</h5>
            </div>
            <div class="card-body">
                <canvas id="subscriptionChart" height="300"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activity Row -->
<div class="row">
    <!-- Recent Movies -->
    <div class="col-xl-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">أحدث الأفلام</h5>
                <a href="/admin/content?type=movies" class="btn btn-sm btn-outline-primary">عرض الكل</a>
            </div>
            <div class="card-body">
                <div class="recent-items">
                    <?php foreach ($recent_movies as $movie): ?>
                    <div class="recent-item">
                        <img src="<?= $movie['poster'] ?? '/assets/images/no-poster.jpg' ?>" 
                             alt="<?= htmlspecialchars($movie['title']) ?>" 
                             class="recent-item-image">
                        <div class="recent-item-content">
                            <h6><?= htmlspecialchars($movie['title']) ?></h6>
                            <p class="text-muted"><?= $movie['year'] ?></p>
                            <small class="text-muted"><?= date('d/m/Y', strtotime($movie['created_at'])) ?></small>
                        </div>
                        <div class="recent-item-actions">
                            <span class="badge bg-<?= $movie['status'] === 'active' ? 'success' : 'secondary' ?>">
                                <?= $movie['status'] ?>
                            </span>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Recent Series -->
    <div class="col-xl-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">أحدث المسلسلات</h5>
                <a href="/admin/content?type=series" class="btn btn-sm btn-outline-primary">عرض الكل</a>
            </div>
            <div class="card-body">
                <div class="recent-items">
                    <?php foreach ($recent_series as $series): ?>
                    <div class="recent-item">
                        <img src="<?= $series['poster'] ?? '/assets/images/no-poster.jpg' ?>" 
                             alt="<?= htmlspecialchars($series['title']) ?>" 
                             class="recent-item-image">
                        <div class="recent-item-content">
                            <h6><?= htmlspecialchars($series['title']) ?></h6>
                            <p class="text-muted"><?= $series['total_episodes'] ?> حلقة</p>
                            <small class="text-muted"><?= date('d/m/Y', strtotime($series['created_at'])) ?></small>
                        </div>
                        <div class="recent-item-actions">
                            <span class="badge bg-<?= $series['status'] === 'active' ? 'success' : 'secondary' ?>">
                                <?= $series['status'] ?>
                            </span>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Recent Users -->
    <div class="col-xl-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">أحدث المستخدمين</h5>
                <a href="/admin/users" class="btn btn-sm btn-outline-primary">عرض الكل</a>
            </div>
            <div class="card-body">
                <div class="recent-items">
                    <?php foreach ($recent_users as $user): ?>
                    <div class="recent-item">
                        <img src="<?= $user['avatar'] ?? '/assets/images/default-avatar.png' ?>" 
                             alt="<?= htmlspecialchars($user['name']) ?>" 
                             class="recent-item-image rounded-circle">
                        <div class="recent-item-content">
                            <h6><?= htmlspecialchars($user['name']) ?></h6>
                            <p class="text-muted"><?= htmlspecialchars($user['email']) ?></p>
                            <small class="text-muted"><?= date('d/m/Y', strtotime($user['created_at'])) ?></small>
                        </div>
                        <div class="recent-item-actions">
                            <span class="badge bg-<?= $user['status'] === 'active' ? 'success' : 'secondary' ?>">
                                <?= $user['status'] ?>
                            </span>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Revenue Chart
const revenueCtx = document.getElementById('revenueChart').getContext('2d');
const revenueChart = new Chart(revenueCtx, {
    type: 'line',
    data: {
        labels: [<?php foreach ($revenue_data as $data): ?>'<?= $data['month'] ?>',<?php endforeach; ?>],
        datasets: [{
            label: 'الإيرادات ($)',
            data: [<?php foreach ($revenue_data as $data): ?><?= $data['revenue'] ?? 0 ?>,<?php endforeach; ?>],
            borderColor: '#e50914',
            backgroundColor: 'rgba(229, 9, 20, 0.1)',
            borderWidth: 3,
            fill: true,
            tension: 0.4
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return '$' + value.toLocaleString();
                    }
                }
            }
        }
    }
});

// Subscription Chart
const subscriptionCtx = document.getElementById('subscriptionChart').getContext('2d');
const subscriptionChart = new Chart(subscriptionCtx, {
    type: 'doughnut',
    data: {
        labels: ['مجاني', 'Premium', 'VIP'],
        datasets: [{
            data: [45, 35, 20],
            backgroundColor: ['#6c757d', '#e50914', '#ffc107'],
            borderWidth: 0
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

// Refresh Dashboard
function refreshDashboard() {
    location.reload();
}

// Real-time updates (simulate)
setInterval(function() {
    // Update notification badge
    const badge = $('.notification-badge');
    const currentCount = parseInt(badge.text()) || 0;
    if (Math.random() > 0.95) { // 5% chance every interval
        badge.text(currentCount + 1);
    }
}, 30000); // Every 30 seconds
</script>

<?php include 'views/admin/layout/footer.php'; ?>
