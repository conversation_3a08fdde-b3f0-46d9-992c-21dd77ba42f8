<?php
/**
 * فحص جداول قاعدة البيانات وإصلاح المشاكل
 * Check Database Tables and Fix Issues
 */

echo "<h1>🗄️ فحص جداول قاعدة البيانات</h1>";

try {
    $pdo = new PDO("mysql:host=localhost;dbname=shahid_platform;charset=utf8mb4", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>✅ تم الاتصال بقاعدة البيانات بنجاح</h3>";
    echo "</div>";
    
    // الجداول المطلوبة حسب لوحة المراقبة
    $requiredTables = [
        'users' => 'المستخدمين',
        'movies' => 'الأفلام', 
        'series' => 'المسلسلات',
        'categories' => 'التصنيفات',
        'ratings' => 'التقييمات',
        'favorites' => 'المفضلة',
        'watch_history' => 'سجل المشاهدة'
    ];
    
    // جلب الجداول الموجودة
    $stmt = $pdo->query("SHOW TABLES");
    $existingTables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "<h2>📊 حالة الجداول الحالية</h2>";
    
    echo "<table style='width: 100%; border-collapse: collapse; margin: 20px 0; background: rgba(0,0,0,0.3); border-radius: 10px; overflow: hidden;'>";
    echo "<tr style='background: rgba(229, 9, 20, 0.2);'>";
    echo "<th style='padding: 15px; border: 1px solid #ddd; text-align: right; color: #fff;'>الجدول</th>";
    echo "<th style='padding: 15px; border: 1px solid #ddd; text-align: center; color: #fff;'>الحالة</th>";
    echo "<th style='padding: 15px; border: 1px solid #ddd; text-align: right; color: #fff;'>الوصف</th>";
    echo "<th style='padding: 15px; border: 1px solid #ddd; text-align: center; color: #fff;'>عدد السجلات</th>";
    echo "</tr>";
    
    $missingTables = [];
    $existingCount = 0;
    
    foreach ($requiredTables as $table => $description) {
        $exists = in_array($table, $existingTables);
        $status = $exists ? '✅ موجود' : '❌ مفقود';
        $color = $exists ? 'green' : 'red';
        
        $recordCount = 0;
        if ($exists) {
            try {
                $stmt = $pdo->query("SELECT COUNT(*) FROM `$table`");
                $recordCount = $stmt->fetchColumn();
                $existingCount++;
            } catch (Exception $e) {
                $recordCount = 'خطأ';
            }
        } else {
            $missingTables[] = $table;
        }
        
        echo "<tr style='background: rgba(255,255,255,0.05);'>";
        echo "<td style='padding: 12px; border: 1px solid #ddd; color: #fff;'><strong>$table</strong></td>";
        echo "<td style='padding: 12px; border: 1px solid #ddd; text-align: center; color: $color; font-weight: bold;'>$status</td>";
        echo "<td style='padding: 12px; border: 1px solid #ddd; color: #ccc;'>$description</td>";
        echo "<td style='padding: 12px; border: 1px solid #ddd; text-align: center; color: #ccc;'>$recordCount</td>";
        echo "</tr>";
    }
    
    echo "</table>";
    
    // عرض الجداول الموجودة الإضافية
    $extraTables = array_diff($existingTables, array_keys($requiredTables));
    
    if (!empty($extraTables)) {
        echo "<h3>📋 جداول إضافية موجودة:</h3>";
        echo "<div style='background: rgba(33, 150, 243, 0.1); border: 1px solid rgba(33, 150, 243, 0.3); border-radius: 10px; padding: 1.5rem; margin: 1rem 0;'>";
        echo "<ul style='margin-right: 2rem; color: #ccc;'>";
        foreach ($extraTables as $table) {
            try {
                $stmt = $pdo->query("SELECT COUNT(*) FROM `$table`");
                $count = $stmt->fetchColumn();
                echo "<li style='margin: 0.5rem 0;'><strong>$table</strong> - $count سجل</li>";
            } catch (Exception $e) {
                echo "<li style='margin: 0.5rem 0;'><strong>$table</strong> - خطأ في القراءة</li>";
            }
        }
        echo "</ul>";
        echo "</div>";
    }
    
    // ملخص الحالة
    $totalRequired = count($requiredTables);
    $totalMissing = count($missingTables);
    $completionRate = round(($existingCount / $totalRequired) * 100, 1);
    
    echo "<h2>📊 ملخص الحالة</h2>";
    
    echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin: 2rem 0;'>";
    
    // بطاقة الجداول الموجودة
    echo "<div style='background: rgba(76, 175, 80, 0.1); border: 1px solid rgba(76, 175, 80, 0.3); border-radius: 10px; padding: 1.5rem; text-align: center;'>";
    echo "<h3 style='color: #4CAF50; margin-bottom: 1rem;'>✅ موجود</h3>";
    echo "<div style='font-size: 2.5rem; font-weight: bold; color: #4CAF50; margin-bottom: 0.5rem;'>$existingCount</div>";
    echo "<div style='opacity: 0.8; color: #ccc;'>جدول</div>";
    echo "</div>";
    
    // بطاقة الجداول المفقودة
    echo "<div style='background: rgba(244, 67, 54, 0.1); border: 1px solid rgba(244, 67, 54, 0.3); border-radius: 10px; padding: 1.5rem; text-align: center;'>";
    echo "<h3 style='color: #F44336; margin-bottom: 1rem;'>❌ مفقود</h3>";
    echo "<div style='font-size: 2.5rem; font-weight: bold; color: #F44336; margin-bottom: 0.5rem;'>$totalMissing</div>";
    echo "<div style='opacity: 0.8; color: #ccc;'>جدول</div>";
    echo "</div>";
    
    // بطاقة المجموع
    echo "<div style='background: rgba(229, 9, 20, 0.1); border: 1px solid rgba(229, 9, 20, 0.3); border-radius: 10px; padding: 1.5rem; text-align: center;'>";
    echo "<h3 style='color: #E50914; margin-bottom: 1rem;'>📊 المجموع</h3>";
    echo "<div style='font-size: 2.5rem; font-weight: bold; color: #E50914; margin-bottom: 0.5rem;'>$totalRequired</div>";
    echo "<div style='opacity: 0.8; color: #ccc;'>مطلوب</div>";
    echo "</div>";
    
    // بطاقة نسبة الإكمال
    $completionColor = $completionRate >= 80 ? '#4CAF50' : ($completionRate >= 60 ? '#FF9800' : '#F44336');
    echo "<div style='background: rgba(156, 39, 176, 0.1); border: 1px solid rgba(156, 39, 176, 0.3); border-radius: 10px; padding: 1.5rem; text-align: center;'>";
    echo "<h3 style='color: #9C27B0; margin-bottom: 1rem;'>🎯 الإكمال</h3>";
    echo "<div style='font-size: 2.5rem; font-weight: bold; color: $completionColor; margin-bottom: 0.5rem;'>$completionRate%</div>";
    echo "<div style='opacity: 0.8; color: #ccc;'>مكتمل</div>";
    echo "</div>";
    
    echo "</div>";
    
    // إنشاء الجداول المفقودة
    if (!empty($missingTables)) {
        echo "<h2>🔧 إنشاء الجداول المفقودة</h2>";
        
        echo "<div style='background: rgba(255, 152, 0, 0.1); border: 1px solid rgba(255, 152, 0, 0.3); border-radius: 10px; padding: 2rem; margin: 2rem 0;'>";
        echo "<h3 style='color: #FF9800; margin-bottom: 1rem;'>⚠️ الجداول المفقودة:</h3>";
        echo "<ul style='margin-right: 2rem; color: #ccc;'>";
        foreach ($missingTables as $table) {
            echo "<li style='margin: 0.5rem 0;'><strong>$table</strong> - {$requiredTables[$table]}</li>";
        }
        echo "</ul>";
        echo "</div>";
        
        // أزرار الإصلاح
        echo "<div style='text-align: center; margin: 2rem 0;'>";
        echo "<a href='create_missing_tables.php' style='background: #4CAF50; color: white; padding: 1rem 2rem; text-decoration: none; border-radius: 8px; font-weight: bold; margin: 0.5rem; display: inline-block;'>🔧 إنشاء الجداول المفقودة</a>";
        echo "<a href='fix_database_structure.php' style='background: #2196F3; color: white; padding: 1rem 2rem; text-decoration: none; border-radius: 8px; font-weight: bold; margin: 0.5rem; display: inline-block;'>🗄️ إصلاح قاعدة البيانات الكاملة</a>";
        echo "</div>";
    } else {
        echo "<div style='background: rgba(76, 175, 80, 0.1); border: 1px solid rgba(76, 175, 80, 0.3); border-radius: 10px; padding: 2rem; margin: 2rem 0; text-align: center;'>";
        echo "<h3 style='color: #4CAF50; margin-bottom: 1rem;'>🎉 جميع الجداول المطلوبة موجودة!</h3>";
        echo "<p style='color: #ccc;'>قاعدة البيانات مكتملة وجاهزة للاستخدام.</p>";
        echo "</div>";
    }
    
    // معلومات إضافية عن قاعدة البيانات
    echo "<h2>📋 معلومات قاعدة البيانات</h2>";
    
    // حجم قاعدة البيانات
    $stmt = $pdo->query("SELECT ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS size_mb FROM information_schema.tables WHERE table_schema = 'shahid_platform'");
    $dbSize = $stmt->fetchColumn() ?: 0;
    
    // عدد السجلات الإجمالي
    $totalRecords = 0;
    foreach ($existingTables as $table) {
        try {
            $stmt = $pdo->query("SELECT COUNT(*) FROM `$table`");
            $totalRecords += $stmt->fetchColumn();
        } catch (Exception $e) {
            // تجاهل الأخطاء
        }
    }
    
    echo "<div style='background: rgba(47, 47, 47, 0.9); border-radius: 10px; padding: 2rem; margin: 2rem 0; border: 1px solid rgba(229, 9, 20, 0.2);'>";
    echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;'>";
    
    echo "<div style='text-align: center;'>";
    echo "<h4 style='color: #E50914; margin-bottom: 0.5rem;'>📊 إجمالي الجداول</h4>";
    echo "<div style='font-size: 2rem; font-weight: bold; color: #2196F3;'>" . count($existingTables) . "</div>";
    echo "</div>";
    
    echo "<div style='text-align: center;'>";
    echo "<h4 style='color: #E50914; margin-bottom: 0.5rem;'>📈 إجمالي السجلات</h4>";
    echo "<div style='font-size: 2rem; font-weight: bold; color: #4CAF50;'>" . number_format($totalRecords) . "</div>";
    echo "</div>";
    
    echo "<div style='text-align: center;'>";
    echo "<h4 style='color: #E50914; margin-bottom: 0.5rem;'>💾 حجم قاعدة البيانات</h4>";
    echo "<div style='font-size: 2rem; font-weight: bold; color: #FF9800;'>$dbSize MB</div>";
    echo "</div>";
    
    echo "<div style='text-align: center;'>";
    echo "<h4 style='color: #E50914; margin-bottom: 0.5rem;'>🎯 نسبة الإكمال</h4>";
    echo "<div style='font-size: 2rem; font-weight: bold; color: $completionColor;'>$completionRate%</div>";
    echo "</div>";
    
    echo "</div>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>❌ خطأ في الاتصال بقاعدة البيانات:</h3>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

// روابط سريعة
echo "<div style='text-align: center; margin: 3rem 0;'>";
echo "<h3 style='color: #E50914; margin-bottom: 2rem;'>🔗 روابط سريعة</h3>";

$quick_links = [
    ['url' => 'dashboard_live.php', 'title' => '📊 لوحة المراقبة المباشرة', 'color' => '#E50914'],
    ['url' => 'create_missing_tables.php', 'title' => '🔧 إنشاء الجداول المفقودة', 'color' => '#4CAF50'],
    ['url' => 'fix_database_structure.php', 'title' => '🗄️ إصلاح قاعدة البيانات', 'color' => '#2196F3'],
    ['url' => 'project_audit_complete.php', 'title' => '📋 المراجعة الشاملة', 'color' => '#FF9800'],
    ['url' => 'homepage_working.php', 'title' => '🏠 الصفحة الرئيسية', 'color' => '#9C27B0']
];

foreach ($quick_links as $link) {
    echo "<a href='{$link['url']}' style='background: {$link['color']}; color: white; padding: 1rem 2rem; text-decoration: none; border-radius: 8px; font-weight: bold; margin: 0.5rem; display: inline-block; transition: transform 0.3s ease;' onmouseover='this.style.transform=\"translateY(-3px)\"' onmouseout='this.style.transform=\"translateY(0)\"'>{$link['title']}</a>";
}

echo "</div>";
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🗄️ فحص جداول قاعدة البيانات - Shahid Platform</title>
    <style>
        body {
            font-family: 'Segoe UI', 'Cairo', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #2d2d2d 100%);
            color: #fff;
            min-height: 100vh;
            padding: 2rem;
            margin: 0;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(47, 47, 47, 0.95);
            border-radius: 20px;
            padding: 3rem;
            border: 2px solid rgba(229, 9, 20, 0.3);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
        }
        h1, h2, h3 { 
            color: #E50914; 
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
        }
        h1 {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 2rem;
            background: linear-gradient(45deg, #E50914, #FF6B35);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        table {
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        }
        th {
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        tr:hover {
            background: rgba(229, 9, 20, 0.1) !important;
        }
        .btn {
            transition: all 0.3s ease;
        }
        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
        }
        @media (max-width: 768px) {
            .container {
                padding: 1rem;
                margin: 1rem;
            }
            h1 {
                font-size: 2rem;
            }
            table {
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- المحتوى يتم عرضه من PHP أعلاه -->
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🗄️ فحص جداول قاعدة البيانات جاهز!');
            
            // تأثيرات تفاعلية للجدول
            const rows = document.querySelectorAll('table tr');
            rows.forEach((row, index) => {
                if (index > 0) { // تجاهل الهيدر
                    row.style.opacity = '0';
                    row.style.transform = 'translateY(20px)';
                    
                    setTimeout(() => {
                        row.style.transition = 'all 0.5s ease';
                        row.style.opacity = '1';
                        row.style.transform = 'translateY(0)';
                    }, index * 100);
                }
            });
        });
    </script>
</body>
</html>
