<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🗄️ إعداد قاعدة البيانات على XAMPP</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', 'Cairo', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #2d2d2d 100%);
            color: #fff;
            min-height: 100vh;
            padding: 2rem;
        }
        
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: rgba(47, 47, 47, 0.95);
            border-radius: 20px;
            padding: 3rem;
            border: 2px solid rgba(229, 9, 20, 0.3);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
        }
        
        .header {
            text-align: center;
            margin-bottom: 3rem;
            padding-bottom: 2rem;
            border-bottom: 3px solid #E50914;
        }
        
        .header h1 {
            color: #E50914;
            font-size: 2.8rem;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }
        
        .header p {
            color: #ccc;
            font-size: 1.2rem;
        }
        
        .step-card {
            background: linear-gradient(145deg, rgba(0, 0, 0, 0.4), rgba(47, 47, 47, 0.6));
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            border: 2px solid rgba(255, 255, 255, 0.1);
        }
        
        .step-header {
            display: flex;
            align-items: center;
            margin-bottom: 1.5rem;
        }
        
        .step-number {
            background: #E50914;
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-left: 1rem;
        }
        
        .step-title {
            font-size: 1.4rem;
            font-weight: bold;
            color: #fff;
        }
        
        .step-content {
            color: #ccc;
            line-height: 1.6;
        }
        
        .btn {
            background: linear-gradient(45deg, #E50914, #B8070F);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 10px;
            cursor: pointer;
            font-size: 1.1rem;
            font-weight: bold;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            margin: 0.5rem;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(229, 9, 20, 0.3);
            color: white;
            text-decoration: none;
        }
        
        .btn-success {
            background: linear-gradient(45deg, #4CAF50, #45a049);
        }
        
        .btn-warning {
            background: linear-gradient(45deg, #FF9800, #F57C00);
        }
        
        .btn-secondary {
            background: linear-gradient(45deg, #555, #333);
        }
        
        .status-check {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 1.5rem;
            margin: 1rem 0;
        }
        
        .check-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0.8rem 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .check-item:last-child {
            border-bottom: none;
        }
        
        .check-status {
            font-size: 1.5rem;
        }
        
        .success { color: #4CAF50; }
        .error { color: #F44336; }
        .warning { color: #FF9800; }
        
        .code-block {
            background: rgba(0, 0, 0, 0.5);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
            font-family: 'Courier New', monospace;
            color: #00ff00;
            overflow-x: auto;
        }
        
        .progress-bar {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            height: 20px;
            margin: 1rem 0;
            overflow: hidden;
        }
        
        .progress-fill {
            background: linear-gradient(90deg, #E50914, #B8070F);
            height: 100%;
            transition: width 0.3s ease;
        }
        
        .alert {
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem 0;
        }
        
        .alert-success {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid #4CAF50;
            color: #4CAF50;
        }
        
        .alert-error {
            background: rgba(244, 67, 54, 0.2);
            border: 1px solid #F44336;
            color: #F44336;
        }
        
        .alert-warning {
            background: rgba(255, 152, 0, 0.2);
            border: 1px solid #FF9800;
            color: #FF9800;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🗄️ إعداد قاعدة البيانات على XAMPP</h1>
            <p>دليل شامل لإضافة قاعدة بيانات منصة شاهد إلى XAMPP</p>
        </div>

        <?php
        // فحص حالة XAMPP
        $xamppRunning = false;
        $mysqlRunning = false;
        $apacheRunning = false;
        
        // فحص Apache
        $apacheCheck = @file_get_contents('http://localhost/');
        $apacheRunning = $apacheCheck !== false;
        
        // فحص MySQL
        try {
            $pdo = new PDO("mysql:host=localhost", "root", "");
            $mysqlRunning = true;
        } catch (Exception $e) {
            $mysqlRunning = false;
        }
        
        $xamppRunning = $apacheRunning && $mysqlRunning;
        
        // فحص وجود قاعدة البيانات
        $databaseExists = false;
        $tablesCount = 0;
        
        if ($mysqlRunning) {
            try {
                $pdo = new PDO("mysql:host=localhost", "root", "");
                $stmt = $pdo->query("SHOW DATABASES LIKE 'shahid_platform'");
                $databaseExists = $stmt->rowCount() > 0;
                
                if ($databaseExists) {
                    $pdo->exec("USE shahid_platform");
                    $stmt = $pdo->query("SHOW TABLES");
                    $tablesCount = $stmt->rowCount();
                }
            } catch (Exception $e) {
                // تجاهل الأخطاء
            }
        }
        ?>

        <!-- فحص حالة النظام -->
        <div class="step-card">
            <div class="step-header">
                <div class="step-number">✓</div>
                <div class="step-title">فحص حالة النظام</div>
            </div>
            <div class="step-content">
                <div class="status-check">
                    <div class="check-item">
                        <div>
                            <strong>Apache Server</strong><br>
                            <small>خادم الويب</small>
                        </div>
                        <div class="check-status <?php echo $apacheRunning ? 'success' : 'error'; ?>">
                            <?php echo $apacheRunning ? '✅' : '❌'; ?>
                        </div>
                    </div>
                    
                    <div class="check-item">
                        <div>
                            <strong>MySQL Database</strong><br>
                            <small>خادم قاعدة البيانات</small>
                        </div>
                        <div class="check-status <?php echo $mysqlRunning ? 'success' : 'error'; ?>">
                            <?php echo $mysqlRunning ? '✅' : '❌'; ?>
                        </div>
                    </div>
                    
                    <div class="check-item">
                        <div>
                            <strong>قاعدة بيانات shahid_platform</strong><br>
                            <small>قاعدة البيانات الرئيسية</small>
                        </div>
                        <div class="check-status <?php echo $databaseExists ? 'success' : 'warning'; ?>">
                            <?php echo $databaseExists ? '✅' : '⚠️'; ?>
                        </div>
                    </div>
                    
                    <div class="check-item">
                        <div>
                            <strong>الجداول</strong><br>
                            <small>عدد الجداول: <?php echo $tablesCount; ?></small>
                        </div>
                        <div class="check-status <?php echo $tablesCount > 10 ? 'success' : 'warning'; ?>">
                            <?php echo $tablesCount > 10 ? '✅' : '⚠️'; ?>
                        </div>
                    </div>
                </div>
                
                <?php if (!$xamppRunning): ?>
                <div class="alert alert-error">
                    <strong>⚠️ تحذير:</strong> XAMPP غير مشغل بالكامل. يرجى تشغيل Apache و MySQL من لوحة تحكم XAMPP.
                </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- الخطوة 1: تشغيل XAMPP -->
        <div class="step-card">
            <div class="step-header">
                <div class="step-number">1</div>
                <div class="step-title">تشغيل XAMPP</div>
            </div>
            <div class="step-content">
                <p>تأكد من تشغيل XAMPP بشكل صحيح:</p>
                <ul style="margin: 1rem 0; padding-right: 2rem;">
                    <li>افتح XAMPP Control Panel</li>
                    <li>اضغط "Start" بجانب Apache</li>
                    <li>اضغط "Start" بجانب MySQL</li>
                    <li>تأكد من ظهور اللون الأخضر</li>
                </ul>
                
                <div class="code-block">
                    Apache: ✅ Running on Port 80<br>
                    MySQL: ✅ Running on Port 3306
                </div>
                
                <a href="http://localhost/xampp/" target="_blank" class="btn btn-secondary">
                    🌐 فتح XAMPP Dashboard
                </a>
            </div>
        </div>

        <!-- الخطوة 2: إنشاء قاعدة البيانات -->
        <div class="step-card">
            <div class="step-header">
                <div class="step-number">2</div>
                <div class="step-title">إنشاء قاعدة البيانات</div>
            </div>
            <div class="step-content">
                <p>اختر إحدى الطرق التالية لإنشاء قاعدة البيانات:</p>
                
                <h4 style="color: #E50914; margin: 1.5rem 0 1rem;">الطريقة الأولى: التلقائية (الأسهل)</h4>
                <p>استخدم أداة الإعداد التلقائي:</p>
                
                <?php if ($xamppRunning): ?>
                <a href="fix_database.php" class="btn btn-success">
                    🚀 إعداد تلقائي للقاعدة
                </a>
                <a href="setup_complete_system.php" class="btn btn-success">
                    ⚙️ إعداد النظام الكامل
                </a>
                <?php else: ?>
                <div class="alert alert-warning">
                    يرجى تشغيل XAMPP أولاً لاستخدام الإعداد التلقائي
                </div>
                <?php endif; ?>
                
                <h4 style="color: #E50914; margin: 1.5rem 0 1rem;">الطريقة الثانية: يدوياً عبر phpMyAdmin</h4>
                <ol style="margin: 1rem 0; padding-right: 2rem;">
                    <li>افتح phpMyAdmin</li>
                    <li>اضغط على "New" لإنشاء قاعدة بيانات جديدة</li>
                    <li>اكتب اسم القاعدة: <code>shahid_platform</code></li>
                    <li>اختر Collation: <code>utf8mb4_unicode_ci</code></li>
                    <li>اضغط "Create"</li>
                </ol>
                
                <a href="http://localhost/phpmyadmin/" target="_blank" class="btn btn-warning">
                    📊 فتح phpMyAdmin
                </a>
            </div>
        </div>

        <!-- الخطوة 3: استيراد البيانات -->
        <div class="step-card">
            <div class="step-header">
                <div class="step-number">3</div>
                <div class="step-title">استيراد الجداول والبيانات</div>
            </div>
            <div class="step-content">
                <p>بعد إنشاء قاعدة البيانات، قم بإضافة الجداول والبيانات:</p>
                
                <h4 style="color: #E50914; margin: 1.5rem 0 1rem;">الطريقة الأولى: استخدام أدوات النظام</h4>
                
                <?php if ($databaseExists): ?>
                <a href="database/update_database.php" class="btn btn-success">
                    📋 تحديث قاعدة البيانات
                </a>
                <a href="create_additional_tables.php" class="btn btn-success">
                    ➕ إضافة الجداول الإضافية
                </a>
                <?php else: ?>
                <div class="alert alert-warning">
                    يرجى إنشاء قاعدة البيانات أولاً
                </div>
                <?php endif; ?>
                
                <h4 style="color: #E50914; margin: 1.5rem 0 1rem;">الطريقة الثانية: استيراد ملفات SQL</h4>
                <p>في phpMyAdmin:</p>
                <ol style="margin: 1rem 0; padding-right: 2rem;">
                    <li>اختر قاعدة البيانات <code>shahid_platform</code></li>
                    <li>اضغط على تبويب "Import"</li>
                    <li>اختر ملف <code>database/schema.sql</code></li>
                    <li>اضغط "Go" لاستيراد الجداول</li>
                    <li>كرر العملية مع <code>database/production_data.sql</code></li>
                </ol>
                
                <div class="code-block">
                    📁 الملفات المطلوبة:<br>
                    ├── database/schema.sql (الجداول)<br>
                    └── database/production_data.sql (البيانات)
                </div>
            </div>
        </div>

        <!-- الخطوة 4: التحقق من النجاح -->
        <div class="step-card">
            <div class="step-header">
                <div class="step-number">4</div>
                <div class="step-title">التحقق من النجاح</div>
            </div>
            <div class="step-content">
                <p>تأكد من نجاح العملية:</p>
                
                <div class="progress-bar">
                    <div class="progress-fill" style="width: <?php echo $databaseExists ? ($tablesCount > 10 ? '100%' : '60%') : '20%'; ?>"></div>
                </div>
                
                <?php if ($databaseExists && $tablesCount > 10): ?>
                <div class="alert alert-success">
                    <strong>🎉 تم بنجاح!</strong> قاعدة البيانات جاهزة للاستخدام مع <?php echo $tablesCount; ?> جدول.
                </div>
                
                <h4 style="color: #4CAF50; margin: 1.5rem 0 1rem;">الروابط المفيدة:</h4>
                <a href="homepage.php" class="btn btn-success">🏠 الصفحة الرئيسية</a>
                <a href="admin/dashboard.php" class="btn btn-success">🎛️ لوحة الإدارة</a>
                <a href="api/test.php" class="btn btn-success">🔗 اختبار API</a>
                <a href="dashboard_live.php" class="btn btn-success">📊 مراقبة مباشرة</a>
                
                <?php elseif ($databaseExists): ?>
                <div class="alert alert-warning">
                    <strong>⚠️ تحذير:</strong> قاعدة البيانات موجودة لكن الجداول ناقصة (<?php echo $tablesCount; ?> جدول فقط).
                </div>
                
                <a href="database/update_database.php" class="btn btn-warning">
                    🔧 إصلاح الجداول
                </a>
                
                <?php else: ?>
                <div class="alert alert-error">
                    <strong>❌ خطأ:</strong> قاعدة البيانات غير موجودة. يرجى إنشاؤها أولاً.
                </div>
                <?php endif; ?>
                
                <h4 style="color: #E50914; margin: 1.5rem 0 1rem;">بيانات تسجيل الدخول الافتراضية:</h4>
                <div class="code-block">
                    👤 المدير:<br>
                    البريد: <EMAIL><br>
                    كلمة المرور: admin123<br><br>
                    
                    👤 مستخدم تجريبي:<br>
                    البريد: <EMAIL><br>
                    كلمة المرور: password123
                </div>
            </div>
        </div>

        <!-- أدوات إضافية -->
        <div class="step-card">
            <div class="step-header">
                <div class="step-number">+</div>
                <div class="step-title">أدوات إضافية</div>
            </div>
            <div class="step-content">
                <p>أدوات مفيدة لإدارة قاعدة البيانات:</p>
                
                <a href="system_status.php" class="btn btn-secondary">📊 حالة النظام</a>
                <a href="monitoring_index.php" class="btn btn-secondary">🔍 أدوات المراقبة</a>
                <a href="create_admin.php" class="btn btn-secondary">👤 إنشاء مدير</a>
                <a href="quick_admin_fix.php" class="btn btn-secondary">⚡ إصلاح سريع</a>
                
                <h4 style="color: #E50914; margin: 1.5rem 0 1rem;">ملفات Batch للتشغيل السريع:</h4>
                <div class="code-block">
                    📁 update_database.bat - تحديث قاعدة البيانات<br>
                    📁 start_xampp.bat - تشغيل XAMPP (إذا كان متوفراً)
                </div>
            </div>
        </div>
    </div>

    <script>
        // تحديث الصفحة كل 30 ثانية لمراقبة التقدم
        setTimeout(() => {
            location.reload();
        }, 30000);
        
        console.log('🗄️ أداة إعداد قاعدة البيانات جاهزة');
    </script>
</body>
</html>
