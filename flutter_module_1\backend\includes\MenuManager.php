<?php
/**
 * نظام إدارة القوائم الديناميكية
 * Dynamic Menu Management System
 */

class MenuManager {
    private $pdo;
    private $security;
    private $cache = [];
    
    public function __construct($pdo, $security) {
        $this->pdo = $pdo;
        $this->security = $security;
    }
    
    /**
     * الحصول على القائمة الرئيسية
     */
    public function getMainMenu($location = 'header') {
        try {
            if (!isset($this->cache[$location])) {
                $stmt = $this->pdo->prepare("
                    SELECT * FROM dynamic_menus 
                    WHERE is_active = 1 AND is_visible = 1 
                    ORDER BY position ASC, name ASC
                ");
                $stmt->execute();
                
                $menuItems = $stmt->fetchAll(PDO::FETCH_ASSOC);
                $this->cache[$location] = $this->buildMenuTree($menuItems);
            }
            
            return $this->cache[$location];
            
        } catch (Exception $e) {
            error_log("Failed to get main menu: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * بناء شجرة القائمة
     */
    private function buildMenuTree($items, $parentId = null) {
        $tree = [];
        
        foreach ($items as $item) {
            if ($item['parent_id'] == $parentId) {
                $item['children'] = $this->buildMenuTree($items, $item['id']);
                $item['has_children'] = !empty($item['children']);
                
                // التحقق من الصلاحيات
                if ($this->checkMenuPermissions($item)) {
                    $tree[] = $item;
                }
            }
        }
        
        return $tree;
    }
    
    /**
     * التحقق من صلاحيات عنصر القائمة
     */
    private function checkMenuPermissions($item) {
        if (empty($item['permissions'])) {
            return true;
        }
        
        $permissions = json_decode($item['permissions'], true);
        if (!is_array($permissions)) {
            return true;
        }
        
        // إذا لم يكن المستخدم مسجل دخول
        if (!isset($_SESSION['user_id'])) {
            return in_array('guest', $permissions);
        }
        
        // التحقق من صلاحيات المستخدم
        foreach ($permissions as $permission) {
            if ($permission === 'authenticated' || 
                $this->security->hasPermission($_SESSION['user_id'], $permission)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * إنشاء عنصر قائمة جديد
     */
    public function createMenuItem($data) {
        try {
            // التحقق من البيانات المطلوبة
            $required = ['name', 'slug'];
            foreach ($required as $field) {
                if (empty($data[$field])) {
                    throw new Exception("الحقل $field مطلوب");
                }
            }
            
            // التحقق من عدم تكرار الـ slug
            $stmt = $this->pdo->prepare("SELECT COUNT(*) FROM dynamic_menus WHERE slug = ?");
            $stmt->execute([$data['slug']]);
            
            if ($stmt->fetchColumn() > 0) {
                throw new Exception("الرابط المختصر موجود مسبقاً");
            }
            
            // إنشاء عنصر القائمة
            $stmt = $this->pdo->prepare("
                INSERT INTO dynamic_menus 
                (name, name_en, slug, parent_id, icon, url, target, css_class, position, is_active, is_visible, permissions, meta_data) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            $stmt->execute([
                $data['name'],
                $data['name_en'] ?? null,
                $data['slug'],
                $data['parent_id'] ?? null,
                $data['icon'] ?? null,
                $data['url'] ?? null,
                $data['target'] ?? '_self',
                $data['css_class'] ?? null,
                $data['position'] ?? 0,
                $data['is_active'] ?? 1,
                $data['is_visible'] ?? 1,
                isset($data['permissions']) ? json_encode($data['permissions']) : null,
                isset($data['meta_data']) ? json_encode($data['meta_data']) : null
            ]);
            
            $menuId = $this->pdo->lastInsertId();
            
            // مسح الكاش
            $this->cache = [];
            
            // تسجيل النشاط
            if (isset($_SESSION['user_id'])) {
                $this->security->logActivity(
                    $_SESSION['user_id'],
                    'menu.create',
                    "تم إنشاء عنصر قائمة جديد: {$data['name']}",
                    'dynamic_menus',
                    $menuId,
                    null,
                    $data
                );
            }
            
            return $menuId;
            
        } catch (Exception $e) {
            throw new Exception("فشل في إنشاء عنصر القائمة: " . $e->getMessage());
        }
    }
    
    /**
     * تحديث عنصر قائمة
     */
    public function updateMenuItem($id, $data) {
        try {
            // الحصول على البيانات القديمة
            $stmt = $this->pdo->prepare("SELECT * FROM dynamic_menus WHERE id = ?");
            $stmt->execute([$id]);
            $oldData = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$oldData) {
                throw new Exception("عنصر القائمة غير موجود");
            }
            
            // التحقق من تكرار الـ slug
            if (isset($data['slug']) && $data['slug'] !== $oldData['slug']) {
                $stmt = $this->pdo->prepare("SELECT COUNT(*) FROM dynamic_menus WHERE slug = ? AND id != ?");
                $stmt->execute([$data['slug'], $id]);
                
                if ($stmt->fetchColumn() > 0) {
                    throw new Exception("الرابط المختصر موجود مسبقاً");
                }
            }
            
            // بناء استعلام التحديث
            $updateFields = [];
            $updateValues = [];
            
            $allowedFields = [
                'name', 'name_en', 'slug', 'parent_id', 'icon', 'url', 
                'target', 'css_class', 'position', 'is_active', 'is_visible'
            ];
            
            foreach ($allowedFields as $field) {
                if (isset($data[$field])) {
                    $updateFields[] = "$field = ?";
                    $updateValues[] = $data[$field];
                }
            }
            
            // معالجة الحقول الخاصة
            if (isset($data['permissions'])) {
                $updateFields[] = "permissions = ?";
                $updateValues[] = json_encode($data['permissions']);
            }
            
            if (isset($data['meta_data'])) {
                $updateFields[] = "meta_data = ?";
                $updateValues[] = json_encode($data['meta_data']);
            }
            
            if (empty($updateFields)) {
                throw new Exception("لا توجد بيانات للتحديث");
            }
            
            $updateFields[] = "updated_at = NOW()";
            $updateValues[] = $id;
            
            $sql = "UPDATE dynamic_menus SET " . implode(', ', $updateFields) . " WHERE id = ?";
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($updateValues);
            
            // مسح الكاش
            $this->cache = [];
            
            // تسجيل النشاط
            if (isset($_SESSION['user_id'])) {
                $this->security->logActivity(
                    $_SESSION['user_id'],
                    'menu.update',
                    "تم تحديث عنصر القائمة: {$data['name'] ?? $oldData['name']}",
                    'dynamic_menus',
                    $id,
                    $oldData,
                    $data
                );
            }
            
            return true;
            
        } catch (Exception $e) {
            throw new Exception("فشل في تحديث عنصر القائمة: " . $e->getMessage());
        }
    }
    
    /**
     * حذف عنصر قائمة
     */
    public function deleteMenuItem($id) {
        try {
            // الحصول على بيانات العنصر
            $stmt = $this->pdo->prepare("SELECT * FROM dynamic_menus WHERE id = ?");
            $stmt->execute([$id]);
            $menuItem = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$menuItem) {
                throw new Exception("عنصر القائمة غير موجود");
            }
            
            // التحقق من وجود عناصر فرعية
            $stmt = $this->pdo->prepare("SELECT COUNT(*) FROM dynamic_menus WHERE parent_id = ?");
            $stmt->execute([$id]);
            
            if ($stmt->fetchColumn() > 0) {
                throw new Exception("لا يمكن حذف عنصر القائمة لوجود عناصر فرعية");
            }
            
            // حذف العنصر
            $stmt = $this->pdo->prepare("DELETE FROM dynamic_menus WHERE id = ?");
            $stmt->execute([$id]);
            
            // مسح الكاش
            $this->cache = [];
            
            // تسجيل النشاط
            if (isset($_SESSION['user_id'])) {
                $this->security->logActivity(
                    $_SESSION['user_id'],
                    'menu.delete',
                    "تم حذف عنصر القائمة: {$menuItem['name']}",
                    'dynamic_menus',
                    $id,
                    $menuItem,
                    null
                );
            }
            
            return true;
            
        } catch (Exception $e) {
            throw new Exception("فشل في حذف عنصر القائمة: " . $e->getMessage());
        }
    }
    
    /**
     * إعادة ترتيب عناصر القائمة
     */
    public function reorderMenuItems($items) {
        try {
            $this->pdo->beginTransaction();
            
            foreach ($items as $position => $itemId) {
                $stmt = $this->pdo->prepare("UPDATE dynamic_menus SET position = ? WHERE id = ?");
                $stmt->execute([$position + 1, $itemId]);
            }
            
            $this->pdo->commit();
            
            // مسح الكاش
            $this->cache = [];
            
            // تسجيل النشاط
            if (isset($_SESSION['user_id'])) {
                $this->security->logActivity(
                    $_SESSION['user_id'],
                    'menu.reorder',
                    "تم إعادة ترتيب عناصر القائمة",
                    'dynamic_menus',
                    null,
                    null,
                    ['items' => $items]
                );
            }
            
            return true;
            
        } catch (Exception $e) {
            $this->pdo->rollBack();
            throw new Exception("فشل في إعادة ترتيب القائمة: " . $e->getMessage());
        }
    }
    
    /**
     * الحصول على جميع عناصر القائمة للإدارة
     */
    public function getAllMenuItems() {
        try {
            $stmt = $this->pdo->query("
                SELECT m.*, 
                       p.name as parent_name,
                       COUNT(c.id) as children_count
                FROM dynamic_menus m 
                LEFT JOIN dynamic_menus p ON m.parent_id = p.id 
                LEFT JOIN dynamic_menus c ON m.id = c.parent_id 
                GROUP BY m.id 
                ORDER BY m.position ASC, m.name ASC
            ");
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            throw new Exception("فشل في جلب عناصر القائمة: " . $e->getMessage());
        }
    }
    
    /**
     * الحصول على عنصر قائمة محدد
     */
    public function getMenuItem($id) {
        try {
            $stmt = $this->pdo->prepare("
                SELECT m.*, p.name as parent_name 
                FROM dynamic_menus m 
                LEFT JOIN dynamic_menus p ON m.parent_id = p.id 
                WHERE m.id = ?
            ");
            
            $stmt->execute([$id]);
            $item = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($item) {
                // تحليل البيانات المخزنة كـ JSON
                $item['permissions'] = json_decode($item['permissions'], true) ?: [];
                $item['meta_data'] = json_decode($item['meta_data'], true) ?: [];
            }
            
            return $item;
            
        } catch (Exception $e) {
            throw new Exception("فشل في جلب عنصر القائمة: " . $e->getMessage());
        }
    }
    
    /**
     * الحصول على العناصر الرئيسية (بدون والد)
     */
    public function getParentMenuItems() {
        try {
            $stmt = $this->pdo->query("
                SELECT id, name, slug 
                FROM dynamic_menus 
                WHERE parent_id IS NULL 
                ORDER BY position ASC, name ASC
            ");
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            throw new Exception("فشل في جلب العناصر الرئيسية: " . $e->getMessage());
        }
    }
    
    /**
     * تفعيل/إلغاء تفعيل عنصر قائمة
     */
    public function toggleMenuItem($id, $field = 'is_active') {
        try {
            if (!in_array($field, ['is_active', 'is_visible'])) {
                throw new Exception("حقل غير صحيح");
            }
            
            $stmt = $this->pdo->prepare("
                UPDATE dynamic_menus 
                SET $field = NOT $field, updated_at = NOW() 
                WHERE id = ?
            ");
            
            $stmt->execute([$id]);
            
            // مسح الكاش
            $this->cache = [];
            
            // تسجيل النشاط
            if (isset($_SESSION['user_id'])) {
                $this->security->logActivity(
                    $_SESSION['user_id'],
                    'menu.toggle',
                    "تم تغيير حالة عنصر القائمة",
                    'dynamic_menus',
                    $id,
                    null,
                    ['field' => $field]
                );
            }
            
            return true;
            
        } catch (Exception $e) {
            throw new Exception("فشل في تغيير حالة عنصر القائمة: " . $e->getMessage());
        }
    }
    
    /**
     * تصدير القائمة
     */
    public function exportMenu() {
        try {
            $menuItems = $this->getAllMenuItems();
            
            $export = [
                'version' => '1.0',
                'exported_at' => date('Y-m-d H:i:s'),
                'menu_items' => $menuItems
            ];
            
            return json_encode($export, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
            
        } catch (Exception $e) {
            throw new Exception("فشل في تصدير القائمة: " . $e->getMessage());
        }
    }
    
    /**
     * استيراد القائمة
     */
    public function importMenu($jsonData, $replaceExisting = false) {
        try {
            $data = json_decode($jsonData, true);
            
            if (!$data || !isset($data['menu_items'])) {
                throw new Exception("بيانات غير صحيحة");
            }
            
            $this->pdo->beginTransaction();
            
            if ($replaceExisting) {
                $this->pdo->exec("DELETE FROM dynamic_menus");
            }
            
            foreach ($data['menu_items'] as $item) {
                unset($item['id']); // إزالة المعرف القديم
                $this->createMenuItem($item);
            }
            
            $this->pdo->commit();
            
            // مسح الكاش
            $this->cache = [];
            
            // تسجيل النشاط
            if (isset($_SESSION['user_id'])) {
                $this->security->logActivity(
                    $_SESSION['user_id'],
                    'menu.import',
                    "تم استيراد القائمة",
                    'dynamic_menus',
                    null,
                    null,
                    ['items_count' => count($data['menu_items'])]
                );
            }
            
            return true;
            
        } catch (Exception $e) {
            $this->pdo->rollBack();
            throw new Exception("فشل في استيراد القائمة: " . $e->getMessage());
        }
    }
}
?>
