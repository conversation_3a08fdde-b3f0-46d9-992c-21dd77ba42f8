<?php
session_start();

$query = trim($_GET['q'] ?? '');
$type = $_GET['type'] ?? 'all';
$results = [];
$totalResults = 0;

if (!empty($query)) {
    try {
        $pdo = new PDO("mysql:host=localhost;dbname=shahid_platform;charset=utf8mb4", "root", "");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        $searchTerm = "%$query%";
        
        if ($type === 'all' || $type === 'movies') {
            // البحث في الأفلام
            $movieStmt = $pdo->prepare("
                SELECT 'movie' as type, id, title, title_en, description, poster, rating, release_year, views 
                FROM movies 
                WHERE status = 'active' AND (title LIKE ? OR title_en LIKE ? OR description LIKE ?) 
                ORDER BY rating DESC, views DESC 
                LIMIT 20
            ");
            $movieStmt->execute([$searchTerm, $searchTerm, $searchTerm]);
            $movies = $movieStmt->fetchAll(PDO::FETCH_ASSOC);
            $results = array_merge($results, $movies);
        }
        
        if ($type === 'all' || $type === 'series') {
            // البحث في المسلسلات
            $seriesStmt = $pdo->prepare("
                SELECT 'series' as type, id, title, title_en, description, poster, rating, release_year, views, total_seasons, total_episodes 
                FROM series 
                WHERE status = 'active' AND (title LIKE ? OR title_en LIKE ? OR description LIKE ?) 
                ORDER BY rating DESC, views DESC 
                LIMIT 20
            ");
            $seriesStmt->execute([$searchTerm, $searchTerm, $searchTerm]);
            $series = $seriesStmt->fetchAll(PDO::FETCH_ASSOC);
            $results = array_merge($results, $series);
        }
        
        $totalResults = count($results);
        
        // ترتيب النتائج حسب الصلة
        usort($results, function($a, $b) use ($query) {
            $scoreA = 0;
            $scoreB = 0;
            
            // نقاط إضافية للعنوان المطابق
            if (stripos($a['title'], $query) !== false) $scoreA += 10;
            if (stripos($b['title'], $query) !== false) $scoreB += 10;
            
            // نقاط للتقييم
            $scoreA += $a['rating'];
            $scoreB += $b['rating'];
            
            return $scoreB <=> $scoreA;
        });
        
    } catch (Exception $e) {
        $results = [];
        $totalResults = 0;
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>البحث<?php echo !empty($query) ? ' - ' . htmlspecialchars($query) : ''; ?> - Shahid Platform</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', 'Cairo', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #2d2d2d 100%);
            color: #fff;
            min-height: 100vh;
        }
        
        .header {
            background: rgba(47, 47, 47, 0.95);
            padding: 1rem 0;
            border-bottom: 2px solid rgba(229, 9, 20, 0.3);
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }
        
        .nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }
        
        .logo {
            color: #E50914;
            font-size: 1.8rem;
            font-weight: bold;
            text-decoration: none;
        }
        
        .nav-links {
            display: flex;
            gap: 2rem;
            align-items: center;
        }
        
        .nav-links a {
            color: #fff;
            text-decoration: none;
            transition: color 0.3s ease;
        }
        
        .nav-links a:hover, .nav-links a.active {
            color: #E50914;
        }
        
        .search-section {
            background: rgba(47, 47, 47, 0.8);
            padding: 3rem 0;
            text-align: center;
        }
        
        .search-form {
            max-width: 600px;
            margin: 0 auto;
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
            justify-content: center;
        }
        
        .search-input {
            flex: 1;
            min-width: 300px;
            padding: 1rem;
            border: 2px solid rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.05);
            color: #fff;
            font-size: 1.1rem;
        }
        
        .search-input:focus {
            outline: none;
            border-color: #E50914;
            background: rgba(255, 255, 255, 0.1);
        }
        
        .search-type {
            padding: 1rem;
            border: 2px solid rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.05);
            color: #fff;
            font-size: 1rem;
        }
        
        .search-btn {
            padding: 1rem 2rem;
            background: linear-gradient(45deg, #E50914, #B8070F);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .search-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(229, 9, 20, 0.3);
        }
        
        .results-section {
            padding: 3rem 0;
        }
        
        .results-header {
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .results-header h2 {
            color: #E50914;
            margin-bottom: 0.5rem;
        }
        
        .results-stats {
            color: #ccc;
            font-size: 1.1rem;
        }
        
        .results-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 2rem;
        }
        
        .result-card {
            background: rgba(47, 47, 47, 0.8);
            border-radius: 15px;
            overflow: hidden;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }
        
        .result-card:hover {
            transform: translateY(-5px);
            border-color: rgba(229, 9, 20, 0.5);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.5);
        }
        
        .result-poster {
            width: 100%;
            height: 200px;
            background: linear-gradient(45deg, #333, #555);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            color: #666;
            position: relative;
        }
        
        .result-poster img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .result-type {
            position: absolute;
            top: 0.5rem;
            left: 0.5rem;
            background: rgba(229, 9, 20, 0.9);
            color: #fff;
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: bold;
        }
        
        .result-info {
            padding: 1.5rem;
        }
        
        .result-title {
            font-size: 1.2rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
            color: #fff;
        }
        
        .result-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            font-size: 0.9rem;
            color: #ccc;
        }
        
        .result-rating {
            display: flex;
            align-items: center;
            gap: 0.25rem;
            color: #FFD700;
        }
        
        .result-description {
            color: #ccc;
            font-size: 0.9rem;
            line-height: 1.4;
            margin-bottom: 1rem;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        
        .result-actions {
            display: flex;
            gap: 0.5rem;
        }
        
        .btn-small {
            padding: 0.5rem 1rem;
            font-size: 0.9rem;
            flex: 1;
            text-align: center;
            background: linear-gradient(45deg, #E50914, #B8070F);
            color: white;
            border: none;
            border-radius: 8px;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        
        .btn-small:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(229, 9, 20, 0.3);
        }
        
        .no-results {
            text-align: center;
            padding: 4rem 2rem;
            color: #ccc;
        }
        
        .no-results h3 {
            color: #E50914;
            margin-bottom: 1rem;
            font-size: 2rem;
        }
        
        .no-results p {
            font-size: 1.1rem;
            margin-bottom: 2rem;
        }
        
        .suggestions {
            background: rgba(33, 150, 243, 0.1);
            border: 1px solid rgba(33, 150, 243, 0.3);
            border-radius: 10px;
            padding: 2rem;
            margin: 2rem 0;
        }
        
        .suggestions h4 {
            color: #2196F3;
            margin-bottom: 1rem;
        }
        
        .suggestions ul {
            list-style: none;
            color: #ccc;
        }
        
        .suggestions li {
            margin: 0.5rem 0;
            padding-right: 1rem;
        }
        
        .suggestions li::before {
            content: "💡";
            margin-left: 0.5rem;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 0 1rem;
            }
            
            .nav {
                flex-direction: column;
                gap: 1rem;
            }
            
            .search-form {
                flex-direction: column;
                align-items: stretch;
            }
            
            .search-input {
                min-width: auto;
            }
            
            .results-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="container">
            <nav class="nav">
                <a href="index.php" class="logo">🎬 شاهد</a>
                <div class="nav-links">
                    <a href="index.php">الرئيسية</a>
                    <a href="movies.php">الأفلام</a>
                    <a href="series.php">المسلسلات</a>
                    <a href="search.php" class="active">البحث</a>
                    <?php if (isset($_SESSION['user_id'])): ?>
                        <a href="profile.php">الملف الشخصي</a>
                        <a href="favorites.php">المفضلة</a>
                        <a href="logout.php">تسجيل الخروج</a>
                    <?php else: ?>
                        <a href="login.php">تسجيل الدخول</a>
                        <a href="register.php">إنشاء حساب</a>
                    <?php endif; ?>
                </div>
            </nav>
        </div>
    </header>
    
    <section class="search-section">
        <div class="container">
            <h1 style="margin-bottom: 2rem; color: #E50914; font-size: 2.5rem;">🔍 البحث في المحتوى</h1>
            
            <form method="GET" action="" class="search-form">
                <input type="text" name="q" class="search-input" 
                       value="<?php echo htmlspecialchars($query); ?>" 
                       placeholder="ابحث عن أفلام، مسلسلات، ممثلين..."
                       autofocus>
                
                <select name="type" class="search-type">
                    <option value="all" <?php echo $type === 'all' ? 'selected' : ''; ?>>الكل</option>
                    <option value="movies" <?php echo $type === 'movies' ? 'selected' : ''; ?>>الأفلام فقط</option>
                    <option value="series" <?php echo $type === 'series' ? 'selected' : ''; ?>>المسلسلات فقط</option>
                </select>
                
                <button type="submit" class="search-btn">🔍 بحث</button>
            </form>
        </div>
    </section>
    
    <?php if (!empty($query)): ?>
        <section class="results-section">
            <div class="container">
                <div class="results-header">
                    <h2>نتائج البحث عن: "<?php echo htmlspecialchars($query); ?>"</h2>
                    <p class="results-stats">
                        تم العثور على <?php echo $totalResults; ?> نتيجة
                    </p>
                </div>
                
                <?php if (empty($results)): ?>
                    <div class="no-results">
                        <h3>😔 لا توجد نتائج</h3>
                        <p>لم يتم العثور على أي محتوى يطابق بحثك</p>
                        
                        <div class="suggestions">
                            <h4>💡 اقتراحات للحصول على نتائج أفضل:</h4>
                            <ul>
                                <li>تأكد من كتابة الكلمات بشكل صحيح</li>
                                <li>جرب استخدام كلمات مختلفة أو أكثر عمومية</li>
                                <li>استخدم كلمات مفتاحية أقل</li>
                                <li>جرب البحث باللغة الإنجليزية</li>
                            </ul>
                        </div>
                        
                        <div style="margin-top: 2rem;">
                            <a href="movies.php" class="btn-small" style="margin: 0.5rem;">تصفح الأفلام</a>
                            <a href="series.php" class="btn-small" style="margin: 0.5rem;">تصفح المسلسلات</a>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="results-grid">
                        <?php foreach ($results as $item): ?>
                            <div class="result-card">
                                <div class="result-poster">
                                    <div class="result-type">
                                        <?php echo $item['type'] === 'movie' ? '🎬 فيلم' : '📺 مسلسل'; ?>
                                    </div>
                                    
                                    <?php if (!empty($item['poster'])): ?>
                                        <img src="<?php echo htmlspecialchars($item['poster']); ?>" 
                                             alt="<?php echo htmlspecialchars($item['title']); ?>">
                                    <?php else: ?>
                                        <?php echo $item['type'] === 'movie' ? '🎬' : '📺'; ?>
                                    <?php endif; ?>
                                </div>
                                
                                <div class="result-info">
                                    <h3 class="result-title"><?php echo htmlspecialchars($item['title']); ?></h3>
                                    
                                    <div class="result-meta">
                                        <span><?php echo $item['release_year'] ?? 'غير محدد'; ?></span>
                                        <div class="result-rating">
                                            <span>⭐</span>
                                            <span><?php echo number_format($item['rating'], 1); ?></span>
                                        </div>
                                    </div>
                                    
                                    <?php if ($item['type'] === 'series'): ?>
                                        <div style="color: #ccc; font-size: 0.9rem; margin-bottom: 1rem;">
                                            🎬 <?php echo $item['total_seasons'] ?? 1; ?> موسم • 
                                            📺 <?php echo $item['total_episodes'] ?? 0; ?> حلقة
                                        </div>
                                    <?php endif; ?>
                                    
                                    <?php if (!empty($item['description'])): ?>
                                        <p class="result-description">
                                            <?php echo htmlspecialchars($item['description']); ?>
                                        </p>
                                    <?php endif; ?>
                                    
                                    <div class="result-actions">
                                        <a href="watch.php?type=<?php echo $item['type']; ?>&id=<?php echo $item['id']; ?>" 
                                           class="btn-small">
                                            ▶️ مشاهدة
                                        </a>
                                        <a href="<?php echo $item['type']; ?>_details.php?id=<?php echo $item['id']; ?>" 
                                           class="btn-small">
                                            📋 التفاصيل
                                        </a>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </section>
    <?php else: ?>
        <section class="results-section">
            <div class="container">
                <div class="no-results">
                    <h3>🔍 ابدأ البحث</h3>
                    <p>استخدم مربع البحث أعلاه للعثور على الأفلام والمسلسلات المفضلة لديك</p>
                    
                    <div style="margin-top: 2rem;">
                        <a href="movies.php" class="btn-small" style="margin: 0.5rem;">تصفح الأفلام</a>
                        <a href="series.php" class="btn-small" style="margin: 0.5rem;">تصفح المسلسلات</a>
                        <a href="index.php" class="btn-small" style="margin: 0.5rem;">الصفحة الرئيسية</a>
                    </div>
                </div>
            </div>
        </section>
    <?php endif; ?>
    
    <script>
        // تركيز تلقائي على مربع البحث
        document.addEventListener('DOMContentLoaded', function() {
            const searchInput = document.querySelector('.search-input');
            if (searchInput && !searchInput.value) {
                searchInput.focus();
            }
        });
        
        // تأثيرات تفاعلية للبطاقات
        document.querySelectorAll('.result-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-5px) scale(1.02)';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });
        
        // تحديث تلقائي لنوع البحث
        document.querySelector('select[name="type"]').addEventListener('change', function() {
            if (document.querySelector('input[name="q"]').value) {
                this.form.submit();
            }
        });
    </script>
</body>
</html>
