<?php
/**
 * Shahid Platform - User Management System
 * Complete User Management for Admin Panel
 */

session_start();
require_once '../config/database.php';
require_once '../config/auth.php';

// Check admin authentication
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../auth/login.php');
    exit;
}

// Database connection
try {
    $pdo = new PDO("mysql:host=localhost;dbname=shahid_platform;charset=utf8mb4", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    die("Database connection failed: " . $e->getMessage());
}

// Handle form submissions
$message = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';

    switch ($action) {
        case 'add_user':
            $result = addUser($pdo, $_POST);
            if ($result['success']) {
                $message = 'تم إضافة المستخدم بنجاح!';
            } else {
                $error = $result['error'];
            }
            break;

        case 'update_user':
            $result = updateUser($pdo, $_POST);
            if ($result['success']) {
                $message = 'تم تحديث المستخدم بنجاح!';
            } else {
                $error = $result['error'];
            }
            break;

        case 'delete_user':
            $result = deleteUser($pdo, $_POST['user_id']);
            if ($result['success']) {
                $message = 'تم حذف المستخدم بنجاح!';
            } else {
                $error = $result['error'];
            }
            break;

        case 'ban_user':
            $result = banUser($pdo, $_POST['user_id']);
            if ($result['success']) {
                $message = 'تم حظر المستخدم بنجاح!';
            } else {
                $error = $result['error'];
            }
            break;

        case 'unban_user':
            $result = unbanUser($pdo, $_POST['user_id']);
            if ($result['success']) {
                $message = 'تم إلغاء حظر المستخدم بنجاح!';
            } else {
                $error = $result['error'];
            }
            break;
    }
}

// Get users for display
$users = getUsers($pdo);
$userStats = getUserStats($pdo);

// Functions
function addUser($pdo, $data) {
    try {
        // Check if email already exists
        $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ?");
        $stmt->execute([$data['email']]);
        if ($stmt->fetch()) {
            return ['success' => false, 'error' => 'البريد الإلكتروني موجود بالفعل'];
        }

        $hashedPassword = password_hash($data['password'], PASSWORD_DEFAULT);

        $stmt = $pdo->prepare("
            INSERT INTO users (name, email, password, role, subscription_type, subscription_end, status, created_at)
            VALUES (?, ?, ?, ?, ?, ?, 'active', NOW())
        ");

        $subscriptionEnd = null;
        if ($data['subscription_type'] !== 'free') {
            $months = $data['subscription_type'] === 'premium' ? 12 : 6;
            $subscriptionEnd = date('Y-m-d H:i:s', strtotime("+$months months"));
        }

        $stmt->execute([
            $data['name'],
            $data['email'],
            $hashedPassword,
            $data['role'],
            $data['subscription_type'],
            $subscriptionEnd
        ]);

        return ['success' => true];
    } catch (Exception $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

function updateUser($pdo, $data) {
    try {
        $sql = "UPDATE users SET name = ?, email = ?, role = ?, subscription_type = ?";
        $params = [$data['name'], $data['email'], $data['role'], $data['subscription_type']];

        if (!empty($data['password'])) {
            $sql .= ", password = ?";
            $params[] = password_hash($data['password'], PASSWORD_DEFAULT);
        }

        $subscriptionEnd = null;
        if ($data['subscription_type'] !== 'free') {
            $months = $data['subscription_type'] === 'premium' ? 12 : 6;
            $subscriptionEnd = date('Y-m-d H:i:s', strtotime("+$months months"));
        }
        $sql .= ", subscription_end = ?";
        $params[] = $subscriptionEnd;

        $sql .= " WHERE id = ?";
        $params[] = $data['user_id'];

        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);

        return ['success' => true];
    } catch (Exception $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

function deleteUser($pdo, $userId) {
    try {
        $stmt = $pdo->prepare("UPDATE users SET status = 'deleted' WHERE id = ? AND role != 'admin'");
        $stmt->execute([$userId]);
        return ['success' => true];
    } catch (Exception $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

function banUser($pdo, $userId) {
    try {
        $stmt = $pdo->prepare("UPDATE users SET status = 'banned' WHERE id = ? AND role != 'admin'");
        $stmt->execute([$userId]);
        return ['success' => true];
    } catch (Exception $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

function unbanUser($pdo, $userId) {
    try {
        $stmt = $pdo->prepare("UPDATE users SET status = 'active' WHERE id = ?");
        $stmt->execute([$userId]);
        return ['success' => true];
    } catch (Exception $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

function getUsers($pdo) {
    $stmt = $pdo->query("
        SELECT u.*,
               COUNT(DISTINCT wh.id) as watch_count,
               COUNT(DISTINCT f.id) as favorites_count
        FROM users u
        LEFT JOIN watch_history wh ON u.id = wh.user_id
        LEFT JOIN favorites f ON u.id = f.user_id
        WHERE u.status != 'deleted'
        GROUP BY u.id
        ORDER BY u.created_at DESC
        LIMIT 50
    ");
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

function getUserStats($pdo) {
    $stats = [];

    // Total users
    $stmt = $pdo->query("SELECT COUNT(*) FROM users WHERE status = 'active'");
    $stats['total_users'] = $stmt->fetchColumn();

    // Premium users
    $stmt = $pdo->query("SELECT COUNT(*) FROM users WHERE subscription_type = 'premium' AND status = 'active'");
    $stats['premium_users'] = $stmt->fetchColumn();

    // Basic users
    $stmt = $pdo->query("SELECT COUNT(*) FROM users WHERE subscription_type = 'basic' AND status = 'active'");
    $stats['basic_users'] = $stmt->fetchColumn();

    // Free users
    $stmt = $pdo->query("SELECT COUNT(*) FROM users WHERE subscription_type = 'free' AND status = 'active'");
    $stats['free_users'] = $stmt->fetchColumn();

    // Banned users
    $stmt = $pdo->query("SELECT COUNT(*) FROM users WHERE status = 'banned'");
    $stats['banned_users'] = $stmt->fetchColumn();

    // New users today
    $stmt = $pdo->query("SELECT COUNT(*) FROM users WHERE DATE(created_at) = CURDATE() AND status = 'active'");
    $stats['new_today'] = $stmt->fetchColumn();

    return $stats;
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المستخدمين - Shahid Platform</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', 'Cairo', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #2d2d2d 100%);
            color: #fff;
            line-height: 1.6;
            min-height: 100vh;
        }

        .header {
            background: linear-gradient(135deg, #E50914 0%, #B8070F 100%);
            padding: 2rem 0;
            text-align: center;
            box-shadow: 0 4px 20px rgba(229, 9, 20, 0.3);
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 3rem;
        }

        .stat-card {
            background: rgba(47, 47, 47, 0.9);
            border-radius: 15px;
            padding: 2rem;
            text-align: center;
            border: 1px solid rgba(229, 9, 20, 0.2);
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(229, 9, 20, 0.3);
        }

        .stat-card .number {
            font-size: 2.5rem;
            font-weight: bold;
            color: #E50914;
            display: block;
            margin-bottom: 0.5rem;
        }

        .stat-card .label {
            color: #ccc;
            font-size: 1rem;
        }

        .nav-tabs {
            display: flex;
            background: rgba(47, 47, 47, 0.9);
            border-radius: 10px;
            margin-bottom: 2rem;
            overflow: hidden;
        }

        .nav-tab {
            flex: 1;
            padding: 1rem;
            background: transparent;
            border: none;
            color: #ccc;
            cursor: pointer;
            font-size: 1.1rem;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .nav-tab.active {
            background: #E50914;
            color: white;
        }

        .nav-tab:hover {
            background: rgba(229, 9, 20, 0.7);
            color: white;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .form-section {
            background: rgba(47, 47, 47, 0.9);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            border: 1px solid rgba(229, 9, 20, 0.2);
        }

        .form-section h2 {
            color: #E50914;
            margin-bottom: 1.5rem;
            font-size: 1.8rem;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: #ccc;
            font-weight: bold;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 0.8rem;
            border: 1px solid #555;
            border-radius: 5px;
            background: rgba(0, 0, 0, 0.3);
            color: #fff;
            font-size: 1rem;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #E50914;
            box-shadow: 0 0 10px rgba(229, 9, 20, 0.3);
        }

        .btn {
            background: linear-gradient(45deg, #E50914, #B8070F);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: bold;
            transition: all 0.3s ease;
            margin-left: 0.5rem;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(229, 9, 20, 0.3);
        }

        .btn-danger {
            background: linear-gradient(45deg, #dc3545, #c82333);
        }

        .btn-warning {
            background: linear-gradient(45deg, #ffc107, #e0a800);
        }

        .btn-success {
            background: linear-gradient(45deg, #28a745, #1e7e34);
        }

        .btn-small {
            padding: 0.5rem 1rem;
            font-size: 0.9rem;
        }

        .users-table {
            background: rgba(47, 47, 47, 0.9);
            border-radius: 15px;
            padding: 2rem;
            border: 1px solid rgba(229, 9, 20, 0.2);
            overflow-x: auto;
        }

        .users-table h2 {
            color: #E50914;
            margin-bottom: 1.5rem;
            font-size: 1.8rem;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }

        th, td {
            padding: 1rem;
            text-align: right;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        th {
            background: rgba(229, 9, 20, 0.2);
            color: #E50914;
            font-weight: bold;
        }

        tr:hover {
            background: rgba(229, 9, 20, 0.1);
        }

        .status-badge {
            padding: 0.3rem 0.8rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .status-active {
            background: rgba(76, 175, 80, 0.2);
            color: #4CAF50;
        }

        .status-banned {
            background: rgba(244, 67, 54, 0.2);
            color: #F44336;
        }

        .subscription-premium {
            background: rgba(255, 193, 7, 0.2);
            color: #FFC107;
        }

        .subscription-basic {
            background: rgba(33, 150, 243, 0.2);
            color: #2196F3;
        }

        .subscription-free {
            background: rgba(158, 158, 158, 0.2);
            color: #9E9E9E;
        }

        .message {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid #4CAF50;
            color: #4CAF50;
            padding: 1rem;
            border-radius: 5px;
            margin-bottom: 1rem;
        }

        .error {
            background: rgba(244, 67, 54, 0.2);
            border: 1px solid #F44336;
            color: #F44336;
            padding: 1rem;
            border-radius: 5px;
            margin-bottom: 1rem;
        }

        .back-link {
            display: inline-block;
            background: linear-gradient(45deg, #555, #333);
            color: white;
            padding: 0.8rem 1.5rem;
            border-radius: 8px;
            text-decoration: none;
            margin-bottom: 2rem;
            transition: all 0.3s ease;
        }

        .back-link:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
            color: white;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.8);
        }

        .modal-content {
            background: rgba(47, 47, 47, 0.95);
            margin: 5% auto;
            padding: 2rem;
            border-radius: 15px;
            width: 90%;
            max-width: 600px;
            border: 1px solid rgba(229, 9, 20, 0.3);
        }

        .close {
            color: #aaa;
            float: left;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover {
            color: #E50914;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>👥 إدارة المستخدمين</h1>
        <p>إدارة شاملة لجميع مستخدمي المنصة</p>
    </div>

    <div class="container">
        <a href="dashboard.php" class="back-link">← العودة إلى لوحة التحكم</a>

        <?php if ($message): ?>
            <div class="message"><?php echo htmlspecialchars($message); ?></div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="error"><?php echo htmlspecialchars($error); ?></div>
        <?php endif; ?>

        <!-- User Statistics -->
        <div class="stats-grid">
            <div class="stat-card">
                <span class="number"><?php echo $userStats['total_users']; ?></span>
                <span class="label">إجمالي المستخدمين</span>
            </div>
            <div class="stat-card">
                <span class="number"><?php echo $userStats['premium_users']; ?></span>
                <span class="label">مستخدمين مميزين</span>
            </div>
            <div class="stat-card">
                <span class="number"><?php echo $userStats['basic_users']; ?></span>
                <span class="label">مستخدمين أساسيين</span>
            </div>
            <div class="stat-card">
                <span class="number"><?php echo $userStats['free_users']; ?></span>
                <span class="label">مستخدمين مجانيين</span>
            </div>
            <div class="stat-card">
                <span class="number"><?php echo $userStats['banned_users']; ?></span>
                <span class="label">مستخدمين محظورين</span>
            </div>
            <div class="stat-card">
                <span class="number"><?php echo $userStats['new_today']; ?></span>
                <span class="label">جدد اليوم</span>
            </div>
        </div>

        <div class="nav-tabs">
            <button class="nav-tab active" onclick="showTab('manage-users')">إدارة المستخدمين</button>
            <button class="nav-tab" onclick="showTab('add-user')">إضافة مستخدم</button>
        </div>

        <!-- Manage Users Tab -->
        <div id="manage-users" class="tab-content active">
            <div class="users-table">
                <h2>قائمة المستخدمين</h2>
                <table>
                    <thead>
                        <tr>
                            <th>الاسم</th>
                            <th>البريد الإلكتروني</th>
                            <th>الدور</th>
                            <th>نوع الاشتراك</th>
                            <th>الحالة</th>
                            <th>المشاهدات</th>
                            <th>المفضلة</th>
                            <th>تاريخ التسجيل</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($users as $user): ?>
                        <tr>
                            <td><?php echo htmlspecialchars($user['name']); ?></td>
                            <td><?php echo htmlspecialchars($user['email']); ?></td>
                            <td>
                                <span class="status-badge <?php echo $user['role'] === 'admin' ? 'subscription-premium' : 'subscription-basic'; ?>">
                                    <?php echo $user['role'] === 'admin' ? 'مدير' : 'مستخدم'; ?>
                                </span>
                            </td>
                            <td>
                                <span class="status-badge subscription-<?php echo $user['subscription_type']; ?>">
                                    <?php
                                    echo $user['subscription_type'] === 'premium' ? 'مميز' :
                                        ($user['subscription_type'] === 'basic' ? 'أساسي' : 'مجاني');
                                    ?>
                                </span>
                            </td>
                            <td>
                                <span class="status-badge status-<?php echo $user['status']; ?>">
                                    <?php echo $user['status'] === 'active' ? 'نشط' : 'محظور'; ?>
                                </span>
                            </td>
                            <td><?php echo $user['watch_count']; ?></td>
                            <td><?php echo $user['favorites_count']; ?></td>
                            <td><?php echo date('Y-m-d', strtotime($user['created_at'])); ?></td>
                            <td>
                                <?php if ($user['role'] !== 'admin'): ?>
                                    <button class="btn btn-small" onclick="editUser(<?php echo $user['id']; ?>, '<?php echo htmlspecialchars($user['name']); ?>', '<?php echo htmlspecialchars($user['email']); ?>', '<?php echo $user['role']; ?>', '<?php echo $user['subscription_type']; ?>')">تعديل</button>

                                    <?php if ($user['status'] === 'active'): ?>
                                        <form method="POST" style="display: inline;">
                                            <input type="hidden" name="action" value="ban_user">
                                            <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                            <button type="submit" class="btn btn-warning btn-small" onclick="return confirm('هل أنت متأكد من حظر هذا المستخدم؟')">حظر</button>
                                        </form>
                                    <?php else: ?>
                                        <form method="POST" style="display: inline;">
                                            <input type="hidden" name="action" value="unban_user">
                                            <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                            <button type="submit" class="btn btn-success btn-small">إلغاء الحظر</button>
                                        </form>
                                    <?php endif; ?>

                                    <form method="POST" style="display: inline;">
                                        <input type="hidden" name="action" value="delete_user">
                                        <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                        <button type="submit" class="btn btn-danger btn-small" onclick="return confirm('هل أنت متأكد من حذف هذا المستخدم؟')">حذف</button>
                                    </form>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Add User Tab -->
        <div id="add-user" class="tab-content">
            <div class="form-section">
                <h2>إضافة مستخدم جديد</h2>
                <form method="POST">
                    <input type="hidden" name="action" value="add_user">
                    <div class="form-grid">
                        <div class="form-group">
                            <label>الاسم الكامل</label>
                            <input type="text" name="name" required>
                        </div>
                        <div class="form-group">
                            <label>البريد الإلكتروني</label>
                            <input type="email" name="email" required>
                        </div>
                        <div class="form-group">
                            <label>كلمة المرور</label>
                            <input type="password" name="password" required>
                        </div>
                        <div class="form-group">
                            <label>الدور</label>
                            <select name="role" required>
                                <option value="user">مستخدم</option>
                                <option value="admin">مدير</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>نوع الاشتراك</label>
                            <select name="subscription_type" required>
                                <option value="free">مجاني</option>
                                <option value="basic">أساسي</option>
                                <option value="premium">مميز</option>
                            </select>
                        </div>
                    </div>
                    <button type="submit" class="btn">إضافة المستخدم</button>
                </form>
            </div>
        </div>
    </div>

    <!-- Edit User Modal -->
    <div id="editModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal()">&times;</span>
            <h2>تعديل المستخدم</h2>
            <form method="POST">
                <input type="hidden" name="action" value="update_user">
                <input type="hidden" name="user_id" id="edit_user_id">
                <div class="form-group">
                    <label>الاسم الكامل</label>
                    <input type="text" name="name" id="edit_name" required>
                </div>
                <div class="form-group">
                    <label>البريد الإلكتروني</label>
                    <input type="email" name="email" id="edit_email" required>
                </div>
                <div class="form-group">
                    <label>كلمة المرور الجديدة (اتركها فارغة للاحتفاظ بالحالية)</label>
                    <input type="password" name="password" id="edit_password">
                </div>
                <div class="form-group">
                    <label>الدور</label>
                    <select name="role" id="edit_role" required>
                        <option value="user">مستخدم</option>
                        <option value="admin">مدير</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>نوع الاشتراك</label>
                    <select name="subscription_type" id="edit_subscription" required>
                        <option value="free">مجاني</option>
                        <option value="basic">أساسي</option>
                        <option value="premium">مميز</option>
                    </select>
                </div>
                <button type="submit" class="btn">تحديث المستخدم</button>
            </form>
        </div>
    </div>

    <script>
        function showTab(tabName) {
            // Hide all tab contents
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(tab => tab.classList.remove('active'));

            // Remove active class from all tabs
            const tabs = document.querySelectorAll('.nav-tab');
            tabs.forEach(tab => tab.classList.remove('active'));

            // Show selected tab content
            document.getElementById(tabName).classList.add('active');

            // Add active class to clicked tab
            event.target.classList.add('active');
        }

        function editUser(id, name, email, role, subscription) {
            document.getElementById('edit_user_id').value = id;
            document.getElementById('edit_name').value = name;
            document.getElementById('edit_email').value = email;
            document.getElementById('edit_role').value = role;
            document.getElementById('edit_subscription').value = subscription;
            document.getElementById('editModal').style.display = 'block';
        }

        function closeModal() {
            document.getElementById('editModal').style.display = 'none';
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('editModal');
            if (event.target === modal) {
                modal.style.display = 'none';
            }
        }

        console.log('👥 User Management System loaded successfully!');
    </script>
</body>
</html>