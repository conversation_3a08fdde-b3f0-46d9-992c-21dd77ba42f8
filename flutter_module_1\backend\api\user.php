<?php
/**
 * Shahid User API
 * Professional Video Streaming Platform
 */

require_once '../core/Database.php';
require_once '../core/Security.php';
require_once '../core/ApiController.php';
require_once '../models/User.php';

class UserAPI extends ApiController {
    
    public function handleRequest() {
        $route = trim($_SERVER['REQUEST_URI'], '/');
        $segments = explode('/', $route);
        
        // Remove 'api/user' from segments
        $segments = array_slice($segments, 2);
        
        $action = $segments[0] ?? null;
        $id = $segments[1] ?? null;
        
        switch ($action) {
            case null:
            case '':
            case 'profile':
                $this->getUserProfile();
                break;
            case 'update':
                $this->updateUserProfile();
                break;
            case 'change-password':
                $this->changePassword();
                break;
            case 'upload-avatar':
                $this->uploadAvatar();
                break;
            case 'subscription':
                $this->getUserSubscription();
                break;
            case 'favorites':
                $this->handleFavorites($id);
                break;
            case 'watchlist':
                $this->handleWatchlist($id);
                break;
            case 'history':
                $this->handleHistory($id);
                break;
            case 'devices':
                $this->handleDevices($id);
                break;
            case 'notifications':
                $this->handleNotifications($id);
                break;
            case 'preferences':
                $this->handlePreferences();
                break;
            default:
                $this->sendError('Invalid user endpoint', 404);
        }
    }
    
    private function getUserProfile() {
        if (!$this->validateMethod(['GET'])) return;
        
        $user = $this->authenticateUser();
        if (!$user) return;
        
        $userModel = new User();
        $profile = $userModel->getProfile($user['id']);
        
        // Remove sensitive information
        unset($profile['password']);
        
        $this->sendSuccess($profile);
    }
    
    private function updateUserProfile() {
        if (!$this->validateMethod(['PUT', 'POST'])) return;
        
        $user = $this->authenticateUser();
        if (!$user) return;
        
        $allowedFields = ['name', 'phone', 'date_of_birth', 'gender', 'country', 'language', 'timezone'];
        $updateData = [];
        
        foreach ($allowedFields as $field) {
            if (isset($this->requestData[$field])) {
                $updateData[$field] = $this->sanitizeInput($this->requestData[$field]);
            }
        }
        
        if (empty($updateData)) {
            $this->sendError('No valid fields to update', 400);
            return;
        }
        
        // Validate specific fields
        if (isset($updateData['phone']) && !empty($updateData['phone'])) {
            if (!preg_match('/^[\+]?[0-9\s\-\(\)]{10,20}$/', $updateData['phone'])) {
                $this->sendError('Invalid phone number format', 400);
                return;
            }
        }
        
        if (isset($updateData['date_of_birth']) && !empty($updateData['date_of_birth'])) {
            if (!strtotime($updateData['date_of_birth'])) {
                $this->sendError('Invalid date format', 400);
                return;
            }
        }
        
        $userModel = new User();
        $result = $userModel->update($user['id'], $updateData);
        
        if ($result) {
            $this->sendSuccess(null, 'Profile updated successfully');
        } else {
            $this->sendError('Failed to update profile', 500);
        }
    }
    
    private function changePassword() {
        if (!$this->validateMethod(['POST'])) return;
        
        $user = $this->authenticateUser();
        if (!$user) return;
        
        if (!$this->validateRequired(['current_password', 'new_password'])) return;
        
        $currentPassword = $this->requestData['current_password'];
        $newPassword = $this->requestData['new_password'];
        
        if (!password_verify($currentPassword, $user['password'])) {
            $this->sendError('Current password is incorrect', 400);
            return;
        }
        
        $passwordError = $this->validatePassword($newPassword);
        if ($passwordError) {
            $this->sendError($passwordError, 400);
            return;
        }
        
        $userModel = new User();
        $result = $userModel->update($user['id'], [
            'password' => password_hash($newPassword, PASSWORD_DEFAULT),
            'password_changed_at' => date('Y-m-d H:i:s')
        ]);
        
        if ($result) {
            $this->sendSuccess(null, 'Password changed successfully');
        } else {
            $this->sendError('Failed to change password', 500);
        }
    }
    
    private function uploadAvatar() {
        if (!$this->validateMethod(['POST'])) return;
        
        $user = $this->authenticateUser();
        if (!$user) return;
        
        $allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
        $maxSize = 5 * 1024 * 1024; // 5MB
        
        $file = $this->handleFileUpload('avatar', $allowedTypes, $maxSize);
        if ($file === false) return;
        
        if (!$file) {
            $this->sendError('No file uploaded', 400);
            return;
        }
        
        // Generate unique filename
        $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
        $filename = 'avatar_' . $user['id'] . '_' . time() . '.' . $extension;
        $uploadPath = '../storage/avatars/' . $filename;
        
        // Create directory if it doesn't exist
        $uploadDir = dirname($uploadPath);
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }
        
        if (move_uploaded_file($file['tmp_name'], $uploadPath)) {
            // Delete old avatar if exists
            if ($user['avatar'] && file_exists('../storage/avatars/' . basename($user['avatar']))) {
                unlink('../storage/avatars/' . basename($user['avatar']));
            }
            
            // Update user avatar
            $avatarUrl = '/storage/avatars/' . $filename;
            $userModel = new User();
            $result = $userModel->update($user['id'], ['avatar' => $avatarUrl]);
            
            if ($result) {
                $this->sendSuccess(['avatar_url' => $avatarUrl], 'Avatar uploaded successfully');
            } else {
                $this->sendError('Failed to update avatar', 500);
            }
        } else {
            $this->sendError('Failed to upload file', 500);
        }
    }
    
    private function getUserSubscription() {
        if (!$this->validateMethod(['GET'])) return;
        
        $user = $this->authenticateUser();
        if (!$user) return;
        
        $userModel = new User();
        $subscription = $userModel->getCurrentSubscription($user['id']);
        
        $this->sendSuccess($subscription);
    }
    
    private function handleFavorites($action) {
        $user = $this->authenticateUser();
        if (!$user) return;
        
        switch ($action) {
            case null:
            case '':
                $this->getFavorites();
                break;
            case 'add':
                $this->addToFavorites();
                break;
            case 'remove':
                $this->removeFromFavorites();
                break;
            case 'toggle':
                $this->toggleFavorite();
                break;
            default:
                $this->sendError('Invalid favorites action', 400);
        }
    }
    
    private function getFavorites() {
        if (!$this->validateMethod(['GET'])) return;
        
        $pagination = $this->getPaginationParams();
        $type = $_GET['type'] ?? 'all'; // all, movies, series
        
        $userModel = new User();
        $favorites = $userModel->getFavorites($this->user['id'], $pagination['page'], $pagination['limit'], $type);
        $total = $userModel->getFavoritesCount($this->user['id'], $type);
        
        $this->sendPaginatedResponse($favorites, $total, $pagination['page'], $pagination['limit']);
    }
    
    private function addToFavorites() {
        if (!$this->validateMethod(['POST'])) return;
        if (!$this->validateRequired(['content_id', 'content_type'])) return;
        
        $contentId = intval($this->requestData['content_id']);
        $contentType = $this->requestData['content_type'];
        
        if (!in_array($contentType, ['movie', 'series'])) {
            $this->sendError('Invalid content type', 400);
            return;
        }
        
        $userModel = new User();
        $result = $userModel->addToFavorites($this->user['id'], $contentId, $contentType);
        
        if ($result) {
            $this->sendSuccess(null, 'Added to favorites successfully');
        } else {
            $this->sendError('Failed to add to favorites or already exists', 400);
        }
    }
    
    private function removeFromFavorites() {
        if (!$this->validateMethod(['DELETE', 'POST'])) return;
        if (!$this->validateRequired(['content_id', 'content_type'])) return;
        
        $contentId = intval($this->requestData['content_id']);
        $contentType = $this->requestData['content_type'];
        
        $userModel = new User();
        $result = $userModel->removeFromFavorites($this->user['id'], $contentId, $contentType);
        
        if ($result) {
            $this->sendSuccess(null, 'Removed from favorites successfully');
        } else {
            $this->sendError('Failed to remove from favorites', 400);
        }
    }
    
    private function toggleFavorite() {
        if (!$this->validateMethod(['POST'])) return;
        if (!$this->validateRequired(['content_id', 'content_type'])) return;
        
        $contentId = intval($this->requestData['content_id']);
        $contentType = $this->requestData['content_type'];
        
        $userModel = new User();
        $isFavorite = $userModel->isFavorite($this->user['id'], $contentId, $contentType);
        
        if ($isFavorite) {
            $result = $userModel->removeFromFavorites($this->user['id'], $contentId, $contentType);
            $message = 'Removed from favorites';
            $action = 'removed';
        } else {
            $result = $userModel->addToFavorites($this->user['id'], $contentId, $contentType);
            $message = 'Added to favorites';
            $action = 'added';
        }
        
        if ($result) {
            $this->sendSuccess(['action' => $action, 'is_favorite' => !$isFavorite], $message);
        } else {
            $this->sendError('Failed to toggle favorite', 500);
        }
    }
    
    private function handleWatchlist($action) {
        $user = $this->authenticateUser();
        if (!$user) return;
        
        switch ($action) {
            case null:
            case '':
                $this->getWatchlist();
                break;
            case 'add':
                $this->addToWatchlist();
                break;
            case 'remove':
                $this->removeFromWatchlist();
                break;
            default:
                $this->sendError('Invalid watchlist action', 400);
        }
    }
    
    private function getWatchlist() {
        if (!$this->validateMethod(['GET'])) return;
        
        $pagination = $this->getPaginationParams();
        $type = $_GET['type'] ?? 'all';
        
        $userModel = new User();
        $watchlist = $userModel->getWatchlist($this->user['id'], $pagination['page'], $pagination['limit'], $type);
        $total = $userModel->getWatchlistCount($this->user['id'], $type);
        
        $this->sendPaginatedResponse($watchlist, $total, $pagination['page'], $pagination['limit']);
    }
    
    private function addToWatchlist() {
        if (!$this->validateMethod(['POST'])) return;
        if (!$this->validateRequired(['content_id', 'content_type'])) return;
        
        $contentId = intval($this->requestData['content_id']);
        $contentType = $this->requestData['content_type'];
        
        $userModel = new User();
        $result = $userModel->addToWatchlist($this->user['id'], $contentId, $contentType);
        
        if ($result) {
            $this->sendSuccess(null, 'Added to watchlist successfully');
        } else {
            $this->sendError('Failed to add to watchlist', 400);
        }
    }
    
    private function removeFromWatchlist() {
        if (!$this->validateMethod(['DELETE', 'POST'])) return;
        if (!$this->validateRequired(['content_id', 'content_type'])) return;
        
        $contentId = intval($this->requestData['content_id']);
        $contentType = $this->requestData['content_type'];
        
        $userModel = new User();
        $result = $userModel->removeFromWatchlist($this->user['id'], $contentId, $contentType);
        
        if ($result) {
            $this->sendSuccess(null, 'Removed from watchlist successfully');
        } else {
            $this->sendError('Failed to remove from watchlist', 400);
        }
    }
    
    private function handleHistory($action) {
        $user = $this->authenticateUser();
        if (!$user) return;
        
        switch ($action) {
            case null:
            case '':
                $this->getWatchHistory();
                break;
            case 'update':
                $this->updateWatchProgress();
                break;
            case 'clear':
                $this->clearWatchHistory();
                break;
            default:
                $this->sendError('Invalid history action', 400);
        }
    }
    
    private function getWatchHistory() {
        if (!$this->validateMethod(['GET'])) return;
        
        $pagination = $this->getPaginationParams();
        $type = $_GET['type'] ?? 'all';
        
        $userModel = new User();
        $history = $userModel->getWatchHistory($this->user['id'], $pagination['page'], $pagination['limit'], $type);
        $total = $userModel->getWatchHistoryCount($this->user['id'], $type);
        
        $this->sendPaginatedResponse($history, $total, $pagination['page'], $pagination['limit']);
    }
    
    private function updateWatchProgress() {
        if (!$this->validateMethod(['POST'])) return;
        if (!$this->validateRequired(['content_id', 'content_type', 'progress', 'duration'])) return;
        
        $contentId = intval($this->requestData['content_id']);
        $contentType = $this->requestData['content_type'];
        $progress = intval($this->requestData['progress']);
        $duration = intval($this->requestData['duration']);
        
        if ($progress < 0 || $duration <= 0 || $progress > $duration) {
            $this->sendError('Invalid progress parameters', 400);
            return;
        }
        
        $userModel = new User();
        $result = $userModel->updateWatchProgress($this->user['id'], $contentId, $contentType, $progress, $duration);
        
        if ($result) {
            $this->sendSuccess(null, 'Watch progress updated successfully');
        } else {
            $this->sendError('Failed to update watch progress', 500);
        }
    }
    
    private function clearWatchHistory() {
        if (!$this->validateMethod(['DELETE', 'POST'])) return;
        
        $userModel = new User();
        $result = $userModel->clearWatchHistory($this->user['id']);
        
        if ($result) {
            $this->sendSuccess(null, 'Watch history cleared successfully');
        } else {
            $this->sendError('Failed to clear watch history', 500);
        }
    }
    
    private function handleDevices($action) {
        $user = $this->authenticateUser();
        if (!$user) return;
        
        switch ($action) {
            case null:
            case '':
                $this->getUserDevices();
                break;
            case 'register':
                $this->registerDevice();
                break;
            case 'remove':
                $this->removeDevice();
                break;
            default:
                $this->sendError('Invalid devices action', 400);
        }
    }
    
    private function getUserDevices() {
        if (!$this->validateMethod(['GET'])) return;
        
        $userModel = new User();
        $devices = $userModel->getUserDevices($this->user['id']);
        
        $this->sendSuccess($devices);
    }
    
    private function registerDevice() {
        if (!$this->validateMethod(['POST'])) return;
        if (!$this->validateRequired(['device_name', 'device_type'])) return;
        
        $deviceName = $this->sanitizeInput($this->requestData['device_name']);
        $deviceType = $this->sanitizeInput($this->requestData['device_type']);
        $deviceId = $this->requestData['device_id'] ?? null;
        
        $userModel = new User();
        $result = $userModel->registerDevice($this->user['id'], $deviceName, $deviceType, $deviceId);
        
        if ($result) {
            $this->sendSuccess(['device_id' => $result], 'Device registered successfully');
        } else {
            $this->sendError('Failed to register device', 500);
        }
    }
    
    private function removeDevice() {
        if (!$this->validateMethod(['DELETE', 'POST'])) return;
        if (!$this->validateRequired(['device_id'])) return;
        
        $deviceId = intval($this->requestData['device_id']);
        
        $userModel = new User();
        $result = $userModel->removeDevice($this->user['id'], $deviceId);
        
        if ($result) {
            $this->sendSuccess(null, 'Device removed successfully');
        } else {
            $this->sendError('Failed to remove device', 400);
        }
    }
    
    private function handleNotifications($action) {
        $user = $this->authenticateUser();
        if (!$user) return;
        
        switch ($action) {
            case null:
            case '':
                $this->getNotifications();
                break;
            case 'mark-read':
                $this->markNotificationAsRead();
                break;
            case 'mark-all-read':
                $this->markAllNotificationsAsRead();
                break;
            default:
                $this->sendError('Invalid notifications action', 400);
        }
    }
    
    private function getNotifications() {
        if (!$this->validateMethod(['GET'])) return;
        
        $pagination = $this->getPaginationParams();
        $unreadOnly = $_GET['unread_only'] ?? false;
        
        $userModel = new User();
        $notifications = $userModel->getNotifications($this->user['id'], $pagination['page'], $pagination['limit'], $unreadOnly);
        $total = $userModel->getNotificationsCount($this->user['id'], $unreadOnly);
        
        $this->sendPaginatedResponse($notifications, $total, $pagination['page'], $pagination['limit']);
    }
    
    private function markNotificationAsRead() {
        if (!$this->validateMethod(['POST'])) return;
        if (!$this->validateRequired(['notification_id'])) return;
        
        $notificationId = intval($this->requestData['notification_id']);
        
        $userModel = new User();
        $result = $userModel->markNotificationAsRead($this->user['id'], $notificationId);
        
        if ($result) {
            $this->sendSuccess(null, 'Notification marked as read');
        } else {
            $this->sendError('Failed to mark notification as read', 400);
        }
    }
    
    private function markAllNotificationsAsRead() {
        if (!$this->validateMethod(['POST'])) return;
        
        $userModel = new User();
        $result = $userModel->markAllNotificationsAsRead($this->user['id']);
        
        if ($result) {
            $this->sendSuccess(null, 'All notifications marked as read');
        } else {
            $this->sendError('Failed to mark notifications as read', 500);
        }
    }
    
    private function handlePreferences() {
        $user = $this->authenticateUser();
        if (!$user) return;
        
        if ($this->requestMethod === 'GET') {
            $this->getUserPreferences();
        } elseif ($this->requestMethod === 'POST' || $this->requestMethod === 'PUT') {
            $this->updateUserPreferences();
        } else {
            $this->sendError('Method not allowed', 405);
        }
    }
    
    private function getUserPreferences() {
        $userModel = new User();
        $preferences = $userModel->getUserPreferences($this->user['id']);
        
        $this->sendSuccess($preferences);
    }
    
    private function updateUserPreferences() {
        $allowedPreferences = [
            'language', 'subtitle_language', 'video_quality', 'autoplay',
            'notifications_email', 'notifications_push', 'adult_content'
        ];
        
        $preferences = [];
        foreach ($allowedPreferences as $pref) {
            if (isset($this->requestData[$pref])) {
                $preferences[$pref] = $this->requestData[$pref];
            }
        }
        
        if (empty($preferences)) {
            $this->sendError('No valid preferences to update', 400);
            return;
        }
        
        $userModel = new User();
        $result = $userModel->updateUserPreferences($this->user['id'], $preferences);
        
        if ($result) {
            $this->sendSuccess(null, 'Preferences updated successfully');
        } else {
            $this->sendError('Failed to update preferences', 500);
        }
    }
}

// Handle the request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

$api = new UserAPI();
$api->handleRequest();
?>
