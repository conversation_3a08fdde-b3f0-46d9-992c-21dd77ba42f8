<?php
/**
 * إنشاء جداول النظام الديناميكي
 * Create Dynamic System Tables
 */

echo "<h1>🏗️ إنشاء جداول النظام الديناميكي</h1>";

try {
    $pdo = new PDO("mysql:host=localhost;dbname=shahid_platform;charset=utf8mb4", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>✅ متصل بقاعدة البيانات</h3>";
    echo "</div>";
    
    // 1. جدول القوائم الديناميكية
    echo "<h2>📋 إنشاء جدول القوائم الديناميكية</h2>";
    
    $menuSQL = "
        CREATE TABLE IF NOT EXISTS `dynamic_menus` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `name` varchar(100) NOT NULL,
            `name_en` varchar(100) DEFAULT NULL,
            `slug` varchar(100) NOT NULL UNIQUE,
            `parent_id` int(11) DEFAULT NULL,
            `icon` varchar(50) DEFAULT NULL,
            `url` varchar(255) DEFAULT NULL,
            `target` enum('_self','_blank') DEFAULT '_self',
            `css_class` varchar(100) DEFAULT NULL,
            `position` int(11) DEFAULT 0,
            `is_active` tinyint(1) DEFAULT 1,
            `is_visible` tinyint(1) DEFAULT 1,
            `permissions` json DEFAULT NULL,
            `meta_data` json DEFAULT NULL,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `idx_parent` (`parent_id`),
            KEY `idx_position` (`position`),
            KEY `idx_active` (`is_active`),
            FOREIGN KEY (`parent_id`) REFERENCES `dynamic_menus`(`id`) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    $pdo->exec($menuSQL);
    echo "<p style='color: #4CAF50;'>✅ تم إنشاء جدول القوائم الديناميكية</p>";
    
    // 2. جدول الصفحات الديناميكية
    echo "<h2>📄 إنشاء جدول الصفحات الديناميكية</h2>";
    
    $pagesSQL = "
        CREATE TABLE IF NOT EXISTS `dynamic_pages` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `title` varchar(255) NOT NULL,
            `title_en` varchar(255) DEFAULT NULL,
            `slug` varchar(255) NOT NULL UNIQUE,
            `content` longtext DEFAULT NULL,
            `content_en` longtext DEFAULT NULL,
            `excerpt` text DEFAULT NULL,
            `featured_image` varchar(255) DEFAULT NULL,
            `template` varchar(100) DEFAULT 'default',
            `status` enum('draft','published','private') DEFAULT 'draft',
            `seo_title` varchar(255) DEFAULT NULL,
            `seo_description` text DEFAULT NULL,
            `seo_keywords` text DEFAULT NULL,
            `custom_css` text DEFAULT NULL,
            `custom_js` text DEFAULT NULL,
            `meta_data` json DEFAULT NULL,
            `author_id` int(11) DEFAULT NULL,
            `views` int(11) DEFAULT 0,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `idx_slug` (`slug`),
            KEY `idx_status` (`status`),
            KEY `idx_author` (`author_id`),
            FOREIGN KEY (`author_id`) REFERENCES `users`(`id`) ON DELETE SET NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    $pdo->exec($pagesSQL);
    echo "<p style='color: #4CAF50;'>✅ تم إنشاء جدول الصفحات الديناميكية</p>";
    
    // 3. جدول إعدادات النظام
    echo "<h2>⚙️ إنشاء جدول إعدادات النظام</h2>";
    
    $systemSettingsSQL = "
        CREATE TABLE IF NOT EXISTS `system_settings` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `setting_key` varchar(100) NOT NULL UNIQUE,
            `setting_value` longtext DEFAULT NULL,
            `setting_type` enum('text','number','boolean','json','file','color','email','url') DEFAULT 'text',
            `category` varchar(50) DEFAULT 'general',
            `label` varchar(255) NOT NULL,
            `description` text DEFAULT NULL,
            `is_public` tinyint(1) DEFAULT 0,
            `validation_rules` json DEFAULT NULL,
            `options` json DEFAULT NULL,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `idx_category` (`category`),
            KEY `idx_public` (`is_public`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    $pdo->exec($systemSettingsSQL);
    echo "<p style='color: #4CAF50;'>✅ تم إنشاء جدول إعدادات النظام</p>";
    
    // 4. جدول الأدوار والصلاحيات
    echo "<h2>🔐 إنشاء جدول الأدوار والصلاحيات</h2>";
    
    $rolesSQL = "
        CREATE TABLE IF NOT EXISTS `roles` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `name` varchar(100) NOT NULL UNIQUE,
            `display_name` varchar(255) NOT NULL,
            `description` text DEFAULT NULL,
            `permissions` json DEFAULT NULL,
            `is_system` tinyint(1) DEFAULT 0,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    $pdo->exec($rolesSQL);
    echo "<p style='color: #4CAF50;'>✅ تم إنشاء جدول الأدوار</p>";
    
    $userRolesSQL = "
        CREATE TABLE IF NOT EXISTS `user_roles` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `user_id` int(11) NOT NULL,
            `role_id` int(11) NOT NULL,
            `assigned_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `assigned_by` int(11) DEFAULT NULL,
            PRIMARY KEY (`id`),
            UNIQUE KEY `unique_user_role` (`user_id`, `role_id`),
            FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
            FOREIGN KEY (`role_id`) REFERENCES `roles`(`id`) ON DELETE CASCADE,
            FOREIGN KEY (`assigned_by`) REFERENCES `users`(`id`) ON DELETE SET NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    $pdo->exec($userRolesSQL);
    echo "<p style='color: #4CAF50;'>✅ تم إنشاء جدول أدوار المستخدمين</p>";
    
    // 5. جدول سجل النشاطات
    echo "<h2>📊 إنشاء جدول سجل النشاطات</h2>";
    
    $activityLogSQL = "
        CREATE TABLE IF NOT EXISTS `activity_logs` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `user_id` int(11) DEFAULT NULL,
            `action` varchar(100) NOT NULL,
            `description` text DEFAULT NULL,
            `model_type` varchar(100) DEFAULT NULL,
            `model_id` int(11) DEFAULT NULL,
            `old_values` json DEFAULT NULL,
            `new_values` json DEFAULT NULL,
            `ip_address` varchar(45) DEFAULT NULL,
            `user_agent` text DEFAULT NULL,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `idx_user` (`user_id`),
            KEY `idx_action` (`action`),
            KEY `idx_model` (`model_type`, `model_id`),
            KEY `idx_created` (`created_at`),
            FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE SET NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    $pdo->exec($activityLogSQL);
    echo "<p style='color: #4CAF50;'>✅ تم إنشاء جدول سجل النشاطات</p>";
    
    // 6. جدول الويدجت والمكونات
    echo "<h2>🧩 إنشاء جدول الويدجت</h2>";
    
    $widgetsSQL = "
        CREATE TABLE IF NOT EXISTS `widgets` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `name` varchar(100) NOT NULL,
            `title` varchar(255) NOT NULL,
            `content` longtext DEFAULT NULL,
            `widget_type` varchar(50) NOT NULL,
            `position` varchar(50) DEFAULT 'sidebar',
            `sort_order` int(11) DEFAULT 0,
            `is_active` tinyint(1) DEFAULT 1,
            `settings` json DEFAULT NULL,
            `permissions` json DEFAULT NULL,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `idx_position` (`position`),
            KEY `idx_active` (`is_active`),
            KEY `idx_sort` (`sort_order`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    $pdo->exec($widgetsSQL);
    echo "<p style='color: #4CAF50;'>✅ تم إنشاء جدول الويدجت</p>";
    
    // 7. جدول القوالب والثيمات
    echo "<h2>🎨 إنشاء جدول القوالب</h2>";
    
    $themesSQL = "
        CREATE TABLE IF NOT EXISTS `themes` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `name` varchar(100) NOT NULL UNIQUE,
            `display_name` varchar(255) NOT NULL,
            `description` text DEFAULT NULL,
            `version` varchar(20) DEFAULT '1.0.0',
            `author` varchar(100) DEFAULT NULL,
            `preview_image` varchar(255) DEFAULT NULL,
            `is_active` tinyint(1) DEFAULT 0,
            `settings` json DEFAULT NULL,
            `custom_css` longtext DEFAULT NULL,
            `custom_js` longtext DEFAULT NULL,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    $pdo->exec($themesSQL);
    echo "<p style='color: #4CAF50;'>✅ تم إنشاء جدول القوالب</p>";
    
    // 8. جدول الملفات والوسائط
    echo "<h2>📁 إنشاء جدول الملفات</h2>";
    
    $mediaSQL = "
        CREATE TABLE IF NOT EXISTS `media_files` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `filename` varchar(255) NOT NULL,
            `original_name` varchar(255) NOT NULL,
            `file_path` varchar(500) NOT NULL,
            `file_url` varchar(500) NOT NULL,
            `file_size` bigint(20) DEFAULT NULL,
            `mime_type` varchar(100) DEFAULT NULL,
            `file_type` enum('image','video','audio','document','other') DEFAULT 'other',
            `alt_text` varchar(255) DEFAULT NULL,
            `caption` text DEFAULT NULL,
            `uploaded_by` int(11) DEFAULT NULL,
            `is_public` tinyint(1) DEFAULT 1,
            `meta_data` json DEFAULT NULL,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `idx_type` (`file_type`),
            KEY `idx_uploader` (`uploaded_by`),
            KEY `idx_public` (`is_public`),
            FOREIGN KEY (`uploaded_by`) REFERENCES `users`(`id`) ON DELETE SET NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    $pdo->exec($mediaSQL);
    echo "<p style='color: #4CAF50;'>✅ تم إنشاء جدول الملفات</p>";
    
    // إدراج البيانات الافتراضية
    echo "<h2>📝 إدراج البيانات الافتراضية</h2>";
    
    // إعدادات النظام الافتراضية
    $defaultSettings = [
        ['site_name', 'Shahid Platform', 'text', 'general', 'اسم الموقع', 'اسم الموقع الذي يظهر في العنوان', 1],
        ['site_description', 'منصة مشاهدة الأفلام والمسلسلات', 'text', 'general', 'وصف الموقع', 'وصف مختصر للموقع', 1],
        ['site_logo', '/assets/images/logo.png', 'file', 'general', 'شعار الموقع', 'شعار الموقع الرئيسي', 1],
        ['site_favicon', '/assets/images/favicon.ico', 'file', 'general', 'أيقونة الموقع', 'أيقونة الموقع في المتصفح', 1],
        ['primary_color', '#E50914', 'color', 'appearance', 'اللون الأساسي', 'اللون الأساسي للموقع', 1],
        ['secondary_color', '#FF6B35', 'color', 'appearance', 'اللون الثانوي', 'اللون الثانوي للموقع', 1],
        ['enable_registration', '1', 'boolean', 'users', 'تفعيل التسجيل', 'السماح للمستخدمين بإنشاء حسابات جديدة', 0],
        ['require_email_verification', '0', 'boolean', 'users', 'تأكيد البريد الإلكتروني', 'طلب تأكيد البريد الإلكتروني عند التسجيل', 0],
        ['max_login_attempts', '5', 'number', 'security', 'محاولات تسجيل الدخول', 'عدد محاولات تسجيل الدخول المسموحة', 0],
        ['session_timeout', '3600', 'number', 'security', 'انتهاء الجلسة', 'مدة انتهاء الجلسة بالثواني', 0]
    ];
    
    $settingStmt = $pdo->prepare("INSERT IGNORE INTO system_settings (setting_key, setting_value, setting_type, category, label, description, is_public) VALUES (?, ?, ?, ?, ?, ?, ?)");
    
    foreach ($defaultSettings as $setting) {
        $settingStmt->execute($setting);
    }
    
    echo "<p style='color: #4CAF50;'>✅ تم إدراج الإعدادات الافتراضية</p>";
    
    // الأدوار الافتراضية
    $defaultRoles = [
        ['super_admin', 'مدير عام', 'صلاحيات كاملة للنظام', '["*"]', 1],
        ['admin', 'مدير', 'صلاحيات إدارية', '["admin.*", "users.*", "content.*"]', 1],
        ['editor', 'محرر', 'إدارة المحتوى', '["content.*", "media.*"]', 1],
        ['moderator', 'مشرف', 'إشراف على المحتوى', '["content.view", "content.edit", "users.view"]', 1],
        ['user', 'مستخدم', 'مستخدم عادي', '["profile.*", "favorites.*"]', 1]
    ];
    
    $roleStmt = $pdo->prepare("INSERT IGNORE INTO roles (name, display_name, description, permissions, is_system) VALUES (?, ?, ?, ?, ?)");
    
    foreach ($defaultRoles as $role) {
        $roleStmt->execute($role);
    }
    
    echo "<p style='color: #4CAF50;'>✅ تم إدراج الأدوار الافتراضية</p>";
    
    // القوائم الافتراضية
    $defaultMenus = [
        ['الرئيسية', 'Home', 'home', null, '🏠', '/', '_self', '', 1, 1, 1],
        ['الأفلام', 'Movies', 'movies', null, '🎬', '/movies.php', '_self', '', 2, 1, 1],
        ['المسلسلات', 'Series', 'series', null, '📺', '/series.php', '_self', '', 3, 1, 1],
        ['التصنيفات', 'Categories', 'categories', null, '📂', '#', '_self', 'dropdown', 4, 1, 1],
        ['البحث', 'Search', 'search', null, '🔍', '/search.php', '_self', '', 5, 1, 1],
        ['حسابي', 'My Account', 'account', null, '👤', '#', '_self', 'dropdown', 6, 1, 1]
    ];
    
    $menuStmt = $pdo->prepare("INSERT IGNORE INTO dynamic_menus (name, name_en, slug, parent_id, icon, url, target, css_class, position, is_active, is_visible) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
    
    foreach ($defaultMenus as $menu) {
        $menuStmt->execute($menu);
    }
    
    echo "<p style='color: #4CAF50;'>✅ تم إدراج القوائم الافتراضية</p>";
    
    // القالب الافتراضي
    $defaultTheme = [
        'default',
        'القالب الافتراضي',
        'القالب الافتراضي لمنصة شاهد',
        '1.0.0',
        'Shahid Team',
        '/assets/images/theme-preview.jpg',
        1,
        '{"layout": "modern", "sidebar": "right", "colors": {"primary": "#E50914", "secondary": "#FF6B35"}}',
        '',
        ''
    ];
    
    $themeStmt = $pdo->prepare("INSERT IGNORE INTO themes (name, display_name, description, version, author, preview_image, is_active, settings, custom_css, custom_js) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
    $themeStmt->execute($defaultTheme);
    
    echo "<p style='color: #4CAF50;'>✅ تم إدراج القالب الافتراضي</p>";
    
    // ملخص نهائي
    echo "<h2>🎉 تم الانتهاء بنجاح!</h2>";
    
    echo "<div style='background: rgba(76, 175, 80, 0.1); border: 1px solid rgba(76, 175, 80, 0.3); border-radius: 10px; padding: 2rem; margin: 2rem 0; text-align: center;'>";
    echo "<h3 style='color: #4CAF50; margin-bottom: 1rem;'>✅ النظام الديناميكي جاهز!</h3>";
    echo "<ul style='text-align: right; color: #ccc; margin-right: 2rem;'>";
    echo "<li>✅ جداول النظام الديناميكي</li>";
    echo "<li>✅ نظام الأدوار والصلاحيات</li>";
    echo "<li>✅ إعدادات النظام القابلة للتخصيص</li>";
    echo "<li>✅ القوائم الديناميكية</li>";
    echo "<li>✅ نظام الصفحات الديناميكية</li>";
    echo "<li>✅ إدارة الملفات والوسائط</li>";
    echo "<li>✅ سجل النشاطات</li>";
    echo "<li>✅ نظام القوالب</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>❌ خطأ:</h3>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

// روابط سريعة
echo "<div style='text-align: center; margin: 3rem 0;'>";
echo "<h3 style='color: #E50914; margin-bottom: 2rem;'>🔗 الخطوات التالية</h3>";

$nextSteps = [
    ['url' => 'create_admin_panel.php', 'title' => '🎛️ إنشاء لوحة الإدارة', 'color' => '#E50914'],
    ['url' => 'create_security_system.php', 'title' => '🔒 نظام الأمان', 'color' => '#4CAF50'],
    ['url' => 'create_dynamic_frontend.php', 'title' => '🎨 الواجهة الديناميكية', 'color' => '#2196F3'],
    ['url' => 'project_status.php', 'title' => '📊 حالة المشروع', 'color' => '#FF9800']
];

foreach ($nextSteps as $step) {
    echo "<a href='{$step['url']}' style='background: {$step['color']}; color: white; padding: 1rem 2rem; text-decoration: none; border-radius: 8px; font-weight: bold; margin: 0.5rem; display: inline-block; transition: transform 0.3s ease;' onmouseover='this.style.transform=\"translateY(-3px)\"' onmouseout='this.style.transform=\"translateY(0)\"'>{$step['title']}</a>";
}

echo "</div>";
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🏗️ إنشاء النظام الديناميكي - Shahid Platform</title>
    <style>
        body {
            font-family: 'Segoe UI', 'Cairo', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #2d2d2d 100%);
            color: #fff;
            min-height: 100vh;
            padding: 2rem;
            margin: 0;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(47, 47, 47, 0.95);
            border-radius: 20px;
            padding: 3rem;
            border: 2px solid rgba(229, 9, 20, 0.3);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
        }
        h1, h2, h3 { 
            color: #E50914; 
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
        }
        h1 {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 2rem;
            background: linear-gradient(45deg, #E50914, #FF6B35);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        p, li {
            line-height: 1.6;
            margin: 0.5rem 0;
        }
        ul {
            margin: 1rem 0;
        }
        .btn {
            transition: all 0.3s ease;
        }
        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
        }
        @media (max-width: 768px) {
            .container {
                padding: 1rem;
                margin: 1rem;
            }
            h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- المحتوى يتم عرضه من PHP أعلاه -->
    </div>
</body>
</html>
