/**
 * Live System Monitoring JavaScript
 * مراقبة النظام المباشرة - سكريبت جافاسكريبت
 */

class SystemMonitor {
    constructor() {
        this.updateInterval = 5000; // 5 seconds
        this.isUpdating = false;
        this.retryCount = 0;
        this.maxRetries = 3;

        this.init();
    }

    init() {
        console.log('🔍 نظام المراقبة المباشرة تم تحميله');
        this.startAutoUpdate();
        this.setupEventListeners();
    }

    setupEventListeners() {
        // Manual refresh on click
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('monitor-card')) {
                this.updateStatus();
            }
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.key === 'F5' || (e.ctrlKey && e.key === 'r')) {
                e.preventDefault();
                this.updateStatus();
            }
        });

        // Visibility change handling
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.stopAutoUpdate();
            } else {
                this.startAutoUpdate();
                this.updateStatus(); // Immediate update when tab becomes visible
            }
        });
    }

    startAutoUpdate() {
        if (this.intervalId) {
            clearInterval(this.intervalId);
        }

        this.intervalId = setInterval(() => {
            this.updateStatus();
        }, this.updateInterval);

        console.log('✅ التحديث التلقائي بدأ');
    }

    stopAutoUpdate() {
        if (this.intervalId) {
            clearInterval(this.intervalId);
            this.intervalId = null;
        }
        console.log('⏸️ التحديث التلقائي توقف');
    }

    async updateStatus() {
        if (this.isUpdating) {
            return;
        }

        this.isUpdating = true;
        this.showLoadingState();

        try {
            const response = await fetch('?ajax=1&t=' + Date.now());

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            this.updateUI(data);
            this.retryCount = 0; // Reset retry count on success

        } catch (error) {
            console.error('❌ خطأ في تحديث الحالة:', error);
            this.handleError(error);
        } finally {
            this.isUpdating = false;
            this.hideLoadingState();
        }
    }

    updateUI(data) {
        // Update API status
        this.updateCard('api', data.api, {
            successText: 'يعمل بشكل طبيعي',
            errorText: 'يحتاج إصلاح',
            details: `النقاط النشطة: ${data.api.working}/${data.api.total}`
        });

        // Update Database status
        this.updateCard('db', data.database, {
            successText: 'متصلة',
            errorText: 'غير متصلة',
            details: `الجداول: ${data.database.tables}`
        });

        // Update Tables status
        this.updateCard('tables', data.tables, {
            successText: 'مكتملة',
            errorText: 'ناقصة',
            details: `الموجود: ${data.tables.total} من ${data.tables.required}`
        });

        // Update Admin status
        this.updateCard('admin', data.admin, {
            successText: 'موجود',
            errorText: 'غير موجود',
            details: `عدد المديرين: ${data.admin.count}`
        });

        // Update timestamp
        this.updateTimestamp(data.timestamp);

        console.log('✅ تم تحديث جميع المكونات');
    }

    updateCard(type, data, texts) {
        const card = document.getElementById(type + '-card');
        const indicator = card.querySelector('.status-indicator');
        const statusText = document.getElementById(type + '-status');
        const detailsText = document.getElementById(type + '-details');

        if (!card || !indicator || !statusText) {
            console.warn(`⚠️ عنصر غير موجود: ${type}`);
            return;
        }

        // Update card classes with animation
        card.className = 'monitor-card ' + data.status;
        indicator.className = 'status-indicator ' + data.status;

        // Update text content
        statusText.textContent = data.status === 'success' ? texts.successText : texts.errorText;

        if (detailsText) {
            detailsText.innerHTML = texts.details;
        }

        // Add update animation
        card.style.transform = 'scale(1.02)';
        setTimeout(() => {
            card.style.transform = '';
        }, 200);
    }

    updateTimestamp(timestamp) {
        const timestampElement = document.getElementById('last-update');
        if (timestampElement) {
            timestampElement.textContent = timestamp;

            // Add flash effect
            timestampElement.style.color = '#E50914';
            setTimeout(() => {
                timestampElement.style.color = '';
            }, 500);
        }
    }

    showLoadingState() {
        const cards = document.querySelectorAll('.monitor-card');
        cards.forEach(card => {
            card.classList.add('updating');
        });
    }

    hideLoadingState() {
        const cards = document.querySelectorAll('.monitor-card');
        cards.forEach(card => {
            card.classList.remove('updating');
        });
    }

    handleError(error) {
        this.retryCount++;

        if (this.retryCount <= this.maxRetries) {
            console.log(`🔄 إعادة المحاولة ${this.retryCount}/${this.maxRetries}`);
            setTimeout(() => {
                this.updateStatus();
            }, 2000 * this.retryCount); // Exponential backoff
        } else {
            console.error('❌ فشل في التحديث بعد عدة محاولات');
            this.showErrorState();
        }
    }

    showErrorState() {
        const cards = document.querySelectorAll('.monitor-card');
        cards.forEach(card => {
            card.style.opacity = '0.5';
            card.style.border = '2px solid #F44336';
        });

        // Show error message
        const errorMsg = document.createElement('div');
        errorMsg.innerHTML = '⚠️ خطأ في الاتصال - سيتم إعادة المحاولة قريباً';
        errorMsg.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #F44336;
            color: white;
            padding: 1rem 2rem;
            border-radius: 10px;
            z-index: 1000;
            animation: slideIn 0.3s ease;
        `;

        document.body.appendChild(errorMsg);

        setTimeout(() => {
            errorMsg.remove();
            this.resetErrorState();
        }, 5000);
    }

    resetErrorState() {
        const cards = document.querySelectorAll('.monitor-card');
        cards.forEach(card => {
            card.style.opacity = '';
            card.style.border = '';
        });

        this.retryCount = 0;
        this.updateStatus();
    }

    // Public methods
    forceUpdate() {
        this.retryCount = 0;
        this.updateStatus();
    }

    setUpdateInterval(interval) {
        this.updateInterval = interval;
        this.startAutoUpdate();
    }

    getStatus() {
        return {
            isUpdating: this.isUpdating,
            retryCount: this.retryCount,
            updateInterval: this.updateInterval
        };
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.systemMonitor = new SystemMonitor();

    // Add CSS animations
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        .monitor-card {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .updating {
            position: relative;
            overflow: hidden;
        }

        .updating::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg,
                transparent,
                rgba(229, 9, 20, 0.2),
                transparent);
            animation: loading 1.5s infinite;
        }

        @keyframes loading {
            0% { left: -100%; }
            100% { left: 100%; }
        }
    `;
    document.head.appendChild(style);
});

// Global functions for console access
window.forceUpdate = () => window.systemMonitor?.forceUpdate();
window.setUpdateInterval = (interval) => window.systemMonitor?.setUpdateInterval(interval);
window.getMonitorStatus = () => window.systemMonitor?.getStatus();