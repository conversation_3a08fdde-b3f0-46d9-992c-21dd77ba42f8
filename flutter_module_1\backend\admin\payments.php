<?php
session_start();

// التحقق من تسجيل الدخول والصلاحيات
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit;
}

try {
    $pdo = new PDO("mysql:host=localhost;dbname=shahid_platform;charset=utf8mb4", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // التحقق من صلاحيات الإدارة
    $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ? AND (subscription_type = 'vip' OR email LIKE '%admin%')");
    $stmt->execute([$_SESSION['user_id']]);
    $admin = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$admin) {
        header('Location: ../index.php');
        exit;
    }
    
    $message = '';
    $error = '';
    
    // معالجة العمليات
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'refund_payment':
                    $payment_id = $_POST['payment_id'];
                    
                    $stmt = $pdo->prepare("UPDATE payments SET status = 'refunded' WHERE id = ?");
                    if ($stmt->execute([$payment_id])) {
                        $message = "تم استرداد المبلغ بنجاح";
                    } else {
                        $error = "فشل في استرداد المبلغ";
                    }
                    break;
                    
                case 'mark_as_failed':
                    $payment_id = $_POST['payment_id'];
                    
                    $stmt = $pdo->prepare("UPDATE payments SET status = 'failed' WHERE id = ?");
                    if ($stmt->execute([$payment_id])) {
                        $message = "تم تحديث حالة الدفع";
                    } else {
                        $error = "فشل في تحديث حالة الدفع";
                    }
                    break;
            }
        }
    }
    
    // معاملات الاستعلام
    $limit = 50;
    $offset = isset($_GET['page']) ? (max(1, intval($_GET['page'])) - 1) * $limit : 0;
    $status_filter = isset($_GET['status']) ? $_GET['status'] : '';
    $date_from = isset($_GET['date_from']) ? $_GET['date_from'] : '';
    $date_to = isset($_GET['date_to']) ? $_GET['date_to'] : '';
    
    // بناء الاستعلام
    $where_conditions = [];
    $params = [];
    
    if ($status_filter) {
        $where_conditions[] = "p.status = ?";
        $params[] = $status_filter;
    }
    
    if ($date_from) {
        $where_conditions[] = "DATE(p.created_at) >= ?";
        $params[] = $date_from;
    }
    
    if ($date_to) {
        $where_conditions[] = "DATE(p.created_at) <= ?";
        $params[] = $date_to;
    }
    
    $where_clause = $where_conditions ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
    
    // الحصول على المدفوعات
    $payments_query = "
        SELECT p.*, u.username, u.email, u.full_name, s.name as subscription_name 
        FROM payments p 
        LEFT JOIN users u ON p.user_id = u.id 
        LEFT JOIN subscriptions s ON p.subscription_id = s.id 
        $where_clause
        ORDER BY p.created_at DESC 
        LIMIT ? OFFSET ?
    ";
    
    $params[] = $limit;
    $params[] = $offset;
    
    $stmt = $pdo->prepare($payments_query);
    $stmt->execute($params);
    $payments = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // الحصول على إجمالي العدد
    $count_params = array_slice($params, 0, -2); // إزالة limit و offset
    $count_query = "
        SELECT COUNT(*) 
        FROM payments p 
        LEFT JOIN users u ON p.user_id = u.id 
        LEFT JOIN subscriptions s ON p.subscription_id = s.id 
        $where_clause
    ";
    
    $stmt = $pdo->prepare($count_query);
    $stmt->execute($count_params);
    $total_payments = $stmt->fetchColumn();
    
    // إحصائيات
    $stats = [
        'total_payments' => $pdo->query("SELECT COUNT(*) FROM payments")->fetchColumn(),
        'completed_payments' => $pdo->query("SELECT COUNT(*) FROM payments WHERE status = 'completed'")->fetchColumn(),
        'pending_payments' => $pdo->query("SELECT COUNT(*) FROM payments WHERE status = 'pending'")->fetchColumn(),
        'failed_payments' => $pdo->query("SELECT COUNT(*) FROM payments WHERE status = 'failed'")->fetchColumn(),
        'refunded_payments' => $pdo->query("SELECT COUNT(*) FROM payments WHERE status = 'refunded'")->fetchColumn(),
        'total_revenue' => $pdo->query("SELECT SUM(amount) FROM payments WHERE status = 'completed'")->fetchColumn(),
        'monthly_revenue' => $pdo->query("
            SELECT SUM(amount) FROM payments 
            WHERE status = 'completed' AND created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
        ")->fetchColumn(),
        'daily_revenue' => $pdo->query("
            SELECT SUM(amount) FROM payments 
            WHERE status = 'completed' AND DATE(created_at) = CURDATE()
        ")->fetchColumn()
    ];
    
    // إحصائيات طرق الدفع
    $payment_methods = $pdo->query("
        SELECT payment_method, COUNT(*) as count, SUM(amount) as total_amount 
        FROM payments 
        WHERE status = 'completed' 
        GROUP BY payment_method 
        ORDER BY total_amount DESC
    ")->fetchAll(PDO::FETCH_ASSOC);
    
} catch (Exception $e) {
    $error = "خطأ في قاعدة البيانات: " . $e->getMessage();
}

function formatPaymentMethod($method) {
    $methods = [
        'credit_card' => 'بطاقة ائتمان',
        'paypal' => 'PayPal',
        'apple_pay' => 'Apple Pay',
        'stc_pay' => 'STC Pay',
        'bank_transfer' => 'تحويل بنكي'
    ];
    return $methods[$method] ?? $method;
}

function getStatusColor($status) {
    $colors = [
        'completed' => '#4CAF50',
        'pending' => '#FF9800',
        'failed' => '#F44336',
        'refunded' => '#9C27B0'
    ];
    return $colors[$status] ?? '#666';
}

function getStatusText($status) {
    $texts = [
        'completed' => 'مكتمل',
        'pending' => 'معلق',
        'failed' => 'فاشل',
        'refunded' => 'مسترد'
    ];
    return $texts[$status] ?? $status;
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المدفوعات - Shahid Platform</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', 'Cairo', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #2d2d2d 100%);
            color: #fff;
            min-height: 100vh;
        }
        
        .admin-container {
            display: flex;
            min-height: 100vh;
        }
        
        .sidebar {
            width: 280px;
            background: rgba(47, 47, 47, 0.95);
            border-left: 2px solid rgba(229, 9, 20, 0.3);
            padding: 2rem 0;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
        }
        
        .sidebar-header {
            padding: 0 2rem 2rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            margin-bottom: 2rem;
        }
        
        .logo {
            color: #E50914;
            font-size: 1.8rem;
            font-weight: bold;
            text-decoration: none;
        }
        
        .sidebar-menu {
            list-style: none;
            padding: 0 1rem;
        }
        
        .sidebar-menu li {
            margin-bottom: 0.5rem;
        }
        
        .sidebar-menu a {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            color: #ccc;
            text-decoration: none;
            border-radius: 10px;
            transition: all 0.3s ease;
        }
        
        .sidebar-menu a:hover,
        .sidebar-menu a.active {
            background: rgba(229, 9, 20, 0.2);
            color: #fff;
        }
        
        .main-content {
            flex: 1;
            margin-right: 280px;
            padding: 2rem;
        }
        
        .header {
            background: rgba(47, 47, 47, 0.8);
            padding: 1.5rem 2rem;
            border-radius: 15px;
            margin-bottom: 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .header h1 {
            color: #E50914;
            font-size: 2rem;
        }
        
        .btn {
            padding: 0.75rem 1.5rem;
            background: linear-gradient(45deg, #E50914, #B8070F);
            color: white;
            border: none;
            border-radius: 8px;
            text-decoration: none;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(229, 9, 20, 0.3);
        }
        
        .btn-secondary {
            background: linear-gradient(45deg, #2196F3, #1976D2);
        }
        
        .btn-danger {
            background: linear-gradient(45deg, #F44336, #D32F2F);
        }
        
        .btn-warning {
            background: linear-gradient(45deg, #FF9800, #F57C00);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: rgba(47, 47, 47, 0.8);
            padding: 1.5rem;
            border-radius: 15px;
            border: 2px solid rgba(229, 9, 20, 0.1);
            text-align: center;
            transition: all 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
            border-color: rgba(229, 9, 20, 0.3);
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #E50914;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            color: #ccc;
        }
        
        .filters-section {
            background: rgba(47, 47, 47, 0.8);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            border: 2px solid rgba(229, 9, 20, 0.1);
        }
        
        .filters-form {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            align-items: end;
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
        }
        
        .form-group label {
            margin-bottom: 0.5rem;
            color: #E50914;
            font-weight: bold;
        }
        
        .form-group input,
        .form-group select {
            padding: 0.75rem;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.1);
            color: #fff;
            font-size: 1rem;
        }
        
        .payments-section {
            background: rgba(47, 47, 47, 0.8);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            border: 2px solid rgba(229, 9, 20, 0.1);
        }
        
        .payments-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }
        
        .payments-table th,
        .payments-table td {
            padding: 1rem;
            text-align: right;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .payments-table th {
            background: rgba(229, 9, 20, 0.2);
            color: #E50914;
            font-weight: bold;
        }
        
        .payments-table tr:hover {
            background: rgba(255, 255, 255, 0.05);
        }
        
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: bold;
            color: white;
        }
        
        .payment-methods-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-top: 2rem;
        }
        
        .method-card {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 1.5rem;
            border: 2px solid rgba(255, 255, 255, 0.1);
            text-align: center;
        }
        
        .method-icon {
            font-size: 2rem;
            color: #E50914;
            margin-bottom: 1rem;
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            gap: 0.5rem;
            margin-top: 2rem;
        }
        
        .pagination a,
        .pagination span {
            padding: 0.75rem 1rem;
            background: rgba(47, 47, 47, 0.8);
            color: #fff;
            text-decoration: none;
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .pagination a:hover {
            background: rgba(229, 9, 20, 0.2);
        }
        
        .pagination .current {
            background: #E50914;
        }
        
        .alert {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 2rem;
        }
        
        .alert-success {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid rgba(76, 175, 80, 0.5);
            color: #4CAF50;
        }
        
        .alert-error {
            background: rgba(244, 67, 54, 0.2);
            border: 1px solid rgba(244, 67, 54, 0.5);
            color: #F44336;
        }
        
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }
            
            .main-content {
                margin-right: 0;
                padding: 1rem;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .filters-form {
                grid-template-columns: 1fr;
            }
            
            .payments-table {
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <aside class="sidebar">
            <div class="sidebar-header">
                <a href="../index.php" class="logo">
                    <i class="fas fa-play-circle"></i> شاهد
                </a>
            </div>

            <ul class="sidebar-menu">
                <li><a href="dashboard.php"><i class="fas fa-tachometer-alt"></i> لوحة التحكم</a></li>
                <li><a href="content_manager.php"><i class="fas fa-film"></i> مدير المحتوى</a></li>
                <li><a href="upload_center.php"><i class="fas fa-upload"></i> مركز الرفع</a></li>
                <li><a href="users.php"><i class="fas fa-users"></i> إدارة المستخدمين</a></li>
                <li><a href="subscription_manager.php"><i class="fas fa-crown"></i> مدير الاشتراكات</a></li>
                <li><a href="payments.php" class="active"><i class="fas fa-credit-card"></i> المدفوعات</a></li>
                <li><a href="settings.php"><i class="fas fa-cog"></i> الإعدادات</a></li>
                <li><a href="../logout.php"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a></li>
            </ul>
        </aside>

        <main class="main-content">
            <div class="header">
                <h1><i class="fas fa-credit-card"></i> إدارة المدفوعات</h1>
                <div>
                    <a href="reports.php" class="btn btn-secondary">
                        <i class="fas fa-chart-bar"></i> التقارير
                    </a>
                </div>
            </div>

            <?php if ($message): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i> <?php echo $message; ?>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-circle"></i> <?php echo $error; ?>
                </div>
            <?php endif; ?>

            <!-- إحصائيات -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number"><?php echo number_format($stats['total_payments']); ?></div>
                    <div class="stat-label">إجمالي المعاملات</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo number_format($stats['completed_payments']); ?></div>
                    <div class="stat-label">معاملات مكتملة</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo number_format($stats['pending_payments']); ?></div>
                    <div class="stat-label">معاملات معلقة</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo number_format($stats['total_revenue'], 2); ?> ر.س</div>
                    <div class="stat-label">إجمالي الإيرادات</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo number_format($stats['monthly_revenue'], 2); ?> ر.س</div>
                    <div class="stat-label">إيرادات الشهر</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo number_format($stats['daily_revenue'], 2); ?> ر.س</div>
                    <div class="stat-label">إيرادات اليوم</div>
                </div>
            </div>

            <!-- فلاتر البحث -->
            <div class="filters-section">
                <h3 style="color: #E50914; margin-bottom: 1rem;">
                    <i class="fas fa-filter"></i> فلاتر البحث
                </h3>

                <form method="GET" class="filters-form">
                    <div class="form-group">
                        <label>الحالة</label>
                        <select name="status">
                            <option value="">جميع الحالات</option>
                            <option value="completed" <?php echo $status_filter === 'completed' ? 'selected' : ''; ?>>مكتمل</option>
                            <option value="pending" <?php echo $status_filter === 'pending' ? 'selected' : ''; ?>>معلق</option>
                            <option value="failed" <?php echo $status_filter === 'failed' ? 'selected' : ''; ?>>فاشل</option>
                            <option value="refunded" <?php echo $status_filter === 'refunded' ? 'selected' : ''; ?>>مسترد</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label>من تاريخ</label>
                        <input type="date" name="date_from" value="<?php echo htmlspecialchars($date_from); ?>">
                    </div>

                    <div class="form-group">
                        <label>إلى تاريخ</label>
                        <input type="date" name="date_to" value="<?php echo htmlspecialchars($date_to); ?>">
                    </div>

                    <div class="form-group">
                        <button type="submit" class="btn">
                            <i class="fas fa-search"></i> بحث
                        </button>
                    </div>
                </form>
            </div>

            <!-- جدول المدفوعات -->
            <div class="payments-section">
                <h3 style="color: #E50914; margin-bottom: 1rem;">
                    <i class="fas fa-list"></i> المعاملات المالية
                </h3>

                <table class="payments-table">
                    <thead>
                        <tr>
                            <th>رقم المعاملة</th>
                            <th>المستخدم</th>
                            <th>الخطة</th>
                            <th>المبلغ</th>
                            <th>طريقة الدفع</th>
                            <th>الحالة</th>
                            <th>التاريخ</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($payments as $payment): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($payment['transaction_id']); ?></td>
                                <td>
                                    <div>
                                        <strong><?php echo htmlspecialchars($payment['full_name'] ?? $payment['username']); ?></strong><br>
                                        <small style="color: #ccc;"><?php echo htmlspecialchars($payment['email']); ?></small>
                                    </div>
                                </td>
                                <td><?php echo htmlspecialchars($payment['subscription_name']); ?></td>
                                <td><?php echo number_format($payment['amount'], 2); ?> ر.س</td>
                                <td><?php echo formatPaymentMethod($payment['payment_method']); ?></td>
                                <td>
                                    <span class="status-badge" style="background-color: <?php echo getStatusColor($payment['status']); ?>">
                                        <?php echo getStatusText($payment['status']); ?>
                                    </span>
                                </td>
                                <td><?php echo date('Y-m-d H:i', strtotime($payment['created_at'])); ?></td>
                                <td>
                                    <?php if ($payment['status'] === 'completed'): ?>
                                        <form method="POST" style="display: inline;" onsubmit="return confirm('هل أنت متأكد من استرداد هذا المبلغ؟')">
                                            <input type="hidden" name="action" value="refund_payment">
                                            <input type="hidden" name="payment_id" value="<?php echo $payment['id']; ?>">
                                            <button type="submit" class="btn btn-warning" style="padding: 0.5rem;">
                                                <i class="fas fa-undo"></i> استرداد
                                            </button>
                                        </form>
                                    <?php elseif ($payment['status'] === 'pending'): ?>
                                        <form method="POST" style="display: inline;" onsubmit="return confirm('هل أنت متأكد من تحديد هذه المعاملة كفاشلة؟')">
                                            <input type="hidden" name="action" value="mark_as_failed">
                                            <input type="hidden" name="payment_id" value="<?php echo $payment['id']; ?>">
                                            <button type="submit" class="btn btn-danger" style="padding: 0.5rem;">
                                                <i class="fas fa-times"></i> فشل
                                            </button>
                                        </form>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>

                <!-- الصفحات -->
                <?php if ($total_payments > $limit): ?>
                    <div class="pagination">
                        <?php
                        $current_page = floor($offset / $limit) + 1;
                        $total_pages = ceil($total_payments / $limit);

                        // بناء رابط الصفحة
                        $base_url = '?';
                        if ($status_filter) $base_url .= 'status=' . urlencode($status_filter) . '&';
                        if ($date_from) $base_url .= 'date_from=' . urlencode($date_from) . '&';
                        if ($date_to) $base_url .= 'date_to=' . urlencode($date_to) . '&';

                        // الصفحة السابقة
                        if ($current_page > 1): ?>
                            <a href="<?php echo $base_url; ?>page=<?php echo $current_page - 1; ?>">السابق</a>
                        <?php endif; ?>

                        <!-- أرقام الصفحات -->
                        <?php for ($i = max(1, $current_page - 2); $i <= min($total_pages, $current_page + 2); $i++): ?>
                            <?php if ($i == $current_page): ?>
                                <span class="current"><?php echo $i; ?></span>
                            <?php else: ?>
                                <a href="<?php echo $base_url; ?>page=<?php echo $i; ?>"><?php echo $i; ?></a>
                            <?php endif; ?>
                        <?php endfor; ?>

                        <!-- الصفحة التالية -->
                        <?php if ($current_page < $total_pages): ?>
                            <a href="<?php echo $base_url; ?>page=<?php echo $current_page + 1; ?>">التالي</a>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
            </div>

            <!-- إحصائيات طرق الدفع -->
            <div class="payments-section">
                <h3 style="color: #E50914; margin-bottom: 1rem;">
                    <i class="fas fa-chart-pie"></i> إحصائيات طرق الدفع
                </h3>

                <div class="payment-methods-grid">
                    <?php foreach ($payment_methods as $method): ?>
                        <div class="method-card">
                            <div class="method-icon">
                                <?php
                                $icons = [
                                    'credit_card' => 'fas fa-credit-card',
                                    'paypal' => 'fab fa-paypal',
                                    'apple_pay' => 'fab fa-apple-pay',
                                    'stc_pay' => 'fas fa-mobile-alt'
                                ];
                                $icon = $icons[$method['payment_method']] ?? 'fas fa-money-bill';
                                ?>
                                <i class="<?php echo $icon; ?>"></i>
                            </div>
                            <h4 style="color: #E50914; margin-bottom: 0.5rem;">
                                <?php echo formatPaymentMethod($method['payment_method']); ?>
                            </h4>
                            <div style="margin-bottom: 0.5rem;">
                                <strong><?php echo number_format($method['count']); ?></strong> معاملة
                            </div>
                            <div style="color: #4CAF50; font-weight: bold;">
                                <?php echo number_format($method['total_amount'], 2); ?> ر.س
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </main>
    </div>

    <script>
        // تحديث الصفحة كل 30 ثانية للمعاملات المعلقة
        setInterval(function() {
            if (window.location.search.includes('status=pending')) {
                window.location.reload();
            }
        }, 30000);

        console.log('إدارة المدفوعات جاهزة!');
    </script>
</body>
</html>
