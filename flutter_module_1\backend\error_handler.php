<?php
/**
 * نظام إدارة الأخطاء المتقدم
 * يتضمن تسجيل الأخطاء، إرسال تقارير، ومعالجة الاستثناءات
 */

class ErrorHandler {
    private static $instance = null;
    private $logPath;
    private $emailNotifications;
    private $debugMode;
    
    private function __construct() {
        $this->logPath = __DIR__ . '/logs/';
        $this->emailNotifications = true;
        $this->debugMode = true; // تغيير إلى false في الإنتاج
        
        // إنشاء مجلد السجلات إذا لم يكن موجوداً
        if (!is_dir($this->logPath)) {
            mkdir($this->logPath, 0755, true);
        }
        
        // تسجيل معالجات الأخطاء
        set_error_handler([$this, 'handleError']);
        set_exception_handler([$this, 'handleException']);
        register_shutdown_function([$this, 'handleFatalError']);
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * معالجة الأخطاء العادية
     */
    public function handleError($severity, $message, $file, $line) {
        if (!(error_reporting() & $severity)) {
            return false;
        }
        
        $errorData = [
            'type' => 'ERROR',
            'severity' => $this->getSeverityName($severity),
            'message' => $message,
            'file' => $file,
            'line' => $line,
            'timestamp' => date('Y-m-d H:i:s'),
            'url' => $_SERVER['REQUEST_URI'] ?? 'CLI',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown',
            'ip' => $this->getRealIP(),
            'trace' => debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS)
        ];
        
        $this->logError($errorData);
        
        if ($this->shouldNotify($severity)) {
            $this->sendNotification($errorData);
        }
        
        if ($this->debugMode) {
            $this->displayError($errorData);
        }
        
        return true;
    }
    
    /**
     * معالجة الاستثناءات
     */
    public function handleException($exception) {
        $errorData = [
            'type' => 'EXCEPTION',
            'class' => get_class($exception),
            'message' => $exception->getMessage(),
            'file' => $exception->getFile(),
            'line' => $exception->getLine(),
            'timestamp' => date('Y-m-d H:i:s'),
            'url' => $_SERVER['REQUEST_URI'] ?? 'CLI',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown',
            'ip' => $this->getRealIP(),
            'trace' => $exception->getTrace()
        ];
        
        $this->logError($errorData);
        $this->sendNotification($errorData);
        
        if ($this->debugMode) {
            $this->displayError($errorData);
        } else {
            $this->displayGenericError();
        }
    }
    
    /**
     * معالجة الأخطاء الفادحة
     */
    public function handleFatalError() {
        $error = error_get_last();
        
        if ($error && in_array($error['type'], [E_ERROR, E_PARSE, E_CORE_ERROR, E_COMPILE_ERROR])) {
            $errorData = [
                'type' => 'FATAL_ERROR',
                'severity' => $this->getSeverityName($error['type']),
                'message' => $error['message'],
                'file' => $error['file'],
                'line' => $error['line'],
                'timestamp' => date('Y-m-d H:i:s'),
                'url' => $_SERVER['REQUEST_URI'] ?? 'CLI',
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown',
                'ip' => $this->getRealIP()
            ];
            
            $this->logError($errorData);
            $this->sendNotification($errorData);
            
            if (!$this->debugMode) {
                $this->displayGenericError();
            }
        }
    }
    
    /**
     * تسجيل الخطأ في ملف السجل
     */
    private function logError($errorData) {
        $logFile = $this->logPath . 'error_' . date('Y-m-d') . '.log';
        $logEntry = json_encode($errorData, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "\n";
        
        file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
        
        // تنظيف السجلات القديمة (أكثر من 30 يوم)
        $this->cleanOldLogs();
    }
    
    /**
     * إرسال إشعار بالخطأ
     */
    private function sendNotification($errorData) {
        if (!$this->emailNotifications) {
            return;
        }
        
        $subject = "خطأ في منصة Shahid - {$errorData['type']}";
        $message = $this->formatErrorEmail($errorData);
        
        // في التطبيق الحقيقي، استخدم مكتبة إرسال البريد الإلكتروني
        error_log("Error Notification: " . $subject);
    }
    
    /**
     * عرض الخطأ للمطور
     */
    private function displayError($errorData) {
        if (php_sapi_name() === 'cli') {
            echo "\n=== خطأ في النظام ===\n";
            echo "النوع: {$errorData['type']}\n";
            echo "الرسالة: {$errorData['message']}\n";
            echo "الملف: {$errorData['file']}:{$errorData['line']}\n";
            echo "الوقت: {$errorData['timestamp']}\n\n";
            return;
        }
        
        echo "<!DOCTYPE html>
        <html lang='ar' dir='rtl'>
        <head>
            <meta charset='UTF-8'>
            <title>خطأ في النظام</title>
            <style>
                body { font-family: Arial, sans-serif; background: #f5f5f5; margin: 0; padding: 20px; }
                .error-container { 
                    max-width: 800px; margin: 0 auto; background: white;
                    border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                    overflow: hidden;
                }
                .error-header { 
                    background: #E50914; color: white; padding: 20px;
                    text-align: center;
                }
                .error-content { padding: 20px; }
                .error-details { 
                    background: #f8f9fa; border-radius: 4px;
                    padding: 15px; margin: 15px 0; font-family: monospace;
                }
                .trace { max-height: 300px; overflow-y: auto; }
                .btn { 
                    background: #E50914; color: white; padding: 10px 20px;
                    border: none; border-radius: 4px; cursor: pointer;
                    text-decoration: none; display: inline-block;
                }
            </style>
        </head>
        <body>
            <div class='error-container'>
                <div class='error-header'>
                    <h1>⚠️ خطأ في النظام</h1>
                    <p>حدث خطأ أثناء تنفيذ العملية</p>
                </div>
                <div class='error-content'>
                    <div class='error-details'>
                        <strong>النوع:</strong> {$errorData['type']}<br>
                        <strong>الرسالة:</strong> {$errorData['message']}<br>
                        <strong>الملف:</strong> {$errorData['file']}<br>
                        <strong>السطر:</strong> {$errorData['line']}<br>
                        <strong>الوقت:</strong> {$errorData['timestamp']}
                    </div>";
        
        if (isset($errorData['trace']) && !empty($errorData['trace'])) {
            echo "<details>
                    <summary>تفاصيل التتبع</summary>
                    <div class='trace'>";
            foreach ($errorData['trace'] as $i => $trace) {
                $file = $trace['file'] ?? 'Unknown';
                $line = $trace['line'] ?? 'Unknown';
                $function = $trace['function'] ?? 'Unknown';
                $class = isset($trace['class']) ? $trace['class'] . '::' : '';
                
                echo "<div>#{$i} {$file}({$line}): {$class}{$function}()</div>";
            }
            echo "</div></details>";
        }
        
        echo "      <div style='text-align: center; margin-top: 20px;'>
                        <a href='javascript:history.back()' class='btn'>العودة</a>
                        <a href='/' class='btn' style='margin-right: 10px;'>الصفحة الرئيسية</a>
                    </div>
                </div>
            </div>
        </body>
        </html>";
    }
    
    /**
     * عرض رسالة خطأ عامة للمستخدمين
     */
    private function displayGenericError() {
        if (php_sapi_name() === 'cli') {
            echo "حدث خطأ في النظام. يرجى المحاولة لاحقاً.\n";
            return;
        }
        
        http_response_code(500);
        echo "<!DOCTYPE html>
        <html lang='ar' dir='rtl'>
        <head>
            <meta charset='UTF-8'>
            <title>خطأ في الخادم</title>
            <style>
                body { 
                    font-family: Arial, sans-serif; background: linear-gradient(135deg, #141414, #2F2F2F);
                    color: white; margin: 0; padding: 0; min-height: 100vh;
                    display: flex; align-items: center; justify-content: center;
                }
                .error-container { 
                    text-align: center; max-width: 500px; padding: 40px;
                    background: rgba(47, 47, 47, 0.9); border-radius: 15px;
                    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
                }
                .error-icon { font-size: 4em; margin-bottom: 20px; }
                h1 { color: #E50914; margin-bottom: 20px; }
                .btn { 
                    background: linear-gradient(45deg, #E50914, #B8070F);
                    color: white; padding: 12px 25px; border: none;
                    border-radius: 8px; cursor: pointer; text-decoration: none;
                    display: inline-block; margin: 10px;
                }
            </style>
        </head>
        <body>
            <div class='error-container'>
                <div class='error-icon'>🚫</div>
                <h1>عذراً، حدث خطأ</h1>
                <p>نعتذر عن هذا الخطأ. فريقنا التقني يعمل على حل المشكلة.</p>
                <p>يرجى المحاولة مرة أخرى خلال بضع دقائق.</p>
                <div>
                    <a href='javascript:history.back()' class='btn'>العودة</a>
                    <a href='/' class='btn'>الصفحة الرئيسية</a>
                </div>
            </div>
        </body>
        </html>";
    }
    
    /**
     * تنسيق رسالة البريد الإلكتروني للخطأ
     */
    private function formatErrorEmail($errorData) {
        $message = "تم اكتشاف خطأ في منصة Shahid:\n\n";
        $message .= "النوع: {$errorData['type']}\n";
        $message .= "الرسالة: {$errorData['message']}\n";
        $message .= "الملف: {$errorData['file']}\n";
        $message .= "السطر: {$errorData['line']}\n";
        $message .= "الوقت: {$errorData['timestamp']}\n";
        $message .= "الرابط: {$errorData['url']}\n";
        $message .= "عنوان IP: {$errorData['ip']}\n";
        $message .= "متصفح المستخدم: {$errorData['user_agent']}\n\n";
        
        if (isset($errorData['trace'])) {
            $message .= "تفاصيل التتبع:\n";
            foreach ($errorData['trace'] as $i => $trace) {
                $file = $trace['file'] ?? 'Unknown';
                $line = $trace['line'] ?? 'Unknown';
                $function = $trace['function'] ?? 'Unknown';
                $message .= "#{$i} {$file}({$line}): {$function}()\n";
            }
        }
        
        return $message;
    }
    
    /**
     * الحصول على اسم مستوى الخطأ
     */
    private function getSeverityName($severity) {
        $severities = [
            E_ERROR => 'ERROR',
            E_WARNING => 'WARNING',
            E_PARSE => 'PARSE',
            E_NOTICE => 'NOTICE',
            E_CORE_ERROR => 'CORE_ERROR',
            E_CORE_WARNING => 'CORE_WARNING',
            E_COMPILE_ERROR => 'COMPILE_ERROR',
            E_COMPILE_WARNING => 'COMPILE_WARNING',
            E_USER_ERROR => 'USER_ERROR',
            E_USER_WARNING => 'USER_WARNING',
            E_USER_NOTICE => 'USER_NOTICE',
            E_STRICT => 'STRICT',
            E_RECOVERABLE_ERROR => 'RECOVERABLE_ERROR',
            E_DEPRECATED => 'DEPRECATED',
            E_USER_DEPRECATED => 'USER_DEPRECATED'
        ];
        
        return $severities[$severity] ?? 'UNKNOWN';
    }
    
    /**
     * تحديد ما إذا كان يجب إرسال إشعار
     */
    private function shouldNotify($severity) {
        $notifyLevels = [E_ERROR, E_CORE_ERROR, E_COMPILE_ERROR, E_USER_ERROR, E_RECOVERABLE_ERROR];
        return in_array($severity, $notifyLevels);
    }
    
    /**
     * الحصول على عنوان IP الحقيقي
     */
    private function getRealIP() {
        $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ips = explode(',', $_SERVER[$key]);
                $ip = trim($ips[0]);
                
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    }
    
    /**
     * تنظيف السجلات القديمة
     */
    private function cleanOldLogs() {
        $files = glob($this->logPath . 'error_*.log');
        $cutoff = time() - (30 * 24 * 60 * 60); // 30 يوم
        
        foreach ($files as $file) {
            if (filemtime($file) < $cutoff) {
                unlink($file);
            }
        }
    }
    
    /**
     * تسجيل خطأ مخصص
     */
    public function logCustomError($message, $context = []) {
        $errorData = [
            'type' => 'CUSTOM',
            'message' => $message,
            'context' => $context,
            'timestamp' => date('Y-m-d H:i:s'),
            'url' => $_SERVER['REQUEST_URI'] ?? 'CLI',
            'ip' => $this->getRealIP(),
            'trace' => debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 5)
        ];
        
        $this->logError($errorData);
    }
    
    /**
     * الحصول على إحصائيات الأخطاء
     */
    public function getErrorStats($days = 7) {
        $stats = [
            'total_errors' => 0,
            'error_types' => [],
            'daily_counts' => [],
            'top_files' => []
        ];
        
        for ($i = 0; $i < $days; $i++) {
            $date = date('Y-m-d', strtotime("-{$i} days"));
            $logFile = $this->logPath . "error_{$date}.log";
            
            if (file_exists($logFile)) {
                $content = file_get_contents($logFile);
                $errors = array_filter(explode("\n", $content));
                
                $dailyCount = 0;
                foreach ($errors as $error) {
                    if (trim($error)) {
                        $errorData = json_decode($error, true);
                        if ($errorData) {
                            $stats['total_errors']++;
                            $dailyCount++;
                            
                            $type = $errorData['type'] ?? 'UNKNOWN';
                            $stats['error_types'][$type] = ($stats['error_types'][$type] ?? 0) + 1;
                            
                            $file = basename($errorData['file'] ?? 'unknown');
                            $stats['top_files'][$file] = ($stats['top_files'][$file] ?? 0) + 1;
                        }
                    }
                }
                
                $stats['daily_counts'][$date] = $dailyCount;
            } else {
                $stats['daily_counts'][$date] = 0;
            }
        }
        
        // ترتيب النتائج
        arsort($stats['error_types']);
        arsort($stats['top_files']);
        
        return $stats;
    }
}

// تهيئة معالج الأخطاء
ErrorHandler::getInstance();

// دالة مساعدة لتسجيل الأخطاء المخصصة
function log_custom_error($message, $context = []) {
    ErrorHandler::getInstance()->logCustomError($message, $context);
}
?>
