-- ===================================================================
-- Shahid Platform - Production Database with Real Data
-- Complete Production-Ready Database Setup
-- ===================================================================

-- إدراج بيانات حقيقية للأفلام
INSERT INTO movies (title, title_ar, description, description_ar, year, duration, rating, genre, poster, trailer, video_url, status, created_at) VALUES

-- أفلام عربية مشهورة
('The Blue Elephant', 'الفيل الأزرق', 'A psychological thriller about a psychiatrist who returns to work after a breakdown.', 'فيلم نفسي مثير عن طبيب نفسي يعود للعمل بعد انهيار عصبي.', 2014, 170, 8.2, 'Drama,Thriller', 'https://image.tmdb.org/t/p/w500/blue_elephant.jpg', 'https://www.youtube.com/watch?v=xyz', 'videos/blue_elephant.mp4', 'active', NOW()),

('Clash', 'اشتباك', 'Set entirely in a police van during the 2013 Egyptian protests.', 'يدور بالكامل داخل عربة شرطة أثناء احتجاجات 2013 المصرية.', 2016, 97, 7.4, 'Drama,Thriller', 'https://image.tmdb.org/t/p/w500/clash.jpg', 'https://www.youtube.com/watch?v=abc', 'videos/clash.mp4', 'active', NOW()),

('Sheikh Jackson', 'الشيخ جاكسون', 'An imam struggles with his faith after Michael Jackson dies.', 'إمام يصارع إيمانه بعد وفاة مايكل جاكسون.', 2017, 93, 7.1, 'Drama', 'https://image.tmdb.org/t/p/w500/sheikh_jackson.jpg', 'https://www.youtube.com/watch?v=def', 'videos/sheikh_jackson.mp4', 'active', NOW()),

('Yomeddine', 'يوم الدين', 'A leper and his young apprentice travel through Egypt.', 'أبرص وتلميذه الصغير يسافران عبر مصر.', 2018, 97, 7.3, 'Drama,Adventure', 'https://image.tmdb.org/t/p/w500/yomeddine.jpg', 'https://www.youtube.com/watch?v=ghi', 'videos/yomeddine.mp4', 'active', NOW()),

('The Guest', 'الضيف', 'A man seeks revenge for his brother death.', 'رجل يسعى للانتقام لموت أخيه.', 2019, 120, 7.8, 'Action,Drama', 'https://image.tmdb.org/t/p/w500/guest.jpg', 'https://www.youtube.com/watch?v=jkl', 'videos/guest.mp4', 'active', NOW()),

-- أفلام عالمية مشهورة
('The Shawshank Redemption', 'الخلاص من شاوشانك', 'Two imprisoned men bond over years, finding solace and redemption.', 'رجلان مسجونان يتآلفان على مر السنين، ويجدان العزاء والخلاص.', 1994, 142, 9.3, 'Drama', 'https://image.tmdb.org/t/p/w500/shawshank.jpg', 'https://www.youtube.com/watch?v=6hB3S9bIaco', 'videos/shawshank.mp4', 'active', NOW()),

('The Godfather', 'العراب', 'The aging patriarch transfers control to his reluctant son.', 'البطريرك المسن ينقل السيطرة إلى ابنه المتردد.', 1972, 175, 9.2, 'Crime,Drama', 'https://image.tmdb.org/t/p/w500/godfather.jpg', 'https://www.youtube.com/watch?v=sY1S34973zA', 'videos/godfather.mp4', 'active', NOW()),

('The Dark Knight', 'فارس الظلام', 'Batman faces the Joker in Gotham City.', 'باتمان يواجه الجوكر في مدينة جوثام.', 2008, 152, 9.0, 'Action,Crime,Drama', 'https://image.tmdb.org/t/p/w500/dark_knight.jpg', 'https://www.youtube.com/watch?v=EXeTwQWrcwY', 'videos/dark_knight.mp4', 'active', NOW()),

('Pulp Fiction', 'لب الخيال', 'The lives of two mob hitmen intertwine.', 'حياة قاتلين من المافيا تتشابك.', 1994, 154, 8.9, 'Crime,Drama', 'https://image.tmdb.org/t/p/w500/pulp_fiction.jpg', 'https://www.youtube.com/watch?v=s7EdQ4FqbhY', 'videos/pulp_fiction.mp4', 'active', NOW()),

('Forrest Gump', 'فورست غامب', 'A simple man witnesses historical events.', 'رجل بسيط يشهد أحداثاً تاريخية.', 1994, 142, 8.8, 'Drama,Romance', 'https://image.tmdb.org/t/p/w500/forrest_gump.jpg', 'https://www.youtube.com/watch?v=bLvqoHBptjg', 'videos/forrest_gump.mp4', 'active', NOW()),

('Inception', 'البداية', 'A thief enters dreams to steal secrets.', 'لص يدخل الأحلام لسرقة الأسرار.', 2010, 148, 8.8, 'Action,Sci-Fi,Thriller', 'https://image.tmdb.org/t/p/w500/inception.jpg', 'https://www.youtube.com/watch?v=YoHD9XEInc0', 'videos/inception.mp4', 'active', NOW()),

('The Matrix', 'المصفوفة', 'A programmer discovers reality is a simulation.', 'مبرمج يكتشف أن الواقع محاكاة.', 1999, 136, 8.7, 'Action,Sci-Fi', 'https://image.tmdb.org/t/p/w500/matrix.jpg', 'https://www.youtube.com/watch?v=vKQi3bBA1y8', 'videos/matrix.mp4', 'active', NOW()),

('Goodfellas', 'الرفاق الطيبون', 'The story of Henry Hill and his life in the mob.', 'قصة هنري هيل وحياته في المافيا.', 1990, 146, 8.7, 'Biography,Crime,Drama', 'https://image.tmdb.org/t/p/w500/goodfellas.jpg', 'https://www.youtube.com/watch?v=qo5jJpHtI1Y', 'videos/goodfellas.mp4', 'active', NOW()),

('The Lord of the Rings', 'سيد الخواتم', 'A hobbit sets out to destroy the One Ring.', 'هوبيت ينطلق لتدمير الخاتم الواحد.', 2001, 178, 8.8, 'Adventure,Drama,Fantasy', 'https://image.tmdb.org/t/p/w500/lotr.jpg', 'https://www.youtube.com/watch?v=V75dMMIW2B4', 'videos/lotr.mp4', 'active', NOW()),

('Star Wars', 'حرب النجوم', 'A young farm boy joins the Rebellion.', 'فتى مزرعة شاب ينضم إلى التمرد.', 1977, 121, 8.6, 'Adventure,Fantasy,Sci-Fi', 'https://image.tmdb.org/t/p/w500/star_wars.jpg', 'https://www.youtube.com/watch?v=1g3_CFmnU7k', 'videos/star_wars.mp4', 'active', NOW()),

-- أفلام حديثة
('Avengers: Endgame', 'المنتقمون: نهاية اللعبة', 'The Avengers assemble for the final battle.', 'المنتقمون يجتمعون للمعركة الأخيرة.', 2019, 181, 8.4, 'Action,Adventure,Drama', 'https://image.tmdb.org/t/p/w500/avengers_endgame.jpg', 'https://www.youtube.com/watch?v=TcMBFSGVi1c', 'videos/avengers_endgame.mp4', 'active', NOW()),

('Joker', 'الجوكر', 'A failed comedian becomes the Joker.', 'كوميدي فاشل يصبح الجوكر.', 2019, 122, 8.4, 'Crime,Drama,Thriller', 'https://image.tmdb.org/t/p/w500/joker.jpg', 'https://www.youtube.com/watch?v=zAGVQLHvwOY', 'videos/joker.mp4', 'active', NOW()),

('Parasite', 'الطفيلي', 'A poor family infiltrates a wealthy household.', 'عائلة فقيرة تتسلل إلى منزل ثري.', 2019, 132, 8.6, 'Comedy,Drama,Thriller', 'https://image.tmdb.org/t/p/w500/parasite.jpg', 'https://www.youtube.com/watch?v=5xH0HfJHsaY', 'videos/parasite.mp4', 'active', NOW()),

('1917', '1917', 'Two soldiers must deliver a crucial message.', 'جنديان يجب أن يوصلا رسالة حاسمة.', 2019, 119, 8.3, 'Drama,War', 'https://image.tmdb.org/t/p/w500/1917.jpg', 'https://www.youtube.com/watch?v=YqNYrYUiMfg', 'videos/1917.mp4', 'active', NOW()),

('Once Upon a Time in Hollywood', 'ذات مرة في هوليوود', 'A fading actor and his stunt double navigate 1969 Hollywood.', 'ممثل يتلاشى ومضاعفه يتنقلان في هوليوود 1969.', 2019, 161, 7.6, 'Comedy,Drama', 'https://image.tmdb.org/t/p/w500/once_upon_hollywood.jpg', 'https://www.youtube.com/watch?v=ELeMaP8EPAA', 'videos/once_upon_hollywood.mp4', 'active', NOW());

-- إدراج بيانات حقيقية للمسلسلات
INSERT INTO series (title, title_ar, description, description_ar, year, seasons, episodes, rating, genre, poster, trailer, status, created_at) VALUES

-- مسلسلات عربية مشهورة
('Al Kabeer Awy', 'الكبير أوي', 'Comedy series about a man who thinks he is important.', 'مسلسل كوميدي عن رجل يظن نفسه مهماً.', 2010, 6, 180, 8.5, 'Comedy', 'https://image.tmdb.org/t/p/w500/kabeer_awy.jpg', 'https://www.youtube.com/watch?v=xyz', 'completed', NOW()),

('Grand Hotel', 'الجراند', 'Drama series set in a luxury hotel.', 'مسلسل درامي يدور في فندق فاخر.', 2016, 4, 120, 8.2, 'Drama,Romance', 'https://image.tmdb.org/t/p/w500/grand_hotel.jpg', 'https://www.youtube.com/watch?v=abc', 'completed', NOW()),

('Layali Eugenie', 'ليالي أوجيني', 'Historical drama set in 1960s Egypt.', 'دراما تاريخية تدور في مصر الستينات.', 2018, 1, 30, 7.8, 'Drama,History', 'https://image.tmdb.org/t/p/w500/layali_eugenie.jpg', 'https://www.youtube.com/watch?v=def', 'completed', NOW()),

('The Choice', 'الاختيار', 'Military drama about Egyptian special forces.', 'دراما عسكرية عن القوات الخاصة المصرية.', 2020, 2, 60, 9.1, 'Action,Drama,War', 'https://image.tmdb.org/t/p/w500/choice.jpg', 'https://www.youtube.com/watch?v=ghi', 'ongoing', NOW()),

('Nemra Etneen', 'نمرة اتنين', 'Comedy about two friends and their adventures.', 'كوميديا عن صديقين ومغامراتهما.', 2021, 2, 60, 7.5, 'Comedy', 'https://image.tmdb.org/t/p/w500/nemra_etneen.jpg', 'https://www.youtube.com/watch?v=jkl', 'ongoing', NOW()),

-- مسلسلات عالمية مشهورة
('Breaking Bad', 'بريكنغ باد', 'A chemistry teacher turns to cooking meth.', 'مدرس كيمياء يتحول لطبخ المخدرات.', 2008, 5, 62, 9.5, 'Crime,Drama,Thriller', 'https://image.tmdb.org/t/p/w500/breaking_bad.jpg', 'https://www.youtube.com/watch?v=HhesaQXLuRY', 'completed', NOW()),

('Game of Thrones', 'صراع العروش', 'Noble families fight for the Iron Throne.', 'عائلات نبيلة تقاتل من أجل العرش الحديدي.', 2011, 8, 73, 9.3, 'Action,Adventure,Drama', 'https://image.tmdb.org/t/p/w500/game_of_thrones.jpg', 'https://www.youtube.com/watch?v=rlR4PJn8b8I', 'completed', NOW()),

('Stranger Things', 'أشياء غريبة', 'Kids in a small town face supernatural forces.', 'أطفال في بلدة صغيرة يواجهون قوى خارقة.', 2016, 4, 42, 8.7, 'Drama,Fantasy,Horror', 'https://image.tmdb.org/t/p/w500/stranger_things.jpg', 'https://www.youtube.com/watch?v=b9EkMc79ZSU', 'ongoing', NOW()),

('The Crown', 'التاج', 'The reign of Queen Elizabeth II.', 'عهد الملكة إليزابيث الثانية.', 2016, 6, 60, 8.7, 'Biography,Drama,History', 'https://image.tmdb.org/t/p/w500/crown.jpg', 'https://www.youtube.com/watch?v=JWtnJjn6ng0', 'ongoing', NOW()),

('Money Heist', 'بيت المال', 'A criminal mastermind plans the perfect heist.', 'عقل إجرامي يخطط للسرقة المثالية.', 2017, 5, 52, 8.3, 'Action,Crime,Mystery', 'https://image.tmdb.org/t/p/w500/money_heist.jpg', 'https://www.youtube.com/watch?v=_InqQJRqGW4', 'completed', NOW()),

('The Witcher', 'الساحر', 'A monster hunter struggles to find his place.', 'صياد وحوش يكافح للعثور على مكانه.', 2019, 3, 24, 8.2, 'Action,Adventure,Drama', 'https://image.tmdb.org/t/p/w500/witcher.jpg', 'https://www.youtube.com/watch?v=ndl1W4ltcmg', 'ongoing', NOW()),

('The Mandalorian', 'المندلوري', 'A bounty hunter in the Star Wars universe.', 'صياد جوائز في عالم حرب النجوم.', 2019, 3, 24, 8.8, 'Action,Adventure,Sci-Fi', 'https://image.tmdb.org/t/p/w500/mandalorian.jpg', 'https://www.youtube.com/watch?v=aOC8E8z_ifw', 'ongoing', NOW()),

('Squid Game', 'لعبة الحبار', 'Desperate people compete in deadly games.', 'أشخاص يائسون يتنافسون في ألعاب مميتة.', 2021, 2, 18, 8.0, 'Action,Drama,Mystery', 'https://image.tmdb.org/t/p/w500/squid_game.jpg', 'https://www.youtube.com/watch?v=oqxAJKy0ii4', 'ongoing', NOW()),

('House of the Dragon', 'بيت التنين', 'Prequel to Game of Thrones.', 'مقدمة لصراع العروش.', 2022, 2, 20, 8.5, 'Action,Adventure,Drama', 'https://image.tmdb.org/t/p/w500/house_dragon.jpg', 'https://www.youtube.com/watch?v=DotnJ7tTA34', 'ongoing', NOW()),

('Wednesday', 'الأربعاء', 'Wednesday Addams at Nevermore Academy.', 'الأربعاء آدامز في أكاديمية نيفرمور.', 2022, 2, 16, 8.1, 'Comedy,Crime,Horror', 'https://image.tmdb.org/t/p/w500/wednesday.jpg', 'https://www.youtube.com/watch?v=Di310WS8zLk', 'ongoing', NOW());

-- إدراج التصنيفات
INSERT INTO categories (name, name_ar, description, description_ar, created_at) VALUES
('Action', 'أكشن', 'High-energy movies with intense sequences', 'أفلام عالية الطاقة مع مشاهد مكثفة', NOW()),
('Drama', 'دراما', 'Character-driven stories with emotional depth', 'قصص مدفوعة بالشخصيات مع عمق عاطفي', NOW()),
('Comedy', 'كوميديا', 'Humorous content designed to entertain', 'محتوى فكاهي مصمم للترفيه', NOW()),
('Thriller', 'إثارة', 'Suspenseful movies that keep you on edge', 'أفلام مشوقة تبقيك في حالة ترقب', NOW()),
('Horror', 'رعب', 'Scary movies designed to frighten', 'أفلام مخيفة مصممة للإرعاب', NOW()),
('Romance', 'رومانسية', 'Love stories and romantic relationships', 'قصص حب وعلاقات رومانسية', NOW()),
('Sci-Fi', 'خيال علمي', 'Science fiction and futuristic themes', 'خيال علمي ومواضيع مستقبلية', NOW()),
('Fantasy', 'فانتازيا', 'Magical and supernatural elements', 'عناصر سحرية وخارقة للطبيعة', NOW()),
('Crime', 'جريمة', 'Criminal activities and investigations', 'أنشطة إجرامية وتحقيقات', NOW()),
('Documentary', 'وثائقي', 'Non-fiction educational content', 'محتوى تعليمي غير خيالي', NOW()),
('Animation', 'رسوم متحركة', 'Animated movies and series', 'أفلام ومسلسلات رسوم متحركة', NOW()),
('War', 'حرب', 'Military conflicts and war stories', 'صراعات عسكرية وقصص حرب', NOW()),
('History', 'تاريخي', 'Historical events and periods', 'أحداث وفترات تاريخية', NOW()),
('Biography', 'سيرة ذاتية', 'Life stories of real people', 'قصص حياة أشخاص حقيقيين', NOW()),
('Adventure', 'مغامرة', 'Exciting journeys and quests', 'رحلات ومهام مثيرة', NOW());

-- إدراج المستخدمين التجريبيين
INSERT INTO users (name, email, password, role, subscription_type, subscription_end, profile_image, created_at) VALUES
('أحمد محمد', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'user', 'premium', DATE_ADD(NOW(), INTERVAL 1 YEAR), 'profiles/ahmed.jpg', NOW()),
('فاطمة علي', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'user', 'basic', DATE_ADD(NOW(), INTERVAL 6 MONTH), 'profiles/fatima.jpg', NOW()),
('محمد حسن', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'user', 'premium', DATE_ADD(NOW(), INTERVAL 1 YEAR), 'profiles/mohamed.jpg', NOW()),
('سارة أحمد', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'user', 'basic', DATE_ADD(NOW(), INTERVAL 3 MONTH), 'profiles/sara.jpg', NOW()),
('عمر خالد', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'user', 'premium', DATE_ADD(NOW(), INTERVAL 1 YEAR), 'profiles/omar.jpg', NOW()),
('مريم يوسف', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'user', 'free', NULL, 'profiles/mariam.jpg', NOW()),
('كريم سمير', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'user', 'premium', DATE_ADD(NOW(), INTERVAL 1 YEAR), 'profiles/karim.jpg', NOW()),
('نور الدين', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'user', 'basic', DATE_ADD(NOW(), INTERVAL 6 MONTH), 'profiles/nour.jpg', NOW()),
('ياسمين طارق', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'user', 'premium', DATE_ADD(NOW(), INTERVAL 1 YEAR), 'profiles/yasmin.jpg', NOW()),
('Admin User', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin', 'premium', DATE_ADD(NOW(), INTERVAL 10 YEAR), 'profiles/admin.jpg', NOW());

-- إدراج التقييمات
INSERT INTO ratings (user_id, movie_id, series_id, rating, review, created_at) VALUES
(1, 1, NULL, 5, 'فيلم رائع ومثير للغاية، أداء ممتاز من جميع الممثلين', NOW()),
(2, 1, NULL, 4, 'فيلم جيد لكن طويل قليلاً', NOW()),
(3, 2, NULL, 5, 'من أفضل الأفلام العربية التي شاهدتها', NOW()),
(1, NULL, 1, 5, 'مسلسل كوميدي رائع، يستحق المشاهدة', NOW()),
(2, NULL, 1, 4, 'مسلسل مسلي ومضحك', NOW()),
(4, 6, NULL, 5, 'تحفة سينمائية حقيقية', NOW()),
(5, 7, NULL, 5, 'أفضل فيلم في التاريخ', NOW()),
(6, 8, NULL, 4, 'فيلم ممتاز ومثير', NOW()),
(7, NULL, 6, 5, 'أفضل مسلسل شاهدته على الإطلاق', NOW()),
(8, NULL, 7, 5, 'مسلسل ملحمي ورائع', NOW());

-- إدراج المفضلة
INSERT INTO favorites (user_id, movie_id, series_id, created_at) VALUES
(1, 1, NULL, NOW()),
(1, 6, NULL, NOW()),
(1, NULL, 1, NOW()),
(1, NULL, 6, NOW()),
(2, 2, NULL, NOW()),
(2, 7, NULL, NOW()),
(2, NULL, 2, NOW()),
(3, 3, NULL, NOW()),
(3, 8, NULL, NOW()),
(3, NULL, 3, NOW()),
(4, 4, NULL, NOW()),
(4, 9, NULL, NOW()),
(4, NULL, 4, NOW()),
(5, 5, NULL, NOW()),
(5, 10, NULL, NOW()),
(5, NULL, 5, NOW());

-- إدراج سجل المشاهدة
INSERT INTO watch_history (user_id, movie_id, series_id, episode_id, watch_time, duration, completed, created_at) VALUES
(1, 1, NULL, NULL, 120, 170, 0, NOW()),
(1, 6, NULL, NULL, 142, 142, 1, DATE_SUB(NOW(), INTERVAL 1 DAY)),
(2, 2, NULL, NULL, 97, 97, 1, DATE_SUB(NOW(), INTERVAL 2 DAY)),
(2, 7, NULL, NULL, 175, 175, 1, DATE_SUB(NOW(), INTERVAL 3 DAY)),
(3, 3, NULL, NULL, 93, 93, 1, DATE_SUB(NOW(), INTERVAL 1 WEEK)),
(3, NULL, 1, 1, 45, 45, 1, DATE_SUB(NOW(), INTERVAL 2 WEEK)),
(4, 4, NULL, NULL, 60, 97, 0, DATE_SUB(NOW(), INTERVAL 3 WEEK)),
(4, NULL, 2, 1, 30, 60, 0, DATE_SUB(NOW(), INTERVAL 1 MONTH)),
(5, 5, NULL, NULL, 120, 120, 1, DATE_SUB(NOW(), INTERVAL 2 MONTH)),
(5, NULL, 3, 1, 45, 45, 1, DATE_SUB(NOW(), INTERVAL 3 MONTH));

-- إدراج الاشتراكات
INSERT INTO subscriptions (user_id, plan_type, price, start_date, end_date, status, payment_method, created_at) VALUES
(1, 'premium', 99.99, NOW(), DATE_ADD(NOW(), INTERVAL 1 YEAR), 'active', 'credit_card', NOW()),
(2, 'basic', 49.99, NOW(), DATE_ADD(NOW(), INTERVAL 6 MONTH), 'active', 'paypal', NOW()),
(3, 'premium', 99.99, NOW(), DATE_ADD(NOW(), INTERVAL 1 YEAR), 'active', 'credit_card', NOW()),
(4, 'basic', 49.99, NOW(), DATE_ADD(NOW(), INTERVAL 3 MONTH), 'active', 'bank_transfer', NOW()),
(5, 'premium', 99.99, NOW(), DATE_ADD(NOW(), INTERVAL 1 YEAR), 'active', 'credit_card', NOW()),
(6, 'free', 0.00, NOW(), NULL, 'active', 'free', NOW()),
(7, 'premium', 99.99, NOW(), DATE_ADD(NOW(), INTERVAL 1 YEAR), 'active', 'paypal', NOW()),
(8, 'basic', 49.99, NOW(), DATE_ADD(NOW(), INTERVAL 6 MONTH), 'active', 'credit_card', NOW()),
(9, 'premium', 99.99, NOW(), DATE_ADD(NOW(), INTERVAL 1 YEAR), 'active', 'bank_transfer', NOW()),
(10, 'premium', 99.99, NOW(), DATE_ADD(NOW(), INTERVAL 10 YEAR), 'active', 'admin', NOW());

-- إدراج المدفوعات
INSERT INTO payments (user_id, subscription_id, amount, currency, payment_method, transaction_id, status, created_at) VALUES
(1, 1, 99.99, 'USD', 'credit_card', 'txn_1234567890', 'completed', NOW()),
(2, 2, 49.99, 'USD', 'paypal', 'pp_1234567890', 'completed', NOW()),
(3, 3, 99.99, 'USD', 'credit_card', 'txn_2345678901', 'completed', NOW()),
(4, 4, 49.99, 'USD', 'bank_transfer', 'bt_1234567890', 'completed', NOW()),
(5, 5, 99.99, 'USD', 'credit_card', 'txn_3456789012', 'completed', NOW()),
(7, 7, 99.99, 'USD', 'paypal', 'pp_2345678901', 'completed', NOW()),
(8, 8, 49.99, 'USD', 'credit_card', 'txn_4567890123', 'completed', NOW()),
(9, 9, 99.99, 'USD', 'bank_transfer', 'bt_2345678901', 'completed', NOW());

-- إدراج الإشعارات
INSERT INTO notifications (user_id, title, title_ar, message, message_ar, type, status, created_at) VALUES
(1, 'Welcome to Shahid!', 'مرحباً بك في شاهد!', 'Thank you for joining our platform.', 'شكراً لانضمامك إلى منصتنا.', 'welcome', 'unread', NOW()),
(1, 'New Movie Added', 'فيلم جديد مضاف', 'Check out the latest movie: The Blue Elephant', 'شاهد أحدث فيلم: الفيل الأزرق', 'content', 'unread', NOW()),
(2, 'Subscription Renewal', 'تجديد الاشتراك', 'Your subscription will expire soon.', 'اشتراكك سينتهي قريباً.', 'subscription', 'read', NOW()),
(3, 'New Series Episode', 'حلقة جديدة من المسلسل', 'New episode of Breaking Bad is available.', 'حلقة جديدة من بريكنغ باد متاحة.', 'content', 'unread', NOW()),
(4, 'Payment Successful', 'دفع ناجح', 'Your payment has been processed successfully.', 'تم معالجة دفعتك بنجاح.', 'payment', 'read', NOW()),
(5, 'Profile Updated', 'تم تحديث الملف الشخصي', 'Your profile information has been updated.', 'تم تحديث معلومات ملفك الشخصي.', 'profile', 'read', NOW());

-- إدراج بيانات التحليلات
INSERT INTO analytics (date, total_users, active_users, new_registrations, total_views, revenue, created_at) VALUES
(CURDATE(), 1000, 750, 25, 5000, 2500.00, NOW()),
(DATE_SUB(CURDATE(), INTERVAL 1 DAY), 975, 720, 20, 4800, 2400.00, DATE_SUB(NOW(), INTERVAL 1 DAY)),
(DATE_SUB(CURDATE(), INTERVAL 2 DAY), 955, 700, 18, 4600, 2300.00, DATE_SUB(NOW(), INTERVAL 2 DAY)),
(DATE_SUB(CURDATE(), INTERVAL 3 DAY), 937, 680, 22, 4400, 2200.00, DATE_SUB(NOW(), INTERVAL 3 DAY)),
(DATE_SUB(CURDATE(), INTERVAL 4 DAY), 915, 660, 15, 4200, 2100.00, DATE_SUB(NOW(), INTERVAL 4 DAY)),
(DATE_SUB(CURDATE(), INTERVAL 5 DAY), 900, 640, 30, 4000, 2000.00, DATE_SUB(NOW(), INTERVAL 5 DAY)),
(DATE_SUB(CURDATE(), INTERVAL 6 DAY), 870, 620, 28, 3800, 1900.00, DATE_SUB(NOW(), INTERVAL 6 DAY)),
(DATE_SUB(CURDATE(), INTERVAL 1 WEEK), 842, 600, 25, 3600, 1800.00, DATE_SUB(NOW(), INTERVAL 1 WEEK));

-- إدراج سجلات الأخطاء التجريبية
INSERT INTO error_logs (level, message, file, line, user_id, ip_address, user_agent, created_at) VALUES
('warning', 'Slow database query detected', 'api/movies.php', 45, 1, '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)', DATE_SUB(NOW(), INTERVAL 1 HOUR)),
('error', 'Failed to load video file', 'streaming/player.php', 120, 2, '*************', 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0)', DATE_SUB(NOW(), INTERVAL 2 HOUR)),
('info', 'User login successful', 'auth/login.php', 30, 3, '*************', 'Mozilla/5.0 (Android 11; Mobile)', DATE_SUB(NOW(), INTERVAL 3 HOUR)),
('warning', 'High memory usage detected', 'admin/dashboard.php', 78, 10, '*************', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)', DATE_SUB(NOW(), INTERVAL 4 HOUR));

-- إدراج سجلات الأداء
INSERT INTO performance_logs (endpoint, response_time, memory_usage, cpu_usage, user_id, created_at) VALUES
('/api/movies', 45.2, 12.5, 8.3, 1, NOW()),
('/api/series', 38.7, 10.2, 6.1, 2, NOW()),
('/api/search', 52.1, 15.8, 12.4, 3, NOW()),
('/streaming/play', 120.5, 25.3, 18.7, 4, NOW()),
('/admin/dashboard', 78.9, 20.1, 15.2, 10, NOW()),
('/api/user/profile', 25.3, 8.7, 4.2, 5, NOW());

-- إدراج سجلات الأمان
INSERT INTO security_logs (event_type, description, ip_address, user_id, severity, created_at) VALUES
('login_attempt', 'Successful login', '*************', 1, 'info', NOW()),
('failed_login', 'Failed login attempt - wrong password', '*************', NULL, 'warning', DATE_SUB(NOW(), INTERVAL 30 MINUTE)),
('suspicious_activity', 'Multiple failed login attempts', '*************', NULL, 'high', DATE_SUB(NOW(), INTERVAL 25 MINUTE)),
('password_change', 'User changed password', '*************', 2, 'info', DATE_SUB(NOW(), INTERVAL 1 HOUR)),
('admin_access', 'Admin accessed user management', '*************', 10, 'medium', DATE_SUB(NOW(), INTERVAL 2 HOUR));

-- إدراج بيانات SEO
INSERT INTO seo_data (page_url, title, description, keywords, meta_tags, views, created_at) VALUES
('/movies/the-blue-elephant', 'The Blue Elephant - Watch Online | Shahid', 'Watch The Blue Elephant online in HD quality. Psychological thriller movie.', 'blue elephant, movie, thriller, arabic cinema', '{"og:title":"The Blue Elephant","og:description":"Psychological thriller movie"}', 1250, NOW()),
('/series/breaking-bad', 'Breaking Bad - Complete Series | Shahid', 'Watch Breaking Bad complete series online. Crime drama series.', 'breaking bad, series, crime, drama', '{"og:title":"Breaking Bad","og:description":"Crime drama series"}', 2100, NOW()),
('/movies/inception', 'Inception - Watch Online | Shahid', 'Watch Inception online in HD. Sci-fi thriller by Christopher Nolan.', 'inception, movie, sci-fi, nolan', '{"og:title":"Inception","og:description":"Sci-fi thriller movie"}', 1800, NOW()),
('/series/game-of-thrones', 'Game of Thrones - Complete Series | Shahid', 'Watch Game of Thrones complete series. Fantasy drama series.', 'game of thrones, series, fantasy, drama', '{"og:title":"Game of Thrones","og:description":"Fantasy drama series"}', 3200, NOW());