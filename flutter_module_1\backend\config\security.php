<?php
/**
 * Shahid Platform - Advanced Security System
 * Production-Ready Security Configuration
 */

// Security Headers
function setSecurityHeaders() {
    // Prevent XSS attacks
    header('X-Content-Type-Options: nosniff');
    header('X-Frame-Options: DENY');
    header('X-XSS-Protection: 1; mode=block');
    
    // HTTPS enforcement
    header('Strict-Transport-Security: max-age=31536000; includeSubDomains');
    
    // Content Security Policy
    header("Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' https:; connect-src 'self';");
    
    // Referrer Policy
    header('Referrer-Policy: strict-origin-when-cross-origin');
    
    // Feature Policy
    header("Permissions-Policy: geolocation=(), microphone=(), camera=()");
}

// Rate Limiting
class RateLimiter {
    private $pdo;
    private $limits = [
        'login' => ['requests' => 5, 'window' => 900], // 5 attempts per 15 minutes
        'api' => ['requests' => 100, 'window' => 3600], // 100 requests per hour
        'search' => ['requests' => 50, 'window' => 3600], // 50 searches per hour
        'register' => ['requests' => 3, 'window' => 3600] // 3 registrations per hour
    ];
    
    public function __construct($pdo) {
        $this->pdo = $pdo;
    }
    
    public function checkLimit($action, $identifier = null) {
        if (!isset($this->limits[$action])) {
            return true;
        }
        
        $identifier = $identifier ?: $this->getClientIdentifier();
        $limit = $this->limits[$action];
        
        // Clean old entries
        $this->cleanOldEntries($action, $limit['window']);
        
        // Check current count
        $stmt = $this->pdo->prepare("
            SELECT COUNT(*) FROM rate_limits 
            WHERE action = ? AND identifier = ? AND created_at > DATE_SUB(NOW(), INTERVAL ? SECOND)
        ");
        $stmt->execute([$action, $identifier, $limit['window']]);
        $count = $stmt->fetchColumn();
        
        if ($count >= $limit['requests']) {
            $this->logSecurityEvent('rate_limit_exceeded', "Rate limit exceeded for action: $action", $identifier);
            return false;
        }
        
        // Record this request
        $stmt = $this->pdo->prepare("
            INSERT INTO rate_limits (action, identifier, ip_address, user_agent, created_at) 
            VALUES (?, ?, ?, ?, NOW())
        ");
        $stmt->execute([
            $action, 
            $identifier, 
            $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
        ]);
        
        return true;
    }
    
    private function getClientIdentifier() {
        $ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
        return hash('sha256', $ip . $userAgent);
    }
    
    private function cleanOldEntries($action, $window) {
        $stmt = $this->pdo->prepare("
            DELETE FROM rate_limits 
            WHERE action = ? AND created_at < DATE_SUB(NOW(), INTERVAL ? SECOND)
        ");
        $stmt->execute([$action, $window]);
    }
    
    private function logSecurityEvent($type, $description, $identifier) {
        $stmt = $this->pdo->prepare("
            INSERT INTO security_logs (event_type, description, ip_address, user_id, severity, created_at) 
            VALUES (?, ?, ?, ?, 'high', NOW())
        ");
        $stmt->execute([
            $type,
            $description,
            $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            $_SESSION['user_id'] ?? null
        ]);
    }
}

// Input Validation and Sanitization
class InputValidator {
    public static function sanitizeString($input, $maxLength = 255) {
        $input = trim($input);
        $input = htmlspecialchars($input, ENT_QUOTES, 'UTF-8');
        return substr($input, 0, $maxLength);
    }
    
    public static function validateEmail($email) {
        $email = filter_var($email, FILTER_SANITIZE_EMAIL);
        return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
    }
    
    public static function validatePassword($password) {
        // At least 8 characters, 1 uppercase, 1 lowercase, 1 number
        return preg_match('/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/', $password);
    }
    
    public static function validateURL($url) {
        return filter_var($url, FILTER_VALIDATE_URL) !== false;
    }
    
    public static function validateInteger($value, $min = null, $max = null) {
        $value = filter_var($value, FILTER_VALIDATE_INT);
        if ($value === false) return false;
        
        if ($min !== null && $value < $min) return false;
        if ($max !== null && $value > $max) return false;
        
        return $value;
    }
    
    public static function validateFloat($value, $min = null, $max = null) {
        $value = filter_var($value, FILTER_VALIDATE_FLOAT);
        if ($value === false) return false;
        
        if ($min !== null && $value < $min) return false;
        if ($max !== null && $value > $max) return false;
        
        return $value;
    }
    
    public static function sanitizeFilename($filename) {
        // Remove dangerous characters
        $filename = preg_replace('/[^a-zA-Z0-9._-]/', '', $filename);
        return substr($filename, 0, 255);
    }
}

// File Upload Security
class SecureFileUpload {
    private $allowedTypes = [
        'image' => ['jpg', 'jpeg', 'png', 'gif', 'webp'],
        'video' => ['mp4', 'webm', 'ogg'],
        'document' => ['pdf', 'doc', 'docx', 'txt']
    ];
    
    private $maxSizes = [
        'image' => 5 * 1024 * 1024, // 5MB
        'video' => 500 * 1024 * 1024, // 500MB
        'document' => 10 * 1024 * 1024 // 10MB
    ];
    
    public function validateFile($file, $type) {
        if (!isset($file) || $file['error'] !== UPLOAD_ERR_OK) {
            return ['valid' => false, 'error' => 'File upload error'];
        }
        
        // Check file size
        if ($file['size'] > $this->maxSizes[$type]) {
            return ['valid' => false, 'error' => 'File too large'];
        }
        
        // Check file extension
        $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        if (!in_array($extension, $this->allowedTypes[$type])) {
            return ['valid' => false, 'error' => 'Invalid file type'];
        }
        
        // Check MIME type
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mimeType = finfo_file($finfo, $file['tmp_name']);
        finfo_close($finfo);
        
        $allowedMimes = [
            'image' => ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
            'video' => ['video/mp4', 'video/webm', 'video/ogg'],
            'document' => ['application/pdf', 'application/msword', 'text/plain']
        ];
        
        if (!in_array($mimeType, $allowedMimes[$type])) {
            return ['valid' => false, 'error' => 'Invalid MIME type'];
        }
        
        // Additional security checks for images
        if ($type === 'image') {
            $imageInfo = getimagesize($file['tmp_name']);
            if ($imageInfo === false) {
                return ['valid' => false, 'error' => 'Invalid image file'];
            }
        }
        
        return ['valid' => true];
    }
    
    public function secureUpload($file, $type, $uploadDir) {
        $validation = $this->validateFile($file, $type);
        if (!$validation['valid']) {
            return $validation;
        }
        
        // Generate secure filename
        $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        $filename = bin2hex(random_bytes(16)) . '.' . $extension;
        $uploadPath = $uploadDir . '/' . $filename;
        
        // Ensure upload directory exists
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }
        
        // Move uploaded file
        if (move_uploaded_file($file['tmp_name'], $uploadPath)) {
            // Set secure permissions
            chmod($uploadPath, 0644);
            return ['valid' => true, 'filename' => $filename, 'path' => $uploadPath];
        }
        
        return ['valid' => false, 'error' => 'Failed to move uploaded file'];
    }
}

// SQL Injection Prevention
class SecureDatabase {
    private $pdo;
    
    public function __construct($pdo) {
        $this->pdo = $pdo;
    }
    
    public function secureQuery($sql, $params = []) {
        try {
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            return $stmt;
        } catch (PDOException $e) {
            // Log the error securely
            error_log("Database error: " . $e->getMessage());
            throw new Exception("Database operation failed");
        }
    }
    
    public function secureSelect($table, $columns = '*', $where = '', $params = []) {
        // Validate table name (whitelist approach)
        $allowedTables = ['users', 'movies', 'series', 'ratings', 'favorites', 'watch_history', 'subscriptions', 'payments'];
        if (!in_array($table, $allowedTables)) {
            throw new Exception("Invalid table name");
        }
        
        // Build query
        $sql = "SELECT $columns FROM $table";
        if ($where) {
            $sql .= " WHERE $where";
        }
        
        return $this->secureQuery($sql, $params);
    }
}

// Session Security
class SecureSession {
    public static function start() {
        // Configure session security
        ini_set('session.cookie_httponly', 1);
        ini_set('session.cookie_secure', 1);
        ini_set('session.use_strict_mode', 1);
        ini_set('session.cookie_samesite', 'Strict');
        
        session_start();
        
        // Regenerate session ID periodically
        if (!isset($_SESSION['last_regeneration'])) {
            $_SESSION['last_regeneration'] = time();
        } elseif (time() - $_SESSION['last_regeneration'] > 300) { // 5 minutes
            session_regenerate_id(true);
            $_SESSION['last_regeneration'] = time();
        }
    }
    
    public static function destroy() {
        session_start();
        session_unset();
        session_destroy();
        
        // Clear session cookie
        if (ini_get("session.use_cookies")) {
            $params = session_get_cookie_params();
            setcookie(session_name(), '', time() - 42000,
                $params["path"], $params["domain"],
                $params["secure"], $params["httponly"]
            );
        }
    }
    
    public static function validateSession() {
        if (!isset($_SESSION['user_id'])) {
            return false;
        }
        
        // Check session timeout (30 minutes)
        if (isset($_SESSION['last_activity']) && (time() - $_SESSION['last_activity'] > 1800)) {
            self::destroy();
            return false;
        }
        
        $_SESSION['last_activity'] = time();
        return true;
    }
}

// CSRF Protection
class CSRFProtection {
    public static function generateToken() {
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }
        return $_SESSION['csrf_token'];
    }
    
    public static function validateToken($token) {
        return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
    }
    
    public static function getTokenField() {
        $token = self::generateToken();
        return '<input type="hidden" name="csrf_token" value="' . htmlspecialchars($token) . '">';
    }
}

// Password Security
class PasswordSecurity {
    public static function hash($password) {
        return password_hash($password, PASSWORD_ARGON2ID, [
            'memory_cost' => 65536, // 64 MB
            'time_cost' => 4,       // 4 iterations
            'threads' => 3,         // 3 threads
        ]);
    }
    
    public static function verify($password, $hash) {
        return password_verify($password, $hash);
    }
    
    public static function needsRehash($hash) {
        return password_needs_rehash($hash, PASSWORD_ARGON2ID, [
            'memory_cost' => 65536,
            'time_cost' => 4,
            'threads' => 3,
        ]);
    }
}

// Security Monitoring
class SecurityMonitor {
    private $pdo;
    
    public function __construct($pdo) {
        $this->pdo = $pdo;
    }
    
    public function logEvent($type, $description, $severity = 'info', $userId = null) {
        $stmt = $this->pdo->prepare("
            INSERT INTO security_logs (event_type, description, ip_address, user_id, severity, user_agent, created_at) 
            VALUES (?, ?, ?, ?, ?, ?, NOW())
        ");
        
        $stmt->execute([
            $type,
            $description,
            $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            $userId ?: ($_SESSION['user_id'] ?? null),
            $severity,
            $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
        ]);
    }
    
    public function detectSuspiciousActivity($userId) {
        // Check for multiple failed logins
        $stmt = $this->pdo->prepare("
            SELECT COUNT(*) FROM security_logs 
            WHERE event_type = 'failed_login' 
            AND (user_id = ? OR ip_address = ?) 
            AND created_at > DATE_SUB(NOW(), INTERVAL 1 HOUR)
        ");
        $stmt->execute([$userId, $_SERVER['REMOTE_ADDR'] ?? 'unknown']);
        $failedLogins = $stmt->fetchColumn();
        
        if ($failedLogins >= 5) {
            $this->logEvent('suspicious_activity', 'Multiple failed login attempts detected', 'high', $userId);
            return true;
        }
        
        return false;
    }
}

// Initialize security
setSecurityHeaders();

// Global security functions
function requireAuth() {
    if (!SecureSession::validateSession()) {
        header('Location: /auth/login.php');
        exit;
    }
}

function requireAdmin() {
    requireAuth();
    if ($_SESSION['role'] !== 'admin') {
        header('HTTP/1.1 403 Forbidden');
        exit('Access denied');
    }
}

function sanitizeInput($input) {
    return InputValidator::sanitizeString($input);
}

function validateCSRF() {
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $token = $_POST['csrf_token'] ?? '';
        if (!CSRFProtection::validateToken($token)) {
            header('HTTP/1.1 403 Forbidden');
            exit('CSRF token validation failed');
        }
    }
}
?>
