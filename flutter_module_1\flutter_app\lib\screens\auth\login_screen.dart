import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';
import 'package:local_auth/local_auth.dart';

import '../../core/app_config.dart';
import '../../core/theme/app_theme.dart';
import '../../core/services/storage_service.dart';
import '../../providers/auth_provider.dart';
import '../../core/routes/app_routes.dart';
import '../../widgets/custom_button.dart';
import '../../widgets/custom_text_field.dart';
import '../../widgets/loading_overlay.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen>
    with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _storage = StorageService();
  final _localAuth = LocalAuthentication();

  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  bool _obscurePassword = true;
  bool _rememberMe = false;
  bool _isLoading = false;
  bool _biometricAvailable = false;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadSavedCredentials();
    _checkBiometricAvailability();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.5),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    _animationController.forward();
  }

  void _loadSavedCredentials() {
    final savedEmail = _storage.getString('saved_email');
    final rememberMe = _storage.getBool('remember_me') ?? false;

    if (savedEmail != null && rememberMe) {
      _emailController.text = savedEmail;
      _rememberMe = true;
    }
  }

  Future<void> _checkBiometricAvailability() async {
    try {
      final isAvailable = await _localAuth.canCheckBiometrics;
      final biometricEnabled = _storage.getBool(AppConfig.biometricKey) ?? false;
      
      setState(() {
        _biometricAvailable = isAvailable && biometricEnabled;
      });
    } catch (e) {
      setState(() {
        _biometricAvailable = false;
      });
    }
  }

  Future<void> _login() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      
      final success = await authProvider.login(
        _emailController.text.trim(),
        _passwordController.text,
      );

      if (success) {
        // Save credentials if remember me is checked
        if (_rememberMe) {
          await _storage.setString('saved_email', _emailController.text.trim());
          await _storage.setBool('remember_me', true);
        } else {
          await _storage.remove('saved_email');
          await _storage.setBool('remember_me', false);
        }

        // Navigate to home
        Get.offAllNamed(AppRoutes.home);
        
        // Show success message
        Get.snackbar(
          'نجح تسجيل الدخول',
          AppConfig.loginSuccessMessage,
          backgroundColor: AppTheme.successColor,
          colorText: Colors.white,
          snackPosition: SnackPosition.TOP,
        );
      }
    } catch (e) {
      Get.snackbar(
        'خطأ في تسجيل الدخول',
        e.toString(),
        backgroundColor: AppTheme.errorColor,
        colorText: Colors.white,
        snackPosition: SnackPosition.TOP,
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _biometricLogin() async {
    try {
      final isAuthenticated = await _localAuth.authenticate(
        localizedReason: 'استخدم بصمتك لتسجيل الدخول',
        options: const AuthenticationOptions(
          biometricOnly: true,
          stickyAuth: true,
        ),
      );

      if (isAuthenticated) {
        // Get saved credentials
        final savedEmail = _storage.getString('saved_email');
        final savedPassword = await _storage.getSecureString('saved_password');

        if (savedEmail != null && savedPassword != null) {
          setState(() {
            _isLoading = true;
          });

          final authProvider = Provider.of<AuthProvider>(context, listen: false);
          final success = await authProvider.login(savedEmail, savedPassword);

          if (success) {
            Get.offAllNamed(AppRoutes.home);
          }

          setState(() {
            _isLoading = false;
          });
        }
      }
    } catch (e) {
      Get.snackbar(
        'خطأ في المصادقة البيومترية',
        e.toString(),
        backgroundColor: AppTheme.errorColor,
        colorText: Colors.white,
      );
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.darkBackgroundColor,
      body: LoadingOverlay(
        isLoading: _isLoading,
        child: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                AppTheme.darkBackgroundColor,
                AppTheme.darkSurfaceColor,
              ],
            ),
          ),
          child: SafeArea(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(24),
              child: FadeTransition(
                opacity: _fadeAnimation,
                child: SlideTransition(
                  position: _slideAnimation,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      const SizedBox(height: 40),

                      // Logo and Title
                      _buildHeader(),

                      const SizedBox(height: 50),

                      // Login Form
                      _buildLoginForm(),

                      const SizedBox(height: 30),

                      // Biometric Login
                      if (_biometricAvailable) _buildBiometricLogin(),

                      const SizedBox(height: 30),

                      // Social Login
                      _buildSocialLogin(),

                      const SizedBox(height: 40),

                      // Register Link
                      _buildRegisterLink(),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      children: [
        // Logo
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            gradient: AppTheme.primaryGradient,
          ),
          child: const Icon(
            Icons.play_arrow_rounded,
            size: 40,
            color: Colors.white,
          ),
        ),

        const SizedBox(height: 20),

        // Title
        Text(
          'مرحباً بك في ${AppConfig.appName}',
          style: AppTheme.headlineMedium.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),

        const SizedBox(height: 8),

        Text(
          'سجل دخولك للاستمتاع بأفضل المحتوى',
          style: AppTheme.bodyLarge.copyWith(
            color: Colors.white70,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildLoginForm() {
    return Form(
      key: _formKey,
      child: Column(
        children: [
          // Email Field
          CustomTextField(
            controller: _emailController,
            label: 'البريد الإلكتروني',
            hint: 'أدخل بريدك الإلكتروني',
            keyboardType: TextInputType.emailAddress,
            prefixIcon: Icons.email_outlined,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'البريد الإلكتروني مطلوب';
              }
              if (!AppConfig.isValidEmail(value)) {
                return 'البريد الإلكتروني غير صحيح';
              }
              return null;
            },
          ),

          const SizedBox(height: 20),

          // Password Field
          CustomTextField(
            controller: _passwordController,
            label: 'كلمة المرور',
            hint: 'أدخل كلمة المرور',
            obscureText: _obscurePassword,
            prefixIcon: Icons.lock_outlined,
            suffixIcon: IconButton(
              icon: Icon(
                _obscurePassword ? Icons.visibility : Icons.visibility_off,
                color: Colors.white54,
              ),
              onPressed: () {
                setState(() {
                  _obscurePassword = !_obscurePassword;
                });
              },
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'كلمة المرور مطلوبة';
              }
              if (value.length < AppConfig.minPasswordLength) {
                return 'كلمة المرور يجب أن تكون ${AppConfig.minPasswordLength} أحرف على الأقل';
              }
              return null;
            },
          ),

          const SizedBox(height: 16),

          // Remember Me & Forgot Password
          Row(
            children: [
              Checkbox(
                value: _rememberMe,
                onChanged: (value) {
                  setState(() {
                    _rememberMe = value ?? false;
                  });
                },
                activeColor: AppTheme.primaryColor,
              ),
              Text(
                'تذكرني',
                style: AppTheme.bodyMedium.copyWith(color: Colors.white70),
              ),
              const Spacer(),
              TextButton(
                onPressed: () {
                  Get.toNamed(AppRoutes.forgotPassword);
                },
                child: Text(
                  'نسيت كلمة المرور؟',
                  style: AppTheme.bodyMedium.copyWith(
                    color: AppTheme.primaryColor,
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 30),

          // Login Button
          CustomButton(
            text: 'تسجيل الدخول',
            onPressed: _login,
            isLoading: _isLoading,
          ),
        ],
      ),
    );
  }

  Widget _buildBiometricLogin() {
    return Column(
      children: [
        Row(
          children: [
            const Expanded(child: Divider(color: Colors.white24)),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Text(
                'أو',
                style: AppTheme.bodyMedium.copyWith(color: Colors.white54),
              ),
            ),
            const Expanded(child: Divider(color: Colors.white24)),
          ],
        ),

        const SizedBox(height: 20),

        InkWell(
          onTap: _biometricLogin,
          borderRadius: BorderRadius.circular(50),
          child: Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(color: AppTheme.primaryColor, width: 2),
            ),
            child: const Icon(
              Icons.fingerprint,
              size: 30,
              color: AppTheme.primaryColor,
            ),
          ),
        ),

        const SizedBox(height: 8),

        Text(
          'تسجيل الدخول بالبصمة',
          style: AppTheme.bodySmall.copyWith(color: Colors.white54),
        ),
      ],
    );
  }

  Widget _buildSocialLogin() {
    return Column(
      children: [
        Row(
          children: [
            const Expanded(child: Divider(color: Colors.white24)),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Text(
                'أو سجل دخولك باستخدام',
                style: AppTheme.bodyMedium.copyWith(color: Colors.white54),
              ),
            ),
            const Expanded(child: Divider(color: Colors.white24)),
          ],
        ),

        const SizedBox(height: 20),

        Row(
          children: [
            Expanded(
              child: _buildSocialButton(
                'Google',
                Icons.g_mobiledata,
                Colors.red,
                () {
                  // Implement Google login
                },
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildSocialButton(
                'Facebook',
                Icons.facebook,
                Colors.blue,
                () {
                  // Implement Facebook login
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildSocialButton(
    String text,
    IconData icon,
    Color color,
    VoidCallback onPressed,
  ) {
    return OutlinedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon, color: color),
      label: Text(
        text,
        style: AppTheme.bodyMedium.copyWith(color: Colors.white),
      ),
      style: OutlinedButton.styleFrom(
        padding: const EdgeInsets.symmetric(vertical: 12),
        side: BorderSide(color: color),
      ),
    );
  }

  Widget _buildRegisterLink() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          'ليس لديك حساب؟ ',
          style: AppTheme.bodyMedium.copyWith(color: Colors.white70),
        ),
        TextButton(
          onPressed: () {
            Get.toNamed(AppRoutes.register);
          },
          child: Text(
            'إنشاء حساب جديد',
            style: AppTheme.bodyMedium.copyWith(
              color: AppTheme.primaryColor,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ],
    );
  }
}
