<?php
/**
 * نظام إدارة الإعدادات الديناميكية
 * Dynamic Settings Management System
 */

class SettingsManager {
    private $pdo;
    private $security;
    private $cache = [];
    
    public function __construct($pdo, $security) {
        $this->pdo = $pdo;
        $this->security = $security;
        $this->loadSettings();
    }
    
    /**
     * تحميل جميع الإعدادات في الذاكرة
     */
    private function loadSettings() {
        try {
            $stmt = $this->pdo->query("SELECT setting_key, setting_value, setting_type FROM system_settings");
            
            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                $this->cache[$row['setting_key']] = $this->parseValue($row['setting_value'], $row['setting_type']);
            }
            
        } catch (Exception $e) {
            error_log("Failed to load settings: " . $e->getMessage());
        }
    }
    
    /**
     * تحليل قيمة الإعداد حسب نوعها
     */
    private function parseValue($value, $type) {
        switch ($type) {
            case 'boolean':
                return filter_var($value, FILTER_VALIDATE_BOOLEAN);
            case 'number':
                return is_numeric($value) ? (float)$value : 0;
            case 'json':
                return json_decode($value, true) ?: [];
            default:
                return $value;
        }
    }
    
    /**
     * تحويل القيمة للتخزين
     */
    private function formatValue($value, $type) {
        switch ($type) {
            case 'boolean':
                return $value ? '1' : '0';
            case 'number':
                return (string)$value;
            case 'json':
                return json_encode($value);
            default:
                return (string)$value;
        }
    }
    
    /**
     * الحصول على قيمة إعداد
     */
    public function get($key, $default = null) {
        return $this->cache[$key] ?? $default;
    }
    
    /**
     * تعيين قيمة إعداد
     */
    public function set($key, $value, $type = 'text') {
        try {
            $formattedValue = $this->formatValue($value, $type);
            
            // الحصول على القيمة القديمة
            $oldValue = $this->get($key);
            
            // تحديث في قاعدة البيانات
            $stmt = $this->pdo->prepare("
                INSERT INTO system_settings (setting_key, setting_value, setting_type, updated_at) 
                VALUES (?, ?, ?, NOW()) 
                ON DUPLICATE KEY UPDATE 
                setting_value = VALUES(setting_value), 
                setting_type = VALUES(setting_type),
                updated_at = NOW()
            ");
            
            $stmt->execute([$key, $formattedValue, $type]);
            
            // تحديث الكاش
            $this->cache[$key] = $value;
            
            // تسجيل النشاط
            if (isset($_SESSION['user_id'])) {
                $this->security->logActivity(
                    $_SESSION['user_id'],
                    'setting.update',
                    "تم تحديث الإعداد: $key",
                    'system_settings',
                    null,
                    ['key' => $key, 'old_value' => $oldValue],
                    ['key' => $key, 'new_value' => $value]
                );
            }
            
            return true;
            
        } catch (Exception $e) {
            throw new Exception("فشل في تحديث الإعداد: " . $e->getMessage());
        }
    }
    
    /**
     * حذف إعداد
     */
    public function delete($key) {
        try {
            $oldValue = $this->get($key);
            
            $stmt = $this->pdo->prepare("DELETE FROM system_settings WHERE setting_key = ?");
            $stmt->execute([$key]);
            
            unset($this->cache[$key]);
            
            // تسجيل النشاط
            if (isset($_SESSION['user_id'])) {
                $this->security->logActivity(
                    $_SESSION['user_id'],
                    'setting.delete',
                    "تم حذف الإعداد: $key",
                    'system_settings',
                    null,
                    ['key' => $key, 'value' => $oldValue],
                    null
                );
            }
            
            return true;
            
        } catch (Exception $e) {
            throw new Exception("فشل في حذف الإعداد: " . $e->getMessage());
        }
    }
    
    /**
     * الحصول على إعدادات فئة معينة
     */
    public function getByCategory($category) {
        try {
            $stmt = $this->pdo->prepare("
                SELECT setting_key, setting_value, setting_type, label, description, validation_rules, options 
                FROM system_settings 
                WHERE category = ? 
                ORDER BY setting_key
            ");
            
            $stmt->execute([$category]);
            $settings = [];
            
            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                $settings[$row['setting_key']] = [
                    'value' => $this->parseValue($row['setting_value'], $row['setting_type']),
                    'type' => $row['setting_type'],
                    'label' => $row['label'],
                    'description' => $row['description'],
                    'validation_rules' => json_decode($row['validation_rules'], true),
                    'options' => json_decode($row['options'], true)
                ];
            }
            
            return $settings;
            
        } catch (Exception $e) {
            throw new Exception("فشل في جلب إعدادات الفئة: " . $e->getMessage());
        }
    }
    
    /**
     * الحصول على جميع الفئات
     */
    public function getCategories() {
        try {
            $stmt = $this->pdo->query("
                SELECT DISTINCT category, COUNT(*) as setting_count 
                FROM system_settings 
                GROUP BY category 
                ORDER BY category
            ");
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            throw new Exception("فشل في جلب الفئات: " . $e->getMessage());
        }
    }
    
    /**
     * إنشاء إعداد جديد
     */
    public function createSetting($key, $value, $type, $category, $label, $description = null, $isPublic = false, $validationRules = null, $options = null) {
        try {
            // التحقق من عدم وجود الإعداد
            if (isset($this->cache[$key])) {
                throw new Exception("الإعداد موجود مسبقاً");
            }
            
            $stmt = $this->pdo->prepare("
                INSERT INTO system_settings 
                (setting_key, setting_value, setting_type, category, label, description, is_public, validation_rules, options) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            $stmt->execute([
                $key,
                $this->formatValue($value, $type),
                $type,
                $category,
                $label,
                $description,
                $isPublic ? 1 : 0,
                $validationRules ? json_encode($validationRules) : null,
                $options ? json_encode($options) : null
            ]);
            
            // تحديث الكاش
            $this->cache[$key] = $value;
            
            // تسجيل النشاط
            if (isset($_SESSION['user_id'])) {
                $this->security->logActivity(
                    $_SESSION['user_id'],
                    'setting.create',
                    "تم إنشاء إعداد جديد: $label",
                    'system_settings',
                    null,
                    null,
                    ['key' => $key, 'label' => $label, 'category' => $category]
                );
            }
            
            return true;
            
        } catch (Exception $e) {
            throw new Exception("فشل في إنشاء الإعداد: " . $e->getMessage());
        }
    }
    
    /**
     * التحقق من صحة قيمة الإعداد
     */
    public function validateSetting($key, $value) {
        try {
            $stmt = $this->pdo->prepare("
                SELECT setting_type, validation_rules, options 
                FROM system_settings 
                WHERE setting_key = ?
            ");
            
            $stmt->execute([$key]);
            $setting = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$setting) {
                throw new Exception("الإعداد غير موجود");
            }
            
            $errors = [];
            
            // التحقق من النوع
            switch ($setting['setting_type']) {
                case 'number':
                    if (!is_numeric($value)) {
                        $errors[] = "يجب أن تكون القيمة رقماً";
                    }
                    break;
                    
                case 'email':
                    if (!filter_var($value, FILTER_VALIDATE_EMAIL)) {
                        $errors[] = "يجب أن تكون القيمة بريد إلكتروني صحيح";
                    }
                    break;
                    
                case 'url':
                    if (!filter_var($value, FILTER_VALIDATE_URL)) {
                        $errors[] = "يجب أن تكون القيمة رابط صحيح";
                    }
                    break;
                    
                case 'color':
                    if (!preg_match('/^#[a-fA-F0-9]{6}$/', $value)) {
                        $errors[] = "يجب أن تكون القيمة لون صحيح (مثل #FF0000)";
                    }
                    break;
            }
            
            // التحقق من قواعد التحقق المخصصة
            if ($setting['validation_rules']) {
                $rules = json_decode($setting['validation_rules'], true);
                
                if (isset($rules['required']) && $rules['required'] && empty($value)) {
                    $errors[] = "هذا الحقل مطلوب";
                }
                
                if (isset($rules['min_length']) && strlen($value) < $rules['min_length']) {
                    $errors[] = "يجب أن تكون القيمة {$rules['min_length']} أحرف على الأقل";
                }
                
                if (isset($rules['max_length']) && strlen($value) > $rules['max_length']) {
                    $errors[] = "يجب أن تكون القيمة {$rules['max_length']} أحرف كحد أقصى";
                }
                
                if (isset($rules['min_value']) && is_numeric($value) && $value < $rules['min_value']) {
                    $errors[] = "يجب أن تكون القيمة {$rules['min_value']} أو أكثر";
                }
                
                if (isset($rules['max_value']) && is_numeric($value) && $value > $rules['max_value']) {
                    $errors[] = "يجب أن تكون القيمة {$rules['max_value']} أو أقل";
                }
                
                if (isset($rules['pattern']) && !preg_match($rules['pattern'], $value)) {
                    $errors[] = "تنسيق القيمة غير صحيح";
                }
            }
            
            // التحقق من الخيارات المحددة
            if ($setting['options']) {
                $options = json_decode($setting['options'], true);
                if (is_array($options) && !in_array($value, array_keys($options))) {
                    $errors[] = "القيمة غير مسموحة";
                }
            }
            
            return empty($errors) ? true : $errors;
            
        } catch (Exception $e) {
            throw new Exception("فشل في التحقق من الإعداد: " . $e->getMessage());
        }
    }
    
    /**
     * تحديث إعدادات متعددة
     */
    public function updateMultiple($settings) {
        try {
            $this->pdo->beginTransaction();
            
            foreach ($settings as $key => $value) {
                // التحقق من صحة القيمة
                $validation = $this->validateSetting($key, $value);
                if ($validation !== true) {
                    throw new Exception("خطأ في الإعداد $key: " . implode(', ', $validation));
                }
                
                // الحصول على نوع الإعداد
                $stmt = $this->pdo->prepare("SELECT setting_type FROM system_settings WHERE setting_key = ?");
                $stmt->execute([$key]);
                $type = $stmt->fetchColumn();
                
                if ($type) {
                    $this->set($key, $value, $type);
                }
            }
            
            $this->pdo->commit();
            return true;
            
        } catch (Exception $e) {
            $this->pdo->rollBack();
            throw new Exception("فشل في تحديث الإعدادات: " . $e->getMessage());
        }
    }
    
    /**
     * الحصول على الإعدادات العامة (المرئية للجمهور)
     */
    public function getPublicSettings() {
        try {
            $stmt = $this->pdo->query("
                SELECT setting_key, setting_value, setting_type 
                FROM system_settings 
                WHERE is_public = 1
            ");
            
            $settings = [];
            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                $settings[$row['setting_key']] = $this->parseValue($row['setting_value'], $row['setting_type']);
            }
            
            return $settings;
            
        } catch (Exception $e) {
            throw new Exception("فشل في جلب الإعدادات العامة: " . $e->getMessage());
        }
    }
    
    /**
     * إعادة تعيين الإعدادات للقيم الافتراضية
     */
    public function resetToDefaults($category = null) {
        try {
            $whereClause = $category ? "WHERE category = ?" : "";
            $params = $category ? [$category] : [];
            
            // يمكن إضافة منطق لإعادة تعيين القيم الافتراضية هنا
            // حالياً سنقوم بحذف الإعدادات المخصصة
            
            $stmt = $this->pdo->prepare("DELETE FROM system_settings $whereClause");
            $stmt->execute($params);
            
            // إعادة تحميل الإعدادات
            $this->cache = [];
            $this->loadSettings();
            
            // تسجيل النشاط
            if (isset($_SESSION['user_id'])) {
                $this->security->logActivity(
                    $_SESSION['user_id'],
                    'settings.reset',
                    $category ? "تم إعادة تعيين إعدادات فئة: $category" : "تم إعادة تعيين جميع الإعدادات",
                    'system_settings',
                    null,
                    null,
                    ['category' => $category]
                );
            }
            
            return true;
            
        } catch (Exception $e) {
            throw new Exception("فشل في إعادة تعيين الإعدادات: " . $e->getMessage());
        }
    }
}
?>
