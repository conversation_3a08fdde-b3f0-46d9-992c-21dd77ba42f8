<?php
session_start();

// تضمين الملفات المطلوبة
require_once '../includes/Security.php';
require_once '../includes/PermissionManager.php';
require_once '../includes/SettingsManager.php';
require_once '../includes/MenuManager.php';

// التحقق من تسجيل الدخول والصلاحيات
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php?redirect=' . urlencode($_SERVER['REQUEST_URI']));
    exit;
}

try {
    $pdo = new PDO("mysql:host=localhost;dbname=shahid_platform;charset=utf8mb4", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $security = new Security($pdo);
    $permissionManager = new PermissionManager($pdo, $security);
    $settingsManager = new SettingsManager($pdo, $security);
    $menuManager = new MenuManager($pdo, $security);
    
    // التحقق من صلاحية الوصول للوحة الإدارة
    if (!$security->hasPermission($_SESSION['user_id'], 'admin.access')) {
        header('Location: ../index.php?error=access_denied');
        exit;
    }
    
    // الحصول على معلومات المستخدم
    $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // الحصول على إحصائيات سريعة
    $stats = [
        'users' => $pdo->query("SELECT COUNT(*) FROM users")->fetchColumn(),
        'movies' => $pdo->query("SELECT COUNT(*) FROM movies")->fetchColumn(),
        'series' => $pdo->query("SELECT COUNT(*) FROM series")->fetchColumn(),
        'episodes' => $pdo->query("SELECT COUNT(*) FROM episodes")->fetchColumn(),
        'categories' => $pdo->query("SELECT COUNT(*) FROM categories")->fetchColumn(),
        'active_users' => $pdo->query("SELECT COUNT(*) FROM users WHERE is_active = 1")->fetchColumn(),
        'total_views' => $pdo->query("SELECT SUM(views) FROM movies")->fetchColumn() + 
                        $pdo->query("SELECT SUM(views) FROM series")->fetchColumn(),
        'recent_activities' => $pdo->query("SELECT COUNT(*) FROM activity_logs WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)")->fetchColumn()
    ];
    
    // الحصول على النشاطات الأخيرة
    $recentActivities = $pdo->prepare("
        SELECT al.*, u.full_name, u.username 
        FROM activity_logs al 
        LEFT JOIN users u ON al.user_id = u.id 
        ORDER BY al.created_at DESC 
        LIMIT 10
    ");
    $recentActivities->execute();
    $activities = $recentActivities->fetchAll(PDO::FETCH_ASSOC);
    
    // الحصول على المستخدمين الجدد
    $newUsers = $pdo->query("
        SELECT username, full_name, email, created_at 
        FROM users 
        ORDER BY created_at DESC 
        LIMIT 5
    ")->fetchAll(PDO::FETCH_ASSOC);
    
    // الحصول على المحتوى الأكثر مشاهدة
    $popularContent = $pdo->query("
        (SELECT 'movie' as type, title, views FROM movies ORDER BY views DESC LIMIT 5)
        UNION ALL
        (SELECT 'series' as type, title, views FROM series ORDER BY views DESC LIMIT 5)
        ORDER BY views DESC
        LIMIT 10
    ")->fetchAll(PDO::FETCH_ASSOC);
    
} catch (Exception $e) {
    die("خطأ في قاعدة البيانات: " . $e->getMessage());
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة الإدارة الديناميكية - Shahid Platform</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', 'Cairo', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #2d2d2d 100%);
            color: #fff;
            min-height: 100vh;
        }
        
        .admin-container {
            display: flex;
            min-height: 100vh;
        }
        
        .sidebar {
            width: 280px;
            background: rgba(47, 47, 47, 0.95);
            border-left: 2px solid rgba(229, 9, 20, 0.3);
            padding: 2rem 0;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
            z-index: 1000;
        }
        
        .sidebar-header {
            padding: 0 2rem 2rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            margin-bottom: 2rem;
        }
        
        .logo {
            color: #E50914;
            font-size: 1.8rem;
            font-weight: bold;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .user-info {
            margin-top: 1rem;
            padding: 1rem;
            background: rgba(229, 9, 20, 0.1);
            border-radius: 10px;
            border: 1px solid rgba(229, 9, 20, 0.2);
        }
        
        .user-info h4 {
            color: #E50914;
            margin-bottom: 0.5rem;
        }
        
        .user-info p {
            color: #ccc;
            font-size: 0.9rem;
        }
        
        .sidebar-menu {
            list-style: none;
            padding: 0 1rem;
        }
        
        .sidebar-menu li {
            margin-bottom: 0.5rem;
        }
        
        .sidebar-menu a {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            color: #ccc;
            text-decoration: none;
            border-radius: 10px;
            transition: all 0.3s ease;
        }
        
        .sidebar-menu a:hover,
        .sidebar-menu a.active {
            background: rgba(229, 9, 20, 0.2);
            color: #fff;
            transform: translateX(-5px);
        }
        
        .sidebar-menu i {
            width: 20px;
            text-align: center;
            font-size: 1.1rem;
        }
        
        .main-content {
            flex: 1;
            margin-right: 280px;
            padding: 2rem;
        }
        
        .header {
            background: rgba(47, 47, 47, 0.8);
            padding: 1.5rem 2rem;
            border-radius: 15px;
            margin-bottom: 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border: 2px solid rgba(229, 9, 20, 0.1);
        }
        
        .header h1 {
            color: #E50914;
            font-size: 2rem;
        }
        
        .header-actions {
            display: flex;
            gap: 1rem;
            align-items: center;
        }
        
        .btn {
            padding: 0.75rem 1.5rem;
            background: linear-gradient(45deg, #E50914, #B8070F);
            color: white;
            border: none;
            border-radius: 8px;
            text-decoration: none;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(229, 9, 20, 0.3);
        }
        
        .btn-secondary {
            background: linear-gradient(45deg, #2196F3, #1976D2);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: rgba(47, 47, 47, 0.8);
            padding: 2rem;
            border-radius: 15px;
            border: 2px solid rgba(229, 9, 20, 0.1);
            transition: all 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
            border-color: rgba(229, 9, 20, 0.3);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
        }
        
        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }
        
        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: #fff;
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: #E50914;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            color: #ccc;
            font-size: 1.1rem;
        }
        
        .content-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 2rem;
            margin-bottom: 2rem;
        }
        
        .content-card {
            background: rgba(47, 47, 47, 0.8);
            border-radius: 15px;
            border: 2px solid rgba(229, 9, 20, 0.1);
            overflow: hidden;
        }
        
        .card-header {
            padding: 1.5rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            background: rgba(229, 9, 20, 0.1);
        }
        
        .card-header h3 {
            color: #E50914;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .card-body {
            padding: 1.5rem;
            max-height: 400px;
            overflow-y: auto;
        }
        
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }
            
            .sidebar.open {
                transform: translateX(0);
            }
            
            .main-content {
                margin-right: 0;
                padding: 1rem;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .content-grid {
                grid-template-columns: 1fr;
            }
            
            .header {
                flex-direction: column;
                gap: 1rem;
                text-align: center;
            }
        }
        
        .mobile-menu-btn {
            display: none;
            position: fixed;
            top: 1rem;
            right: 1rem;
            z-index: 1001;
            background: #E50914;
            color: white;
            border: none;
            padding: 1rem;
            border-radius: 50%;
            cursor: pointer;
            font-size: 1.2rem;
        }
        
        @media (max-width: 768px) {
            .mobile-menu-btn {
                display: block;
            }
        }
        
        .success-message {
            background: rgba(76, 175, 80, 0.1);
            border: 1px solid rgba(76, 175, 80, 0.3);
            color: #4CAF50;
            padding: 1rem;
            border-radius: 10px;
            margin-bottom: 2rem;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <button class="mobile-menu-btn" onclick="toggleSidebar()">
            <i class="fas fa-bars"></i>
        </button>
        
        <aside class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <a href="../index.php" class="logo">
                    <i class="fas fa-play-circle"></i>
                    شاهد
                </a>
                
                <div class="user-info">
                    <h4><?php echo htmlspecialchars($user['full_name'] ?? $user['username']); ?></h4>
                    <p><?php echo htmlspecialchars($user['email']); ?></p>
                </div>
            </div>
            
            <ul class="sidebar-menu">
                <li><a href="dynamic_dashboard.php" class="active"><i class="fas fa-tachometer-alt"></i> لوحة التحكم الديناميكية</a></li>
                
                <?php if ($security->hasPermission($_SESSION['user_id'], 'users.view')): ?>
                <li><a href="dynamic_users.php"><i class="fas fa-users"></i> إدارة المستخدمين</a></li>
                <?php endif; ?>
                
                <?php if ($security->hasPermission($_SESSION['user_id'], 'content.view')): ?>
                <li><a href="dynamic_content.php"><i class="fas fa-film"></i> إدارة المحتوى</a></li>
                <?php endif; ?>
                
                <?php if ($security->hasPermission($_SESSION['user_id'], 'categories.view')): ?>
                <li><a href="dynamic_categories.php"><i class="fas fa-folder"></i> التصنيفات</a></li>
                <?php endif; ?>
                
                <?php if ($security->hasPermission($_SESSION['user_id'], 'menus.view')): ?>
                <li><a href="dynamic_menus.php"><i class="fas fa-bars"></i> إدارة القوائم</a></li>
                <?php endif; ?>
                
                <?php if ($security->hasPermission($_SESSION['user_id'], 'pages.view')): ?>
                <li><a href="dynamic_pages.php"><i class="fas fa-file-alt"></i> الصفحات</a></li>
                <?php endif; ?>
                
                <?php if ($security->hasPermission($_SESSION['user_id'], 'media.view')): ?>
                <li><a href="dynamic_media.php"><i class="fas fa-images"></i> مكتبة الوسائط</a></li>
                <?php endif; ?>
                
                <?php if ($security->hasPermission($_SESSION['user_id'], 'themes.view')): ?>
                <li><a href="dynamic_themes.php"><i class="fas fa-palette"></i> القوالب</a></li>
                <?php endif; ?>
                
                <?php if ($security->hasPermission($_SESSION['user_id'], 'system.settings')): ?>
                <li><a href="dynamic_settings.php"><i class="fas fa-cog"></i> إعدادات النظام</a></li>
                <?php endif; ?>
                
                <?php if ($security->hasPermission($_SESSION['user_id'], 'roles.view')): ?>
                <li><a href="dynamic_roles.php"><i class="fas fa-user-shield"></i> الأدوار والصلاحيات</a></li>
                <?php endif; ?>
                
                <?php if ($security->hasPermission($_SESSION['user_id'], 'reports.view')): ?>
                <li><a href="dynamic_reports.php"><i class="fas fa-chart-bar"></i> التقارير</a></li>
                <?php endif; ?>
                
                <?php if ($security->hasPermission($_SESSION['user_id'], 'system.logs')): ?>
                <li><a href="dynamic_logs.php"><i class="fas fa-list-alt"></i> سجلات النظام</a></li>
                <?php endif; ?>
                
                <li><a href="../logout.php"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a></li>
            </ul>
        </aside>
        
        <main class="main-content">
            <div class="success-message">
                <h3>🎉 مرحباً بك في لوحة الإدارة الديناميكية الجديدة!</h3>
                <p>نظام إدارة متكامل مع أمان متقدم وتحكم كامل في جميع جوانب المنصة</p>
            </div>
            
            <div class="header">
                <h1><i class="fas fa-rocket"></i> لوحة التحكم الديناميكية</h1>
                <div class="header-actions">
                    <a href="../index.php" class="btn btn-secondary">
                        <i class="fas fa-eye"></i> عرض الموقع
                    </a>
                    <a href="dynamic_settings.php" class="btn">
                        <i class="fas fa-cog"></i> الإعدادات
                    </a>
                </div>
            </div>
            
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-icon" style="background: linear-gradient(45deg, #2196F3, #1976D2);">
                            <i class="fas fa-users"></i>
                        </div>
                    </div>
                    <div class="stat-number"><?php echo number_format($stats['users']); ?></div>
                    <div class="stat-label">إجمالي المستخدمين</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-icon" style="background: linear-gradient(45deg, #E50914, #B8070F);">
                            <i class="fas fa-film"></i>
                        </div>
                    </div>
                    <div class="stat-number"><?php echo number_format($stats['movies']); ?></div>
                    <div class="stat-label">الأفلام</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-icon" style="background: linear-gradient(45deg, #FF6B35, #FF5722);">
                            <i class="fas fa-tv"></i>
                        </div>
                    </div>
                    <div class="stat-number"><?php echo number_format($stats['series']); ?></div>
                    <div class="stat-label">المسلسلات</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-icon" style="background: linear-gradient(45deg, #4CAF50, #45a049);">
                            <i class="fas fa-play-circle"></i>
                        </div>
                    </div>
                    <div class="stat-number"><?php echo number_format($stats['episodes']); ?></div>
                    <div class="stat-label">الحلقات</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-icon" style="background: linear-gradient(45deg, #FF9800, #F57C00);">
                            <i class="fas fa-folder"></i>
                        </div>
                    </div>
                    <div class="stat-number"><?php echo number_format($stats['categories']); ?></div>
                    <div class="stat-label">التصنيفات</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-icon" style="background: linear-gradient(45deg, #9C27B0, #7B1FA2);">
                            <i class="fas fa-user-check"></i>
                        </div>
                    </div>
                    <div class="stat-number"><?php echo number_format($stats['active_users']); ?></div>
                    <div class="stat-label">المستخدمين النشطين</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-icon" style="background: linear-gradient(45deg, #00BCD4, #0097A7);">
                            <i class="fas fa-eye"></i>
                        </div>
                    </div>
                    <div class="stat-number"><?php echo number_format($stats['total_views']); ?></div>
                    <div class="stat-label">إجمالي المشاهدات</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-icon" style="background: linear-gradient(45deg, #607D8B, #455A64);">
                            <i class="fas fa-chart-line"></i>
                        </div>
                    </div>
                    <div class="stat-number"><?php echo number_format($stats['recent_activities']); ?></div>
                    <div class="stat-label">نشاطات اليوم</div>
                </div>
            </div>
            
            <div class="content-grid">
                <div class="content-card">
                    <div class="card-header">
                        <h3><i class="fas fa-history"></i> النشاطات الأخيرة</h3>
                    </div>
                    <div class="card-body">
                        <?php foreach ($activities as $activity): ?>
                            <div style="padding: 1rem; border-bottom: 1px solid rgba(255,255,255,0.1);">
                                <strong><?php echo htmlspecialchars($activity['description'] ?? $activity['action']); ?></strong><br>
                                <small style="color: #ccc;">
                                    بواسطة: <?php echo htmlspecialchars($activity['full_name'] ?? $activity['username'] ?? 'مجهول'); ?>
                                    • <?php echo date('Y-m-d H:i', strtotime($activity['created_at'])); ?>
                                </small>
                            </div>
                        <?php endforeach; ?>
                        
                        <?php if (empty($activities)): ?>
                            <p style="text-align: center; color: #ccc; padding: 2rem;">لا توجد نشاطات حديثة</p>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="content-card">
                    <div class="card-header">
                        <h3><i class="fas fa-user-plus"></i> مستخدمين جدد</h3>
                    </div>
                    <div class="card-body">
                        <?php foreach ($newUsers as $newUser): ?>
                            <div style="padding: 1rem; border-bottom: 1px solid rgba(255,255,255,0.1);">
                                <strong><?php echo htmlspecialchars($newUser['full_name'] ?? $newUser['username']); ?></strong><br>
                                <small style="color: #ccc;"><?php echo htmlspecialchars($newUser['email']); ?></small>
                            </div>
                        <?php endforeach; ?>
                        
                        <?php if (empty($newUsers)): ?>
                            <p style="text-align: center; color: #ccc; padding: 1rem;">لا يوجد مستخدمين جدد</p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </main>
    </div>
    
    <script>
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('open');
        }
        
        // إغلاق الشريط الجانبي عند النقر خارجه في الهاتف
        document.addEventListener('click', function(e) {
            const sidebar = document.getElementById('sidebar');
            const menuBtn = document.querySelector('.mobile-menu-btn');
            
            if (window.innerWidth <= 768 && 
                !sidebar.contains(e.target) && 
                !menuBtn.contains(e.target)) {
                sidebar.classList.remove('open');
            }
        });
        
        // تحديث الإحصائيات كل 30 ثانية
        setInterval(function() {
            // يمكن إضافة AJAX لتحديث الإحصائيات هنا
        }, 30000);
        
        console.log('🚀 لوحة الإدارة الديناميكية جاهزة!');
        console.log('✅ نظام الأمان المتقدم مفعل');
        console.log('✅ نظام الصلاحيات مفعل');
        console.log('✅ نظام الإعدادات الديناميكية مفعل');
        console.log('✅ نظام القوائم الديناميكية مفعل');
    </script>
</body>
</html>
