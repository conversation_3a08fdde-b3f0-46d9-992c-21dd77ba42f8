<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء حساب المدير - Shahid Platform</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', 'Cairo', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #2d2d2d 100%);
            color: #fff;
            line-height: 1.6;
            min-height: 100vh;
            padding: 2rem;
        }
        
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: rgba(47, 47, 47, 0.9);
            border-radius: 15px;
            padding: 2rem;
            border: 1px solid rgba(229, 9, 20, 0.2);
        }
        
        .header {
            text-align: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #E50914;
        }
        
        .header h1 {
            color: #E50914;
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }
        
        .status {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            border-left: 4px solid #E50914;
        }
        
        .success {
            border-left-color: #4CAF50;
            background: rgba(76, 175, 80, 0.1);
        }
        
        .error {
            border-left-color: #F44336;
            background: rgba(244, 67, 54, 0.1);
        }
        
        .warning {
            border-left-color: #FF9800;
            background: rgba(255, 152, 0, 0.1);
        }
        
        .btn {
            background: linear-gradient(45deg, #E50914, #B8070F);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1.1rem;
            font-weight: bold;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            margin: 0.5rem;
            width: 100%;
            text-align: center;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(229, 9, 20, 0.3);
            color: white;
        }
        
        .btn-secondary {
            background: linear-gradient(45deg, #555, #333);
        }
        
        .admin-info {
            background: rgba(33, 150, 243, 0.1);
            border: 1px solid #2196F3;
            border-radius: 10px;
            padding: 1.5rem;
            margin: 1.5rem 0;
        }
        
        .admin-info h3 {
            color: #2196F3;
            margin-bottom: 1rem;
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
            padding: 0.5rem 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .info-item:last-child {
            border-bottom: none;
        }
        
        .actions {
            display: grid;
            gap: 1rem;
            margin-top: 2rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>👤 إنشاء حساب المدير</h1>
            <p>إنشاء حساب المدير الرئيسي للمنصة</p>
        </div>

        <?php
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['create_admin'])) {
            try {
                // إعدادات قاعدة البيانات
                $host = 'localhost';
                $username = 'root';
                $password = '';
                $database = 'shahid_platform';
                
                // الاتصال بقاعدة البيانات
                $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password);
                $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                
                // التحقق من وجود جدول المستخدمين
                $stmt = $pdo->query("SHOW TABLES LIKE 'users'");
                if ($stmt->rowCount() == 0) {
                    // إنشاء جدول المستخدمين إذا لم يكن موجوداً
                    $pdo->exec("
                        CREATE TABLE users (
                            id INT AUTO_INCREMENT PRIMARY KEY,
                            name VARCHAR(255) NOT NULL,
                            email VARCHAR(255) UNIQUE NOT NULL,
                            password VARCHAR(255) NOT NULL,
                            role ENUM('user', 'admin') DEFAULT 'user',
                            subscription_type ENUM('free', 'basic', 'premium') DEFAULT 'free',
                            subscription_end DATETIME NULL,
                            profile_image VARCHAR(255) NULL,
                            status ENUM('active', 'banned', 'deleted') DEFAULT 'active',
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                        )
                    ");
                }
                
                // حذف حساب المدير القديم إذا كان موجوداً
                $pdo->exec("DELETE FROM users WHERE email = '<EMAIL>'");
                
                // إنشاء حساب المدير الجديد
                $adminPassword = password_hash('admin123', PASSWORD_DEFAULT);
                $stmt = $pdo->prepare("
                    INSERT INTO users (name, email, password, role, subscription_type, subscription_end, status, created_at) 
                    VALUES (?, ?, ?, 'admin', 'premium', DATE_ADD(NOW(), INTERVAL 10 YEAR), 'active', NOW())
                ");
                
                $stmt->execute([
                    'مدير النظام',
                    '<EMAIL>',
                    $adminPassword
                ]);
                
                echo '<div class="status success">';
                echo '<h3>🎉 تم إنشاء حساب المدير بنجاح!</h3>';
                echo '<p>يمكنك الآن تسجيل الدخول باستخدام البيانات أدناه.</p>';
                echo '</div>';
                
                $adminCreated = true;
                
            } catch (PDOException $e) {
                echo '<div class="status error">';
                echo '<h3>❌ خطأ في إنشاء حساب المدير</h3>';
                echo '<p>' . htmlspecialchars($e->getMessage()) . '</p>';
                
                if (strpos($e->getMessage(), 'Connection refused') !== false) {
                    echo '<h4>الحل:</h4>';
                    echo '<ul>';
                    echo '<li>تأكد من تشغيل MySQL في XAMPP</li>';
                    echo '<li>تأكد من أن المنفذ 3306 متاح</li>';
                    echo '</ul>';
                } elseif (strpos($e->getMessage(), 'Unknown database') !== false) {
                    echo '<h4>الحل:</h4>';
                    echo '<ul>';
                    echo '<li>قم بإنشاء قاعدة البيانات أولاً</li>';
                    echo '<li>استخدم رابط "إصلاح قاعدة البيانات" من الصفحة الرئيسية</li>';
                    echo '</ul>';
                }
                
                echo '</div>';
                $adminCreated = false;
            }
        } else {
            // التحقق من حالة قاعدة البيانات
            try {
                $host = 'localhost';
                $username = 'root';
                $password = '';
                $database = 'shahid_platform';
                
                $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password);
                $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                
                // التحقق من وجود حساب المدير
                $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE role = 'admin' AND email = '<EMAIL>'");
                $stmt->execute();
                $adminExists = $stmt->fetchColumn() > 0;
                
                if ($adminExists) {
                    echo '<div class="status success">';
                    echo '<h3>✅ حساب المدير موجود بالفعل</h3>';
                    echo '<p>يمكنك تسجيل الدخول مباشرة.</p>';
                    echo '</div>';
                    $adminCreated = true;
                } else {
                    echo '<div class="status warning">';
                    echo '<h3>⚠️ حساب المدير غير موجود</h3>';
                    echo '<p>اضغط على الزر أدناه لإنشاء حساب المدير.</p>';
                    echo '</div>';
                    $adminCreated = false;
                }
                
            } catch (PDOException $e) {
                echo '<div class="status error">';
                echo '<h3>❌ خطأ في الاتصال بقاعدة البيانات</h3>';
                echo '<p>' . htmlspecialchars($e->getMessage()) . '</p>';
                echo '<p><strong>يجب إصلاح قاعدة البيانات أولاً.</strong></p>';
                echo '</div>';
                $adminCreated = false;
            }
        }
        ?>

        <div class="admin-info">
            <h3>📋 بيانات حساب المدير</h3>
            <div class="info-item">
                <span><strong>البريد الإلكتروني:</strong></span>
                <span><EMAIL></span>
            </div>
            <div class="info-item">
                <span><strong>كلمة المرور:</strong></span>
                <span>admin123</span>
            </div>
            <div class="info-item">
                <span><strong>الصلاحيات:</strong></span>
                <span>مدير كامل</span>
            </div>
            <div class="info-item">
                <span><strong>نوع الاشتراك:</strong></span>
                <span>مميز (10 سنوات)</span>
            </div>
        </div>

        <div class="actions">
            <?php if (!isset($adminCreated) || !$adminCreated): ?>
                <form method="POST">
                    <button type="submit" name="create_admin" class="btn">👤 إنشاء حساب المدير</button>
                </form>
            <?php else: ?>
                <a href="admin/dashboard.php" class="btn">🎛️ دخول لوحة الإدارة</a>
            <?php endif; ?>
            
            <a href="fix_database.php" class="btn btn-secondary">🔧 إصلاح قاعدة البيانات الكاملة</a>
            <a href="homepage.php" class="btn btn-secondary">🏠 العودة للرئيسية</a>
        </div>

        <div style="text-align: center; margin-top: 2rem; padding-top: 1rem; border-top: 1px solid rgba(255, 255, 255, 0.1);">
            <p style="color: #666; font-size: 0.9rem;">
                💡 <strong>نصيحة:</strong> إذا كانت قاعدة البيانات غير موجودة، استخدم "إصلاح قاعدة البيانات الكاملة" أولاً
            </p>
        </div>
    </div>

    <script>
        console.log('👤 Admin Creation Page loaded successfully!');
        
        // Auto-redirect to dashboard if admin created successfully
        <?php if (isset($adminCreated) && $adminCreated && $_SERVER['REQUEST_METHOD'] === 'POST'): ?>
        setTimeout(() => {
            if (confirm('تم إنشاء حساب المدير بنجاح! هل تريد الانتقال إلى لوحة الإدارة؟')) {
                window.location.href = 'admin/dashboard.php';
            }
        }, 2000);
        <?php endif; ?>
    </script>
</body>
</html>
