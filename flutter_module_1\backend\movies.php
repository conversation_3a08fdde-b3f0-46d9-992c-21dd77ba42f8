<?php
session_start();

try {
    $pdo = new PDO("mysql:host=localhost;dbname=shahid_platform;charset=utf8mb4", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // معاملات البحث والتصفية
    $search = $_GET['search'] ?? '';
    $category = $_GET['category'] ?? '';
    $sort = $_GET['sort'] ?? 'latest';
    $page = max(1, intval($_GET['page'] ?? 1));
    $limit = 12;
    $offset = ($page - 1) * $limit;
    
    // بناء استعلام البحث
    $whereConditions = ["status = 'active'"];
    $params = [];
    
    if (!empty($search)) {
        $whereConditions[] = "(title LIKE ? OR title_en LIKE ? OR description LIKE ?)";
        $searchTerm = "%$search%";
        $params[] = $searchTerm;
        $params[] = $searchTerm;
        $params[] = $searchTerm;
    }
    
    if (!empty($category)) {
        $whereConditions[] = "category_id = ?";
        $params[] = $category;
    }
    
    $whereClause = implode(' AND ', $whereConditions);
    
    // ترتيب النتائج
    $orderBy = match($sort) {
        'rating' => 'rating DESC',
        'views' => 'views DESC',
        'title' => 'title ASC',
        'oldest' => 'created_at ASC',
        default => 'created_at DESC'
    };
    
    // جلب الأفلام
    $sql = "SELECT m.*, c.name as category_name FROM movies m
            LEFT JOIN categories c ON m.category_id = c.id
            WHERE $whereClause ORDER BY $orderBy LIMIT $limit OFFSET $offset";
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $movies = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // عدد الأفلام الإجمالي
    $countSql = "SELECT COUNT(*) FROM movies WHERE $whereClause";
    $countStmt = $pdo->prepare($countSql);
    $countStmt->execute($params);
    $totalMovies = $countStmt->fetchColumn();
    $totalPages = ceil($totalMovies / $limit);
    
    // جلب التصنيفات
    $categoriesStmt = $pdo->query("SELECT * FROM categories WHERE status = 'active' ORDER BY sort_order, name");
    $categories = $categoriesStmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch (Exception $e) {
    $movies = [];
    $categories = [];
    $totalMovies = 0;
    $totalPages = 0;
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الأفلام - Shahid Platform</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', 'Cairo', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #2d2d2d 100%);
            color: #fff;
            min-height: 100vh;
        }
        
        .header {
            background: rgba(47, 47, 47, 0.95);
            padding: 1rem 0;
            border-bottom: 2px solid rgba(229, 9, 20, 0.3);
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }
        
        .nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }
        
        .logo {
            color: #E50914;
            font-size: 1.8rem;
            font-weight: bold;
            text-decoration: none;
        }
        
        .nav-links {
            display: flex;
            gap: 2rem;
            align-items: center;
        }
        
        .nav-links a {
            color: #fff;
            text-decoration: none;
            transition: color 0.3s ease;
        }
        
        .nav-links a:hover, .nav-links a.active {
            color: #E50914;
        }
        
        .filters {
            background: rgba(47, 47, 47, 0.8);
            padding: 2rem 0;
            margin-bottom: 2rem;
        }
        
        .filter-row {
            display: flex;
            gap: 1rem;
            align-items: center;
            flex-wrap: wrap;
            justify-content: center;
        }
        
        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }
        
        .filter-group label {
            color: #ccc;
            font-size: 0.9rem;
        }
        
        .filter-group select,
        .filter-group input {
            padding: 0.75rem;
            border: 2px solid rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.05);
            color: #fff;
            min-width: 150px;
        }
        
        .filter-group select:focus,
        .filter-group input:focus {
            outline: none;
            border-color: #E50914;
        }
        
        .btn {
            padding: 0.75rem 1.5rem;
            background: linear-gradient(45deg, #E50914, #B8070F);
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(229, 9, 20, 0.3);
        }
        
        .movies-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }
        
        .movie-card {
            background: rgba(47, 47, 47, 0.8);
            border-radius: 15px;
            overflow: hidden;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }
        
        .movie-card:hover {
            transform: translateY(-10px);
            border-color: rgba(229, 9, 20, 0.5);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.5);
        }
        
        .movie-poster {
            width: 100%;
            height: 350px;
            background: linear-gradient(45deg, #333, #555);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            color: #666;
            position: relative;
            overflow: hidden;
        }
        
        .movie-poster img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .movie-info {
            padding: 1.5rem;
        }
        
        .movie-title {
            font-size: 1.2rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
            color: #fff;
        }
        
        .movie-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            font-size: 0.9rem;
            color: #ccc;
        }
        
        .movie-rating {
            display: flex;
            align-items: center;
            gap: 0.25rem;
            color: #FFD700;
        }
        
        .movie-description {
            color: #ccc;
            font-size: 0.9rem;
            line-height: 1.4;
            margin-bottom: 1rem;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        
        .movie-actions {
            display: flex;
            gap: 0.5rem;
        }
        
        .btn-small {
            padding: 0.5rem 1rem;
            font-size: 0.9rem;
            flex: 1;
            text-align: center;
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 1rem;
            margin: 3rem 0;
        }
        
        .pagination a,
        .pagination span {
            padding: 0.75rem 1rem;
            background: rgba(47, 47, 47, 0.8);
            color: #fff;
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .pagination a:hover {
            background: #E50914;
        }
        
        .pagination .current {
            background: #E50914;
        }
        
        .stats {
            text-align: center;
            margin: 2rem 0;
            color: #ccc;
        }
        
        .no-results {
            text-align: center;
            padding: 4rem 2rem;
            color: #ccc;
        }
        
        .no-results h3 {
            color: #E50914;
            margin-bottom: 1rem;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 0 1rem;
            }
            
            .nav {
                flex-direction: column;
                gap: 1rem;
            }
            
            .filter-row {
                flex-direction: column;
                align-items: stretch;
            }
            
            .filter-group {
                width: 100%;
            }
            
            .movies-grid {
                grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
                gap: 1rem;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="container">
            <nav class="nav">
                <a href="index.php" class="logo">🎬 شاهد</a>
                <div class="nav-links">
                    <a href="index.php">الرئيسية</a>
                    <a href="movies.php" class="active">الأفلام</a>
                    <a href="series.php">المسلسلات</a>
                    <a href="search.php">البحث</a>
                    <?php if (isset($_SESSION['user_id'])): ?>
                        <a href="profile.php">الملف الشخصي</a>
                        <a href="favorites.php">المفضلة</a>
                        <a href="logout.php">تسجيل الخروج</a>
                    <?php else: ?>
                        <a href="login.php">تسجيل الدخول</a>
                        <a href="register.php">إنشاء حساب</a>
                    <?php endif; ?>
                </div>
            </nav>
        </div>
    </header>
    
    <section class="filters">
        <div class="container">
            <form method="GET" action="">
                <div class="filter-row">
                    <div class="filter-group">
                        <label>البحث</label>
                        <input type="text" name="search" value="<?php echo htmlspecialchars($search); ?>" 
                               placeholder="ابحث عن فيلم...">
                    </div>
                    
                    <div class="filter-group">
                        <label>التصنيف</label>
                        <select name="category">
                            <option value="">جميع التصنيفات</option>
                            <?php foreach ($categories as $cat): ?>
                                <option value="<?php echo $cat['id']; ?>" 
                                        <?php echo $category == $cat['id'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($cat['name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="filter-group">
                        <label>الترتيب</label>
                        <select name="sort">
                            <option value="latest" <?php echo $sort === 'latest' ? 'selected' : ''; ?>>الأحدث</option>
                            <option value="oldest" <?php echo $sort === 'oldest' ? 'selected' : ''; ?>>الأقدم</option>
                            <option value="rating" <?php echo $sort === 'rating' ? 'selected' : ''; ?>>الأعلى تقييماً</option>
                            <option value="views" <?php echo $sort === 'views' ? 'selected' : ''; ?>>الأكثر مشاهدة</option>
                            <option value="title" <?php echo $sort === 'title' ? 'selected' : ''; ?>>الاسم</option>
                        </select>
                    </div>
                    
                    <button type="submit" class="btn">🔍 بحث</button>
                </div>
            </form>
        </div>
    </section>
    
    <main class="container">
        <div class="stats">
            <p>عرض <?php echo count($movies); ?> من أصل <?php echo $totalMovies; ?> فيلم</p>
        </div>
        
        <?php if (empty($movies)): ?>
            <div class="no-results">
                <h3>🎬 لا توجد أفلام</h3>
                <p>لم يتم العثور على أفلام تطابق معايير البحث</p>
                <a href="movies.php" class="btn" style="margin-top: 1rem;">عرض جميع الأفلام</a>
            </div>
        <?php else: ?>
            <div class="movies-grid">
                <?php foreach ($movies as $movie): ?>
                    <div class="movie-card">
                        <div class="movie-poster">
                            <?php if (!empty($movie['poster'])): ?>
                                <img src="<?php echo htmlspecialchars($movie['poster']); ?>" 
                                     alt="<?php echo htmlspecialchars($movie['title']); ?>">
                            <?php else: ?>
                                🎬
                            <?php endif; ?>
                        </div>
                        
                        <div class="movie-info">
                            <h3 class="movie-title"><?php echo htmlspecialchars($movie['title']); ?></h3>
                            
                            <div class="movie-meta">
                                <span><?php echo $movie['release_year'] ?? 'غير محدد'; ?></span>
                                <div class="movie-rating">
                                    <span>⭐</span>
                                    <span><?php echo number_format($movie['rating'], 1); ?></span>
                                </div>
                            </div>
                            
                            <?php if (!empty($movie['description'])): ?>
                                <p class="movie-description">
                                    <?php echo htmlspecialchars($movie['description']); ?>
                                </p>
                            <?php endif; ?>
                            
                            <div class="movie-actions">
                                <a href="watch.php?type=movie&id=<?php echo $movie['id']; ?>" class="btn btn-small">
                                    ▶️ مشاهدة
                                </a>
                                <a href="movie_details.php?id=<?php echo $movie['id']; ?>" class="btn btn-small">
                                    📋 التفاصيل
                                </a>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
            
            <?php if ($totalPages > 1): ?>
                <div class="pagination">
                    <?php if ($page > 1): ?>
                        <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>">
                            ← السابق
                        </a>
                    <?php endif; ?>
                    
                    <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                        <?php if ($i === $page): ?>
                            <span class="current"><?php echo $i; ?></span>
                        <?php else: ?>
                            <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>">
                                <?php echo $i; ?>
                            </a>
                        <?php endif; ?>
                    <?php endfor; ?>
                    
                    <?php if ($page < $totalPages): ?>
                        <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>">
                            التالي →
                        </a>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        <?php endif; ?>
    </main>
    
    <script>
        // تحديث تلقائي للنموذج عند تغيير الفلاتر
        document.querySelectorAll('select[name="category"], select[name="sort"]').forEach(select => {
            select.addEventListener('change', function() {
                this.form.submit();
            });
        });
        
        // تأثيرات تفاعلية للبطاقات
        document.querySelectorAll('.movie-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-10px) scale(1.02)';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });
    </script>
</body>
</html>
