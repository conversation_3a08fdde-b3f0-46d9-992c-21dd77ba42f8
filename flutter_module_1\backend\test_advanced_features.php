<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الميزات المتقدمة - Shahid</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #141414, #2F2F2F);
            color: white;
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        h1 {
            text-align: center;
            color: #E50914;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .feature-card {
            background: rgba(47, 47, 47, 0.9);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }
        
        .feature-card:hover {
            border-color: #E50914;
            transform: translateY(-5px);
        }
        
        .feature-card h3 {
            color: #E50914;
            margin-bottom: 15px;
            font-size: 1.5em;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .feature-card .icon {
            font-size: 1.8em;
        }
        
        .test-section {
            background: rgba(20, 20, 20, 0.8);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
        }
        
        .test-section h4 {
            color: #fff;
            margin-bottom: 10px;
            font-size: 1.2em;
        }
        
        .btn {
            background: linear-gradient(45deg, #E50914, #B8070F);
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            transition: all 0.3s ease;
            margin: 5px;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(229, 9, 20, 0.4);
        }
        
        .btn.secondary {
            background: linear-gradient(45deg, #555, #333);
        }
        
        .result {
            background: rgba(0, 0, 0, 0.5);
            border-radius: 8px;
            padding: 15px;
            margin-top: 10px;
            border-left: 4px solid #E50914;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            display: none;
        }
        
        .result.show {
            display: block;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 8px;
        }
        
        .status-indicator.online {
            background: #4CAF50;
            box-shadow: 0 0 10px #4CAF50;
        }
        
        .status-indicator.offline {
            background: #f44336;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        
        .stat-card {
            background: rgba(229, 9, 20, 0.1);
            border: 1px solid #E50914;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
        }
        
        .stat-card .number {
            font-size: 2em;
            font-weight: bold;
            color: #E50914;
            display: block;
        }
        
        .stat-card .label {
            color: #ccc;
            margin-top: 5px;
        }
        
        .input-group {
            margin: 10px 0;
        }
        
        .input-group label {
            display: block;
            margin-bottom: 5px;
            color: #ccc;
        }
        
        .input-group input, .input-group select, .input-group textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #555;
            border-radius: 5px;
            background: #333;
            color: white;
        }
        
        .input-group textarea {
            height: 80px;
            resize: vertical;
        }
        
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #E50914;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .success { color: #4CAF50; }
        .error { color: #f44336; }
        .warning { color: #ff9800; }
        .info { color: #2196F3; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 اختبار الميزات المتقدمة</h1>
        
        <div class="features-grid">
            <!-- نظام المفضلة -->
            <div class="feature-card">
                <h3><span class="icon">❤️</span>نظام المفضلة</h3>
                
                <div class="test-section">
                    <h4>إضافة/إزالة من المفضلة</h4>
                    <div class="input-group">
                        <label>معرف المحتوى:</label>
                        <input type="number" id="favoriteContentId" value="1" min="1">
                    </div>
                    <div class="input-group">
                        <label>نوع المحتوى:</label>
                        <select id="favoriteContentType">
                            <option value="movie">فيلم</option>
                            <option value="series">مسلسل</option>
                        </select>
                    </div>
                    <button class="btn" onclick="toggleFavorite()">تبديل المفضلة</button>
                    <button class="btn secondary" onclick="checkFavorite()">فحص المفضلة</button>
                    <div class="result" id="favoriteResult"></div>
                </div>
                
                <div class="test-section">
                    <h4>عرض قائمة المفضلة</h4>
                    <button class="btn" onclick="getFavorites()">عرض المفضلة</button>
                    <button class="btn secondary" onclick="getFavorites('movie')">الأفلام المفضلة</button>
                    <button class="btn secondary" onclick="getFavorites('series')">المسلسلات المفضلة</button>
                    <div class="result" id="favoritesListResult"></div>
                </div>
            </div>
            
            <!-- سجل المشاهدة -->
            <div class="feature-card">
                <h3><span class="icon">📺</span>سجل المشاهدة</h3>
                
                <div class="test-section">
                    <h4>تحديث تقدم المشاهدة</h4>
                    <div class="input-group">
                        <label>معرف المحتوى:</label>
                        <input type="number" id="watchContentId" value="1" min="1">
                    </div>
                    <div class="input-group">
                        <label>وقت المشاهدة (ثانية):</label>
                        <input type="number" id="watchTime" value="1800" min="0">
                    </div>
                    <div class="input-group">
                        <label>المدة الإجمالية (ثانية):</label>
                        <input type="number" id="totalTime" value="7200" min="1">
                    </div>
                    <button class="btn" onclick="updateWatchProgress()">تحديث التقدم</button>
                    <div class="result" id="watchProgressResult"></div>
                </div>
                
                <div class="test-section">
                    <h4>عرض سجل المشاهدة</h4>
                    <button class="btn" onclick="getWatchHistory()">سجل المشاهدة</button>
                    <button class="btn secondary" onclick="getContinueWatching()">متابعة المشاهدة</button>
                    <div class="result" id="watchHistoryResult"></div>
                </div>
            </div>
            
            <!-- نظام التقييمات -->
            <div class="feature-card">
                <h3><span class="icon">⭐</span>نظام التقييمات</h3>
                
                <div class="test-section">
                    <h4>إضافة تقييم</h4>
                    <div class="input-group">
                        <label>معرف المحتوى:</label>
                        <input type="number" id="ratingContentId" value="1" min="1">
                    </div>
                    <div class="input-group">
                        <label>التقييم (1-10):</label>
                        <input type="number" id="ratingValue" value="8" min="1" max="10" step="0.1">
                    </div>
                    <div class="input-group">
                        <label>التعليق:</label>
                        <textarea id="ratingComment" placeholder="اكتب تعليقك هنا...">فيلم رائع جداً!</textarea>
                    </div>
                    <button class="btn" onclick="addRating()">إضافة تقييم</button>
                    <div class="result" id="addRatingResult"></div>
                </div>
                
                <div class="test-section">
                    <h4>عرض التقييمات</h4>
                    <button class="btn" onclick="getRatings()">عرض التقييمات</button>
                    <button class="btn secondary" onclick="getTopRated('movie')">أفضل الأفلام</button>
                    <button class="btn secondary" onclick="getTopRated('series')">أفضل المسلسلات</button>
                    <div class="result" id="ratingsResult"></div>
                </div>
            </div>
            
            <!-- إحصائيات المستخدم -->
            <div class="feature-card">
                <h3><span class="icon">📊</span>إحصائيات المستخدم</h3>
                
                <div class="test-section">
                    <h4>إحصائيات شاملة</h4>
                    <button class="btn" onclick="getUserStats()">إحصائيات المستخدم</button>
                    <button class="btn secondary" onclick="getSystemStats()">إحصائيات النظام</button>
                    <div class="result" id="statsResult"></div>
                </div>
                
                <div class="stats-grid" id="statsGrid" style="display: none;">
                    <!-- سيتم ملؤها ديناميكياً -->
                </div>
            </div>
            
            <!-- نظام رفع الملفات -->
            <div class="feature-card">
                <h3><span class="icon">📁</span>نظام رفع الملفات</h3>
                
                <div class="test-section">
                    <h4>حالة نظام الرفع</h4>
                    <button class="btn" onclick="checkUploadStatus()">فحص حالة الرفع</button>
                    <a href="upload_handler.php" target="_blank" class="btn secondary">صفحة الرفع</a>
                    <div class="result" id="uploadStatusResult"></div>
                </div>
            </div>
            
            <!-- اختبار الأمان -->
            <div class="feature-card">
                <h3><span class="icon">🔒</span>نظام الأمان</h3>
                
                <div class="test-section">
                    <h4>اختبار الحماية</h4>
                    <button class="btn" onclick="testSecurity()">اختبار الأمان</button>
                    <button class="btn secondary" onclick="testRateLimit()">اختبار Rate Limiting</button>
                    <div class="result" id="securityResult"></div>
                </div>
            </div>
        </div>
        
        <!-- حالة الاتصال -->
        <div class="feature-card">
            <h3>
                <span class="icon">🌐</span>
                حالة الاتصال
                <span class="status-indicator online" id="connectionStatus"></span>
            </h3>
            <p>جميع الأنظمة تعمل بشكل طبيعي</p>
            <button class="btn" onclick="testAllEndpoints()">اختبار جميع النقاط</button>
            <div class="result" id="connectionResult"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'api/advanced.php';
        const USER_ID = 1; // معرف المستخدم للاختبار
        
        // دالة عامة لإرسال طلبات API
        async function apiRequest(endpoint, method = 'GET', data = null) {
            try {
                const url = `${API_BASE}?endpoint=${endpoint}`;
                const options = {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json',
                    }
                };
                
                if (data && method === 'POST') {
                    options.body = JSON.stringify(data);
                }
                
                const response = await fetch(url, options);
                return await response.json();
            } catch (error) {
                return {
                    success: false,
                    error: 'Network error: ' + error.message
                };
            }
        }
        
        // عرض النتائج
        function showResult(elementId, result) {
            const element = document.getElementById(elementId);
            element.innerHTML = `<pre>${JSON.stringify(result, null, 2)}</pre>`;
            element.classList.add('show');
            
            // تلوين النتيجة حسب النجاح
            if (result.success) {
                element.style.borderLeftColor = '#4CAF50';
            } else {
                element.style.borderLeftColor = '#f44336';
            }
        }
        
        // ===== وظائف المفضلة =====
        async function toggleFavorite() {
            const contentId = document.getElementById('favoriteContentId').value;
            const contentType = document.getElementById('favoriteContentType').value;
            
            const result = await apiRequest('toggle_favorite', 'POST', {
                user_id: USER_ID,
                content_id: parseInt(contentId),
                content_type: contentType
            });
            
            showResult('favoriteResult', result);
        }
        
        async function checkFavorite() {
            const contentId = document.getElementById('favoriteContentId').value;
            const contentType = document.getElementById('favoriteContentType').value;
            
            const result = await apiRequest(`is_favorite&user_id=${USER_ID}&content_id=${contentId}&content_type=${contentType}`);
            showResult('favoriteResult', result);
        }
        
        async function getFavorites(contentType = null) {
            let url = `favorites&user_id=${USER_ID}`;
            if (contentType) {
                url += `&content_type=${contentType}`;
            }
            
            const result = await apiRequest(url);
            showResult('favoritesListResult', result);
        }
        
        // ===== وظائف سجل المشاهدة =====
        async function updateWatchProgress() {
            const contentId = document.getElementById('watchContentId').value;
            const watchTime = document.getElementById('watchTime').value;
            const totalTime = document.getElementById('totalTime').value;
            
            const result = await apiRequest('update_watch_progress', 'POST', {
                user_id: USER_ID,
                content_id: parseInt(contentId),
                content_type: 'movie',
                watch_time: parseInt(watchTime),
                total_time: parseInt(totalTime)
            });
            
            showResult('watchProgressResult', result);
        }
        
        async function getWatchHistory() {
            const result = await apiRequest(`watch_history&user_id=${USER_ID}`);
            showResult('watchHistoryResult', result);
        }
        
        async function getContinueWatching() {
            const result = await apiRequest(`continue_watching&user_id=${USER_ID}`);
            showResult('watchHistoryResult', result);
        }
        
        // ===== وظائف التقييمات =====
        async function addRating() {
            const contentId = document.getElementById('ratingContentId').value;
            const rating = document.getElementById('ratingValue').value;
            const comment = document.getElementById('ratingComment').value;
            
            const result = await apiRequest('add_rating', 'POST', {
                user_id: USER_ID,
                content_id: parseInt(contentId),
                content_type: 'movie',
                rating: parseFloat(rating),
                comment: comment
            });
            
            showResult('addRatingResult', result);
        }
        
        async function getRatings() {
            const contentId = document.getElementById('ratingContentId').value;
            const result = await apiRequest(`ratings&content_id=${contentId}&content_type=movie`);
            showResult('ratingsResult', result);
        }
        
        async function getTopRated(contentType) {
            const result = await apiRequest(`top_rated&content_type=${contentType}&limit=5`);
            showResult('ratingsResult', result);
        }
        
        // ===== وظائف الإحصائيات =====
        async function getUserStats() {
            const result = await apiRequest(`user_stats&user_id=${USER_ID}`);
            showResult('statsResult', result);
            
            if (result.success) {
                displayStatsGrid(result.data);
            }
        }
        
        async function getSystemStats() {
            const result = await apiRequest('system_stats');
            showResult('statsResult', result);
            
            if (result.success) {
                displaySystemStatsGrid(result.data);
            }
        }
        
        function displayStatsGrid(stats) {
            const grid = document.getElementById('statsGrid');
            grid.innerHTML = `
                <div class="stat-card">
                    <span class="number">${stats.total_items}</span>
                    <div class="label">عناصر مشاهدة</div>
                </div>
                <div class="stat-card">
                    <span class="number">${stats.completed_items}</span>
                    <div class="label">عناصر مكتملة</div>
                </div>
                <div class="stat-card">
                    <span class="number">${stats.total_favorites}</span>
                    <div class="label">المفضلة</div>
                </div>
                <div class="stat-card">
                    <span class="number">${stats.total_watch_time}</span>
                    <div class="label">وقت المشاهدة</div>
                </div>
            `;
            grid.style.display = 'grid';
        }
        
        function displaySystemStatsGrid(stats) {
            const grid = document.getElementById('statsGrid');
            grid.innerHTML = `
                <div class="stat-card">
                    <span class="number">${stats.content.movies}</span>
                    <div class="label">أفلام</div>
                </div>
                <div class="stat-card">
                    <span class="number">${stats.content.series}</span>
                    <div class="label">مسلسلات</div>
                </div>
                <div class="stat-card">
                    <span class="number">${stats.content.episodes}</span>
                    <div class="label">حلقات</div>
                </div>
                <div class="stat-card">
                    <span class="number">${stats.engagement.total_ratings}</span>
                    <div class="label">تقييمات</div>
                </div>
            `;
            grid.style.display = 'grid';
        }
        
        // ===== وظائف أخرى =====
        async function checkUploadStatus() {
            const result = await apiRequest('upload_status');
            showResult('uploadStatusResult', result);
        }
        
        async function testSecurity() {
            // محاكاة اختبار الأمان
            const result = {
                success: true,
                message: 'Security systems operational',
                tests: {
                    'CSRF Protection': 'Active',
                    'XSS Protection': 'Active',
                    'SQL Injection Protection': 'Active',
                    'Rate Limiting': 'Active',
                    'Session Security': 'Active'
                }
            };
            showResult('securityResult', result);
        }
        
        async function testRateLimit() {
            // اختبار Rate Limiting بإرسال طلبات متعددة
            const results = [];
            for (let i = 0; i < 5; i++) {
                const result = await apiRequest('system_stats');
                results.push(`Request ${i + 1}: ${result.success ? 'Success' : 'Failed'}`);
            }
            
            showResult('securityResult', {
                success: true,
                message: 'Rate limiting test completed',
                results: results
            });
        }
        
        async function testAllEndpoints() {
            const endpoints = [
                'favorites',
                'watch_history',
                'user_stats',
                'system_stats',
                'upload_status'
            ];
            
            const results = {};
            
            for (const endpoint of endpoints) {
                try {
                    const result = await apiRequest(`${endpoint}&user_id=${USER_ID}`);
                    results[endpoint] = result.success ? '✅ Working' : '❌ Failed';
                } catch (error) {
                    results[endpoint] = '❌ Error';
                }
            }
            
            showResult('connectionResult', {
                success: true,
                message: 'Endpoint connectivity test completed',
                results: results
            });
        }
        
        // تحديث حالة الاتصال
        setInterval(async () => {
            try {
                const result = await apiRequest('system_stats');
                const indicator = document.getElementById('connectionStatus');
                
                if (result.success) {
                    indicator.className = 'status-indicator online';
                } else {
                    indicator.className = 'status-indicator offline';
                }
            } catch (error) {
                document.getElementById('connectionStatus').className = 'status-indicator offline';
            }
        }, 30000); // كل 30 ثانية
    </script>
</body>
</html>
