<?php
/**
 * Movies Management - Shahid Admin Panel
 */

session_start();

// Check admin login
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: index.php');
    exit();
}

// Database connection
try {
    $config = include '../config/database.php';
    $pdo = new PDO("mysql:host={$config['host']};dbname={$config['name']};charset=utf8mb4", 
                   $config['username'], $config['password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (Exception $e) {
    die('خطأ في الاتصال بقاعدة البيانات: ' . $e->getMessage());
}

// Handle actions
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['add_movie'])) {
        // Add new movie
        $title = $_POST['title'] ?? '';
        $description = $_POST['description'] ?? '';
        $year = $_POST['year'] ?? '';
        $duration = $_POST['duration'] ?? '';
        $genre = $_POST['genre'] ?? '';
        $rating = $_POST['rating'] ?? '';
        $poster = $_POST['poster'] ?? '';
        $trailer = $_POST['trailer'] ?? '';
        
        if (!empty($title)) {
            try {
                $stmt = $pdo->prepare("INSERT INTO movies (title, description, year, duration, genre, rating, poster, trailer, status, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'published', NOW())");
                $stmt->execute([$title, $description, $year, $duration, $genre, $rating, $poster, $trailer]);
                $message = 'تم إضافة الفيلم بنجاح';
                $messageType = 'success';
            } catch (Exception $e) {
                $message = 'خطأ في إضافة الفيلم: ' . $e->getMessage();
                $messageType = 'error';
            }
        } else {
            $message = 'عنوان الفيلم مطلوب';
            $messageType = 'error';
        }
    } elseif (isset($_POST['delete_movie'])) {
        // Delete movie
        $movieId = $_POST['movie_id'] ?? '';
        if (!empty($movieId)) {
            try {
                $stmt = $pdo->prepare("DELETE FROM movies WHERE id = ?");
                $stmt->execute([$movieId]);
                $message = 'تم حذف الفيلم بنجاح';
                $messageType = 'success';
            } catch (Exception $e) {
                $message = 'خطأ في حذف الفيلم: ' . $e->getMessage();
                $messageType = 'error';
            }
        }
    }
}

// Get movies
$page = (int)($_GET['page'] ?? 1);
$limit = 10;
$offset = ($page - 1) * $limit;

try {
    $stmt = $pdo->prepare("SELECT * FROM movies ORDER BY created_at DESC LIMIT $limit OFFSET $offset");
    $stmt->execute();
    $movies = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get total count
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM movies");
    $total = $stmt->fetch()['total'];
    $totalPages = ceil($total / $limit);
} catch (Exception $e) {
    $movies = [];
    $total = 0;
    $totalPages = 0;
    $message = 'خطأ في جلب الأفلام: ' . $e->getMessage();
    $messageType = 'error';
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الأفلام - Shahid Admin</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            color: #fff;
            min-height: 100vh;
        }
        
        .header {
            background: #E50914;
            padding: 1rem 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .header h1 {
            font-size: 1.8rem;
        }
        
        .header a {
            color: #fff;
            text-decoration: none;
            padding: 0.5rem 1rem;
            border: 1px solid #fff;
            border-radius: 5px;
            transition: all 0.3s;
        }
        
        .header a:hover {
            background: #fff;
            color: #E50914;
        }
        
        .container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 2rem;
        }
        
        .message {
            padding: 1rem;
            border-radius: 5px;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .message.success {
            background: #28a745;
            color: #fff;
        }
        
        .message.error {
            background: #dc3545;
            color: #fff;
        }
        
        .add-form {
            background: #2d2d2d;
            padding: 2rem;
            border-radius: 10px;
            margin-bottom: 3rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }
        
        .add-form h2 {
            color: #E50914;
            margin-bottom: 2rem;
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin-bottom: 1rem;
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        .form-group.full-width {
            grid-column: 1 / -1;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: bold;
        }
        
        .form-group input,
        .form-group textarea {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #555;
            border-radius: 5px;
            background: #1a1a1a;
            color: #fff;
            font-size: 1rem;
        }
        
        .form-group textarea {
            height: 100px;
            resize: vertical;
        }
        
        .form-group input:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #E50914;
        }
        
        .btn {
            padding: 0.75rem 1.5rem;
            background: #E50914;
            color: #fff;
            border: none;
            border-radius: 5px;
            font-size: 1rem;
            cursor: pointer;
            transition: background 0.3s;
        }
        
        .btn:hover {
            background: #b8070f;
        }
        
        .btn.danger {
            background: #dc3545;
        }
        
        .btn.danger:hover {
            background: #c82333;
        }
        
        .movies-table {
            background: #2d2d2d;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }
        
        .table-header {
            background: #E50914;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .table-header h2 {
            font-size: 1.3rem;
        }
        
        .table-content {
            overflow-x: auto;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
        }
        
        th, td {
            padding: 1rem;
            text-align: right;
            border-bottom: 1px solid #555;
        }
        
        th {
            background: #3d3d3d;
            font-weight: bold;
        }
        
        tr:hover {
            background: #3d3d3d;
        }
        
        .poster-img {
            width: 50px;
            height: 75px;
            object-fit: cover;
            border-radius: 5px;
        }
        
        .actions {
            display: flex;
            gap: 0.5rem;
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 2rem;
            gap: 1rem;
        }
        
        .pagination a {
            color: #fff;
            text-decoration: none;
            padding: 0.5rem 1rem;
            border: 1px solid #555;
            border-radius: 5px;
            transition: all 0.3s;
        }
        
        .pagination a:hover,
        .pagination a.active {
            background: #E50914;
            border-color: #E50914;
        }
        
        @media (max-width: 768px) {
            .form-grid {
                grid-template-columns: 1fr;
            }
            
            .container {
                padding: 0 1rem;
            }
            
            .header {
                flex-direction: column;
                gap: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎬 إدارة الأفلام</h1>
        <a href="index.php">← العودة للوحة الرئيسية</a>
    </div>
    
    <div class="container">
        <?php if (!empty($message)): ?>
            <div class="message <?php echo $messageType; ?>">
                <?php echo htmlspecialchars($message); ?>
            </div>
        <?php endif; ?>
        
        <!-- Add Movie Form -->
        <div class="add-form">
            <h2>➕ إضافة فيلم جديد</h2>
            <form method="POST">
                <div class="form-grid">
                    <div class="form-group">
                        <label for="title">عنوان الفيلم *</label>
                        <input type="text" id="title" name="title" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="year">سنة الإنتاج</label>
                        <input type="number" id="year" name="year" min="1900" max="2030">
                    </div>
                    
                    <div class="form-group">
                        <label for="duration">المدة (بالدقائق)</label>
                        <input type="number" id="duration" name="duration" min="1">
                    </div>
                    
                    <div class="form-group">
                        <label for="genre">النوع</label>
                        <input type="text" id="genre" name="genre" placeholder="أكشن، دراما، كوميديا">
                    </div>
                    
                    <div class="form-group">
                        <label for="rating">التقييم</label>
                        <input type="number" id="rating" name="rating" min="0" max="10" step="0.1">
                    </div>
                    
                    <div class="form-group">
                        <label for="poster">رابط الملصق</label>
                        <input type="url" id="poster" name="poster" placeholder="https://example.com/poster.jpg">
                    </div>
                    
                    <div class="form-group full-width">
                        <label for="description">الوصف</label>
                        <textarea id="description" name="description" placeholder="وصف الفيلم..."></textarea>
                    </div>
                    
                    <div class="form-group full-width">
                        <label for="trailer">رابط الإعلان</label>
                        <input type="url" id="trailer" name="trailer" placeholder="https://youtube.com/watch?v=...">
                    </div>
                </div>
                
                <button type="submit" name="add_movie" class="btn">إضافة الفيلم</button>
            </form>
        </div>
        
        <!-- Movies Table -->
        <div class="movies-table">
            <div class="table-header">
                <h2>📋 قائمة الأفلام (<?php echo number_format($total); ?> فيلم)</h2>
            </div>
            
            <div class="table-content">
                <table>
                    <thead>
                        <tr>
                            <th>الملصق</th>
                            <th>العنوان</th>
                            <th>السنة</th>
                            <th>المدة</th>
                            <th>النوع</th>
                            <th>التقييم</th>
                            <th>تاريخ الإضافة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (!empty($movies)): ?>
                            <?php foreach ($movies as $movie): ?>
                                <tr>
                                    <td>
                                        <?php if (!empty($movie['poster'])): ?>
                                            <img src="<?php echo htmlspecialchars($movie['poster']); ?>" 
                                                 alt="<?php echo htmlspecialchars($movie['title']); ?>" 
                                                 class="poster-img">
                                        <?php else: ?>
                                            <div style="width: 50px; height: 75px; background: #555; border-radius: 5px; display: flex; align-items: center; justify-content: center; font-size: 0.8rem;">
                                                لا توجد صورة
                                            </div>
                                        <?php endif; ?>
                                    </td>
                                    <td><strong><?php echo htmlspecialchars($movie['title']); ?></strong></td>
                                    <td><?php echo htmlspecialchars($movie['year'] ?: 'غير محدد'); ?></td>
                                    <td><?php echo htmlspecialchars($movie['duration'] ? $movie['duration'] . ' دقيقة' : 'غير محدد'); ?></td>
                                    <td><?php echo htmlspecialchars($movie['genre'] ?: 'غير محدد'); ?></td>
                                    <td><?php echo htmlspecialchars($movie['rating'] ? $movie['rating'] . '/10' : 'غير مقيم'); ?></td>
                                    <td><?php echo date('Y-m-d', strtotime($movie['created_at'])); ?></td>
                                    <td>
                                        <div class="actions">
                                            <form method="POST" style="display: inline;" onsubmit="return confirm('هل أنت متأكد من حذف هذا الفيلم؟')">
                                                <input type="hidden" name="movie_id" value="<?php echo $movie['id']; ?>">
                                                <button type="submit" name="delete_movie" class="btn danger">حذف</button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="8" style="text-align: center; color: #ccc; padding: 2rem;">
                                    لا توجد أفلام. قم بإضافة فيلم جديد أعلاه.
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
        
        <!-- Pagination -->
        <?php if ($totalPages > 1): ?>
            <div class="pagination">
                <?php if ($page > 1): ?>
                    <a href="?page=<?php echo $page - 1; ?>">← السابق</a>
                <?php endif; ?>
                
                <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                    <a href="?page=<?php echo $i; ?>" <?php echo $i === $page ? 'class="active"' : ''; ?>>
                        <?php echo $i; ?>
                    </a>
                <?php endfor; ?>
                
                <?php if ($page < $totalPages): ?>
                    <a href="?page=<?php echo $page + 1; ?>">التالي →</a>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>
</body>
</html>
