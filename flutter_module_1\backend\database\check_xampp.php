<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فحص XAMPP - Shahid Platform</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', 'Cairo', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #2d2d2d 100%);
            color: #fff;
            line-height: 1.6;
            min-height: 100vh;
            padding: 2rem;
        }
        
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: rgba(47, 47, 47, 0.9);
            border-radius: 15px;
            padding: 2rem;
            border: 1px solid rgba(229, 9, 20, 0.2);
        }
        
        .header {
            text-align: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #E50914;
        }
        
        .header h1 {
            color: #E50914;
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }
        
        .check-item {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            border-left: 4px solid #666;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .check-item.success {
            border-left-color: #4CAF50;
            background: rgba(76, 175, 80, 0.1);
        }
        
        .check-item.error {
            border-left-color: #F44336;
            background: rgba(244, 67, 54, 0.1);
        }
        
        .check-item.warning {
            border-left-color: #FF9800;
            background: rgba(255, 152, 0, 0.1);
        }
        
        .check-info {
            flex: 1;
        }
        
        .check-status {
            font-size: 1.5rem;
            margin-right: 1rem;
        }
        
        .btn {
            background: linear-gradient(45deg, #E50914, #B8070F);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1.1rem;
            font-weight: bold;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            margin: 0.5rem;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(229, 9, 20, 0.3);
            color: white;
        }
        
        .btn-success {
            background: linear-gradient(45deg, #4CAF50, #45a049);
        }
        
        .btn-secondary {
            background: linear-gradient(45deg, #555, #333);
        }
        
        .actions {
            text-align: center;
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .info-box {
            background: rgba(33, 150, 243, 0.1);
            border: 1px solid #2196F3;
            border-radius: 10px;
            padding: 1.5rem;
            margin: 1.5rem 0;
        }
        
        .info-box h3 {
            color: #2196F3;
            margin-bottom: 1rem;
        }
        
        .links-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 2rem;
        }
        
        .link-card {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 1.5rem;
            text-align: center;
            border: 1px solid rgba(229, 9, 20, 0.2);
            transition: all 0.3s ease;
        }
        
        .link-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(229, 9, 20, 0.3);
        }
        
        .link-card a {
            color: #E50914;
            text-decoration: none;
            font-weight: bold;
        }
        
        .link-card a:hover {
            color: #fff;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 فحص XAMPP</h1>
            <p>التحقق من إعدادات الخادم المحلي</p>
        </div>

        <?php
        // فحص PHP
        $phpVersion = phpversion();
        $phpOk = version_compare($phpVersion, '7.4.0', '>=');
        ?>
        
        <div class="check-item <?php echo $phpOk ? 'success' : 'error'; ?>">
            <div class="check-info">
                <h3>🐘 PHP Version</h3>
                <p>الإصدار الحالي: <?php echo $phpVersion; ?></p>
                <p><?php echo $phpOk ? 'متوافق مع المتطلبات' : 'يتطلب PHP 7.4 أو أحدث'; ?></p>
            </div>
            <div class="check-status"><?php echo $phpOk ? '✅' : '❌'; ?></div>
        </div>

        <?php
        // فحص MySQL
        $mysqlOk = false;
        $mysqlError = '';
        try {
            $pdo = new PDO("mysql:host=localhost", "root", "");
            $mysqlOk = true;
            $stmt = $pdo->query("SELECT VERSION()");
            $mysqlVersion = $stmt->fetchColumn();
        } catch (Exception $e) {
            $mysqlError = $e->getMessage();
            $mysqlVersion = 'غير متاح';
        }
        ?>
        
        <div class="check-item <?php echo $mysqlOk ? 'success' : 'error'; ?>">
            <div class="check-info">
                <h3>🗄️ MySQL Database</h3>
                <p>الإصدار: <?php echo $mysqlVersion; ?></p>
                <p><?php echo $mysqlOk ? 'متصل بنجاح' : 'خطأ في الاتصال: ' . $mysqlError; ?></p>
            </div>
            <div class="check-status"><?php echo $mysqlOk ? '✅' : '❌'; ?></div>
        </div>

        <?php
        // فحص Extensions المطلوبة
        $requiredExtensions = [
            'pdo' => 'PDO',
            'pdo_mysql' => 'PDO MySQL',
            'openssl' => 'OpenSSL',
            'curl' => 'cURL',
            'gd' => 'GD',
            'fileinfo' => 'Fileinfo',
            'json' => 'JSON',
            'mbstring' => 'Mbstring'
        ];
        
        $allExtensionsOk = true;
        foreach ($requiredExtensions as $ext => $name) {
            $loaded = extension_loaded($ext);
            if (!$loaded) $allExtensionsOk = false;
        ?>
        
        <div class="check-item <?php echo $loaded ? 'success' : 'error'; ?>">
            <div class="check-info">
                <h3>🔧 <?php echo $name; ?> Extension</h3>
                <p><?php echo $loaded ? 'مثبت ومفعل' : 'غير مثبت أو غير مفعل'; ?></p>
            </div>
            <div class="check-status"><?php echo $loaded ? '✅' : '❌'; ?></div>
        </div>
        
        <?php } ?>

        <?php
        // فحص قاعدة البيانات الحالية
        $dbExists = false;
        $dbTables = 0;
        if ($mysqlOk) {
            try {
                $pdo = new PDO("mysql:host=localhost;dbname=shahid_platform", "root", "");
                $dbExists = true;
                $stmt = $pdo->query("SHOW TABLES");
                $dbTables = $stmt->rowCount();
            } catch (Exception $e) {
                // قاعدة البيانات غير موجودة
            }
        }
        ?>
        
        <div class="check-item <?php echo $dbExists ? ($dbTables > 0 ? 'success' : 'warning') : 'error'; ?>">
            <div class="check-info">
                <h3>🗄️ قاعدة بيانات Shahid</h3>
                <p><?php 
                if ($dbExists) {
                    echo $dbTables > 0 ? "موجودة مع $dbTables جدول" : 'موجودة لكن فارغة';
                } else {
                    echo 'غير موجودة';
                }
                ?></p>
            </div>
            <div class="check-status"><?php 
            echo $dbExists ? ($dbTables > 0 ? '✅' : '⚠️') : '❌'; 
            ?></div>
        </div>

        <?php
        // فحص الملفات المطلوبة
        $requiredFiles = [
            'schema.sql' => 'ملف Schema',
            'production_data.sql' => 'ملف البيانات الإنتاجية'
        ];
        
        $allFilesOk = true;
        foreach ($requiredFiles as $file => $name) {
            $exists = file_exists(__DIR__ . '/' . $file);
            if (!$exists) $allFilesOk = false;
        ?>
        
        <div class="check-item <?php echo $exists ? 'success' : 'error'; ?>">
            <div class="check-info">
                <h3>📄 <?php echo $name; ?></h3>
                <p><?php echo $exists ? 'موجود' : 'غير موجود'; ?></p>
            </div>
            <div class="check-status"><?php echo $exists ? '✅' : '❌'; ?></div>
        </div>
        
        <?php } ?>

        <?php if (!$phpOk || !$mysqlOk || !$allExtensionsOk || !$allFilesOk): ?>
        <div class="info-box">
            <h3>⚠️ مشاكل يجب حلها:</h3>
            <ul style="padding-right: 2rem;">
                <?php if (!$phpOk): ?>
                <li>قم بترقية PHP إلى الإصدار 7.4 أو أحدث</li>
                <?php endif; ?>
                <?php if (!$mysqlOk): ?>
                <li>تأكد من تشغيل خدمة MySQL في XAMPP</li>
                <li>تحقق من إعدادات الاتصال</li>
                <?php endif; ?>
                <?php if (!$allExtensionsOk): ?>
                <li>قم بتفعيل Extensions المطلوبة في php.ini</li>
                <?php endif; ?>
                <?php if (!$allFilesOk): ?>
                <li>تأكد من وجود ملفات قاعدة البيانات</li>
                <?php endif; ?>
            </ul>
        </div>
        <?php endif; ?>

        <div class="actions">
            <?php if ($phpOk && $mysqlOk && $allExtensionsOk && $allFilesOk): ?>
                <a href="quick_update.php" class="btn btn-success">🚀 تحديث قاعدة البيانات</a>
            <?php else: ?>
                <button class="btn" disabled>❌ حل المشاكل أولاً</button>
            <?php endif; ?>
            
            <a href="../homepage.php" class="btn btn-secondary">🏠 العودة للرئيسية</a>
        </div>

        <div class="links-grid">
            <div class="link-card">
                <h4>📊 phpMyAdmin</h4>
                <a href="http://localhost/phpmyadmin/" target="_blank">إدارة قاعدة البيانات</a>
            </div>
            <div class="link-card">
                <h4>🎛️ XAMPP Control</h4>
                <a href="http://localhost/xampp/" target="_blank">لوحة تحكم XAMPP</a>
            </div>
            <div class="link-card">
                <h4>🔧 PHP Info</h4>
                <a href="data:text/html,<?php echo urlencode('<?php phpinfo(); ?>'); ?>" target="_blank">معلومات PHP</a>
            </div>
            <div class="link-card">
                <h4>📋 System Info</h4>
                <p>PHP: <?php echo $phpVersion; ?><br>
                MySQL: <?php echo $mysqlOk ? '✅' : '❌'; ?></p>
            </div>
        </div>
    </div>
</body>
</html>
