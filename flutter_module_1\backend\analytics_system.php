<?php
/**
 * نظام التحليلات والإحصائيات المتقدم
 * يتضمن Google Analytics، تتبع المستخدمين، وإحصائيات مفصلة
 */

class AnalyticsSystem {
    private $pdo;
    private $googleAnalyticsId;
    
    public function __construct() {
        try {
            $this->pdo = new PDO("mysql:host=localhost;dbname=shahid_platform;charset=utf8mb4", "root", "");
            $this->pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        } catch (PDOException $e) {
            throw new Exception("خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage());
        }
        
        $this->googleAnalyticsId = 'G-XXXXXXXXXX'; // يجب تغييره بالمعرف الحقيقي
    }
    
    /**
     * تسجيل زيارة صفحة
     */
    public function trackPageView($page, $userId = null, $sessionId = null) {
        try {
            $stmt = $this->pdo->prepare("
                INSERT INTO page_views (page, user_id, session_id, ip_address, user_agent, created_at)
                VALUES (?, ?, ?, ?, ?, NOW())
            ");
            
            $stmt->execute([
                $page,
                $userId,
                $sessionId ?: session_id(),
                $this->getRealIP(),
                $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown'
            ]);
            
            return $this->pdo->lastInsertId();
            
        } catch (Exception $e) {
            error_log("Analytics Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * تسجيل مشاهدة محتوى
     */
    public function trackContentView($contentId, $contentType, $userId = null, $duration = 0) {
        try {
            // التحقق من وجود مشاهدة سابقة في نفس الجلسة
            $stmt = $this->pdo->prepare("
                SELECT id FROM content_views 
                WHERE content_id = ? AND content_type = ? AND session_id = ? 
                AND created_at > DATE_SUB(NOW(), INTERVAL 1 HOUR)
            ");
            $stmt->execute([$contentId, $contentType, session_id()]);
            
            if (!$stmt->fetch()) {
                // إضافة مشاهدة جديدة
                $stmt = $this->pdo->prepare("
                    INSERT INTO content_views (content_id, content_type, user_id, session_id, duration, ip_address, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, NOW())
                ");
                
                $stmt->execute([
                    $contentId,
                    $contentType,
                    $userId,
                    session_id(),
                    $duration,
                    $this->getRealIP()
                ]);
                
                // تحديث عداد المشاهدات في جدول المحتوى
                $this->updateViewCount($contentId, $contentType);
                
                return true;
            }
            
            return false;
            
        } catch (Exception $e) {
            error_log("Analytics Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * تحديث عداد المشاهدات
     */
    private function updateViewCount($contentId, $contentType) {
        $table = ($contentType === 'movie') ? 'movies' : 'series';
        
        $stmt = $this->pdo->prepare("
            UPDATE {$table} 
            SET view_count = COALESCE(view_count, 0) + 1 
            WHERE id = ?
        ");
        $stmt->execute([$contentId]);
    }
    
    /**
     * تسجيل حدث مخصص
     */
    public function trackEvent($eventName, $eventData = [], $userId = null) {
        try {
            $stmt = $this->pdo->prepare("
                INSERT INTO analytics_events (event_name, event_data, user_id, session_id, ip_address, created_at)
                VALUES (?, ?, ?, ?, ?, NOW())
            ");
            
            $stmt->execute([
                $eventName,
                json_encode($eventData),
                $userId,
                session_id(),
                $this->getRealIP()
            ]);
            
            return true;
            
        } catch (Exception $e) {
            error_log("Analytics Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * الحصول على إحصائيات الصفحات
     */
    public function getPageStats($startDate = null, $endDate = null) {
        $startDate = $startDate ?: date('Y-m-d', strtotime('-30 days'));
        $endDate = $endDate ?: date('Y-m-d');
        
        $stmt = $this->pdo->prepare("
            SELECT 
                page,
                COUNT(*) as views,
                COUNT(DISTINCT session_id) as unique_views,
                COUNT(DISTINCT ip_address) as unique_visitors
            FROM page_views 
            WHERE DATE(created_at) BETWEEN ? AND ?
            GROUP BY page
            ORDER BY views DESC
        ");
        
        $stmt->execute([$startDate, $endDate]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * الحصول على إحصائيات المحتوى
     */
    public function getContentStats($contentType = null, $startDate = null, $endDate = null) {
        $startDate = $startDate ?: date('Y-m-d', strtotime('-30 days'));
        $endDate = $endDate ?: date('Y-m-d');
        
        $sql = "
            SELECT 
                cv.content_id,
                cv.content_type,
                CASE 
                    WHEN cv.content_type = 'movie' THEN m.title
                    WHEN cv.content_type = 'series' THEN s.title
                END as title,
                COUNT(*) as views,
                COUNT(DISTINCT cv.session_id) as unique_views,
                AVG(cv.duration) as avg_duration
            FROM content_views cv
            LEFT JOIN movies m ON cv.content_id = m.id AND cv.content_type = 'movie'
            LEFT JOIN series s ON cv.content_id = s.id AND cv.content_type = 'series'
            WHERE DATE(cv.created_at) BETWEEN ? AND ?
        ";
        
        $params = [$startDate, $endDate];
        
        if ($contentType) {
            $sql .= " AND cv.content_type = ?";
            $params[] = $contentType;
        }
        
        $sql .= " GROUP BY cv.content_id, cv.content_type ORDER BY views DESC LIMIT 20";
        
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute($params);
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * الحصول على إحصائيات المستخدمين
     */
    public function getUserStats($startDate = null, $endDate = null) {
        $startDate = $startDate ?: date('Y-m-d', strtotime('-30 days'));
        $endDate = $endDate ?: date('Y-m-d');
        
        // إحصائيات عامة
        $stmt = $this->pdo->prepare("
            SELECT 
                COUNT(DISTINCT session_id) as total_sessions,
                COUNT(DISTINCT ip_address) as unique_visitors,
                COUNT(*) as total_page_views,
                AVG(
                    SELECT COUNT(*) 
                    FROM page_views pv2 
                    WHERE pv2.session_id = page_views.session_id
                ) as avg_pages_per_session
            FROM page_views 
            WHERE DATE(created_at) BETWEEN ? AND ?
        ");
        
        $stmt->execute([$startDate, $endDate]);
        $generalStats = $stmt->fetch(PDO::FETCH_ASSOC);
        
        // إحصائيات يومية
        $stmt = $this->pdo->prepare("
            SELECT 
                DATE(created_at) as date,
                COUNT(DISTINCT session_id) as sessions,
                COUNT(DISTINCT ip_address) as visitors,
                COUNT(*) as page_views
            FROM page_views 
            WHERE DATE(created_at) BETWEEN ? AND ?
            GROUP BY DATE(created_at)
            ORDER BY date
        ");
        
        $stmt->execute([$startDate, $endDate]);
        $dailyStats = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        return [
            'general' => $generalStats,
            'daily' => $dailyStats
        ];
    }
    
    /**
     * الحصول على أهم الأحداث
     */
    public function getTopEvents($startDate = null, $endDate = null, $limit = 10) {
        $startDate = $startDate ?: date('Y-m-d', strtotime('-30 days'));
        $endDate = $endDate ?: date('Y-m-d');
        
        $stmt = $this->pdo->prepare("
            SELECT 
                event_name,
                COUNT(*) as count,
                COUNT(DISTINCT session_id) as unique_sessions
            FROM analytics_events 
            WHERE DATE(created_at) BETWEEN ? AND ?
            GROUP BY event_name
            ORDER BY count DESC
            LIMIT ?
        ");
        
        $stmt->execute([$startDate, $endDate, $limit]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * الحصول على إحصائيات الأجهزة والمتصفحات
     */
    public function getDeviceStats($startDate = null, $endDate = null) {
        $startDate = $startDate ?: date('Y-m-d', strtotime('-30 days'));
        $endDate = $endDate ?: date('Y-m-d');
        
        $stmt = $this->pdo->prepare("
            SELECT 
                user_agent,
                COUNT(*) as count,
                COUNT(DISTINCT session_id) as unique_sessions
            FROM page_views 
            WHERE DATE(created_at) BETWEEN ? AND ?
            GROUP BY user_agent
            ORDER BY count DESC
            LIMIT 20
        ");
        
        $stmt->execute([$startDate, $endDate]);
        $userAgents = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // تحليل User Agents
        $devices = ['mobile' => 0, 'tablet' => 0, 'desktop' => 0];
        $browsers = [];
        $os = [];
        
        foreach ($userAgents as $ua) {
            $agent = $ua['user_agent'];
            $count = $ua['count'];
            
            // تحديد نوع الجهاز
            if (preg_match('/Mobile|Android|iPhone|iPad/', $agent)) {
                if (preg_match('/iPad/', $agent)) {
                    $devices['tablet'] += $count;
                } else {
                    $devices['mobile'] += $count;
                }
            } else {
                $devices['desktop'] += $count;
            }
            
            // تحديد المتصفح
            if (preg_match('/Chrome/', $agent)) {
                $browsers['Chrome'] = ($browsers['Chrome'] ?? 0) + $count;
            } elseif (preg_match('/Firefox/', $agent)) {
                $browsers['Firefox'] = ($browsers['Firefox'] ?? 0) + $count;
            } elseif (preg_match('/Safari/', $agent)) {
                $browsers['Safari'] = ($browsers['Safari'] ?? 0) + $count;
            } elseif (preg_match('/Edge/', $agent)) {
                $browsers['Edge'] = ($browsers['Edge'] ?? 0) + $count;
            } else {
                $browsers['Other'] = ($browsers['Other'] ?? 0) + $count;
            }
            
            // تحديد نظام التشغيل
            if (preg_match('/Windows/', $agent)) {
                $os['Windows'] = ($os['Windows'] ?? 0) + $count;
            } elseif (preg_match('/Mac/', $agent)) {
                $os['macOS'] = ($os['macOS'] ?? 0) + $count;
            } elseif (preg_match('/Linux/', $agent)) {
                $os['Linux'] = ($os['Linux'] ?? 0) + $count;
            } elseif (preg_match('/Android/', $agent)) {
                $os['Android'] = ($os['Android'] ?? 0) + $count;
            } elseif (preg_match('/iOS/', $agent)) {
                $os['iOS'] = ($os['iOS'] ?? 0) + $count;
            } else {
                $os['Other'] = ($os['Other'] ?? 0) + $count;
            }
        }
        
        return [
            'devices' => $devices,
            'browsers' => $browsers,
            'operating_systems' => $os
        ];
    }
    
    /**
     * إنشاء تقرير شامل
     */
    public function generateReport($startDate = null, $endDate = null) {
        $startDate = $startDate ?: date('Y-m-d', strtotime('-30 days'));
        $endDate = $endDate ?: date('Y-m-d');
        
        return [
            'period' => [
                'start_date' => $startDate,
                'end_date' => $endDate,
                'days' => (strtotime($endDate) - strtotime($startDate)) / (60 * 60 * 24) + 1
            ],
            'user_stats' => $this->getUserStats($startDate, $endDate),
            'page_stats' => $this->getPageStats($startDate, $endDate),
            'content_stats' => $this->getContentStats(null, $startDate, $endDate),
            'top_events' => $this->getTopEvents($startDate, $endDate),
            'device_stats' => $this->getDeviceStats($startDate, $endDate)
        ];
    }
    
    /**
     * إنشاء كود Google Analytics
     */
    public function getGoogleAnalyticsCode() {
        return "
        <!-- Google Analytics -->
        <script async src=\"https://www.googletagmanager.com/gtag/js?id={$this->googleAnalyticsId}\"></script>
        <script>
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', '{$this->googleAnalyticsId}');
        </script>
        ";
    }
    
    /**
     * الحصول على عنوان IP الحقيقي
     */
    private function getRealIP() {
        $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ips = explode(',', $_SERVER[$key]);
                $ip = trim($ips[0]);
                
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    }
}

// إنشاء جداول التحليلات إذا لم تكن موجودة
function createAnalyticsTables() {
    try {
        $pdo = new PDO("mysql:host=localhost;dbname=shahid_platform;charset=utf8mb4", "root", "");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // جدول مشاهدات الصفحات
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS page_views (
                id INT AUTO_INCREMENT PRIMARY KEY,
                page VARCHAR(255) NOT NULL,
                user_id INT NULL,
                session_id VARCHAR(255) NOT NULL,
                ip_address VARCHAR(45) NOT NULL,
                user_agent TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_page (page),
                INDEX idx_session (session_id),
                INDEX idx_created_at (created_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        
        // جدول مشاهدات المحتوى
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS content_views (
                id INT AUTO_INCREMENT PRIMARY KEY,
                content_id INT NOT NULL,
                content_type ENUM('movie', 'series', 'episode') NOT NULL,
                user_id INT NULL,
                session_id VARCHAR(255) NOT NULL,
                duration INT DEFAULT 0,
                ip_address VARCHAR(45) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_content (content_id, content_type),
                INDEX idx_session (session_id),
                INDEX idx_created_at (created_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        
        // جدول الأحداث
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS analytics_events (
                id INT AUTO_INCREMENT PRIMARY KEY,
                event_name VARCHAR(255) NOT NULL,
                event_data JSON,
                user_id INT NULL,
                session_id VARCHAR(255) NOT NULL,
                ip_address VARCHAR(45) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_event_name (event_name),
                INDEX idx_session (session_id),
                INDEX idx_created_at (created_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        
        // إضافة عمود view_count للأفلام والمسلسلات إذا لم يكن موجوداً
        try {
            $pdo->exec("ALTER TABLE movies ADD COLUMN view_count INT DEFAULT 0");
        } catch (Exception $e) {
            // العمود موجود بالفعل
        }
        
        try {
            $pdo->exec("ALTER TABLE series ADD COLUMN view_count INT DEFAULT 0");
        } catch (Exception $e) {
            // العمود موجود بالفعل
        }
        
        return true;
        
    } catch (Exception $e) {
        error_log("Analytics Tables Error: " . $e->getMessage());
        return false;
    }
}

// معالجة طلبات API
if ($_SERVER['REQUEST_METHOD'] === 'POST' || $_SERVER['REQUEST_METHOD'] === 'GET') {
    header('Content-Type: application/json');
    
    $action = $_REQUEST['action'] ?? '';
    
    if ($action) {
        $analytics = new AnalyticsSystem();
        
        switch ($action) {
            case 'track_page':
                $page = $_POST['page'] ?? '';
                $userId = $_POST['user_id'] ?? null;
                
                $result = $analytics->trackPageView($page, $userId);
                echo json_encode([
                    'success' => $result !== false,
                    'view_id' => $result
                ]);
                break;
                
            case 'track_content':
                $contentId = $_POST['content_id'] ?? 0;
                $contentType = $_POST['content_type'] ?? 'movie';
                $userId = $_POST['user_id'] ?? null;
                $duration = $_POST['duration'] ?? 0;
                
                $result = $analytics->trackContentView($contentId, $contentType, $userId, $duration);
                echo json_encode([
                    'success' => $result,
                    'message' => $result ? 'Content view tracked' : 'Already tracked'
                ]);
                break;
                
            case 'track_event':
                $eventName = $_POST['event_name'] ?? '';
                $eventData = $_POST['event_data'] ?? [];
                $userId = $_POST['user_id'] ?? null;
                
                $result = $analytics->trackEvent($eventName, $eventData, $userId);
                echo json_encode([
                    'success' => $result
                ]);
                break;
                
            case 'get_report':
                $startDate = $_GET['start_date'] ?? null;
                $endDate = $_GET['end_date'] ?? null;
                
                $report = $analytics->generateReport($startDate, $endDate);
                echo json_encode([
                    'success' => true,
                    'data' => $report
                ]);
                break;
                
            case 'create_tables':
                $result = createAnalyticsTables();
                echo json_encode([
                    'success' => $result,
                    'message' => $result ? 'Analytics tables created successfully' : 'Failed to create tables'
                ]);
                break;
                
            default:
                echo json_encode([
                    'success' => false,
                    'error' => 'Invalid action'
                ]);
        }
        exit;
    }
}
?>
