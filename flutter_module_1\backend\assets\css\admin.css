/* Shahid Admin Panel Stylesheet */

:root {
    --admin-primary: #e50914;
    --admin-secondary: #221f1f;
    --admin-dark: #141414;
    --admin-light: #f8f9fa;
    --admin-white: #ffffff;
    --admin-gray: #6c757d;
    --admin-border: #dee2e6;
    --admin-success: #28a745;
    --admin-info: #17a2b8;
    --admin-warning: #ffc107;
    --admin-danger: #dc3545;
    --sidebar-width: 280px;
    --topbar-height: 70px;
    --transition: all 0.3s ease;
    --shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
}

/* Global Admin Styles */
.admin-body {
    font-family: 'Cairo', sans-serif;
    background-color: var(--admin-light);
    margin: 0;
    padding: 0;
    overflow-x: hidden;
}

/* Sidebar */
.sidebar {
    position: fixed;
    top: 0;
    right: 0;
    width: var(--sidebar-width);
    height: 100vh;
    background: var(--admin-white);
    border-left: 1px solid var(--admin-border);
    box-shadow: var(--shadow-lg);
    z-index: 1000;
    transition: var(--transition);
    overflow-y: auto;
}

.sidebar-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--admin-border);
    text-align: center;
}

.sidebar-header h4 {
    margin: 0.5rem 0 0 0;
    color: var(--admin-dark);
    font-weight: 600;
    font-size: 1.1rem;
}

.sidebar-menu {
    list-style: none;
    padding: 1rem 0;
    margin: 0;
}

.menu-item {
    margin-bottom: 0.25rem;
}

.menu-link {
    display: flex;
    align-items: center;
    padding: 0.75rem 1.5rem;
    color: var(--admin-gray);
    text-decoration: none;
    transition: var(--transition);
    border-right: 3px solid transparent;
}

.menu-link:hover {
    background-color: rgba(229, 9, 20, 0.1);
    color: var(--admin-primary);
}

.menu-link.active {
    background-color: rgba(229, 9, 20, 0.1);
    color: var(--admin-primary);
    border-right-color: var(--admin-primary);
    font-weight: 600;
}

.menu-link i {
    width: 20px;
    margin-left: 0.75rem;
    text-align: center;
}

.submenu-arrow {
    margin-right: auto;
    transition: var(--transition);
}

.submenu {
    list-style: none;
    padding: 0;
    margin: 0;
    background-color: rgba(0, 0, 0, 0.05);
}

.submenu-link {
    display: block;
    padding: 0.5rem 1.5rem 0.5rem 3.5rem;
    color: var(--admin-gray);
    text-decoration: none;
    font-size: 0.9rem;
    transition: var(--transition);
}

.submenu-link:hover {
    background-color: rgba(229, 9, 20, 0.1);
    color: var(--admin-primary);
}

.sidebar-footer {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 1rem 1.5rem;
    border-top: 1px solid var(--admin-border);
}

/* Main Content */
.main-content {
    margin-right: var(--sidebar-width);
    min-height: 100vh;
    transition: var(--transition);
}

/* Top Navbar */
.top-navbar {
    background: var(--admin-white);
    border-bottom: 1px solid var(--admin-border);
    height: var(--topbar-height);
    box-shadow: var(--shadow);
    position: sticky;
    top: 0;
    z-index: 999;
}

.navbar-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 1.5rem;
    height: 100%;
}

.sidebar-toggle {
    background: none;
    border: none;
    color: var(--admin-gray);
    font-size: 1.2rem;
    padding: 0.5rem;
    border-radius: 0.25rem;
    transition: var(--transition);
}

.sidebar-toggle:hover {
    background-color: var(--admin-light);
    color: var(--admin-primary);
}

.navbar-search {
    flex: 1;
    max-width: 400px;
    margin: 0 2rem;
}

.search-box {
    position: relative;
}

.search-box input {
    width: 100%;
    padding: 0.5rem 1rem 0.5rem 2.5rem;
    border: 1px solid var(--admin-border);
    border-radius: 25px;
    background-color: var(--admin-light);
    transition: var(--transition);
}

.search-box input:focus {
    outline: none;
    border-color: var(--admin-primary);
    box-shadow: 0 0 0 0.2rem rgba(229, 9, 20, 0.25);
}

.search-icon {
    position: absolute;
    right: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--admin-gray);
}

.navbar-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.notification-btn {
    position: relative;
    background: none;
    border: none;
    color: var(--admin-gray);
    font-size: 1.2rem;
    padding: 0.5rem;
    border-radius: 0.25rem;
    transition: var(--transition);
}

.notification-btn:hover {
    background-color: var(--admin-light);
    color: var(--admin-primary);
}

.notification-badge {
    position: absolute;
    top: 0.25rem;
    left: 0.25rem;
    background: var(--admin-danger);
    color: white;
    font-size: 0.7rem;
    padding: 0.2rem 0.4rem;
    border-radius: 50%;
    min-width: 1.2rem;
    text-align: center;
}

.user-menu {
    display: flex;
    align-items: center;
    background: none;
    border: none;
    color: var(--admin-dark);
    text-decoration: none;
    padding: 0.5rem;
    border-radius: 0.5rem;
    transition: var(--transition);
}

.user-menu:hover {
    background-color: var(--admin-light);
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    margin-left: 0.5rem;
}

.user-name {
    font-weight: 500;
    margin: 0 0.5rem;
}

/* Page Content */
.page-content {
    padding: 2rem;
}

.page-header {
    margin-bottom: 2rem;
}

.page-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--admin-dark);
    margin-bottom: 0.5rem;
}

.page-subtitle {
    color: var(--admin-gray);
    margin-bottom: 0;
}

/* Statistics Cards */
.stats-card {
    background: var(--admin-white);
    border-radius: 0.5rem;
    padding: 1.5rem;
    box-shadow: var(--shadow);
    border: 1px solid var(--admin-border);
    position: relative;
    overflow: hidden;
    transition: var(--transition);
}

.stats-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.stats-card.bg-primary {
    background: linear-gradient(135deg, var(--admin-primary) 0%, #b20710 100%);
    color: white;
    border: none;
}

.stats-card.bg-success {
    background: linear-gradient(135deg, var(--admin-success) 0%, #1e7e34 100%);
    color: white;
    border: none;
}

.stats-card.bg-info {
    background: linear-gradient(135deg, var(--admin-info) 0%, #117a8b 100%);
    color: white;
    border: none;
}

.stats-card.bg-warning {
    background: linear-gradient(135deg, var(--admin-warning) 0%, #e0a800 100%);
    color: white;
    border: none;
}

.stats-card.bg-gradient-primary {
    background: linear-gradient(135deg, var(--admin-primary) 0%, #b20710 100%);
    color: white;
    border: none;
}

.stats-card.bg-gradient-success {
    background: linear-gradient(135deg, var(--admin-success) 0%, #1e7e34 100%);
    color: white;
    border: none;
}

.stats-icon {
    position: absolute;
    top: 1rem;
    left: 1rem;
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
}

.stats-content {
    text-align: right;
}

.stats-content h3 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.stats-content p {
    font-size: 1rem;
    margin-bottom: 0.5rem;
    opacity: 0.9;
}

.stats-trend {
    font-size: 0.875rem;
    opacity: 0.8;
}

.stats-trend i {
    margin-left: 0.25rem;
}

/* Cards */
.card {
    background: var(--admin-white);
    border: 1px solid var(--admin-border);
    border-radius: 0.5rem;
    box-shadow: var(--shadow);
}

.card-header {
    background: var(--admin-white);
    border-bottom: 1px solid var(--admin-border);
    padding: 1rem 1.5rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.card-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--admin-dark);
    margin: 0;
}

.card-tools {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.card-body {
    padding: 1.5rem;
}

/* Recent Items */
.recent-items {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.recent-item {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    background: var(--admin-light);
    border-radius: 0.5rem;
    transition: var(--transition);
}

.recent-item:hover {
    background: rgba(229, 9, 20, 0.1);
}

.recent-item-image {
    width: 50px;
    height: 75px;
    object-fit: cover;
    border-radius: 0.25rem;
    margin-left: 1rem;
}

.recent-item-content {
    flex: 1;
}

.recent-item-content h6 {
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
    color: var(--admin-dark);
}

.recent-item-content p {
    font-size: 0.8rem;
    color: var(--admin-gray);
    margin-bottom: 0.25rem;
}

.recent-item-actions {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* Dropdowns */
.notification-dropdown {
    width: 350px;
    max-height: 400px;
    overflow-y: auto;
}

.dropdown-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.75rem 1rem;
    border-bottom: 1px solid var(--admin-border);
}

.dropdown-header h6 {
    margin: 0;
    font-weight: 600;
}

.notification-list {
    max-height: 300px;
    overflow-y: auto;
}

.notification-item {
    display: flex;
    align-items: flex-start;
    padding: 0.75rem 1rem;
    border-bottom: 1px solid var(--admin-border);
    text-decoration: none;
    color: var(--admin-dark);
    transition: var(--transition);
}

.notification-item:hover {
    background-color: var(--admin-light);
}

.notification-item.read {
    opacity: 0.7;
}

.notification-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 0.75rem;
    color: white;
    font-size: 0.875rem;
}

.notification-content h6 {
    font-size: 0.875rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.notification-content p {
    font-size: 0.8rem;
    color: var(--admin-gray);
    margin-bottom: 0.25rem;
}

.notification-content small {
    font-size: 0.75rem;
    color: var(--admin-gray);
}

.dropdown-footer {
    padding: 0.75rem 1rem;
    border-top: 1px solid var(--admin-border);
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: 1rem;
    left: 1rem;
    z-index: 1060;
}

.toast-notification {
    background: var(--admin-white);
    border: 1px solid var(--admin-border);
    border-radius: 0.5rem;
    box-shadow: var(--shadow-lg);
    padding: 1rem;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-width: 300px;
    animation: slideIn 0.3s ease;
}

.toast-success {
    border-right: 4px solid var(--admin-success);
}

.toast-error {
    border-right: 4px solid var(--admin-danger);
}

.toast-warning {
    border-right: 4px solid var(--admin-warning);
}

.toast-info {
    border-right: 4px solid var(--admin-info);
}

.toast-content {
    display: flex;
    align-items: center;
    flex: 1;
}

.toast-close {
    background: none;
    border: none;
    color: var(--admin-gray);
    font-size: 0.875rem;
    padding: 0.25rem;
    cursor: pointer;
}

@keyframes slideIn {
    from {
        transform: translateX(-100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(100%);
    }
    
    .sidebar.show {
        transform: translateX(0);
    }
    
    .main-content {
        margin-right: 0;
    }
    
    .sidebar-collapsed .sidebar {
        transform: translateX(100%);
    }
    
    .page-content {
        padding: 1rem;
    }
    
    .navbar-search {
        display: none;
    }
    
    .stats-card {
        margin-bottom: 1rem;
    }
}

/* Sidebar Collapsed State */
.sidebar-collapsed .sidebar {
    width: 70px;
}

.sidebar-collapsed .main-content {
    margin-right: 70px;
}

.sidebar-collapsed .sidebar-header h4,
.sidebar-collapsed .menu-link span,
.sidebar-collapsed .sidebar-footer {
    display: none;
}

.sidebar-collapsed .menu-link {
    justify-content: center;
    padding: 0.75rem;
}

.sidebar-collapsed .menu-link i {
    margin: 0;
}

/* Utilities */
.text-primary { color: var(--admin-primary) !important; }
.text-success { color: var(--admin-success) !important; }
.text-info { color: var(--admin-info) !important; }
.text-warning { color: var(--admin-warning) !important; }
.text-danger { color: var(--admin-danger) !important; }

.bg-primary { background-color: var(--admin-primary) !important; }
.bg-success { background-color: var(--admin-success) !important; }
.bg-info { background-color: var(--admin-info) !important; }
.bg-warning { background-color: var(--admin-warning) !important; }
.bg-danger { background-color: var(--admin-danger) !important; }
