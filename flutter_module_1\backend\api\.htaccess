# Shahid API - Apache Configuration
# Professional Video Streaming Platform API

RewriteEngine On

# Allow CORS for all origins
Header always set Access-Control-Allow-Origin "*"
Header always set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
Header always set Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With"

# Handle preflight OPTIONS requests
RewriteCond %{REQUEST_METHOD} OPTIONS
RewriteRule ^(.*)$ fixed_api.php [QSA,L]

# API Routes
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d

# Route specific endpoints
RewriteRule ^status/?$ fixed_api.php?endpoint=status [QSA,L]
RewriteRule ^movies/?$ fixed_api.php?endpoint=movies [QSA,L]
RewriteRule ^series/?$ fixed_api.php?endpoint=series [QSA,L]
RewriteRule ^search/?$ fixed_api.php?endpoint=search [QSA,L]
RewriteRule ^auth/login/?$ fixed_api.php?endpoint=login [QSA,L]
RewriteRule ^auth/register/?$ fixed_api.php?endpoint=register [QSA,L]
RewriteRule ^login/?$ fixed_api.php?endpoint=login [QSA,L]
RewriteRule ^register/?$ fixed_api.php?endpoint=register [QSA,L]

# Default route to status
RewriteRule ^$ fixed_api.php?endpoint=status [QSA,L]

# Fallback to fixed_api.php for any other requests
RewriteRule ^(.*)$ fixed_api.php [QSA,L]

# Security headers
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"

# Cache control for API responses
<FilesMatch "\.(php)$">
    Header set Cache-Control "no-cache, no-store, must-revalidate"
    Header set Pragma "no-cache"
    Header set Expires 0
</FilesMatch>

# Allow access to all files
<RequireAll>
    Require all granted
</RequireAll>
