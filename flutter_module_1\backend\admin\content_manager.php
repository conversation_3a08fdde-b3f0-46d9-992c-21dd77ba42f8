<?php
session_start();

// التحقق من تسجيل الدخول والصلاحيات
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit;
}

try {
    $pdo = new PDO("mysql:host=localhost;dbname=shahid_platform;charset=utf8mb4", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // التحقق من صلاحيات الإدارة
    $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ? AND (subscription_type = 'vip' OR email LIKE '%admin%')");
    $stmt->execute([$_SESSION['user_id']]);
    $admin = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$admin) {
        header('Location: ../index.php');
        exit;
    }

    // معالجة العمليات
    $message = '';
    $error = '';

    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'add_movie':
                    $title = $_POST['title'];
                    $description = $_POST['description'];
                    $release_year = $_POST['release_year'];
                    $category_id = $_POST['category_id'];
                    $video_url = $_POST['video_url'];
                    $poster = $_POST['poster'];
                    $rating = $_POST['rating'];

                    $stmt = $pdo->prepare("
                        INSERT INTO movies (title, description, release_year, category_id, video_url, poster, rating, status)
                        VALUES (?, ?, ?, ?, ?, ?, ?, 'active')
                    ");

                    if ($stmt->execute([$title, $description, $release_year, $category_id, $video_url, $poster, $rating])) {
                        $message = "تم إضافة الفيلم بنجاح";
                    } else {
                        $error = "فشل في إضافة الفيلم";
                    }
                    break;

                case 'add_series':
                    $title = $_POST['title'];
                    $description = $_POST['description'];
                    $release_year = $_POST['release_year'];
                    $category_id = $_POST['category_id'];
                    $poster = $_POST['poster'];
                    $rating = $_POST['rating'];
                    $total_seasons = $_POST['total_seasons'];

                    $stmt = $pdo->prepare("
                        INSERT INTO series (title, description, release_year, category_id, poster, rating, total_seasons, status)
                        VALUES (?, ?, ?, ?, ?, ?, ?, 'active')
                    ");

                    if ($stmt->execute([$title, $description, $release_year, $category_id, $poster, $rating, $total_seasons])) {
                        $message = "تم إضافة المسلسل بنجاح";
                    } else {
                        $error = "فشل في إضافة المسلسل";
                    }
                    break;

                case 'delete_content':
                    $content_id = $_POST['content_id'];
                    $content_type = $_POST['content_type'];

                    if ($content_type === 'movie') {
                        $stmt = $pdo->prepare("DELETE FROM movies WHERE id = ?");
                    } else {
                        $stmt = $pdo->prepare("DELETE FROM series WHERE id = ?");
                    }

                    if ($stmt->execute([$content_id])) {
                        $message = "تم حذف المحتوى بنجاح";
                    } else {
                        $error = "فشل في حذف المحتوى";
                    }
                    break;

                case 'toggle_status':
                    $content_id = $_POST['content_id'];
                    $content_type = $_POST['content_type'];
                    $new_status = $_POST['new_status'];

                    if ($content_type === 'movie') {
                        $stmt = $pdo->prepare("UPDATE movies SET status = ? WHERE id = ?");
                    } else {
                        $stmt = $pdo->prepare("UPDATE series SET status = ? WHERE id = ?");
                    }

                    if ($stmt->execute([$new_status, $content_id])) {
                        $message = "تم تحديث حالة المحتوى";
                    } else {
                        $error = "فشل في تحديث الحالة";
                    }
                    break;
            }
        }
    }

    // الحصول على الأفلام
    $movies = $pdo->query("
        SELECT m.*, c.name as category_name
        FROM movies m
        LEFT JOIN categories c ON m.category_id = c.id
        ORDER BY m.created_at DESC
    ")->fetchAll(PDO::FETCH_ASSOC);

    // الحصول على المسلسلات
    $series = $pdo->query("
        SELECT s.*, c.name as category_name
        FROM series s
        LEFT JOIN categories c ON s.category_id = c.id
        ORDER BY s.created_at DESC
    ")->fetchAll(PDO::FETCH_ASSOC);

    // الحصول على التصنيفات
    $categories = $pdo->query("SELECT * FROM categories ORDER BY name")->fetchAll(PDO::FETCH_ASSOC);

    // إحصائيات
    $stats = [
        'total_movies' => $pdo->query("SELECT COUNT(*) FROM movies")->fetchColumn(),
        'total_series' => $pdo->query("SELECT COUNT(*) FROM series")->fetchColumn(),
        'active_movies' => $pdo->query("SELECT COUNT(*) FROM movies WHERE status = 'active'")->fetchColumn(),
        'active_series' => $pdo->query("SELECT COUNT(*) FROM series WHERE status = 'active'")->fetchColumn(),
        'total_views' => $pdo->query("SELECT SUM(views) FROM movies")->fetchColumn() +
                        $pdo->query("SELECT SUM(views) FROM series")->fetchColumn()
    ];

} catch (Exception $e) {
    $error = "خطأ في قاعدة البيانات: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مدير المحتوى - Shahid Platform</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', 'Cairo', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #2d2d2d 100%);
            color: #fff;
            min-height: 100vh;
        }

        .admin-container {
            display: flex;
            min-height: 100vh;
        }

        .sidebar {
            width: 280px;
            background: rgba(47, 47, 47, 0.95);
            border-left: 2px solid rgba(229, 9, 20, 0.3);
            padding: 2rem 0;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
        }

        .sidebar-header {
            padding: 0 2rem 2rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            margin-bottom: 2rem;
        }

        .logo {
            color: #E50914;
            font-size: 1.8rem;
            font-weight: bold;
            text-decoration: none;
        }

        .sidebar-menu {
            list-style: none;
            padding: 0 1rem;
        }

        .sidebar-menu li {
            margin-bottom: 0.5rem;
        }

        .sidebar-menu a {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            color: #ccc;
            text-decoration: none;
            border-radius: 10px;
            transition: all 0.3s ease;
        }

        .sidebar-menu a:hover,
        .sidebar-menu a.active {
            background: rgba(229, 9, 20, 0.2);
            color: #fff;
        }

        .main-content {
            flex: 1;
            margin-right: 280px;
            padding: 2rem;
        }

        .header {
            background: rgba(47, 47, 47, 0.8);
            padding: 1.5rem 2rem;
            border-radius: 15px;
            margin-bottom: 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            color: #E50914;
            font-size: 2rem;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            background: linear-gradient(45deg, #E50914, #B8070F);
            color: white;
            border: none;
            border-radius: 8px;
            text-decoration: none;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(229, 9, 20, 0.3);
        }

        .btn-secondary {
            background: linear-gradient(45deg, #2196F3, #1976D2);
        }

        .btn-danger {
            background: linear-gradient(45deg, #F44336, #D32F2F);
        }

        .btn-success {
            background: linear-gradient(45deg, #4CAF50, #45a049);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: rgba(47, 47, 47, 0.8);
            padding: 1.5rem;
            border-radius: 15px;
            border: 2px solid rgba(229, 9, 20, 0.1);
            text-align: center;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #E50914;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: #ccc;
        }

        .content-section {
            background: rgba(47, 47, 47, 0.8);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            border: 2px solid rgba(229, 9, 20, 0.1);
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
        }

        .section-title {
            color: #E50914;
            font-size: 1.5rem;
        }

        .content-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }

        .content-table th,
        .content-table td {
            padding: 1rem;
            text-align: right;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .content-table th {
            background: rgba(229, 9, 20, 0.2);
            color: #E50914;
            font-weight: bold;
        }

        .content-table tr:hover {
            background: rgba(255, 255, 255, 0.05);
        }

        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .status-active {
            background: rgba(76, 175, 80, 0.2);
            color: #4CAF50;
        }

        .status-inactive {
            background: rgba(244, 67, 54, 0.2);
            color: #F44336;
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            z-index: 1000;
        }

        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(47, 47, 47, 0.95);
            border-radius: 15px;
            padding: 2rem;
            max-width: 600px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: #E50914;
            font-weight: bold;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.1);
            color: #fff;
            font-size: 1rem;
        }

        .form-group textarea {
            resize: vertical;
            min-height: 100px;
        }

        .alert {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 2rem;
        }

        .alert-success {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid rgba(76, 175, 80, 0.5);
            color: #4CAF50;
        }

        .alert-error {
            background: rgba(244, 67, 54, 0.2);
            border: 1px solid rgba(244, 67, 54, 0.5);
            color: #F44336;
        }

        .close {
            position: absolute;
            top: 1rem;
            left: 1rem;
            background: none;
            border: none;
            color: #ccc;
            font-size: 1.5rem;
            cursor: pointer;
        }

        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }

            .main-content {
                margin-right: 0;
                padding: 1rem;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .content-table {
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <aside class="sidebar">
            <div class="sidebar-header">
                <a href="../index.php" class="logo">
                    <i class="fas fa-play-circle"></i> شاهد
                </a>
            </div>

            <ul class="sidebar-menu">
                <li><a href="dashboard.php"><i class="fas fa-tachometer-alt"></i> لوحة التحكم</a></li>
                <li><a href="content_manager.php" class="active"><i class="fas fa-film"></i> مدير المحتوى</a></li>
                <li><a href="upload_center.php"><i class="fas fa-upload"></i> مركز الرفع</a></li>
                <li><a href="users.php"><i class="fas fa-users"></i> إدارة المستخدمين</a></li>
                <li><a href="settings.php"><i class="fas fa-cog"></i> الإعدادات</a></li>
                <li><a href="../logout.php"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a></li>
            </ul>
        </aside>

        <main class="main-content">
            <div class="header">
                <h1><i class="fas fa-film"></i> مدير المحتوى</h1>
                <div>
                    <button class="btn" onclick="openModal('movieModal')">
                        <i class="fas fa-plus"></i> إضافة فيلم
                    </button>
                    <button class="btn btn-secondary" onclick="openModal('seriesModal')">
                        <i class="fas fa-plus"></i> إضافة مسلسل
                    </button>
                </div>
            </div>

            <?php if ($message): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i> <?php echo $message; ?>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-circle"></i> <?php echo $error; ?>
                </div>
            <?php endif; ?>

            <!-- إحصائيات -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number"><?php echo number_format($stats['total_movies']); ?></div>
                    <div class="stat-label">إجمالي الأفلام</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo number_format($stats['total_series']); ?></div>
                    <div class="stat-label">إجمالي المسلسلات</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo number_format($stats['active_movies']); ?></div>
                    <div class="stat-label">أفلام نشطة</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo number_format($stats['active_series']); ?></div>
                    <div class="stat-label">مسلسلات نشطة</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo number_format($stats['total_views']); ?></div>
                    <div class="stat-label">إجمالي المشاهدات</div>
                </div>
            </div>

            <!-- قسم الأفلام -->
            <div class="content-section">
                <div class="section-header">
                    <h2 class="section-title"><i class="fas fa-film"></i> الأفلام</h2>
                </div>

                <table class="content-table">
                    <thead>
                        <tr>
                            <th>العنوان</th>
                            <th>التصنيف</th>
                            <th>سنة الإنتاج</th>
                            <th>التقييم</th>
                            <th>المشاهدات</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($movies as $movie): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($movie['title']); ?></td>
                                <td><?php echo htmlspecialchars($movie['category_name'] ?? 'غير محدد'); ?></td>
                                <td><?php echo $movie['release_year']; ?></td>
                                <td><?php echo number_format($movie['rating'], 1); ?></td>
                                <td><?php echo number_format($movie['views']); ?></td>
                                <td>
                                    <span class="status-badge <?php echo $movie['status'] === 'active' ? 'status-active' : 'status-inactive'; ?>">
                                        <?php echo $movie['status'] === 'active' ? 'نشط' : 'غير نشط'; ?>
                                    </span>
                                </td>
                                <td>
                                    <form method="POST" style="display: inline;">
                                        <input type="hidden" name="action" value="toggle_status">
                                        <input type="hidden" name="content_id" value="<?php echo $movie['id']; ?>">
                                        <input type="hidden" name="content_type" value="movie">
                                        <input type="hidden" name="new_status" value="<?php echo $movie['status'] === 'active' ? 'inactive' : 'active'; ?>">
                                        <button type="submit" class="btn btn-secondary" style="padding: 0.5rem;">
                                            <i class="fas fa-<?php echo $movie['status'] === 'active' ? 'pause' : 'play'; ?>"></i>
                                        </button>
                                    </form>

                                    <form method="POST" style="display: inline;" onsubmit="return confirm('هل أنت متأكد من حذف هذا الفيلم؟')">
                                        <input type="hidden" name="action" value="delete_content">
                                        <input type="hidden" name="content_id" value="<?php echo $movie['id']; ?>">
                                        <input type="hidden" name="content_type" value="movie">
                                        <button type="submit" class="btn btn-danger" style="padding: 0.5rem;">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

            <!-- قسم المسلسلات -->
            <div class="content-section">
                <div class="section-header">
                    <h2 class="section-title"><i class="fas fa-tv"></i> المسلسلات</h2>
                </div>

                <table class="content-table">
                    <thead>
                        <tr>
                            <th>العنوان</th>
                            <th>التصنيف</th>
                            <th>سنة الإنتاج</th>
                            <th>المواسم</th>
                            <th>التقييم</th>
                            <th>المشاهدات</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($series as $serie): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($serie['title']); ?></td>
                                <td><?php echo htmlspecialchars($serie['category_name'] ?? 'غير محدد'); ?></td>
                                <td><?php echo $serie['release_year']; ?></td>
                                <td><?php echo $serie['total_seasons']; ?></td>
                                <td><?php echo number_format($serie['rating'], 1); ?></td>
                                <td><?php echo number_format($serie['views']); ?></td>
                                <td>
                                    <span class="status-badge <?php echo $serie['status'] === 'active' ? 'status-active' : 'status-inactive'; ?>">
                                        <?php echo $serie['status'] === 'active' ? 'نشط' : 'غير نشط'; ?>
                                    </span>
                                </td>
                                <td>
                                    <form method="POST" style="display: inline;">
                                        <input type="hidden" name="action" value="toggle_status">
                                        <input type="hidden" name="content_id" value="<?php echo $serie['id']; ?>">
                                        <input type="hidden" name="content_type" value="series">
                                        <input type="hidden" name="new_status" value="<?php echo $serie['status'] === 'active' ? 'inactive' : 'active'; ?>">
                                        <button type="submit" class="btn btn-secondary" style="padding: 0.5rem;">
                                            <i class="fas fa-<?php echo $serie['status'] === 'active' ? 'pause' : 'play'; ?>"></i>
                                        </button>
                                    </form>

                                    <form method="POST" style="display: inline;" onsubmit="return confirm('هل أنت متأكد من حذف هذا المسلسل؟')">
                                        <input type="hidden" name="action" value="delete_content">
                                        <input type="hidden" name="content_id" value="<?php echo $serie['id']; ?>">
                                        <input type="hidden" name="content_type" value="series">
                                        <button type="submit" class="btn btn-danger" style="padding: 0.5rem;">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </main>
    </div>

    <!-- نافذة إضافة فيلم -->
    <div id="movieModal" class="modal">
        <div class="modal-content">
            <button class="close" onclick="closeModal('movieModal')">&times;</button>
            <h3 style="color: #E50914; margin-bottom: 2rem;">إضافة فيلم جديد</h3>

            <form method="POST">
                <input type="hidden" name="action" value="add_movie">

                <div class="form-group">
                    <label>عنوان الفيلم</label>
                    <input type="text" name="title" required>
                </div>

                <div class="form-group">
                    <label>الوصف</label>
                    <textarea name="description" required></textarea>
                </div>

                <div class="form-group">
                    <label>سنة الإنتاج</label>
                    <input type="number" name="release_year" min="1900" max="2030" required>
                </div>

                <div class="form-group">
                    <label>التصنيف</label>
                    <select name="category_id" required>
                        <option value="">اختر التصنيف</option>
                        <?php foreach ($categories as $category): ?>
                            <option value="<?php echo $category['id']; ?>"><?php echo htmlspecialchars($category['name']); ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div class="form-group">
                    <label>رابط الفيديو</label>
                    <input type="url" name="video_url" required>
                </div>

                <div class="form-group">
                    <label>رابط الصورة</label>
                    <input type="url" name="poster">
                </div>

                <div class="form-group">
                    <label>التقييم</label>
                    <input type="number" name="rating" min="0" max="10" step="0.1" value="5.0">
                </div>

                <button type="submit" class="btn">
                    <i class="fas fa-save"></i> حفظ الفيلم
                </button>
            </form>
        </div>
    </div>

    <!-- نافذة إضافة مسلسل -->
    <div id="seriesModal" class="modal">
        <div class="modal-content">
            <button class="close" onclick="closeModal('seriesModal')">&times;</button>
            <h3 style="color: #E50914; margin-bottom: 2rem;">إضافة مسلسل جديد</h3>

            <form method="POST">
                <input type="hidden" name="action" value="add_series">

                <div class="form-group">
                    <label>عنوان المسلسل</label>
                    <input type="text" name="title" required>
                </div>

                <div class="form-group">
                    <label>الوصف</label>
                    <textarea name="description" required></textarea>
                </div>

                <div class="form-group">
                    <label>سنة الإنتاج</label>
                    <input type="number" name="release_year" min="1900" max="2030" required>
                </div>

                <div class="form-group">
                    <label>التصنيف</label>
                    <select name="category_id" required>
                        <option value="">اختر التصنيف</option>
                        <?php foreach ($categories as $category): ?>
                            <option value="<?php echo $category['id']; ?>"><?php echo htmlspecialchars($category['name']); ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div class="form-group">
                    <label>رابط الصورة</label>
                    <input type="url" name="poster">
                </div>

                <div class="form-group">
                    <label>التقييم</label>
                    <input type="number" name="rating" min="0" max="10" step="0.1" value="5.0">
                </div>

                <div class="form-group">
                    <label>عدد المواسم</label>
                    <input type="number" name="total_seasons" min="1" value="1" required>
                </div>

                <button type="submit" class="btn">
                    <i class="fas fa-save"></i> حفظ المسلسل
                </button>
            </form>
        </div>
    </div>

    <script>
        function openModal(modalId) {
            document.getElementById(modalId).style.display = 'block';
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        // إغلاق النافذة عند النقر خارجها
        window.onclick = function(event) {
            if (event.target.classList.contains('modal')) {
                event.target.style.display = 'none';
            }
        }

        // تأكيد الحذف
        function confirmDelete(type, title) {
            return confirm(`هل أنت متأكد من حذف ${type} "${title}"؟`);
        }

        console.log('مدير المحتوى جاهز!');
    </script>
</body>
</html>