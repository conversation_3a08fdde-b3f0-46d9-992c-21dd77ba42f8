/// نماذج البيانات الشاملة لتطبيق Shahid Platform
/// تحتوي على جميع النماذج المستخدمة في التطبيق

import 'dart:convert';

// ===== نموذج الفيلم =====
class Movie {
  final int id;
  final String title;
  final String titleAr;
  final String description;
  final String descriptionAr;
  final String? posterUrl;
  final String? trailerUrl;
  final String? videoUrl;
  final double rating;
  final int duration; // بالدقائق
  final String releaseDate;
  final String category;
  final String language;
  final String country;
  final String director;
  final List<String> cast;
  final List<String> genres;
  final int viewCount;
  final bool isPremium;
  final String status;
  final DateTime createdAt;
  final DateTime updatedAt;

  Movie({
    required this.id,
    required this.title,
    required this.titleAr,
    required this.description,
    required this.descriptionAr,
    this.posterUrl,
    this.trailerUrl,
    this.videoUrl,
    required this.rating,
    required this.duration,
    required this.releaseDate,
    required this.category,
    required this.language,
    required this.country,
    required this.director,
    required this.cast,
    required this.genres,
    required this.viewCount,
    required this.isPremium,
    required this.status,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Movie.fromJson(Map<String, dynamic> json) {
    return Movie(
      id: json['id'] ?? 0,
      title: json['title'] ?? '',
      titleAr: json['title_ar'] ?? '',
      description: json['description'] ?? '',
      descriptionAr: json['description_ar'] ?? '',
      posterUrl: json['poster_url'],
      trailerUrl: json['trailer_url'],
      videoUrl: json['video_url'],
      rating: (json['rating'] ?? 0).toDouble(),
      duration: json['duration'] ?? 0,
      releaseDate: json['release_date'] ?? '',
      category: json['category'] ?? '',
      language: json['language'] ?? '',
      country: json['country'] ?? '',
      director: json['director'] ?? '',
      cast: json['cast'] != null ? List<String>.from(json['cast'].split(',')) : [],
      genres: json['genres'] != null ? List<String>.from(json['genres'].split(',')) : [],
      viewCount: json['view_count'] ?? 0,
      isPremium: json['is_premium'] == 1 || json['is_premium'] == true,
      status: json['status'] ?? 'active',
      createdAt: DateTime.parse(json['created_at'] ?? DateTime.now().toIso8601String()),
      updatedAt: DateTime.parse(json['updated_at'] ?? DateTime.now().toIso8601String()),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'title_ar': titleAr,
      'description': description,
      'description_ar': descriptionAr,
      'poster_url': posterUrl,
      'trailer_url': trailerUrl,
      'video_url': videoUrl,
      'rating': rating,
      'duration': duration,
      'release_date': releaseDate,
      'category': category,
      'language': language,
      'country': country,
      'director': director,
      'cast': cast.join(','),
      'genres': genres.join(','),
      'view_count': viewCount,
      'is_premium': isPremium ? 1 : 0,
      'status': status,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  String get formattedDuration {
    final hours = duration ~/ 60;
    final minutes = duration % 60;
    if (hours > 0) {
      return '${hours}س ${minutes}د';
    }
    return '${minutes}د';
  }

  String get formattedRating => rating.toStringAsFixed(1);
}

// ===== نموذج المسلسل =====
class Series {
  final int id;
  final String title;
  final String titleAr;
  final String description;
  final String descriptionAr;
  final String? posterUrl;
  final String? trailerUrl;
  final double rating;
  final String releaseDate;
  final String category;
  final String language;
  final String country;
  final String director;
  final List<String> cast;
  final List<String> genres;
  final int totalSeasons;
  final int totalEpisodes;
  final int viewCount;
  final bool isPremium;
  final String status;
  final DateTime createdAt;
  final DateTime updatedAt;

  Series({
    required this.id,
    required this.title,
    required this.titleAr,
    required this.description,
    required this.descriptionAr,
    this.posterUrl,
    this.trailerUrl,
    required this.rating,
    required this.releaseDate,
    required this.category,
    required this.language,
    required this.country,
    required this.director,
    required this.cast,
    required this.genres,
    required this.totalSeasons,
    required this.totalEpisodes,
    required this.viewCount,
    required this.isPremium,
    required this.status,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Series.fromJson(Map<String, dynamic> json) {
    return Series(
      id: json['id'] ?? 0,
      title: json['title'] ?? '',
      titleAr: json['title_ar'] ?? '',
      description: json['description'] ?? '',
      descriptionAr: json['description_ar'] ?? '',
      posterUrl: json['poster_url'],
      trailerUrl: json['trailer_url'],
      rating: (json['rating'] ?? 0).toDouble(),
      releaseDate: json['release_date'] ?? '',
      category: json['category'] ?? '',
      language: json['language'] ?? '',
      country: json['country'] ?? '',
      director: json['director'] ?? '',
      cast: json['cast'] != null ? List<String>.from(json['cast'].split(',')) : [],
      genres: json['genres'] != null ? List<String>.from(json['genres'].split(',')) : [],
      totalSeasons: json['total_seasons'] ?? 0,
      totalEpisodes: json['total_episodes'] ?? 0,
      viewCount: json['view_count'] ?? 0,
      isPremium: json['is_premium'] == 1 || json['is_premium'] == true,
      status: json['status'] ?? 'active',
      createdAt: DateTime.parse(json['created_at'] ?? DateTime.now().toIso8601String()),
      updatedAt: DateTime.parse(json['updated_at'] ?? DateTime.now().toIso8601String()),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'title_ar': titleAr,
      'description': description,
      'description_ar': descriptionAr,
      'poster_url': posterUrl,
      'trailer_url': trailerUrl,
      'rating': rating,
      'release_date': releaseDate,
      'category': category,
      'language': language,
      'country': country,
      'director': director,
      'cast': cast.join(','),
      'genres': genres.join(','),
      'total_seasons': totalSeasons,
      'total_episodes': totalEpisodes,
      'view_count': viewCount,
      'is_premium': isPremium ? 1 : 0,
      'status': status,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  String get formattedInfo => '$totalSeasons مواسم • $totalEpisodes حلقة';
  String get formattedRating => rating.toStringAsFixed(1);
}

// ===== نموذج الحلقة =====
class Episode {
  final int id;
  final int seriesId;
  final String title;
  final String titleAr;
  final String description;
  final String descriptionAr;
  final String? thumbnailUrl;
  final String? videoUrl;
  final int seasonNumber;
  final int episodeNumber;
  final int duration; // بالدقائق
  final String releaseDate;
  final int viewCount;
  final bool isPremium;
  final String status;
  final DateTime createdAt;
  final DateTime updatedAt;

  Episode({
    required this.id,
    required this.seriesId,
    required this.title,
    required this.titleAr,
    required this.description,
    required this.descriptionAr,
    this.thumbnailUrl,
    this.videoUrl,
    required this.seasonNumber,
    required this.episodeNumber,
    required this.duration,
    required this.releaseDate,
    required this.viewCount,
    required this.isPremium,
    required this.status,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Episode.fromJson(Map<String, dynamic> json) {
    return Episode(
      id: json['id'] ?? 0,
      seriesId: json['series_id'] ?? 0,
      title: json['title'] ?? '',
      titleAr: json['title_ar'] ?? '',
      description: json['description'] ?? '',
      descriptionAr: json['description_ar'] ?? '',
      thumbnailUrl: json['thumbnail_url'],
      videoUrl: json['video_url'],
      seasonNumber: json['season_number'] ?? 1,
      episodeNumber: json['episode_number'] ?? 1,
      duration: json['duration'] ?? 0,
      releaseDate: json['release_date'] ?? '',
      viewCount: json['view_count'] ?? 0,
      isPremium: json['is_premium'] == 1 || json['is_premium'] == true,
      status: json['status'] ?? 'active',
      createdAt: DateTime.parse(json['created_at'] ?? DateTime.now().toIso8601String()),
      updatedAt: DateTime.parse(json['updated_at'] ?? DateTime.now().toIso8601String()),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'series_id': seriesId,
      'title': title,
      'title_ar': titleAr,
      'description': description,
      'description_ar': descriptionAr,
      'thumbnail_url': thumbnailUrl,
      'video_url': videoUrl,
      'season_number': seasonNumber,
      'episode_number': episodeNumber,
      'duration': duration,
      'release_date': releaseDate,
      'view_count': viewCount,
      'is_premium': isPremium ? 1 : 0,
      'status': status,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  String get formattedDuration {
    final hours = duration ~/ 60;
    final minutes = duration % 60;
    if (hours > 0) {
      return '${hours}س ${minutes}د';
    }
    return '${minutes}د';
  }

  String get episodeInfo => 'الموسم $seasonNumber • الحلقة $episodeNumber';
}

// ===== نموذج المستخدم =====
class User {
  final int id;
  final String username;
  final String email;
  final String? fullName;
  final String? avatar;
  final String? phone;
  final String? dateOfBirth;
  final String? gender;
  final String? country;
  final String role;
  final String subscriptionType;
  final DateTime? subscriptionExpiry;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  User({
    required this.id,
    required this.username,
    required this.email,
    this.fullName,
    this.avatar,
    this.phone,
    this.dateOfBirth,
    this.gender,
    this.country,
    required this.role,
    required this.subscriptionType,
    this.subscriptionExpiry,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'] ?? 0,
      username: json['username'] ?? '',
      email: json['email'] ?? '',
      fullName: json['full_name'],
      avatar: json['avatar'],
      phone: json['phone'],
      dateOfBirth: json['date_of_birth'],
      gender: json['gender'],
      country: json['country'],
      role: json['role'] ?? 'user',
      subscriptionType: json['subscription_type'] ?? 'free',
      subscriptionExpiry: json['subscription_expiry'] != null 
          ? DateTime.parse(json['subscription_expiry']) 
          : null,
      isActive: json['is_active'] == 1 || json['is_active'] == true,
      createdAt: DateTime.parse(json['created_at'] ?? DateTime.now().toIso8601String()),
      updatedAt: DateTime.parse(json['updated_at'] ?? DateTime.now().toIso8601String()),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'username': username,
      'email': email,
      'full_name': fullName,
      'avatar': avatar,
      'phone': phone,
      'date_of_birth': dateOfBirth,
      'gender': gender,
      'country': country,
      'role': role,
      'subscription_type': subscriptionType,
      'subscription_expiry': subscriptionExpiry?.toIso8601String(),
      'is_active': isActive ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  bool get isPremium => subscriptionType != 'free';
  bool get isSubscriptionActive => subscriptionExpiry != null && 
      subscriptionExpiry!.isAfter(DateTime.now());
  String get displayName => fullName ?? username;
}

// ===== نموذج التقييم =====
class Rating {
  final int id;
  final int userId;
  final int contentId;
  final String contentType; // movie, series, episode
  final double rating;
  final String? comment;
  final DateTime createdAt;
  final DateTime updatedAt;

  Rating({
    required this.id,
    required this.userId,
    required this.contentId,
    required this.contentType,
    required this.rating,
    this.comment,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Rating.fromJson(Map<String, dynamic> json) {
    return Rating(
      id: json['id'] ?? 0,
      userId: json['user_id'] ?? 0,
      contentId: json['content_id'] ?? 0,
      contentType: json['content_type'] ?? '',
      rating: (json['rating'] ?? 0).toDouble(),
      comment: json['comment'],
      createdAt: DateTime.parse(json['created_at'] ?? DateTime.now().toIso8601String()),
      updatedAt: DateTime.parse(json['updated_at'] ?? DateTime.now().toIso8601String()),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'content_id': contentId,
      'content_type': contentType,
      'rating': rating,
      'comment': comment,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }
}

// ===== نموذج المفضلة =====
class Favorite {
  final int id;
  final int userId;
  final int contentId;
  final String contentType; // movie, series
  final DateTime createdAt;

  Favorite({
    required this.id,
    required this.userId,
    required this.contentId,
    required this.contentType,
    required this.createdAt,
  });

  factory Favorite.fromJson(Map<String, dynamic> json) {
    return Favorite(
      id: json['id'] ?? 0,
      userId: json['user_id'] ?? 0,
      contentId: json['content_id'] ?? 0,
      contentType: json['content_type'] ?? '',
      createdAt: DateTime.parse(json['created_at'] ?? DateTime.now().toIso8601String()),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'content_id': contentId,
      'content_type': contentType,
      'created_at': createdAt.toIso8601String(),
    };
  }
}

// ===== نموذج سجل المشاهدة =====
class WatchHistory {
  final int id;
  final int userId;
  final int contentId;
  final String contentType; // movie, series, episode
  final int watchedDuration; // بالثواني
  final int totalDuration; // بالثواني
  final DateTime lastWatched;

  WatchHistory({
    required this.id,
    required this.userId,
    required this.contentId,
    required this.contentType,
    required this.watchedDuration,
    required this.totalDuration,
    required this.lastWatched,
  });

  factory WatchHistory.fromJson(Map<String, dynamic> json) {
    return WatchHistory(
      id: json['id'] ?? 0,
      userId: json['user_id'] ?? 0,
      contentId: json['content_id'] ?? 0,
      contentType: json['content_type'] ?? '',
      watchedDuration: json['watched_duration'] ?? 0,
      totalDuration: json['total_duration'] ?? 0,
      lastWatched: DateTime.parse(json['last_watched'] ?? DateTime.now().toIso8601String()),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'content_id': contentId,
      'content_type': contentType,
      'watched_duration': watchedDuration,
      'total_duration': totalDuration,
      'last_watched': lastWatched.toIso8601String(),
    };
  }

  double get progressPercentage => 
      totalDuration > 0 ? (watchedDuration / totalDuration) * 100 : 0;
  
  bool get isCompleted => progressPercentage >= 90; // اعتبار 90% كمكتمل
}

// ===== نموذج الإشعار =====
class AppNotification {
  final int id;
  final int? userId;
  final String title;
  final String message;
  final String type; // info, success, warning, error
  final bool isRead;
  final DateTime createdAt;

  AppNotification({
    required this.id,
    this.userId,
    required this.title,
    required this.message,
    required this.type,
    required this.isRead,
    required this.createdAt,
  });

  factory AppNotification.fromJson(Map<String, dynamic> json) {
    return AppNotification(
      id: json['id'] ?? 0,
      userId: json['user_id'],
      title: json['title'] ?? '',
      message: json['message'] ?? '',
      type: json['type'] ?? 'info',
      isRead: json['is_read'] == 1 || json['is_read'] == true,
      createdAt: DateTime.parse(json['created_at'] ?? DateTime.now().toIso8601String()),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'title': title,
      'message': message,
      'type': type,
      'is_read': isRead ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
    };
  }
}

// ===== نموذج الاستجابة العامة =====
class ApiResponse<T> {
  final bool success;
  final String message;
  final T? data;
  final Map<String, dynamic>? meta;
  final List<String>? errors;

  ApiResponse({
    required this.success,
    required this.message,
    this.data,
    this.meta,
    this.errors,
  });

  factory ApiResponse.fromJson(Map<String, dynamic> json, T Function(dynamic)? fromJsonT) {
    return ApiResponse<T>(
      success: json['success'] ?? false,
      message: json['message'] ?? '',
      data: json['data'] != null && fromJsonT != null ? fromJsonT(json['data']) : json['data'],
      meta: json['meta'],
      errors: json['errors'] != null ? List<String>.from(json['errors']) : null,
    );
  }
}

// ===== نموذج البحث =====
class SearchResult {
  final List<Movie> movies;
  final List<Series> series;
  final int totalResults;
  final String query;

  SearchResult({
    required this.movies,
    required this.series,
    required this.totalResults,
    required this.query,
  });

  factory SearchResult.fromJson(Map<String, dynamic> json) {
    return SearchResult(
      movies: json['movies'] != null 
          ? (json['movies'] as List).map((e) => Movie.fromJson(e)).toList()
          : [],
      series: json['series'] != null 
          ? (json['series'] as List).map((e) => Series.fromJson(e)).toList()
          : [],
      totalResults: json['total_results'] ?? 0,
      query: json['query'] ?? '',
    );
  }

  bool get hasResults => totalResults > 0;
  bool get isEmpty => totalResults == 0;
}
