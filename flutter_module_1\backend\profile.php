<?php
session_start();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

$user_id = $_SESSION['user_id'];
$user = null;
$stats = [];
$recentActivity = [];
$error = '';
$success = '';

try {
    $pdo = new PDO("mysql:host=localhost;dbname=shahid_platform;charset=utf8mb4", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // جلب بيانات المستخدم
    $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->execute([$user_id]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$user) {
        session_destroy();
        header('Location: login.php');
        exit;
    }
    
    // جلب إحصائيات المستخدم
    $statsQueries = [
        'favorites_count' => "SELECT COUNT(*) FROM favorites WHERE user_id = ?",
        'watch_history_count' => "SELECT COUNT(*) FROM watch_history WHERE user_id = ?",
        'total_watch_time' => "SELECT SUM(watch_time) FROM watch_history WHERE user_id = ?",
        'movies_watched' => "SELECT COUNT(DISTINCT content_id) FROM watch_history WHERE user_id = ? AND content_type = 'movie'",
        'series_watched' => "SELECT COUNT(DISTINCT content_id) FROM watch_history WHERE user_id = ? AND content_type = 'series'"
    ];
    
    foreach ($statsQueries as $key => $query) {
        $stmt = $pdo->prepare($query);
        $stmt->execute([$user_id]);
        $stats[$key] = $stmt->fetchColumn() ?: 0;
    }
    
    // جلب النشاط الأخير
    $activityStmt = $pdo->prepare("
        SELECT 
            wh.content_type,
            wh.content_id,
            wh.last_watched,
            wh.progress,
            CASE 
                WHEN wh.content_type = 'movie' THEN m.title
                WHEN wh.content_type = 'series' THEN s.title
            END as title,
            CASE 
                WHEN wh.content_type = 'movie' THEN m.poster
                WHEN wh.content_type = 'series' THEN s.poster
            END as poster
        FROM watch_history wh
        LEFT JOIN movies m ON wh.content_type = 'movie' AND wh.content_id = m.id
        LEFT JOIN series s ON wh.content_type = 'series' AND wh.content_id = s.id
        WHERE wh.user_id = ?
        ORDER BY wh.last_watched DESC
        LIMIT 10
    ");
    $activityStmt->execute([$user_id]);
    $recentActivity = $activityStmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch (Exception $e) {
    $error = 'حدث خطأ في تحميل البيانات';
}

// معالجة تحديث الملف الشخصي
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_profile'])) {
    try {
        $full_name = trim($_POST['full_name'] ?? '');
        $email = trim($_POST['email'] ?? '');
        $phone = trim($_POST['phone'] ?? '');
        $country = trim($_POST['country'] ?? '');
        $birth_date = $_POST['birth_date'] ?? '';
        $gender = $_POST['gender'] ?? '';
        
        if (empty($full_name) || empty($email)) {
            $error = 'الاسم والبريد الإلكتروني مطلوبان';
        } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $error = 'البريد الإلكتروني غير صحيح';
        } else {
            // التحقق من عدم وجود البريد الإلكتروني لمستخدم آخر
            $checkStmt = $pdo->prepare("SELECT id FROM users WHERE email = ? AND id != ?");
            $checkStmt->execute([$email, $user_id]);
            
            if ($checkStmt->fetch()) {
                $error = 'البريد الإلكتروني مستخدم من قبل مستخدم آخر';
            } else {
                // تحديث البيانات
                $updateStmt = $pdo->prepare("
                    UPDATE users 
                    SET full_name = ?, email = ?, phone = ?, country = ?, birth_date = ?, gender = ?, updated_at = NOW()
                    WHERE id = ?
                ");
                
                $updateStmt->execute([
                    $full_name, $email, $phone, $country, 
                    $birth_date ?: null, $gender ?: null, $user_id
                ]);
                
                // تحديث بيانات الجلسة
                $_SESSION['email'] = $email;
                $_SESSION['full_name'] = $full_name;
                
                $success = 'تم تحديث الملف الشخصي بنجاح';
                
                // إعادة جلب البيانات المحدثة
                $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
                $stmt->execute([$user_id]);
                $user = $stmt->fetch(PDO::FETCH_ASSOC);
            }
        }
    } catch (Exception $e) {
        $error = 'حدث خطأ أثناء التحديث';
    }
}

// معالجة تغيير كلمة المرور
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['change_password'])) {
    try {
        $current_password = $_POST['current_password'] ?? '';
        $new_password = $_POST['new_password'] ?? '';
        $confirm_password = $_POST['confirm_password'] ?? '';
        
        if (empty($current_password) || empty($new_password) || empty($confirm_password)) {
            $error = 'جميع حقول كلمة المرور مطلوبة';
        } elseif (strlen($new_password) < 6) {
            $error = 'كلمة المرور الجديدة يجب أن تكون 6 أحرف على الأقل';
        } elseif ($new_password !== $confirm_password) {
            $error = 'كلمة المرور الجديدة وتأكيدها غير متطابقتين';
        } elseif (!password_verify($current_password, $user['password'])) {
            $error = 'كلمة المرور الحالية غير صحيحة';
        } else {
            // تحديث كلمة المرور
            $hashedPassword = password_hash($new_password, PASSWORD_DEFAULT);
            $updateStmt = $pdo->prepare("UPDATE users SET password = ?, updated_at = NOW() WHERE id = ?");
            $updateStmt->execute([$hashedPassword, $user_id]);
            
            $success = 'تم تغيير كلمة المرور بنجاح';
        }
    } catch (Exception $e) {
        $error = 'حدث خطأ أثناء تغيير كلمة المرور';
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الملف الشخصي - Shahid Platform</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', 'Cairo', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #2d2d2d 100%);
            color: #fff;
            min-height: 100vh;
        }
        
        .header {
            background: rgba(47, 47, 47, 0.95);
            padding: 1rem 0;
            border-bottom: 2px solid rgba(229, 9, 20, 0.3);
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }
        
        .nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }
        
        .logo {
            color: #E50914;
            font-size: 1.8rem;
            font-weight: bold;
            text-decoration: none;
        }
        
        .nav-links {
            display: flex;
            gap: 2rem;
            align-items: center;
        }
        
        .nav-links a {
            color: #fff;
            text-decoration: none;
            transition: color 0.3s ease;
        }
        
        .nav-links a:hover, .nav-links a.active {
            color: #E50914;
        }
        
        .profile-section {
            padding: 3rem 0;
        }
        
        .profile-header {
            background: rgba(47, 47, 47, 0.8);
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .profile-avatar {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: linear-gradient(45deg, #E50914, #B8070F);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            margin: 0 auto 1rem;
            border: 4px solid rgba(229, 9, 20, 0.3);
        }
        
        .profile-name {
            font-size: 2rem;
            color: #E50914;
            margin-bottom: 0.5rem;
        }
        
        .profile-email {
            color: #ccc;
            font-size: 1.1rem;
            margin-bottom: 1rem;
        }
        
        .subscription-badge {
            display: inline-block;
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: #fff;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: bold;
            text-transform: uppercase;
        }
        
        .profile-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
        }
        
        .profile-card {
            background: rgba(47, 47, 47, 0.8);
            border-radius: 15px;
            padding: 2rem;
            border: 2px solid rgba(229, 9, 20, 0.1);
        }
        
        .card-title {
            color: #E50914;
            font-size: 1.5rem;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 1rem;
        }
        
        .stat-item {
            text-align: center;
            background: rgba(229, 9, 20, 0.1);
            padding: 1rem;
            border-radius: 10px;
            border: 1px solid rgba(229, 9, 20, 0.2);
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #E50914;
            display: block;
        }
        
        .stat-label {
            color: #ccc;
            font-size: 0.9rem;
            margin-top: 0.25rem;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: #ccc;
            font-weight: 500;
        }
        
        .form-group input,
        .form-group select {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.05);
            color: #fff;
            font-size: 1rem;
        }
        
        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #E50914;
            background: rgba(255, 255, 255, 0.1);
        }
        
        .btn {
            padding: 0.75rem 1.5rem;
            background: linear-gradient(45deg, #E50914, #B8070F);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(229, 9, 20, 0.3);
        }
        
        .error {
            background: rgba(244, 67, 54, 0.1);
            border: 1px solid rgba(244, 67, 54, 0.3);
            color: #F44336;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
        }
        
        .success {
            background: rgba(76, 175, 80, 0.1);
            border: 1px solid rgba(76, 175, 80, 0.3);
            color: #4CAF50;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
        }
        
        .activity-list {
            max-height: 400px;
            overflow-y: auto;
        }
        
        .activity-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            transition: background 0.3s ease;
        }
        
        .activity-item:hover {
            background: rgba(229, 9, 20, 0.05);
        }
        
        .activity-poster {
            width: 60px;
            height: 60px;
            border-radius: 8px;
            background: linear-gradient(45deg, #333, #555);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: #666;
        }
        
        .activity-poster img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 8px;
        }
        
        .activity-info {
            flex: 1;
        }
        
        .activity-title {
            font-weight: bold;
            margin-bottom: 0.25rem;
        }
        
        .activity-meta {
            color: #ccc;
            font-size: 0.9rem;
        }
        
        .progress-bar {
            width: 100%;
            height: 4px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 2px;
            margin-top: 0.5rem;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background: #E50914;
            border-radius: 2px;
            transition: width 0.3s ease;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 0 1rem;
            }
            
            .nav {
                flex-direction: column;
                gap: 1rem;
            }
            
            .profile-content {
                grid-template-columns: 1fr;
                gap: 1rem;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .profile-name {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="container">
            <nav class="nav">
                <a href="index.php" class="logo">🎬 شاهد</a>
                <div class="nav-links">
                    <a href="index.php">الرئيسية</a>
                    <a href="movies.php">الأفلام</a>
                    <a href="series.php">المسلسلات</a>
                    <a href="search.php">البحث</a>
                    <a href="profile.php" class="active">الملف الشخصي</a>
                    <a href="favorites.php">المفضلة</a>
                    <a href="logout.php">تسجيل الخروج</a>
                </div>
            </nav>
        </div>
    </header>
    
    <section class="profile-section">
        <div class="container">
            <?php if ($error): ?>
                <div class="error">
                    <strong>خطأ:</strong> <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="success">
                    <?php echo htmlspecialchars($success); ?>
                </div>
            <?php endif; ?>
            
            <div class="profile-header">
                <div class="profile-avatar">
                    👤
                </div>
                <h1 class="profile-name"><?php echo htmlspecialchars($user['full_name']); ?></h1>
                <p class="profile-email"><?php echo htmlspecialchars($user['email']); ?></p>
                <span class="subscription-badge">
                    <?php echo strtoupper($user['subscription_type']); ?>
                </span>
            </div>
            
            <div class="profile-content">
                <!-- إحصائيات المستخدم -->
                <div class="profile-card">
                    <h2 class="card-title">📊 إحصائياتك</h2>
                    
                    <div class="stats-grid">
                        <div class="stat-item">
                            <span class="stat-number"><?php echo $stats['favorites_count']; ?></span>
                            <span class="stat-label">المفضلة</span>
                        </div>
                        
                        <div class="stat-item">
                            <span class="stat-number"><?php echo $stats['movies_watched']; ?></span>
                            <span class="stat-label">أفلام</span>
                        </div>
                        
                        <div class="stat-item">
                            <span class="stat-number"><?php echo $stats['series_watched']; ?></span>
                            <span class="stat-label">مسلسلات</span>
                        </div>
                        
                        <div class="stat-item">
                            <span class="stat-number"><?php echo round($stats['total_watch_time'] / 3600, 1); ?></span>
                            <span class="stat-label">ساعات مشاهدة</span>
                        </div>
                    </div>
                </div>
                
                <!-- النشاط الأخير -->
                <div class="profile-card">
                    <h2 class="card-title">🕒 النشاط الأخير</h2>
                    
                    <?php if (empty($recentActivity)): ?>
                        <p style="color: #ccc; text-align: center; padding: 2rem;">
                            لا يوجد نشاط حديث
                        </p>
                    <?php else: ?>
                        <div class="activity-list">
                            <?php foreach ($recentActivity as $activity): ?>
                                <div class="activity-item">
                                    <div class="activity-poster">
                                        <?php if (!empty($activity['poster'])): ?>
                                            <img src="<?php echo htmlspecialchars($activity['poster']); ?>" 
                                                 alt="<?php echo htmlspecialchars($activity['title']); ?>">
                                        <?php else: ?>
                                            <?php echo $activity['content_type'] === 'movie' ? '🎬' : '📺'; ?>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <div class="activity-info">
                                        <div class="activity-title">
                                            <?php echo htmlspecialchars($activity['title']); ?>
                                        </div>
                                        <div class="activity-meta">
                                            <?php echo $activity['content_type'] === 'movie' ? 'فيلم' : 'مسلسل'; ?> • 
                                            <?php echo date('Y-m-d H:i', strtotime($activity['last_watched'])); ?>
                                        </div>
                                        
                                        <?php if ($activity['progress'] > 0): ?>
                                            <div class="progress-bar">
                                                <div class="progress-fill" style="width: <?php echo min(100, $activity['progress']); ?>%"></div>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </section>
    
    <script>
        // تأثيرات تفاعلية
        document.querySelectorAll('.stat-item').forEach(item => {
            item.addEventListener('mouseenter', function() {
                this.style.transform = 'scale(1.05)';
            });
            
            item.addEventListener('mouseleave', function() {
                this.style.transform = 'scale(1)';
            });
        });
    </script>
</body>
</html>
