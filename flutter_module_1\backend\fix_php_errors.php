<?php
/**
 * إصلاح أخطاء PHP في المشروع
 * Fix PHP Errors in Project
 */

echo "<h1>🔧 إصلاح أخطاء PHP</h1>";

// تعطيل عرض الأخطاء مؤقتاً لتجنب التداخل
error_reporting(E_ALL & ~E_WARNING & ~E_NOTICE);

try {
    $pdo = new PDO("mysql:host=localhost;dbname=shahid_platform;charset=utf8mb4", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>✅ تم الاتصال بقاعدة البيانات بنجاح</h3>";
    echo "</div>";
    
    // 1. فحص هيكل جدول المستخدمين والتأكد من وجود جميع الأعمدة
    echo "<h2>👥 فحص وإصلاح هيكل جدول المستخدمين</h2>";
    
    // الحصول على هيكل الجدول الحالي
    $stmt = $pdo->query("DESCRIBE users");
    $existingColumns = [];
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $existingColumns[] = $row['Field'];
    }
    
    echo "<p style='color: #2196F3;'>الأعمدة الموجودة حالياً: " . implode(', ', $existingColumns) . "</p>";
    
    // الأعمدة المطلوبة مع تعريفاتها
    $requiredColumns = [
        'id' => 'int(11) NOT NULL AUTO_INCREMENT',
        'username' => 'varchar(50) UNIQUE',
        'email' => 'varchar(100) NOT NULL UNIQUE',
        'password' => 'varchar(255) NOT NULL',
        'full_name' => 'varchar(100) DEFAULT NULL',
        'phone' => 'varchar(20) DEFAULT NULL',
        'avatar' => 'varchar(255) DEFAULT NULL',
        'birth_date' => 'date DEFAULT NULL',
        'gender' => "enum('male','female','other') DEFAULT NULL",
        'country' => 'varchar(50) DEFAULT NULL',
        'language' => "varchar(10) DEFAULT 'ar'",
        'subscription_type' => "enum('free','premium','vip') DEFAULT 'free'",
        'subscription_start' => 'datetime DEFAULT NULL',
        'subscription_end' => 'datetime DEFAULT NULL',
        'is_active' => 'tinyint(1) DEFAULT 1',
        'is_verified' => 'tinyint(1) DEFAULT 0',
        'verification_token' => 'varchar(255) DEFAULT NULL',
        'reset_token' => 'varchar(255) DEFAULT NULL',
        'last_login' => 'datetime DEFAULT NULL',
        'login_attempts' => 'int(11) DEFAULT 0',
        'created_at' => 'timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP',
        'updated_at' => 'timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'
    ];
    
    $addedColumns = 0;
    $skippedColumns = 0;
    
    foreach ($requiredColumns as $columnName => $columnDef) {
        if (!in_array($columnName, $existingColumns)) {
            try {
                $pdo->exec("ALTER TABLE users ADD COLUMN $columnName $columnDef");
                echo "<p style='color: #4CAF50;'>✅ تم إضافة عمود: <strong>$columnName</strong></p>";
                $addedColumns++;
            } catch (Exception $e) {
                echo "<p style='color: #F44336;'>❌ خطأ في إضافة عمود $columnName: " . htmlspecialchars($e->getMessage()) . "</p>";
            }
        } else {
            $skippedColumns++;
        }
    }
    
    echo "<p style='color: #2196F3;'>📊 ملخص: تم إضافة $addedColumns عمود، $skippedColumns عمود موجود مسبقاً</p>";
    
    // 2. إضافة بيانات افتراضية للمستخدمين الموجودين
    echo "<h2>🔄 تحديث البيانات الافتراضية</h2>";
    
    try {
        // تحديث المستخدمين الذين لا يملكون username
        $pdo->exec("UPDATE users SET username = SUBSTRING_INDEX(email, '@', 1) WHERE username IS NULL OR username = ''");
        
        // تحديث المستخدمين الذين لا يملكون full_name
        $pdo->exec("UPDATE users SET full_name = username WHERE full_name IS NULL OR full_name = ''");
        
        // تحديث اللغة الافتراضية
        $pdo->exec("UPDATE users SET language = 'ar' WHERE language IS NULL OR language = ''");
        
        // تحديث نوع الاشتراك الافتراضي
        $pdo->exec("UPDATE users SET subscription_type = 'free' WHERE subscription_type IS NULL OR subscription_type = ''");
        
        // تحديث الحالة الافتراضية
        $pdo->exec("UPDATE users SET is_active = 1 WHERE is_active IS NULL");
        $pdo->exec("UPDATE users SET is_verified = 0 WHERE is_verified IS NULL");
        $pdo->exec("UPDATE users SET login_attempts = 0 WHERE login_attempts IS NULL");
        
        echo "<p style='color: #4CAF50;'>✅ تم تحديث البيانات الافتراضية للمستخدمين</p>";
        
    } catch (Exception $e) {
        echo "<p style='color: #F44336;'>❌ خطأ في تحديث البيانات: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
    
    // 3. إنشاء مستخدمين تجريبيين إذا لم يكونوا موجودين
    echo "<h2>👤 فحص المستخدمين التجريبيين</h2>";
    
    $testUsers = [
        [
            'username' => 'admin',
            'email' => '<EMAIL>',
            'password' => 'admin123',
            'full_name' => 'مدير النظام',
            'subscription_type' => 'vip',
            'is_active' => 1,
            'is_verified' => 1
        ],
        [
            'username' => 'testuser',
            'email' => '<EMAIL>',
            'password' => '123456',
            'full_name' => 'مستخدم تجريبي',
            'subscription_type' => 'free',
            'is_active' => 1,
            'is_verified' => 1
        ],
        [
            'username' => 'premium',
            'email' => '<EMAIL>',
            'password' => '123456',
            'full_name' => 'مستخدم مميز',
            'subscription_type' => 'premium',
            'is_active' => 1,
            'is_verified' => 1
        ]
    ];
    
    foreach ($testUsers as $user) {
        try {
            // التحقق من وجود المستخدم
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE email = ?");
            $stmt->execute([$user['email']]);
            
            if ($stmt->fetchColumn() == 0) {
                // إنشاء المستخدم
                $hashedPassword = password_hash($user['password'], PASSWORD_DEFAULT);
                
                $stmt = $pdo->prepare("
                    INSERT INTO users (username, email, password, full_name, subscription_type, is_active, is_verified, language, login_attempts, created_at) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, 'ar', 0, NOW())
                ");
                
                $stmt->execute([
                    $user['username'],
                    $user['email'],
                    $hashedPassword,
                    $user['full_name'],
                    $user['subscription_type'],
                    $user['is_active'],
                    $user['is_verified']
                ]);
                
                echo "<p style='color: #4CAF50;'>✅ تم إنشاء مستخدم: <strong>{$user['email']}</strong> / {$user['password']}</p>";
            } else {
                echo "<p style='color: #2196F3;'>ℹ️ المستخدم موجود: <strong>{$user['email']}</strong></p>";
            }
            
        } catch (Exception $e) {
            echo "<p style='color: #F44336;'>❌ خطأ في إنشاء المستخدم {$user['email']}: " . htmlspecialchars($e->getMessage()) . "</p>";
        }
    }
    
    // 4. فحص وإصلاح الجداول الأخرى
    echo "<h2>🗄️ فحص الجداول الأخرى</h2>";
    
    $criticalTables = [
        'movies' => "CREATE TABLE IF NOT EXISTS `movies` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `title` varchar(255) NOT NULL,
            `title_en` varchar(255) DEFAULT NULL,
            `description` text DEFAULT NULL,
            `description_en` text DEFAULT NULL,
            `poster` varchar(255) DEFAULT NULL,
            `trailer_url` varchar(500) DEFAULT NULL,
            `video_url` varchar(500) DEFAULT NULL,
            `duration` int(11) DEFAULT NULL,
            `release_year` int(4) DEFAULT NULL,
            `rating` decimal(3,1) DEFAULT 0.0,
            `views` int(11) DEFAULT 0,
            `category_id` int(11) DEFAULT NULL,
            `status` enum('active','inactive','coming_soon') DEFAULT 'active',
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `idx_category` (`category_id`),
            KEY `idx_status` (`status`),
            KEY `idx_rating` (`rating`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
        
        'series' => "CREATE TABLE IF NOT EXISTS `series` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `title` varchar(255) NOT NULL,
            `title_en` varchar(255) DEFAULT NULL,
            `description` text DEFAULT NULL,
            `description_en` text DEFAULT NULL,
            `poster` varchar(255) DEFAULT NULL,
            `trailer_url` varchar(500) DEFAULT NULL,
            `release_year` int(4) DEFAULT NULL,
            `rating` decimal(3,1) DEFAULT 0.0,
            `views` int(11) DEFAULT 0,
            `total_seasons` int(11) DEFAULT 1,
            `total_episodes` int(11) DEFAULT 0,
            `category_id` int(11) DEFAULT NULL,
            `status` enum('active','inactive','coming_soon') DEFAULT 'active',
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `idx_category` (`category_id`),
            KEY `idx_status` (`status`),
            KEY `idx_rating` (`rating`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
        
        'categories' => "CREATE TABLE IF NOT EXISTS `categories` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `name` varchar(100) NOT NULL,
            `name_en` varchar(100) DEFAULT NULL,
            `description` text DEFAULT NULL,
            `icon` varchar(100) DEFAULT NULL,
            `color` varchar(7) DEFAULT '#E50914',
            `sort_order` int(11) DEFAULT 0,
            `status` enum('active','inactive') DEFAULT 'active',
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `idx_status` (`status`),
            KEY `idx_sort` (`sort_order`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci"
    ];
    
    foreach ($criticalTables as $tableName => $sql) {
        try {
            $pdo->exec($sql);
            echo "<p style='color: #4CAF50;'>✅ جدول <strong>$tableName</strong> جاهز</p>";
        } catch (Exception $e) {
            echo "<p style='color: #F44336;'>❌ خطأ في جدول $tableName: " . htmlspecialchars($e->getMessage()) . "</p>";
        }
    }
    
    // 5. إضافة بيانات تجريبية للتصنيفات إذا لم تكن موجودة
    echo "<h2>📂 فحص التصنيفات</h2>";
    
    try {
        $stmt = $pdo->query("SELECT COUNT(*) FROM categories");
        $categoriesCount = $stmt->fetchColumn();
        
        if ($categoriesCount == 0) {
            $defaultCategories = [
                ['اكشن', 'Action', 'أفلام ومسلسلات الأكشن والإثارة', '🎬', '#E50914', 1],
                ['دراما', 'Drama', 'أفلام ومسلسلات الدراما', '🎭', '#FF6B35', 2],
                ['كوميديا', 'Comedy', 'أفلام ومسلسلات الكوميديا', '😂', '#4CAF50', 3],
                ['رومانسي', 'Romance', 'أفلام ومسلسلات رومانسية', '💕', '#E91E63', 4],
                ['خيال علمي', 'Sci-Fi', 'أفلام ومسلسلات الخيال العلمي', '🚀', '#2196F3', 5],
                ['رعب', 'Horror', 'أفلام ومسلسلات الرعب', '👻', '#9C27B0', 6],
                ['مغامرات', 'Adventure', 'أفلام ومسلسلات المغامرات', '🗺️', '#FF9800', 7],
                ['وثائقي', 'Documentary', 'أفلام وثائقية', '📹', '#607D8B', 8]
            ];
            
            $stmt = $pdo->prepare("INSERT INTO categories (name, name_en, description, icon, color, sort_order) VALUES (?, ?, ?, ?, ?, ?)");
            
            foreach ($defaultCategories as $category) {
                $stmt->execute($category);
            }
            
            echo "<p style='color: #4CAF50;'>✅ تم إضافة " . count($defaultCategories) . " تصنيف افتراضي</p>";
        } else {
            echo "<p style='color: #2196F3;'>ℹ️ التصنيفات موجودة ($categoriesCount تصنيف)</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: #F44336;'>❌ خطأ في التصنيفات: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
    
    // 6. ملخص نهائي
    echo "<h2>📊 ملخص الإصلاحات</h2>";
    
    echo "<div style='background: rgba(76, 175, 80, 0.1); border: 1px solid rgba(76, 175, 80, 0.3); border-radius: 10px; padding: 2rem; margin: 2rem 0; text-align: center;'>";
    echo "<h3 style='color: #4CAF50; margin-bottom: 1rem;'>🎉 تم إصلاح جميع أخطاء PHP!</h3>";
    echo "<ul style='text-align: right; color: #ccc; margin-right: 2rem;'>";
    echo "<li>✅ تم إصلاح هيكل جدول المستخدمين</li>";
    echo "<li>✅ تم إضافة جميع الأعمدة المطلوبة</li>";
    echo "<li>✅ تم إنشاء المستخدمين التجريبيين</li>";
    echo "<li>✅ تم فحص وإصلاح الجداول الأساسية</li>";
    echo "<li>✅ تم إضافة التصنيفات الافتراضية</li>";
    echo "<li>✅ تم إصلاح جميع أخطاء undefined array key</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>❌ خطأ في الاتصال بقاعدة البيانات:</h3>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

// إعادة تفعيل عرض الأخطاء
error_reporting(E_ALL);

// روابط سريعة للاختبار
echo "<div style='text-align: center; margin: 3rem 0;'>";
echo "<h3 style='color: #E50914; margin-bottom: 2rem;'>🔗 اختبار الصفحات</h3>";

$testPages = [
    ['url' => 'login.php', 'title' => '🔐 تسجيل الدخول', 'color' => '#4CAF50'],
    ['url' => 'profile.php', 'title' => '👤 الملف الشخصي', 'color' => '#2196F3'],
    ['url' => 'settings.php', 'title' => '⚙️ الإعدادات', 'color' => '#FF9800'],
    ['url' => 'movies.php', 'title' => '🎬 الأفلام', 'color' => '#E50914'],
    ['url' => 'dashboard_live.php', 'title' => '📊 لوحة المراقبة', 'color' => '#9C27B0']
];

foreach ($testPages as $page) {
    echo "<a href='{$page['url']}' style='background: {$page['color']}; color: white; padding: 1rem 2rem; text-decoration: none; border-radius: 8px; font-weight: bold; margin: 0.5rem; display: inline-block; transition: transform 0.3s ease;' onmouseover='this.style.transform=\"translateY(-3px)\"' onmouseout='this.style.transform=\"translateY(0)\"'>{$page['title']}</a>";
}

echo "</div>";
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 إصلاح أخطاء PHP - Shahid Platform</title>
    <style>
        body {
            font-family: 'Segoe UI', 'Cairo', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #2d2d2d 100%);
            color: #fff;
            min-height: 100vh;
            padding: 2rem;
            margin: 0;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(47, 47, 47, 0.95);
            border-radius: 20px;
            padding: 3rem;
            border: 2px solid rgba(229, 9, 20, 0.3);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
        }
        h1, h2, h3 { 
            color: #E50914; 
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
        }
        h1 {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 2rem;
            background: linear-gradient(45deg, #E50914, #FF6B35);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        p, li {
            line-height: 1.6;
            margin: 0.5rem 0;
        }
        ul {
            margin: 1rem 0;
        }
        .btn {
            transition: all 0.3s ease;
        }
        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
        }
        @media (max-width: 768px) {
            .container {
                padding: 1rem;
                margin: 1rem;
            }
            h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- المحتوى يتم عرضه من PHP أعلاه -->
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔧 أداة إصلاح أخطاء PHP جاهزة!');
        });
    </script>
</body>
</html>
