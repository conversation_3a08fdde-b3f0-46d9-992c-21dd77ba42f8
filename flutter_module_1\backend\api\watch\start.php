<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// معالجة طلبات OPTIONS
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => 'طريقة الطلب غير مدعومة',
        'error_code' => 'METHOD_NOT_ALLOWED'
    ]);
    exit;
}

try {
    // الاتصال بقاعدة البيانات
    $pdo = new PDO("mysql:host=localhost;dbname=shahid_platform;charset=utf8mb4", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // التحقق من رمز الوصول
    $userId = null;
    $userSubscription = 'free';
    
    if (!isset($_SERVER['HTTP_AUTHORIZATION'])) {
        http_response_code(401);
        echo json_encode([
            'success' => false,
            'message' => 'رمز الوصول مطلوب',
            'error_code' => 'MISSING_TOKEN'
        ]);
        exit;
    }
    
    $token = str_replace('Bearer ', '', $_SERVER['HTTP_AUTHORIZATION']);
    $payload = json_decode(base64_decode($token), true);
    
    if (!$payload || !isset($payload['user_id']) || $payload['expires_at'] <= time()) {
        http_response_code(401);
        echo json_encode([
            'success' => false,
            'message' => 'رمز الوصول غير صالح أو منتهي الصلاحية',
            'error_code' => 'INVALID_TOKEN'
        ]);
        exit;
    }
    
    $userId = $payload['user_id'];
    $userSubscription = $payload['subscription_type'] ?? 'free';
    
    // قراءة البيانات المرسلة
    $input = json_decode(file_get_contents('php://input'), true);
    
    // التحقق من وجود البيانات المطلوبة
    if (!isset($input['content_type']) || !isset($input['content_id'])) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'message' => 'نوع المحتوى ومعرف المحتوى مطلوبان',
            'error_code' => 'MISSING_CONTENT_INFO'
        ]);
        exit;
    }
    
    $contentType = $input['content_type']; // movie, series, episode
    $contentId = intval($input['content_id']);
    $watchTime = isset($input['watch_time']) ? max(0, intval($input['watch_time'])) : 0;
    $quality = isset($input['quality']) ? $input['quality'] : 'auto';
    $device = isset($input['device']) ? $input['device'] : 'web';
    
    // التحقق من صحة نوع المحتوى
    if (!in_array($contentType, ['movie', 'series', 'episode'])) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'message' => 'نوع المحتوى غير صحيح',
            'error_code' => 'INVALID_CONTENT_TYPE'
        ]);
        exit;
    }
    
    // الحصول على معلومات المحتوى
    $content = null;
    $isPremium = false;
    $videoUrl = null;
    
    switch ($contentType) {
        case 'movie':
            $stmt = $pdo->prepare("SELECT * FROM movies WHERE id = ? AND status = 'active'");
            $stmt->execute([$contentId]);
            $content = $stmt->fetch(PDO::FETCH_ASSOC);
            if ($content) {
                $isPremium = $content['rating'] > 8.0; // افتراض
                $videoUrl = $content['video_url'];
            }
            break;
            
        case 'episode':
            $stmt = $pdo->prepare("
                SELECT e.*, s.title as series_title 
                FROM episodes e 
                JOIN series s ON e.series_id = s.id 
                WHERE e.id = ? AND e.status = 'active' AND s.status = 'active'
            ");
            $stmt->execute([$contentId]);
            $content = $stmt->fetch(PDO::FETCH_ASSOC);
            if ($content) {
                $isPremium = $content['is_premium'] ?? false;
                $videoUrl = $content['video_url'];
            }
            break;
            
        case 'series':
            // للمسلسل، نحصل على أول حلقة
            $stmt = $pdo->prepare("
                SELECT e.*, s.title as series_title 
                FROM episodes e 
                JOIN series s ON e.series_id = s.id 
                WHERE s.id = ? AND e.status = 'active' AND s.status = 'active' 
                ORDER BY e.season_number ASC, e.episode_number ASC 
                LIMIT 1
            ");
            $stmt->execute([$contentId]);
            $content = $stmt->fetch(PDO::FETCH_ASSOC);
            if ($content) {
                $contentType = 'episode'; // تحويل إلى حلقة
                $contentId = $content['id'];
                $isPremium = $content['is_premium'] ?? false;
                $videoUrl = $content['video_url'];
            }
            break;
    }
    
    if (!$content) {
        http_response_code(404);
        echo json_encode([
            'success' => false,
            'message' => 'المحتوى غير موجود أو غير متاح',
            'error_code' => 'CONTENT_NOT_FOUND'
        ]);
        exit;
    }
    
    // التحقق من صلاحية المشاهدة
    if ($isPremium && $userSubscription === 'free') {
        http_response_code(403);
        echo json_encode([
            'success' => false,
            'message' => 'هذا المحتوى متاح للمشتركين المدفوعين فقط',
            'error_code' => 'PREMIUM_CONTENT_REQUIRED'
        ]);
        exit;
    }
    
    // تسجيل بداية المشاهدة
    $stmt = $pdo->prepare("
        INSERT INTO watch_history (user_id, content_type, content_id, watch_time, device, quality, watched_at) 
        VALUES (?, ?, ?, ?, ?, ?, NOW())
        ON DUPLICATE KEY UPDATE 
        watch_time = VALUES(watch_time),
        device = VALUES(device),
        quality = VALUES(quality),
        watched_at = NOW()
    ");
    
    $stmt->execute([$userId, $contentType, $contentId, $watchTime, $device, $quality]);
    
    // تحديث عدد المشاهدات
    if ($contentType === 'movie') {
        $pdo->prepare("UPDATE movies SET views = views + 1 WHERE id = ?")->execute([$contentId]);
    } elseif ($contentType === 'episode') {
        $pdo->prepare("UPDATE episodes SET views = views + 1 WHERE id = ?")->execute([$contentId]);
        // تحديث مشاهدات المسلسل أيضاً
        if (isset($content['series_id'])) {
            $pdo->prepare("UPDATE series SET views = views + 1 WHERE id = ?")->execute([$content['series_id']]);
        }
    }
    
    // تسجيل النشاط
    $stmt = $pdo->prepare("
        INSERT INTO activity_logs (user_id, action, description, ip_address, created_at) 
        VALUES (?, 'watch_start', ?, ?, NOW())
    ");
    
    $description = "بدء مشاهدة " . ($contentType === 'movie' ? 'فيلم' : 'حلقة') . ": " . $content['title'];
    $stmt->execute([$userId, $description, $_SERVER['REMOTE_ADDR']]);
    
    // الحصول على التوصيات (محتوى مشابه)
    $recommendations = [];
    if ($contentType === 'movie') {
        $stmt = $pdo->prepare("
            SELECT id, title, poster, rating 
            FROM movies 
            WHERE id != ? AND status = 'active' AND category_id = ? 
            ORDER BY rating DESC, views DESC 
            LIMIT 5
        ");
        $stmt->execute([$contentId, $content['category_id'] ?? 0]);
        $recommendations = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } elseif ($contentType === 'episode') {
        // الحصول على الحلقة التالية
        $stmt = $pdo->prepare("
            SELECT id, title, episode_number, season_number 
            FROM episodes 
            WHERE series_id = ? AND status = 'active' 
            AND (season_number > ? OR (season_number = ? AND episode_number > ?))
            ORDER BY season_number ASC, episode_number ASC 
            LIMIT 1
        ");
        $stmt->execute([
            $content['series_id'], 
            $content['season_number'], 
            $content['season_number'], 
            $content['episode_number']
        ]);
        $nextEpisode = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($nextEpisode) {
            $recommendations[] = [
                'type' => 'next_episode',
                'data' => $nextEpisode
            ];
        }
    }
    
    // إعداد الاستجابة
    $response = [
        'success' => true,
        'message' => 'تم بدء المشاهدة بنجاح',
        'data' => [
            'content' => [
                'id' => $contentId,
                'type' => $contentType,
                'title' => $content['title'],
                'video_url' => $videoUrl,
                'duration' => $content['duration'] ?? null,
                'thumbnail' => $content['thumbnail'] ?? $content['poster'] ?? null,
                'is_premium' => $isPremium
            ],
            'watch_session' => [
                'user_id' => $userId,
                'start_time' => $watchTime,
                'quality' => $quality,
                'device' => $device,
                'session_id' => uniqid('watch_', true)
            ],
            'recommendations' => $recommendations,
            'user_subscription' => [
                'type' => $userSubscription,
                'can_access_premium' => $userSubscription !== 'free'
            ]
        ]
    ];
    
    http_response_code(200);
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    
} catch (PDOException $e) {
    error_log("Database error in watch start API: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'خطأ في الخادم',
        'error_code' => 'DATABASE_ERROR'
    ]);
    
} catch (Exception $e) {
    error_log("General error in watch start API: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'خطأ غير متوقع',
        'error_code' => 'GENERAL_ERROR'
    ]);
}
?>
