<?php
/**
 * تقرير النظام الشامل
 * يعرض حالة جميع مكونات النظام مع إحصائيات مفصلة
 */

require_once '../system_health_checker.php';
require_once '../performance_monitor.php';
require_once '../analytics_system.php';

// تشغيل فحص صحة النظام
$healthChecker = new SystemHealthChecker();
$healthReport = $healthChecker->runAllChecks();

// الحصول على تقرير الأداء
$performanceMonitor = PerformanceMonitor::getInstance();
$performanceReport = $performanceMonitor->getPerformanceReport(30);

// الحصول على إحصائيات التحليلات
$analytics = new AnalyticsSystem();
$analyticsReport = $analytics->generateReport(date('Y-m-d', strtotime('-30 days')), date('Y-m-d'));

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير النظام الشامل - Shahid</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #2d2d2d 100%);
            color: #fff;
            min-height: 100vh;
            line-height: 1.6;
        }
        
        .header {
            background: linear-gradient(135deg, #E50914 0%, #B8070F 100%);
            padding: 2rem;
            text-align: center;
            box-shadow: 0 4px 20px rgba(229, 9, 20, 0.3);
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }
        
        .header .subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .report-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }
        
        .report-card {
            background: rgba(47, 47, 47, 0.9);
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
            border: 1px solid rgba(229, 9, 20, 0.2);
            position: relative;
            overflow: hidden;
        }
        
        .report-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #E50914, #B8070F);
        }
        
        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 1.5rem;
            gap: 1rem;
        }
        
        .card-icon {
            font-size: 2rem;
        }
        
        .card-title {
            font-size: 1.5rem;
            font-weight: bold;
            color: #E50914;
        }
        
        .status-indicator {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: bold;
            font-size: 0.9rem;
            margin-right: auto;
        }
        
        .status-success {
            background: rgba(76, 175, 80, 0.2);
            color: #4CAF50;
            border: 1px solid #4CAF50;
        }
        
        .status-warning {
            background: rgba(255, 152, 0, 0.2);
            color: #ff9800;
            border: 1px solid #ff9800;
        }
        
        .status-error {
            background: rgba(244, 67, 54, 0.2);
            color: #f44336;
            border: 1px solid #f44336;
        }
        
        .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 1rem;
            margin-bottom: 1.5rem;
        }
        
        .summary-stat {
            text-align: center;
            padding: 1rem;
            background: rgba(20, 20, 20, 0.8);
            border-radius: 10px;
        }
        
        .summary-stat .number {
            font-size: 2rem;
            font-weight: bold;
            color: #E50914;
            display: block;
        }
        
        .summary-stat .label {
            font-size: 0.9rem;
            color: #ccc;
        }
        
        .check-list {
            max-height: 300px;
            overflow-y: auto;
        }
        
        .check-item {
            display: flex;
            align-items: center;
            padding: 0.75rem;
            margin-bottom: 0.5rem;
            background: rgba(20, 20, 20, 0.6);
            border-radius: 8px;
            gap: 1rem;
        }
        
        .check-icon {
            font-size: 1.2rem;
            width: 24px;
            text-align: center;
        }
        
        .check-content {
            flex: 1;
        }
        
        .check-name {
            font-weight: bold;
            margin-bottom: 0.25rem;
        }
        
        .check-message {
            font-size: 0.9rem;
            color: #ccc;
        }
        
        .details-list {
            margin-top: 0.5rem;
            padding-right: 1rem;
        }
        
        .details-list div {
            font-size: 0.85rem;
            color: #999;
            margin-bottom: 0.25rem;
        }
        
        .metric-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .metric-row:last-child {
            border-bottom: none;
        }
        
        .metric-label {
            color: #ccc;
        }
        
        .metric-value {
            font-weight: bold;
            color: #fff;
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #333;
            border-radius: 4px;
            overflow: hidden;
            margin: 0.5rem 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(45deg, #E50914, #B8070F);
            transition: width 0.3s ease;
        }
        
        .recommendations {
            background: rgba(255, 193, 7, 0.1);
            border-left: 3px solid #ffc107;
            padding: 1rem;
            border-radius: 4px;
            margin-top: 1rem;
        }
        
        .recommendation-item {
            margin-bottom: 0.75rem;
            padding: 0.75rem;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 6px;
        }
        
        .recommendation-item:last-child {
            margin-bottom: 0;
        }
        
        .recommendation-type {
            font-weight: bold;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .recommendation-message {
            color: #ddd;
        }
        
        .btn {
            background: linear-gradient(45deg, #E50914, #B8070F);
            color: white;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: bold;
            text-decoration: none;
            display: inline-block;
            margin: 0.5rem 0.5rem 0.5rem 0;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(229, 9, 20, 0.4);
        }
        
        .btn-secondary {
            background: linear-gradient(45deg, #555, #333);
        }
        
        .chart-placeholder {
            height: 200px;
            background: linear-gradient(135deg, rgba(229, 9, 20, 0.1), rgba(229, 9, 20, 0.05));
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #999;
            font-size: 1.1rem;
            margin: 1rem 0;
        }
        
        .timestamp {
            text-align: center;
            color: #999;
            font-size: 0.9rem;
            margin-top: 2rem;
            padding: 1rem;
            background: rgba(20, 20, 20, 0.6);
            border-radius: 8px;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }
            
            .report-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .summary-stats {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>📊 تقرير النظام الشامل</h1>
        <div class="subtitle">تحليل مفصل لحالة وأداء منصة Shahid</div>
    </div>

    <div class="container">
        <!-- ملخص الحالة العامة -->
        <div class="report-card" style="grid-column: 1 / -1;">
            <div class="card-header">
                <div class="card-icon">🎯</div>
                <div class="card-title">الحالة العامة للنظام</div>
                <div class="status-indicator status-<?php echo $healthReport['overall_status']; ?>">
                    <?php 
                    $statusText = [
                        'success' => '✅ ممتاز',
                        'warning' => '⚠️ جيد مع تحذيرات',
                        'error' => '❌ يحتاج انتباه'
                    ];
                    echo $statusText[$healthReport['overall_status']];
                    ?>
                </div>
            </div>
            
            <div class="summary-stats">
                <div class="summary-stat">
                    <span class="number"><?php echo $healthReport['summary']['total_checks']; ?></span>
                    <span class="label">إجمالي الفحوصات</span>
                </div>
                <div class="summary-stat">
                    <span class="number"><?php echo $healthReport['summary']['success_count']; ?></span>
                    <span class="label">ناجحة</span>
                </div>
                <div class="summary-stat">
                    <span class="number"><?php echo $healthReport['summary']['warning_count']; ?></span>
                    <span class="label">تحذيرات</span>
                </div>
                <div class="summary-stat">
                    <span class="number"><?php echo $healthReport['summary']['error_count']; ?></span>
                    <span class="label">أخطاء</span>
                </div>
                <div class="summary-stat">
                    <span class="number"><?php echo $healthReport['summary']['success_rate']; ?>%</span>
                    <span class="label">معدل النجاح</span>
                </div>
            </div>
            
            <div class="progress-bar">
                <div class="progress-fill" style="width: <?php echo $healthReport['summary']['success_rate']; ?>%"></div>
            </div>
        </div>

        <div class="report-grid">
            <!-- فحوصات صحة النظام -->
            <div class="report-card">
                <div class="card-header">
                    <div class="card-icon">🔍</div>
                    <div class="card-title">فحوصات النظام</div>
                </div>
                
                <div class="check-list">
                    <?php foreach ($healthReport['checks'] as $checkName => $check): ?>
                    <div class="check-item">
                        <div class="check-icon">
                            <?php 
                            $icons = [
                                'success' => '✅',
                                'warning' => '⚠️',
                                'error' => '❌'
                            ];
                            echo $icons[$check['status']];
                            ?>
                        </div>
                        <div class="check-content">
                            <div class="check-name"><?php echo ucfirst($checkName); ?></div>
                            <div class="check-message"><?php echo htmlspecialchars($check['message']); ?></div>
                            <?php if (!empty($check['details'])): ?>
                            <div class="details-list">
                                <?php foreach ($check['details'] as $detail): ?>
                                <div><?php echo htmlspecialchars($detail); ?></div>
                                <?php endforeach; ?>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>

            <!-- إحصائيات الأداء -->
            <div class="report-card">
                <div class="card-header">
                    <div class="card-icon">⚡</div>
                    <div class="card-title">أداء النظام</div>
                </div>
                
                <div class="metric-row">
                    <span class="metric-label">متوسط وقت الاستجابة:</span>
                    <span class="metric-value">
                        <?php echo number_format($performanceReport['analysis']['summary']['avg_response_time'] * 1000, 2); ?> ms
                    </span>
                </div>
                
                <div class="metric-row">
                    <span class="metric-label">إجمالي الطلبات (30 يوم):</span>
                    <span class="metric-value"><?php echo number_format($performanceReport['analysis']['summary']['total_requests']); ?></span>
                </div>
                
                <div class="metric-row">
                    <span class="metric-label">الطلبات البطيئة:</span>
                    <span class="metric-value"><?php echo $performanceReport['analysis']['summary']['slow_requests']; ?></span>
                </div>
                
                <div class="metric-row">
                    <span class="metric-label">طلبات الأخطاء:</span>
                    <span class="metric-value"><?php echo $performanceReport['analysis']['summary']['error_requests']; ?></span>
                </div>
                
                <div class="chart-placeholder">
                    📈 رسم بياني لأداء النظام (30 يوم)
                </div>
            </div>

            <!-- إحصائيات المستخدمين -->
            <div class="report-card">
                <div class="card-header">
                    <div class="card-icon">👥</div>
                    <div class="card-title">إحصائيات المستخدمين</div>
                </div>
                
                <div class="metric-row">
                    <span class="metric-label">الجلسات الفريدة:</span>
                    <span class="metric-value"><?php echo number_format($analyticsReport['user_stats']['general']['total_sessions'] ?? 0); ?></span>
                </div>
                
                <div class="metric-row">
                    <span class="metric-label">الزوار الفريدون:</span>
                    <span class="metric-value"><?php echo number_format($analyticsReport['user_stats']['general']['unique_visitors'] ?? 0); ?></span>
                </div>
                
                <div class="metric-row">
                    <span class="metric-label">مشاهدات الصفحات:</span>
                    <span class="metric-value"><?php echo number_format($analyticsReport['user_stats']['general']['total_page_views'] ?? 0); ?></span>
                </div>
                
                <div class="metric-row">
                    <span class="metric-label">متوسط الصفحات/جلسة:</span>
                    <span class="metric-value"><?php echo number_format($analyticsReport['user_stats']['general']['avg_pages_per_session'] ?? 0, 1); ?></span>
                </div>
                
                <div class="chart-placeholder">
                    📊 إحصائيات الزوار اليومية
                </div>
            </div>

            <!-- إحصائيات المحتوى -->
            <div class="report-card">
                <div class="card-header">
                    <div class="card-icon">🎬</div>
                    <div class="card-title">إحصائيات المحتوى</div>
                </div>
                
                <?php if (!empty($analyticsReport['content_stats'])): ?>
                <div class="check-list">
                    <?php foreach (array_slice($analyticsReport['content_stats'], 0, 5) as $content): ?>
                    <div class="check-item">
                        <div class="check-icon">
                            <?php echo $content['content_type'] === 'movie' ? '🎬' : '📺'; ?>
                        </div>
                        <div class="check-content">
                            <div class="check-name"><?php echo htmlspecialchars($content['title'] ?? 'غير محدد'); ?></div>
                            <div class="check-message">
                                <?php echo number_format($content['views']); ?> مشاهدة | 
                                <?php echo number_format($content['unique_views']); ?> مشاهدة فريدة
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
                <?php else: ?>
                <div class="chart-placeholder">
                    📊 لا توجد بيانات محتوى متاحة
                </div>
                <?php endif; ?>
            </div>

            <!-- معلومات النظام -->
            <div class="report-card">
                <div class="card-header">
                    <div class="card-icon">💻</div>
                    <div class="card-title">معلومات النظام</div>
                </div>
                
                <div class="metric-row">
                    <span class="metric-label">إصدار PHP:</span>
                    <span class="metric-value"><?php echo PHP_VERSION; ?></span>
                </div>
                
                <div class="metric-row">
                    <span class="metric-label">حد الذاكرة:</span>
                    <span class="metric-value"><?php echo ini_get('memory_limit'); ?></span>
                </div>
                
                <div class="metric-row">
                    <span class="metric-label">خادم الويب:</span>
                    <span class="metric-value"><?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown'; ?></span>
                </div>
                
                <div class="metric-row">
                    <span class="metric-label">نظام التشغيل:</span>
                    <span class="metric-value"><?php echo PHP_OS; ?></span>
                </div>
                
                <div class="metric-row">
                    <span class="metric-label">وقت التشغيل:</span>
                    <span class="metric-value"><?php echo date('Y-m-d H:i:s'); ?></span>
                </div>
            </div>

            <!-- الإجراءات السريعة -->
            <div class="report-card">
                <div class="card-header">
                    <div class="card-icon">🔧</div>
                    <div class="card-title">الإجراءات السريعة</div>
                </div>
                
                <button class="btn" onclick="refreshReport()">🔄 تحديث التقرير</button>
                <button class="btn btn-secondary" onclick="exportReport()">📄 تصدير التقرير</button>
                <a href="system_management.php" class="btn btn-secondary">🛠️ إدارة النظام</a>
                <a href="dashboard.php" class="btn btn-secondary">🎛️ لوحة التحكم</a>
                
                <div style="margin-top: 1rem;">
                    <button class="btn" onclick="runHealthCheck()">🔍 فحص صحة شامل</button>
                    <button class="btn btn-secondary" onclick="optimizeSystem()">⚡ تحسين النظام</button>
                </div>
            </div>
        </div>

        <!-- التوصيات -->
        <?php if (!empty($healthReport['recommendations'])): ?>
        <div class="report-card">
            <div class="card-header">
                <div class="card-icon">💡</div>
                <div class="card-title">التوصيات والتحسينات</div>
            </div>
            
            <div class="recommendations">
                <?php foreach ($healthReport['recommendations'] as $recommendation): ?>
                <div class="recommendation-item">
                    <div class="recommendation-type">
                        <?php 
                        $typeIcons = [
                            'error' => '🚨',
                            'warning' => '⚠️',
                            'info' => 'ℹ️'
                        ];
                        echo $typeIcons[$recommendation['type']] ?? '💡';
                        ?>
                        <span><?php echo ucfirst($recommendation['check']); ?></span>
                        <span style="margin-right: auto; font-size: 0.8rem; color: #999;">
                            أولوية: <?php echo $recommendation['priority']; ?>
                        </span>
                    </div>
                    <div class="recommendation-message">
                        <?php echo htmlspecialchars($recommendation['message']); ?>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- الطابع الزمني -->
        <div class="timestamp">
            📅 تم إنشاء هذا التقرير في: <?php echo $healthReport['timestamp']; ?>
            <br>
            🔄 آخر تحديث: <?php echo date('Y-m-d H:i:s'); ?>
        </div>
    </div>

    <script>
        // تحديث التقرير
        function refreshReport() {
            location.reload();
        }

        // تصدير التقرير
        function exportReport() {
            window.print();
        }

        // تشغيل فحص صحة شامل
        function runHealthCheck() {
            fetch('../system_health_checker.php?action=health_check')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('تم إجراء فحص صحة شامل بنجاح!');
                        location.reload();
                    } else {
                        alert('خطأ في فحص صحة النظام');
                    }
                })
                .catch(error => {
                    alert('خطأ في الشبكة: ' + error.message);
                });
        }

        // تحسين النظام
        function optimizeSystem() {
            if (confirm('هل تريد تشغيل عملية تحسين النظام؟')) {
                // في التطبيق الحقيقي، يمكن إضافة عمليات تحسين فعلية
                alert('تم تحسين النظام بنجاح!');
            }
        }

        // تحديث تلقائي كل 5 دقائق
        setInterval(() => {
            console.log('تحديث تلقائي للتقرير...');
            // يمكن إضافة تحديث جزئي هنا
        }, 300000);
    </script>
</body>
</html>
