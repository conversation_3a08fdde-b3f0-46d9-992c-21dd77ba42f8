# Shahid Platform - Images Directory Protection
# حماية مجلد الصور

# Allow access to image files only
<FilesMatch "\.(jpg|jpeg|png|gif|webp|svg|ico)$">
    Order Allow,Deny
    Allow from all
</FilesMatch>

# Block access to PHP files and other sensitive files
<FilesMatch "\.(php|php3|php4|php5|phtml|pl|py|jsp|asp|sh|cgi)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# Block access to configuration and sensitive files
<FilesMatch "\.(htaccess|htpasswd|ini|log|sh|sql|conf|fla|psd|log)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# Prevent directory browsing
Options -Indexes

# Default file for directory access
DirectoryIndex index.php

# Security headers for images
<IfModule mod_headers.c>
    Header set X-Content-Type-Options nosniff
    Header set X-Frame-Options SAMEORIGIN
    Header set Referrer-Policy "strict-origin-when-cross-origin"
</IfModule>

# Cache control for images
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/webp "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
</IfModule>

# Compression for images
<IfModule mod_deflate.c>
    <FilesMatch "\.(svg)$">
        SetOutputFilter DEFLATE
    </FilesMatch>
</IfModule>
