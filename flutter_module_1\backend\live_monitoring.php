<?php
/**
 * حالة النظام المباشرة - Live System Monitoring
 * مراقبة مستمرة لجميع مكونات المنصة
 */

// Function to check API status
function checkAPIStatus() {
    $endpoints = [
        'api/index.php' => 'API الأساسي',
        'api/advanced.php' => 'API المتقدم',
        'api/test.php' => 'اختبار API'
    ];
    
    $working = 0;
    foreach ($endpoints as $endpoint => $name) {
        if (file_exists(__DIR__ . '/' . $endpoint)) {
            $working++;
        }
    }
    
    return [
        'status' => $working === count($endpoints) ? 'success' : 'error',
        'working' => $working,
        'total' => count($endpoints)
    ];
}

// Function to check database
function checkDatabaseStatus() {
    try {
        $pdo = new PDO("mysql:host=localhost;dbname=shahid_platform;charset=utf8mb4", "root", "");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        $stmt = $pdo->query("SHOW TABLES");
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        return [
            'status' => 'success',
            'tables' => count($tables),
            'connected' => true
        ];
    } catch (Exception $e) {
        return [
            'status' => 'error',
            'tables' => 0,
            'connected' => false
        ];
    }
}

// Function to check admin account
function checkAdminAccount() {
    try {
        $pdo = new PDO("mysql:host=localhost;dbname=shahid_platform;charset=utf8mb4", "root", "");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE role = 'admin'");
        $stmt->execute();
        $adminCount = $stmt->fetchColumn();
        
        return [
            'status' => $adminCount > 0 ? 'success' : 'error',
            'exists' => $adminCount > 0,
            'count' => $adminCount
        ];
    } catch (Exception $e) {
        return [
            'status' => 'error',
            'exists' => false,
            'count' => 0
        ];
    }
}

// Function to check tables
function checkTablesStatus() {
    try {
        $pdo = new PDO("mysql:host=localhost;dbname=shahid_platform;charset=utf8mb4", "root", "");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        $requiredTables = ['users', 'movies', 'series', 'categories', 'ratings'];
        $stmt = $pdo->query("SHOW TABLES");
        $existingTables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        $missingTables = array_diff($requiredTables, $existingTables);
        
        return [
            'status' => empty($missingTables) ? 'success' : 'error',
            'total' => count($existingTables),
            'required' => count($requiredTables),
            'missing' => count($missingTables)
        ];
    } catch (Exception $e) {
        return [
            'status' => 'error',
            'total' => 0,
            'required' => 5,
            'missing' => 5
        ];
    }
}

// Get all statuses
$apiStatus = checkAPIStatus();
$dbStatus = checkDatabaseStatus();
$adminStatus = checkAdminAccount();
$tablesStatus = checkTablesStatus();

// Return JSON for AJAX requests
if (isset($_GET['ajax'])) {
    header('Content-Type: application/json');
    echo json_encode([
        'api' => $apiStatus,
        'database' => $dbStatus,
        'admin' => $adminStatus,
        'tables' => $tablesStatus,
        'timestamp' => date('H:i:s')
    ]);
    exit;
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>حالة النظام المباشرة</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', 'Cairo', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 50%, #0f0f0f 100%);
            color: #fff;
            min-height: 100vh;
            padding: 2rem;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(47, 47, 47, 0.95);
            border-radius: 20px;
            padding: 2rem;
            border: 2px solid rgba(229, 9, 20, 0.3);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
        }
        
        .header {
            text-align: center;
            margin-bottom: 3rem;
            padding-bottom: 1.5rem;
            border-bottom: 3px solid #E50914;
        }
        
        .header h1 {
            color: #E50914;
            font-size: 2.8rem;
            margin-bottom: 0.5rem;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }
        
        .header p {
            color: #ccc;
            font-size: 1.2rem;
        }
        
        .monitoring-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }
        
        .monitor-card {
            background: linear-gradient(145deg, rgba(0, 0, 0, 0.4), rgba(47, 47, 47, 0.6));
            border-radius: 15px;
            padding: 2rem;
            border: 2px solid transparent;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .monitor-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: #666;
            transition: all 0.3s ease;
        }
        
        .monitor-card.success::before {
            background: linear-gradient(90deg, #4CAF50, #45a049);
        }
        
        .monitor-card.error::before {
            background: linear-gradient(90deg, #F44336, #d32f2f);
        }
        
        .monitor-card.warning::before {
            background: linear-gradient(90deg, #FF9800, #F57C00);
        }
        
        .monitor-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(229, 9, 20, 0.2);
        }
        
        .card-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 1.5rem;
        }
        
        .card-title {
            font-size: 1.4rem;
            font-weight: bold;
            color: #fff;
        }
        
        .status-indicator {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }
        
        .status-indicator.success {
            background: #4CAF50;
            box-shadow: 0 0 10px rgba(76, 175, 80, 0.5);
        }
        
        .status-indicator.error {
            background: #F44336;
            box-shadow: 0 0 10px rgba(244, 67, 54, 0.5);
        }
        
        .status-indicator.warning {
            background: #FF9800;
            box-shadow: 0 0 10px rgba(255, 152, 0, 0.5);
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .card-content {
            color: #ccc;
            line-height: 1.6;
        }
        
        .status-text {
            font-size: 1.1rem;
            margin-bottom: 0.5rem;
        }
        
        .status-details {
            font-size: 0.9rem;
            opacity: 0.8;
        }
        
        .refresh-info {
            text-align: center;
            margin-top: 2rem;
            padding-top: 1rem;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            color: #666;
        }
        
        .search-icon {
            position: absolute;
            top: 1rem;
            left: 1rem;
            font-size: 1.5rem;
            color: #E50914;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="search-icon">🔍</div>
        
        <div class="header">
            <h1>حالة النظام المباشرة</h1>
            <p>مراقبة مستمرة لجميع مكونات المنصة</p>
        </div>

        <div class="monitoring-grid">
            <!-- API Status -->
            <div class="monitor-card <?php echo $apiStatus['status']; ?>" id="api-card">
                <div class="card-header">
                    <div class="card-title">API</div>
                    <div class="status-indicator <?php echo $apiStatus['status']; ?>"></div>
                </div>
                <div class="card-content">
                    <div class="status-text" id="api-status">
                        <?php echo $apiStatus['status'] === 'success' ? 'يعمل بشكل طبيعي' : 'يحتاج إصلاح'; ?>
                    </div>
                    <div class="status-details" id="api-details">
                        النقاط النشطة: <span id="api-working"><?php echo $apiStatus['working']; ?></span>/<span id="api-total"><?php echo $apiStatus['total']; ?></span>
                    </div>
                </div>
            </div>

            <!-- Database Status -->
            <div class="monitor-card <?php echo $dbStatus['status']; ?>" id="db-card">
                <div class="card-header">
                    <div class="card-title">قاعدة البيانات</div>
                    <div class="status-indicator <?php echo $dbStatus['status']; ?>"></div>
                </div>
                <div class="card-content">
                    <div class="status-text" id="db-status">
                        <?php echo $dbStatus['connected'] ? 'متصلة' : 'غير متصلة'; ?>
                    </div>
                    <div class="status-details" id="db-details">
                        الجداول: <span id="db-tables"><?php echo $dbStatus['tables']; ?></span>
                    </div>
                </div>
            </div>

            <!-- Tables Status -->
            <div class="monitor-card <?php echo $tablesStatus['status']; ?>" id="tables-card">
                <div class="card-header">
                    <div class="card-title">الجداول</div>
                    <div class="status-indicator <?php echo $tablesStatus['status']; ?>"></div>
                </div>
                <div class="card-content">
                    <div class="status-text" id="tables-status">
                        <?php echo $tablesStatus['status'] === 'success' ? 'مكتملة' : 'ناقصة'; ?>
                    </div>
                    <div class="status-details" id="tables-details">
                        الموجود: <span id="tables-total"><?php echo $tablesStatus['total']; ?></span> من <span id="tables-required"><?php echo $tablesStatus['required']; ?></span>
                    </div>
                </div>
            </div>

            <!-- Admin Account Status -->
            <div class="monitor-card <?php echo $adminStatus['status']; ?>" id="admin-card">
                <div class="card-header">
                    <div class="card-title">حساب المدير</div>
                    <div class="status-indicator <?php echo $adminStatus['status']; ?>"></div>
                </div>
                <div class="card-content">
                    <div class="status-text" id="admin-status">
                        <?php echo $adminStatus['exists'] ? 'موجود' : 'غير موجود'; ?>
                    </div>
                    <div class="status-details" id="admin-details">
                        عدد المديرين: <span id="admin-count"><?php echo $adminStatus['count']; ?></span>
                    </div>
                </div>
            </div>
        </div>

        <div class="refresh-info">
            آخر تحديث: <span id="last-update"><?php echo date('H:i:s'); ?></span> - يتم التحديث كل 5 ثوانٍ
        </div>
    </div>

    <script>
        // Auto refresh function
        function updateStatus() {
            fetch('?ajax=1')
                .then(response => response.json())
                .then(data => {
                    // Update API status
                    updateCard('api', data.api);
                    
                    // Update Database status
                    updateCard('db', data.database);
                    
                    // Update Tables status
                    updateCard('tables', data.tables);
                    
                    // Update Admin status
                    updateCard('admin', data.admin);
                    
                    // Update timestamp
                    document.getElementById('last-update').textContent = data.timestamp;
                })
                .catch(error => {
                    console.error('Error updating status:', error);
                });
        }
        
        function updateCard(type, data) {
            const card = document.getElementById(type + '-card');
            const indicator = card.querySelector('.status-indicator');
            
            // Update card class
            card.className = 'monitor-card ' + data.status;
            indicator.className = 'status-indicator ' + data.status;
            
            // Update specific content based on type
            if (type === 'api') {
                document.getElementById('api-status').textContent = 
                    data.status === 'success' ? 'يعمل بشكل طبيعي' : 'يحتاج إصلاح';
                document.getElementById('api-working').textContent = data.working;
                document.getElementById('api-total').textContent = data.total;
            } else if (type === 'db') {
                document.getElementById('db-status').textContent = 
                    data.connected ? 'متصلة' : 'غير متصلة';
                document.getElementById('db-tables').textContent = data.tables;
            } else if (type === 'tables') {
                document.getElementById('tables-status').textContent = 
                    data.status === 'success' ? 'مكتملة' : 'ناقصة';
                document.getElementById('tables-total').textContent = data.total;
                document.getElementById('tables-required').textContent = data.required;
            } else if (type === 'admin') {
                document.getElementById('admin-status').textContent = 
                    data.exists ? 'موجود' : 'غير موجود';
                document.getElementById('admin-count').textContent = data.count;
            }
        }
        
        // Start auto refresh
        setInterval(updateStatus, 5000);
        
        console.log('🔍 Live monitoring system loaded');
    </script>
</body>
</html>
