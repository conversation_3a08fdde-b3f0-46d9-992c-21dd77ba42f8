<?php
/**
 * دليل النشر والإنتاج - Shahid Platform Deployment Guide
 * دليل شامل لنشر المشروع في بيئة الإنتاج
 */
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>دليل النشر والإنتاج - Shahid Platform</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #2d2d2d 100%);
            color: #fff;
            line-height: 1.6;
            min-height: 100vh;
        }
        
        .header {
            background: linear-gradient(135deg, #E50914 0%, #B8070F 100%);
            padding: 3rem 2rem;
            text-align: center;
            box-shadow: 0 4px 20px rgba(229, 9, 20, 0.3);
        }
        
        .header h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }
        
        .header .subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .guide-section {
            background: rgba(47, 47, 47, 0.9);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
            border: 1px solid rgba(229, 9, 20, 0.2);
            position: relative;
            overflow: hidden;
        }
        
        .guide-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #E50914, #B8070F);
        }
        
        .section-title {
            font-size: 1.8rem;
            color: #E50914;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .section-icon {
            font-size: 2rem;
        }
        
        .step-list {
            list-style: none;
            counter-reset: step-counter;
        }
        
        .step-item {
            counter-increment: step-counter;
            margin-bottom: 2rem;
            padding: 1.5rem;
            background: rgba(20, 20, 20, 0.8);
            border-radius: 10px;
            border-left: 4px solid #E50914;
            position: relative;
        }
        
        .step-item::before {
            content: counter(step-counter);
            position: absolute;
            top: -10px;
            right: 20px;
            background: linear-gradient(45deg, #E50914, #B8070F);
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 0.9rem;
        }
        
        .step-title {
            font-size: 1.3rem;
            font-weight: bold;
            color: #fff;
            margin-bottom: 1rem;
        }
        
        .step-description {
            color: #ccc;
            margin-bottom: 1rem;
        }
        
        .code-block {
            background: #1a1a1a;
            border: 1px solid #333;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
            position: relative;
        }
        
        .code-block::before {
            content: 'Code';
            position: absolute;
            top: 5px;
            left: 10px;
            background: #E50914;
            color: white;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 0.7rem;
            font-weight: bold;
        }
        
        .code-block code {
            color: #4CAF50;
            display: block;
            margin-top: 20px;
        }
        
        .warning-box {
            background: rgba(255, 152, 0, 0.1);
            border-left: 4px solid #ff9800;
            padding: 1rem;
            border-radius: 4px;
            margin: 1rem 0;
        }
        
        .warning-box .warning-title {
            font-weight: bold;
            color: #ff9800;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .info-box {
            background: rgba(33, 150, 243, 0.1);
            border-left: 4px solid #2196F3;
            padding: 1rem;
            border-radius: 4px;
            margin: 1rem 0;
        }
        
        .info-box .info-title {
            font-weight: bold;
            color: #2196F3;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .success-box {
            background: rgba(76, 175, 80, 0.1);
            border-left: 4px solid #4CAF50;
            padding: 1rem;
            border-radius: 4px;
            margin: 1rem 0;
        }
        
        .success-box .success-title {
            font-weight: bold;
            color: #4CAF50;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .checklist {
            list-style: none;
            margin: 1rem 0;
        }
        
        .checklist li {
            padding: 0.5rem 0;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .checklist li::before {
            content: '✅';
            font-size: 1.2rem;
        }
        
        .btn {
            background: linear-gradient(45deg, #E50914, #B8070F);
            color: white;
            padding: 1rem 2rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: bold;
            text-decoration: none;
            display: inline-block;
            margin: 0.5rem 0.5rem 0.5rem 0;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(229, 9, 20, 0.4);
        }
        
        .btn-secondary {
            background: linear-gradient(45deg, #555, #333);
        }
        
        .deployment-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin: 2rem 0;
        }
        
        .deployment-card {
            background: rgba(20, 20, 20, 0.8);
            border-radius: 10px;
            padding: 1.5rem;
            border: 1px solid rgba(229, 9, 20, 0.3);
        }
        
        .deployment-card h3 {
            color: #E50914;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .requirements-table {
            width: 100%;
            border-collapse: collapse;
            margin: 1rem 0;
            background: rgba(20, 20, 20, 0.8);
            border-radius: 8px;
            overflow: hidden;
        }
        
        .requirements-table th,
        .requirements-table td {
            padding: 1rem;
            text-align: right;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .requirements-table th {
            background: rgba(229, 9, 20, 0.2);
            color: #E50914;
            font-weight: bold;
        }
        
        .requirements-table tr:last-child td {
            border-bottom: none;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .deployment-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 دليل النشر والإنتاج</h1>
        <div class="subtitle">دليل شامل لنشر منصة Shahid في بيئة الإنتاج</div>
    </div>

    <div class="container">
        <!-- متطلبات النظام -->
        <div class="guide-section">
            <h2 class="section-title">
                <span class="section-icon">💻</span>
                متطلبات النظام
            </h2>
            
            <table class="requirements-table">
                <thead>
                    <tr>
                        <th>المكون</th>
                        <th>الحد الأدنى</th>
                        <th>المُوصى به</th>
                        <th>ملاحظات</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>PHP</td>
                        <td>8.0+</td>
                        <td>8.2+</td>
                        <td>مع إضافات PDO, GD, Zip</td>
                    </tr>
                    <tr>
                        <td>MySQL</td>
                        <td>8.0+</td>
                        <td>8.0+</td>
                        <td>مع دعم UTF8MB4</td>
                    </tr>
                    <tr>
                        <td>Apache/Nginx</td>
                        <td>2.4+</td>
                        <td>2.4+</td>
                        <td>مع mod_rewrite</td>
                    </tr>
                    <tr>
                        <td>الذاكرة</td>
                        <td>512 MB</td>
                        <td>2 GB+</td>
                        <td>للمعالجة المتقدمة</td>
                    </tr>
                    <tr>
                        <td>مساحة القرص</td>
                        <td>5 GB</td>
                        <td>50 GB+</td>
                        <td>للمحتوى والنسخ الاحتياطية</td>
                    </tr>
                    <tr>
                        <td>Flutter SDK</td>
                        <td>3.1.0+</td>
                        <td>3.16.0+</td>
                        <td>للتطبيق المحمول</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- خطوات النشر -->
        <div class="guide-section">
            <h2 class="section-title">
                <span class="section-icon">📋</span>
                خطوات النشر التفصيلية
            </h2>
            
            <ol class="step-list">
                <li class="step-item">
                    <div class="step-title">إعداد الخادم</div>
                    <div class="step-description">تحضير بيئة الخادم وتثبيت المتطلبات الأساسية</div>
                    
                    <div class="code-block">
                        <code>
# تحديث النظام
sudo apt update && sudo apt upgrade -y

# تثبيت Apache و PHP و MySQL
sudo apt install apache2 php8.2 mysql-server php8.2-mysql php8.2-gd php8.2-zip php8.2-curl php8.2-mbstring

# تفعيل mod_rewrite
sudo a2enmod rewrite
sudo systemctl restart apache2
                        </code>
                    </div>
                    
                    <div class="warning-box">
                        <div class="warning-title">⚠️ تحذير</div>
                        تأكد من تحديث كلمة مرور MySQL وإعداد جدار الحماية بشكل صحيح
                    </div>
                </li>

                <li class="step-item">
                    <div class="step-title">رفع ملفات المشروع</div>
                    <div class="step-description">نقل ملفات المشروع إلى الخادم وإعداد الصلاحيات</div>
                    
                    <div class="code-block">
                        <code>
# رفع الملفات عبر SCP أو FTP
scp -r ./flutter_module_1 user@server:/var/www/html/

# إعداد الصلاحيات
sudo chown -R www-data:www-data /var/www/html/flutter_module_1
sudo chmod -R 755 /var/www/html/flutter_module_1
sudo chmod -R 777 /var/www/html/flutter_module_1/backend/uploads
sudo chmod -R 777 /var/www/html/flutter_module_1/backend/logs
                        </code>
                    </div>
                </li>

                <li class="step-item">
                    <div class="step-title">إعداد قاعدة البيانات</div>
                    <div class="step-description">إنشاء قاعدة البيانات وتشغيل سكريبت الإعداد</div>
                    
                    <div class="code-block">
                        <code>
# الدخول إلى MySQL
mysql -u root -p

# إنشاء قاعدة البيانات والمستخدم
CREATE DATABASE shahid_platform CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'shahid_user'@'localhost' IDENTIFIED BY 'strong_password_here';
GRANT ALL PRIVILEGES ON shahid_platform.* TO 'shahid_user'@'localhost';
FLUSH PRIVILEGES;
EXIT;
                        </code>
                    </div>
                    
                    <div class="info-box">
                        <div class="info-title">ℹ️ معلومة</div>
                        بعد إنشاء قاعدة البيانات، قم بزيارة setup_complete_system.php لإنشاء الجداول والبيانات التجريبية
                    </div>
                </li>

                <li class="step-item">
                    <div class="step-title">تحديث إعدادات الاتصال</div>
                    <div class="step-description">تحديث معلومات الاتصال بقاعدة البيانات في ملفات الإعداد</div>
                    
                    <div class="code-block">
                        <code>
# تحديث ملف config/advanced_config.php
'database' => [
    'host' => 'localhost',
    'username' => 'shahid_user',
    'password' => 'strong_password_here',
    'database' => 'shahid_platform',
    'charset' => 'utf8mb4'
]
                        </code>
                    </div>
                </li>

                <li class="step-item">
                    <div class="step-title">إعداد SSL والأمان</div>
                    <div class="step-description">تفعيل شهادة SSL وإعدادات الأمان المتقدمة</div>
                    
                    <div class="code-block">
                        <code>
# تثبيت Certbot للحصول على شهادة SSL مجانية
sudo apt install certbot python3-certbot-apache

# الحصول على شهادة SSL
sudo certbot --apache -d yourdomain.com

# تجديد تلقائي للشهادة
sudo crontab -e
# إضافة السطر التالي:
0 12 * * * /usr/bin/certbot renew --quiet
                        </code>
                    </div>
                </li>

                <li class="step-item">
                    <div class="step-title">تحسين الأداء</div>
                    <div class="step-description">تفعيل التخزين المؤقت وضغط الملفات</div>
                    
                    <div class="code-block">
                        <code>
# تفعيل ضغط Gzip في Apache
sudo a2enmod deflate
sudo a2enmod expires
sudo a2enmod headers

# إضافة إعدادات التحسين في .htaccess
&lt;IfModule mod_deflate.c&gt;
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
&lt;/IfModule&gt;
                        </code>
                    </div>
                </li>

                <li class="step-item">
                    <div class="step-title">إعداد النسخ الاحتياطية التلقائية</div>
                    <div class="step-description">جدولة النسخ الاحتياطية اليومية للنظام</div>
                    
                    <div class="code-block">
                        <code>
# إضافة مهمة cron للنسخ الاحتياطي اليومي
sudo crontab -e

# إضافة السطر التالي للنسخ الاحتياطي في الساعة 2:00 صباحاً
0 2 * * * /usr/bin/php /var/www/html/flutter_module_1/backend/backup_system.php action=create_backup

# نسخ احتياطي أسبوعي لقاعدة البيانات
0 3 * * 0 mysqldump -u shahid_user -p'password' shahid_platform > /backups/weekly_$(date +\%Y\%m\%d).sql
                        </code>
                    </div>
                </li>

                <li class="step-item">
                    <div class="step-title">مراقبة النظام</div>
                    <div class="step-description">إعداد مراقبة الأداء والتنبيهات</div>
                    
                    <div class="code-block">
                        <code>
# تثبيت أدوات المراقبة
sudo apt install htop iotop nethogs

# إعداد تنبيهات البريد الإلكتروني للأخطاء
# في ملف /etc/php/8.2/apache2/php.ini
log_errors = On
error_log = /var/log/php_errors.log
                        </code>
                    </div>
                </li>
            </ol>
        </div>

        <!-- بيئات النشر المختلفة -->
        <div class="guide-section">
            <h2 class="section-title">
                <span class="section-icon">🌐</span>
                بيئات النشر المختلفة
            </h2>
            
            <div class="deployment-grid">
                <div class="deployment-card">
                    <h3>🔧 بيئة التطوير</h3>
                    <ul class="checklist">
                        <li>تفعيل وضع التطوير</li>
                        <li>عرض الأخطاء مفعل</li>
                        <li>تسجيل مفصل للاستعلامات</li>
                        <li>أدوات التطوير متاحة</li>
                    </ul>
                    
                    <div class="code-block">
                        <code>
'app' => [
    'environment' => 'development',
    'debug' => true
]
                        </code>
                    </div>
                </div>

                <div class="deployment-card">
                    <h3>🧪 بيئة الاختبار</h3>
                    <ul class="checklist">
                        <li>بيانات تجريبية</li>
                        <li>اختبارات تلقائية</li>
                        <li>مراقبة الأداء</li>
                        <li>تسجيل متوسط</li>
                    </ul>
                    
                    <div class="code-block">
                        <code>
'app' => [
    'environment' => 'staging',
    'debug' => false
]
                        </code>
                    </div>
                </div>

                <div class="deployment-card">
                    <h3>🚀 بيئة الإنتاج</h3>
                    <ul class="checklist">
                        <li>أمان عالي</li>
                        <li>تحسين الأداء</li>
                        <li>مراقبة مستمرة</li>
                        <li>نسخ احتياطية تلقائية</li>
                    </ul>
                    
                    <div class="code-block">
                        <code>
'app' => [
    'environment' => 'production',
    'debug' => false
]
                        </code>
                    </div>
                </div>
            </div>
        </div>

        <!-- قائمة التحقق النهائية -->
        <div class="guide-section">
            <h2 class="section-title">
                <span class="section-icon">✅</span>
                قائمة التحقق النهائية
            </h2>
            
            <div class="success-box">
                <div class="success-title">🎯 قبل النشر</div>
                <ul class="checklist">
                    <li>اختبار جميع الوظائف الأساسية</li>
                    <li>التحقق من أمان قاعدة البيانات</li>
                    <li>تحديث كلمات المرور الافتراضية</li>
                    <li>تفعيل شهادة SSL</li>
                    <li>إعداد النسخ الاحتياطية</li>
                    <li>تحسين إعدادات الأداء</li>
                    <li>اختبار التطبيق المحمول</li>
                    <li>التحقق من روابط API</li>
                </ul>
            </div>
            
            <div class="info-box">
                <div class="info-title">📊 بعد النشر</div>
                <ul class="checklist">
                    <li>مراقبة الأداء والأخطاء</li>
                    <li>التحقق من النسخ الاحتياطية</li>
                    <li>مراجعة سجلات الأمان</li>
                    <li>اختبار سرعة التحميل</li>
                    <li>التحقق من SEO</li>
                    <li>مراقبة استخدام الموارد</li>
                    <li>اختبار التطبيق على أجهزة مختلفة</li>
                    <li>جمع ملاحظات المستخدمين</li>
                </ul>
            </div>
        </div>

        <!-- الدعم والصيانة -->
        <div class="guide-section">
            <h2 class="section-title">
                <span class="section-icon">🛠️</span>
                الدعم والصيانة
            </h2>
            
            <div class="warning-box">
                <div class="warning-title">🔄 الصيانة الدورية</div>
                <ul>
                    <li><strong>يومياً:</strong> مراجعة سجلات الأخطاء والأداء</li>
                    <li><strong>أسبوعياً:</strong> تحديث النظام والإضافات</li>
                    <li><strong>شهرياً:</strong> مراجعة الأمان وتحسين الأداء</li>
                    <li><strong>ربع سنوي:</strong> نسخ احتياطية كاملة ومراجعة شاملة</li>
                </ul>
            </div>
            
            <div class="info-box">
                <div class="info-title">📞 الحصول على المساعدة</div>
                <p>في حالة مواجهة مشاكل أثناء النشر:</p>
                <ul>
                    <li>راجع ملفات السجلات في مجلد logs/</li>
                    <li>استخدم صفحة system_health_checker.php للتشخيص</li>
                    <li>تحقق من إعدادات قاعدة البيانات</li>
                    <li>راجع التوثيق الشامل في COMPLETE_DOCUMENTATION.md</li>
                </ul>
            </div>
        </div>

        <!-- أزرار الإجراءات -->
        <div style="text-align: center; margin: 3rem 0;">
            <a href="system_health_checker.php?action=health_check" class="btn">🔍 فحص صحة النظام</a>
            <a href="admin/dashboard.php" class="btn btn-secondary">🎛️ لوحة التحكم</a>
            <a href="setup_complete_system.php" class="btn btn-secondary">⚙️ إعداد النظام</a>
            <a href="../COMPLETE_DOCUMENTATION.md" class="btn btn-secondary">📚 التوثيق الشامل</a>
        </div>
    </div>

    <script>
        // إضافة تأثيرات تفاعلية
        document.querySelectorAll('.step-item').forEach((step, index) => {
            step.addEventListener('mouseenter', () => {
                step.style.transform = 'translateX(-10px)';
                step.style.boxShadow = '0 8px 25px rgba(229, 9, 20, 0.3)';
            });
            
            step.addEventListener('mouseleave', () => {
                step.style.transform = 'translateX(0)';
                step.style.boxShadow = 'none';
            });
        });

        // نسخ الكود عند النقر
        document.querySelectorAll('.code-block').forEach(block => {
            block.addEventListener('click', () => {
                const code = block.querySelector('code').textContent;
                navigator.clipboard.writeText(code).then(() => {
                    const originalBg = block.style.background;
                    block.style.background = 'rgba(76, 175, 80, 0.2)';
                    setTimeout(() => {
                        block.style.background = originalBg;
                    }, 1000);
                });
            });
            
            block.style.cursor = 'pointer';
            block.title = 'انقر لنسخ الكود';
        });
    </script>
</body>
</html>
