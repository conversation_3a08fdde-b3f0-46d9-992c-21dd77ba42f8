/* 
 * Shahid API Dashboard Styles
 * Professional API Interface Design
 */

/* إعدادات أساسية */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', 'Cairo', 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
    background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #2d2d2d 100%);
    color: #fff;
    line-height: 1.6;
    min-height: 100vh;
    direction: rtl;
}

/* خلفية متحركة */
.animated-bg {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    opacity: 0.05;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="white"/></svg>') repeat;
    animation: float 20s linear infinite;
}

@keyframes float {
    0% { transform: translateX(0) translateY(0); }
    100% { transform: translateX(-100px) translateY(-100px); }
}

/* رأس الصفحة */
.header {
    background: linear-gradient(135deg, #E50914 0%, #B8070F 100%);
    padding: 3rem 0;
    text-align: center;
    box-shadow: 0 4px 20px rgba(229, 9, 20, 0.3);
    position: relative;
    overflow: hidden;
}

.header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="white" opacity="0.1"/></svg>') repeat;
    animation: sparkle 3s linear infinite;
}

@keyframes sparkle {
    0% { transform: translateX(0) translateY(0); }
    100% { transform: translateX(-100px) translateY(-100px); }
}

.header h1 {
    font-size: 3.5rem;
    margin-bottom: 0.5rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
    position: relative;
    z-index: 2;
    font-weight: 900;
}

.header .subtitle {
    font-size: 1.3rem;
    opacity: 0.9;
    position: relative;
    z-index: 2;
    font-weight: 300;
}

/* الحاوي الرئيسي */
.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 2rem;
}

/* معلومات API */
.api-info {
    background: rgba(47, 47, 47, 0.9);
    border-radius: 20px;
    padding: 3rem;
    margin-bottom: 3rem;
    border: 1px solid rgba(229, 9, 20, 0.2);
    text-align: center;
    backdrop-filter: blur(10px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.4);
}

.api-info h2 {
    color: #E50914;
    margin-bottom: 1.5rem;
    font-size: 2.5rem;
    font-weight: 800;
    background: linear-gradient(45deg, #E50914, #FF6B35);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.api-info p {
    color: #ccc;
    font-size: 1.2rem;
    line-height: 1.8;
    max-width: 800px;
    margin: 0 auto;
}

/* شبكة الإحصائيات */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 2rem;
    margin-bottom: 4rem;
}

.stat-card {
    background: rgba(47, 47, 47, 0.9);
    border-radius: 20px;
    padding: 2.5rem;
    text-align: center;
    border: 1px solid rgba(229, 9, 20, 0.2);
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(229, 9, 20, 0.1), transparent);
    transition: left 0.5s ease;
}

.stat-card:hover::before {
    left: 100%;
}

.stat-card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 20px 40px rgba(229, 9, 20, 0.3);
    border-color: rgba(229, 9, 20, 0.5);
}

.stat-card .number {
    font-size: 3rem;
    font-weight: 900;
    color: #E50914;
    display: block;
    margin-bottom: 0.8rem;
    text-shadow: 0 2px 4px rgba(229, 9, 20, 0.3);
}

.stat-card .label {
    color: #ccc;
    font-size: 1.1rem;
    font-weight: 500;
}

/* قسم نقاط النهاية */
.endpoints-section {
    margin-bottom: 4rem;
}

.endpoints-section h2 {
    color: #E50914;
    margin-bottom: 3rem;
    font-size: 2.8rem;
    text-align: center;
    font-weight: 800;
    background: linear-gradient(45deg, #E50914, #FF6B35);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.endpoints-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
    gap: 2.5rem;
}

.endpoint-category {
    background: rgba(47, 47, 47, 0.9);
    border-radius: 20px;
    padding: 2.5rem;
    border: 1px solid rgba(229, 9, 20, 0.2);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.endpoint-category:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(229, 9, 20, 0.2);
}

.endpoint-category h3 {
    color: #E50914;
    margin-bottom: 2rem;
    font-size: 1.8rem;
    display: flex;
    align-items: center;
    gap: 0.8rem;
    font-weight: 700;
}

.endpoint-item {
    background: rgba(0, 0, 0, 0.4);
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    border-right: 4px solid #E50914;
    transition: all 0.3s ease;
}

.endpoint-item:hover {
    background: rgba(0, 0, 0, 0.6);
    transform: translateX(-5px);
}

.endpoint-item:last-child {
    margin-bottom: 0;
}

.method {
    display: inline-block;
    padding: 0.4rem 1rem;
    border-radius: 6px;
    font-size: 0.85rem;
    font-weight: bold;
    margin-left: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.method.get { 
    background: linear-gradient(45deg, #4CAF50, #45a049);
    box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
}

.method.post { 
    background: linear-gradient(45deg, #2196F3, #1976D2);
    box-shadow: 0 2px 8px rgba(33, 150, 243, 0.3);
}

.method.put { 
    background: linear-gradient(45deg, #FF9800, #F57C00);
    box-shadow: 0 2px 8px rgba(255, 152, 0, 0.3);
}

.method.delete { 
    background: linear-gradient(45deg, #F44336, #D32F2F);
    box-shadow: 0 2px 8px rgba(244, 67, 54, 0.3);
}

.path {
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
    color: #E50914;
    font-weight: 600;
    font-size: 1.05rem;
    background: rgba(229, 9, 20, 0.1);
    padding: 0.2rem 0.5rem;
    border-radius: 4px;
}

.desc {
    color: #ccc;
    font-size: 0.95rem;
    margin-top: 0.8rem;
    line-height: 1.5;
}

/* قسم الحالة */
.status-section {
    background: rgba(47, 47, 47, 0.9);
    border-radius: 20px;
    padding: 2.5rem;
    margin-bottom: 3rem;
    border: 1px solid rgba(229, 9, 20, 0.2);
    backdrop-filter: blur(10px);
}

.status-section h3 {
    color: #E50914;
    margin-bottom: 2rem;
    font-size: 2rem;
    font-weight: 700;
}

.status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 1.2rem;
    padding: 1.5rem;
    background: rgba(0, 0, 0, 0.4);
    border-radius: 12px;
    transition: all 0.3s ease;
}

.status-item:hover {
    background: rgba(0, 0, 0, 0.6);
    transform: translateX(-5px);
}

.status-indicator {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: #4CAF50;
    box-shadow: 0 0 15px rgba(76, 175, 80, 0.6);
    animation: pulse 2s infinite;
    flex-shrink: 0;
}

.status-indicator.error {
    background: #F44336;
    box-shadow: 0 0 15px rgba(244, 67, 54, 0.6);
}

@keyframes pulse {
    0%, 100% { 
        opacity: 1; 
        transform: scale(1);
    }
    50% { 
        opacity: 0.7; 
        transform: scale(1.1);
    }
}

/* قسم الإجراءات */
.actions-section {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.action-btn {
    background: linear-gradient(45deg, #E50914, #B8070F);
    color: white;
    padding: 2rem;
    border-radius: 15px;
    text-decoration: none;
    text-align: center;
    font-weight: bold;
    font-size: 1.2rem;
    transition: all 0.3s ease;
    box-shadow: 0 8px 25px rgba(229, 9, 20, 0.3);
    position: relative;
    overflow: hidden;
    display: block;
}

.action-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.action-btn:hover::before {
    left: 100%;
}

.action-btn:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 40px rgba(229, 9, 20, 0.4);
    color: white;
}

.action-btn.secondary {
    background: linear-gradient(45deg, #555, #333);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.action-btn.secondary:hover {
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
}

/* التذييل */
.footer {
    text-align: center;
    padding: 3rem;
    color: #999;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(0, 0, 0, 0.2);
}

.footer p {
    margin-bottom: 0.5rem;
    font-weight: 300;
}

/* الرسوم المتحركة */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* التجاوب مع الشاشات الصغيرة */
@media (max-width: 768px) {
    .header h1 {
        font-size: 2.5rem;
    }
    
    .header .subtitle {
        font-size: 1.1rem;
    }
    
    .container {
        padding: 1rem;
    }
    
    .endpoints-grid {
        grid-template-columns: 1fr;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }
    
    .stat-card {
        padding: 2rem;
    }
    
    .stat-card .number {
        font-size: 2.5rem;
    }
    
    .endpoint-category {
        padding: 2rem;
    }
    
    .actions-section {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .header {
        padding: 2rem 0;
    }
    
    .header h1 {
        font-size: 2rem;
    }
    
    .api-info {
        padding: 2rem;
    }
    
    .api-info h2 {
        font-size: 2rem;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .endpoints-section h2 {
        font-size: 2.2rem;
    }
}

/* تحسينات إضافية للأداء */
.stat-card,
.endpoint-category,
.action-btn,
.status-item {
    will-change: transform;
}

/* تأثيرات التركيز للوصولية */
.action-btn:focus,
.endpoint-category:focus {
    outline: 2px solid #E50914;
    outline-offset: 2px;
}

/* تحسين الخطوط */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
