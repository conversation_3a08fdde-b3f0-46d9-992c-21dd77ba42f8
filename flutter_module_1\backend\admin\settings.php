<?php
/**
 * Settings Management - Shahid Admin Panel
 */

session_start();

// Check admin login
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: index.php');
    exit();
}

// Database connection
try {
    $config = include '../config/database.php';
    $pdo = new PDO("mysql:host={$config['host']};dbname={$config['name']};charset=utf8mb4", 
                   $config['username'], $config['password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (Exception $e) {
    die('خطأ في الاتصال بقاعدة البيانات: ' . $e->getMessage());
}

// Handle actions
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['update_settings'])) {
        // Update settings
        $siteName = $_POST['site_name'] ?? '';
        $siteDescription = $_POST['site_description'] ?? '';
        $adminEmail = $_POST['admin_email'] ?? '';
        $maintenanceMode = isset($_POST['maintenance_mode']) ? 1 : 0;
        $allowRegistration = isset($_POST['allow_registration']) ? 1 : 0;
        
        try {
            // Create settings table if not exists
            $pdo->exec("CREATE TABLE IF NOT EXISTS settings (
                id INT AUTO_INCREMENT PRIMARY KEY,
                setting_key VARCHAR(255) UNIQUE NOT NULL,
                setting_value TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )");
            
            // Update or insert settings
            $settings = [
                'site_name' => $siteName,
                'site_description' => $siteDescription,
                'admin_email' => $adminEmail,
                'maintenance_mode' => $maintenanceMode,
                'allow_registration' => $allowRegistration
            ];
            
            foreach ($settings as $key => $value) {
                $stmt = $pdo->prepare("INSERT INTO settings (setting_key, setting_value) VALUES (?, ?) ON DUPLICATE KEY UPDATE setting_value = ?");
                $stmt->execute([$key, $value, $value]);
            }
            
            $message = 'تم حفظ الإعدادات بنجاح';
            $messageType = 'success';
        } catch (Exception $e) {
            $message = 'خطأ في حفظ الإعدادات: ' . $e->getMessage();
            $messageType = 'error';
        }
    } elseif (isset($_POST['clear_cache'])) {
        // Clear cache (simulate)
        $message = 'تم مسح الذاكرة المؤقتة بنجاح';
        $messageType = 'success';
    } elseif (isset($_POST['backup_database'])) {
        // Backup database (simulate)
        $message = 'تم إنشاء نسخة احتياطية من قاعدة البيانات';
        $messageType = 'success';
    }
}

// Get current settings
$currentSettings = [];
try {
    $stmt = $pdo->query("SELECT setting_key, setting_value FROM settings");
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $currentSettings[$row['setting_key']] = $row['setting_value'];
    }
} catch (Exception $e) {
    // Settings table might not exist yet
}

// Default values
$siteName = $currentSettings['site_name'] ?? 'Shahid';
$siteDescription = $currentSettings['site_description'] ?? 'منصة البث الاحترافية';
$adminEmail = $currentSettings['admin_email'] ?? '<EMAIL>';
$maintenanceMode = ($currentSettings['maintenance_mode'] ?? 0) == 1;
$allowRegistration = ($currentSettings['allow_registration'] ?? 1) == 1;

// Get system info
$systemInfo = [
    'php_version' => PHP_VERSION,
    'mysql_version' => $pdo->query('SELECT VERSION()')->fetchColumn(),
    'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
    'document_root' => $_SERVER['DOCUMENT_ROOT'] ?? 'Unknown',
    'upload_max_filesize' => ini_get('upload_max_filesize'),
    'post_max_size' => ini_get('post_max_size'),
    'memory_limit' => ini_get('memory_limit'),
    'max_execution_time' => ini_get('max_execution_time')
];

// Get database stats
$dbStats = [];
try {
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM movies");
    $dbStats['movies'] = $stmt->fetch()['count'];
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM series");
    $dbStats['series'] = $stmt->fetch()['count'];
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM episodes");
    $dbStats['episodes'] = $stmt->fetch()['count'];
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
    $dbStats['users'] = $stmt->fetch()['count'];
    
    // Get database size
    $stmt = $pdo->prepare("SELECT ROUND(SUM(data_length + index_length) / 1024 / 1024, 1) AS 'db_size' FROM information_schema.tables WHERE table_schema = ?");
    $stmt->execute([$config['name']]);
    $dbStats['size'] = $stmt->fetch()['db_size'] . ' MB';
} catch (Exception $e) {
    $dbStats = ['movies' => 0, 'series' => 0, 'episodes' => 0, 'users' => 0, 'size' => 'Unknown'];
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الإعدادات - Shahid Admin</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            color: #fff;
            min-height: 100vh;
        }
        
        .header {
            background: #E50914;
            padding: 1rem 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .header h1 {
            font-size: 1.8rem;
        }
        
        .header a {
            color: #fff;
            text-decoration: none;
            padding: 0.5rem 1rem;
            border: 1px solid #fff;
            border-radius: 5px;
            transition: all 0.3s;
        }
        
        .header a:hover {
            background: #fff;
            color: #E50914;
        }
        
        .container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 2rem;
        }
        
        .message {
            padding: 1rem;
            border-radius: 5px;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .message.success {
            background: #28a745;
            color: #fff;
        }
        
        .message.error {
            background: #dc3545;
            color: #fff;
        }
        
        .settings-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin-bottom: 2rem;
        }
        
        .settings-card {
            background: #2d2d2d;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }
        
        .settings-card h2 {
            color: #E50914;
            margin-bottom: 1.5rem;
            font-size: 1.3rem;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: bold;
        }
        
        .form-group input,
        .form-group textarea {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #555;
            border-radius: 5px;
            background: #1a1a1a;
            color: #fff;
            font-size: 1rem;
        }
        
        .form-group textarea {
            height: 100px;
            resize: vertical;
        }
        
        .form-group input:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #E50914;
        }
        
        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .checkbox-group input[type="checkbox"] {
            width: auto;
        }
        
        .btn {
            padding: 0.75rem 1.5rem;
            background: #E50914;
            color: #fff;
            border: none;
            border-radius: 5px;
            font-size: 1rem;
            cursor: pointer;
            transition: background 0.3s;
            margin-left: 0.5rem;
        }
        
        .btn:hover {
            background: #b8070f;
        }
        
        .btn.secondary {
            background: #6c757d;
        }
        
        .btn.secondary:hover {
            background: #5a6268;
        }
        
        .btn.danger {
            background: #dc3545;
        }
        
        .btn.danger:hover {
            background: #c82333;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .info-item {
            background: #3d3d3d;
            padding: 1rem;
            border-radius: 5px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .info-item .label {
            font-weight: bold;
        }
        
        .info-item .value {
            color: #E50914;
        }
        
        .actions-section {
            background: #2d2d2d;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }
        
        .actions-section h2 {
            color: #E50914;
            margin-bottom: 1.5rem;
        }
        
        .action-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            border-bottom: 1px solid #555;
        }
        
        .action-item:last-child {
            border-bottom: none;
        }
        
        .action-item .description {
            flex: 1;
        }
        
        .action-item .description h3 {
            margin-bottom: 0.5rem;
        }
        
        .action-item .description p {
            color: #ccc;
            font-size: 0.9rem;
        }
        
        @media (max-width: 768px) {
            .settings-grid {
                grid-template-columns: 1fr;
            }
            
            .container {
                padding: 0 1rem;
            }
            
            .header {
                flex-direction: column;
                gap: 1rem;
            }
            
            .action-item {
                flex-direction: column;
                gap: 1rem;
                align-items: flex-start;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>⚙️ الإعدادات</h1>
        <a href="index.php">← العودة للوحة الرئيسية</a>
    </div>
    
    <div class="container">
        <?php if (!empty($message)): ?>
            <div class="message <?php echo $messageType; ?>">
                <?php echo htmlspecialchars($message); ?>
            </div>
        <?php endif; ?>
        
        <!-- Settings Form -->
        <div class="settings-grid">
            <div class="settings-card">
                <h2>🔧 إعدادات الموقع</h2>
                <form method="POST">
                    <div class="form-group">
                        <label for="site_name">اسم الموقع</label>
                        <input type="text" id="site_name" name="site_name" value="<?php echo htmlspecialchars($siteName); ?>">
                    </div>
                    
                    <div class="form-group">
                        <label for="site_description">وصف الموقع</label>
                        <textarea id="site_description" name="site_description"><?php echo htmlspecialchars($siteDescription); ?></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label for="admin_email">بريد المدير</label>
                        <input type="email" id="admin_email" name="admin_email" value="<?php echo htmlspecialchars($adminEmail); ?>">
                    </div>
                    
                    <div class="form-group">
                        <div class="checkbox-group">
                            <input type="checkbox" id="maintenance_mode" name="maintenance_mode" <?php echo $maintenanceMode ? 'checked' : ''; ?>>
                            <label for="maintenance_mode">وضع الصيانة</label>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <div class="checkbox-group">
                            <input type="checkbox" id="allow_registration" name="allow_registration" <?php echo $allowRegistration ? 'checked' : ''; ?>>
                            <label for="allow_registration">السماح بالتسجيل الجديد</label>
                        </div>
                    </div>
                    
                    <button type="submit" name="update_settings" class="btn">حفظ الإعدادات</button>
                </form>
            </div>
            
            <div class="settings-card">
                <h2>📊 إحصائيات قاعدة البيانات</h2>
                <div class="info-grid">
                    <div class="info-item">
                        <span class="label">الأفلام:</span>
                        <span class="value"><?php echo number_format($dbStats['movies']); ?></span>
                    </div>
                    <div class="info-item">
                        <span class="label">المسلسلات:</span>
                        <span class="value"><?php echo number_format($dbStats['series']); ?></span>
                    </div>
                    <div class="info-item">
                        <span class="label">الحلقات:</span>
                        <span class="value"><?php echo number_format($dbStats['episodes']); ?></span>
                    </div>
                    <div class="info-item">
                        <span class="label">المستخدمون:</span>
                        <span class="value"><?php echo number_format($dbStats['users']); ?></span>
                    </div>
                    <div class="info-item">
                        <span class="label">حجم قاعدة البيانات:</span>
                        <span class="value"><?php echo $dbStats['size']; ?></span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- System Information -->
        <div class="settings-card">
            <h2>💻 معلومات النظام</h2>
            <div class="info-grid">
                <div class="info-item">
                    <span class="label">إصدار PHP:</span>
                    <span class="value"><?php echo $systemInfo['php_version']; ?></span>
                </div>
                <div class="info-item">
                    <span class="label">إصدار MySQL:</span>
                    <span class="value"><?php echo $systemInfo['mysql_version']; ?></span>
                </div>
                <div class="info-item">
                    <span class="label">خادم الويب:</span>
                    <span class="value"><?php echo $systemInfo['server_software']; ?></span>
                </div>
                <div class="info-item">
                    <span class="label">حد الذاكرة:</span>
                    <span class="value"><?php echo $systemInfo['memory_limit']; ?></span>
                </div>
                <div class="info-item">
                    <span class="label">حد رفع الملفات:</span>
                    <span class="value"><?php echo $systemInfo['upload_max_filesize']; ?></span>
                </div>
                <div class="info-item">
                    <span class="label">حد POST:</span>
                    <span class="value"><?php echo $systemInfo['post_max_size']; ?></span>
                </div>
                <div class="info-item">
                    <span class="label">وقت التنفيذ الأقصى:</span>
                    <span class="value"><?php echo $systemInfo['max_execution_time']; ?>s</span>
                </div>
            </div>
        </div>
        
        <!-- System Actions -->
        <div class="actions-section">
            <h2>🛠️ إجراءات النظام</h2>
            
            <div class="action-item">
                <div class="description">
                    <h3>مسح الذاكرة المؤقتة</h3>
                    <p>مسح جميع ملفات الذاكرة المؤقتة لتحسين الأداء</p>
                </div>
                <form method="POST" style="display: inline;">
                    <button type="submit" name="clear_cache" class="btn secondary">مسح الذاكرة</button>
                </form>
            </div>
            
            <div class="action-item">
                <div class="description">
                    <h3>نسخة احتياطية من قاعدة البيانات</h3>
                    <p>إنشاء نسخة احتياطية من جميع بيانات الموقع</p>
                </div>
                <form method="POST" style="display: inline;">
                    <button type="submit" name="backup_database" class="btn secondary">إنشاء نسخة احتياطية</button>
                </form>
            </div>
            
            <div class="action-item">
                <div class="description">
                    <h3>اختبار API</h3>
                    <p>فحص حالة API والتأكد من عمل جميع الخدمات</p>
                </div>
                <a href="../api/test_api.php" target="_blank" class="btn secondary">اختبار API</a>
            </div>
            
            <div class="action-item">
                <div class="description">
                    <h3>عرض الموقع</h3>
                    <p>فتح الموقع الرئيسي في نافذة جديدة</p>
                </div>
                <a href="../index_simple.php" target="_blank" class="btn secondary">عرض الموقع</a>
            </div>
            
            <div class="action-item">
                <div class="description">
                    <h3>إعادة تشغيل النظام</h3>
                    <p>إعادة تشغيل جميع خدمات النظام (يتطلب صلاحيات خاصة)</p>
                </div>
                <button onclick="alert('هذه الميزة تتطلب صلاحيات خاصة من الخادم')" class="btn danger">إعادة التشغيل</button>
            </div>
        </div>
    </div>
</body>
</html>
