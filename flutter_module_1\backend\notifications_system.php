<?php
/**
 * نظام الإشعارات المتقدم
 * يدعم الإشعارات الفورية، البريد الإلكتروني، والإشعارات المجدولة
 */

class NotificationsSystem {
    private $pdo;
    private $emailConfig;
    
    public function __construct() {
        try {
            $this->pdo = new PDO("mysql:host=localhost;dbname=shahid_platform;charset=utf8mb4", "root", "");
            $this->pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        } catch (PDOException $e) {
            throw new Exception("خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage());
        }
        
        // إعدادات البريد الإلكتروني
        $this->emailConfig = [
            'smtp_host' => 'smtp.gmail.com',
            'smtp_port' => 587,
            'smtp_username' => '<EMAIL>',
            'smtp_password' => 'your-app-password',
            'from_email' => '<EMAIL>',
            'from_name' => 'Shahid Platform'
        ];
    }
    
    /**
     * إرسال إشعار للمستخدم
     */
    public function sendNotification($userId, $title, $message, $type = 'info', $data = []) {
        try {
            $stmt = $this->pdo->prepare("
                INSERT INTO notifications (user_id, title, message, type, data, created_at)
                VALUES (?, ?, ?, ?, ?, NOW())
            ");
            
            $stmt->execute([
                $userId,
                $title,
                $message,
                $type,
                json_encode($data)
            ]);
            
            $notificationId = $this->pdo->lastInsertId();
            
            // إرسال إشعار فوري إذا كان المستخدم متصل
            $this->sendRealTimeNotification($userId, [
                'id' => $notificationId,
                'title' => $title,
                'message' => $message,
                'type' => $type,
                'data' => $data,
                'created_at' => date('Y-m-d H:i:s')
            ]);
            
            return [
                'success' => true,
                'notification_id' => $notificationId,
                'message' => 'تم إرسال الإشعار بنجاح'
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * إرسال إشعار جماعي
     */
    public function sendBulkNotification($userIds, $title, $message, $type = 'info', $data = []) {
        $results = [];
        
        foreach ($userIds as $userId) {
            $result = $this->sendNotification($userId, $title, $message, $type, $data);
            $results[] = [
                'user_id' => $userId,
                'success' => $result['success']
            ];
        }
        
        return [
            'success' => true,
            'results' => $results,
            'total_sent' => count(array_filter($results, function($r) { return $r['success']; }))
        ];
    }
    
    /**
     * إرسال إشعار لجميع المستخدمين
     */
    public function sendNotificationToAll($title, $message, $type = 'info', $data = []) {
        try {
            $stmt = $this->pdo->query("SELECT id FROM users WHERE status = 'active'");
            $userIds = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            return $this->sendBulkNotification($userIds, $title, $message, $type, $data);
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * الحصول على إشعارات المستخدم
     */
    public function getUserNotifications($userId, $limit = 20, $offset = 0, $unreadOnly = false) {
        try {
            $sql = "
                SELECT * FROM notifications 
                WHERE user_id = ?
            ";
            
            $params = [$userId];
            
            if ($unreadOnly) {
                $sql .= " AND is_read = FALSE";
            }
            
            $sql .= " ORDER BY created_at DESC LIMIT ? OFFSET ?";
            $params[] = $limit;
            $params[] = $offset;
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            
            $notifications = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // تنسيق البيانات
            foreach ($notifications as &$notification) {
                $notification['data'] = json_decode($notification['data'], true);
                $notification['created_at_formatted'] = $this->formatDate($notification['created_at']);
            }
            
            return [
                'success' => true,
                'notifications' => $notifications,
                'total' => count($notifications)
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * تمييز الإشعار كمقروء
     */
    public function markAsRead($notificationId, $userId) {
        try {
            $stmt = $this->pdo->prepare("
                UPDATE notifications 
                SET is_read = TRUE, read_at = NOW()
                WHERE id = ? AND user_id = ?
            ");
            
            $stmt->execute([$notificationId, $userId]);
            
            return [
                'success' => true,
                'message' => 'تم تمييز الإشعار كمقروء'
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * تمييز جميع الإشعارات كمقروءة
     */
    public function markAllAsRead($userId) {
        try {
            $stmt = $this->pdo->prepare("
                UPDATE notifications 
                SET is_read = TRUE, read_at = NOW()
                WHERE user_id = ? AND is_read = FALSE
            ");
            
            $stmt->execute([$userId]);
            $affectedRows = $stmt->rowCount();
            
            return [
                'success' => true,
                'message' => "تم تمييز {$affectedRows} إشعار كمقروء",
                'marked_count' => $affectedRows
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * حذف إشعار
     */
    public function deleteNotification($notificationId, $userId) {
        try {
            $stmt = $this->pdo->prepare("
                DELETE FROM notifications 
                WHERE id = ? AND user_id = ?
            ");
            
            $stmt->execute([$notificationId, $userId]);
            
            return [
                'success' => true,
                'message' => 'تم حذف الإشعار'
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * الحصول على عدد الإشعارات غير المقروءة
     */
    public function getUnreadCount($userId) {
        try {
            $stmt = $this->pdo->prepare("
                SELECT COUNT(*) as count 
                FROM notifications 
                WHERE user_id = ? AND is_read = FALSE
            ");
            
            $stmt->execute([$userId]);
            $count = $stmt->fetch()['count'];
            
            return [
                'success' => true,
                'unread_count' => (int)$count
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * إرسال إشعار بريد إلكتروني
     */
    public function sendEmailNotification($userEmail, $subject, $message, $isHtml = true) {
        try {
            // استخدام PHPMailer أو mail() function
            $headers = [
                'From: ' . $this->emailConfig['from_name'] . ' <' . $this->emailConfig['from_email'] . '>',
                'Reply-To: ' . $this->emailConfig['from_email'],
                'X-Mailer: PHP/' . phpversion()
            ];
            
            if ($isHtml) {
                $headers[] = 'MIME-Version: 1.0';
                $headers[] = 'Content-type: text/html; charset=UTF-8';
            }
            
            $success = mail($userEmail, $subject, $message, implode("\r\n", $headers));
            
            return [
                'success' => $success,
                'message' => $success ? 'تم إرسال البريد الإلكتروني' : 'فشل في إرسال البريد الإلكتروني'
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * إنشاء إشعارات تلقائية للأحداث
     */
    public function createAutoNotifications() {
        // إشعار بالمحتوى الجديد
        $this->notifyNewContent();
        
        // إشعار بالتقييمات الجديدة
        $this->notifyNewRatings();
        
        // إشعار بالمفضلة
        $this->notifyFavoriteUpdates();
    }
    
    /**
     * إشعار بالمحتوى الجديد
     */
    private function notifyNewContent() {
        try {
            // البحث عن محتوى جديد (آخر 24 ساعة)
            $stmt = $this->pdo->query("
                SELECT id, title, 'movie' as type FROM movies 
                WHERE created_at >= DATE_SUB(NOW(), INTERVAL 1 DAY)
                UNION ALL
                SELECT id, title, 'series' as type FROM series 
                WHERE created_at >= DATE_SUB(NOW(), INTERVAL 1 DAY)
            ");
            
            $newContent = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            foreach ($newContent as $content) {
                $title = 'محتوى جديد متاح!';
                $message = "تم إضافة {$content['title']} الجديد. شاهده الآن!";
                $type = 'success';
                $data = [
                    'content_id' => $content['id'],
                    'content_type' => $content['type'],
                    'action' => 'view_content'
                ];
                
                // إرسال لجميع المستخدمين المهتمين
                $this->sendNotificationToAll($title, $message, $type, $data);
            }
            
        } catch (Exception $e) {
            error_log("Auto Notifications Error: " . $e->getMessage());
        }
    }
    
    /**
     * إشعار بالتقييمات الجديدة
     */
    private function notifyNewRatings() {
        try {
            // البحث عن تقييمات جديدة للمحتوى المفضل
            $stmt = $this->pdo->query("
                SELECT DISTINCT f.user_id, r.content_id, r.content_type, r.rating, r.comment
                FROM favorites f
                JOIN ratings r ON f.content_id = r.content_id AND f.content_type = r.content_type
                WHERE r.created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
                AND r.user_id != f.user_id
            ");
            
            $newRatings = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            foreach ($newRatings as $rating) {
                $title = 'تقييم جديد على محتوى مفضل';
                $message = "تم إضافة تقييم جديد ({$rating['rating']}/10) على أحد المحتويات المفضلة لديك";
                $type = 'info';
                $data = [
                    'content_id' => $rating['content_id'],
                    'content_type' => $rating['content_type'],
                    'rating' => $rating['rating'],
                    'action' => 'view_ratings'
                ];
                
                $this->sendNotification($rating['user_id'], $title, $message, $type, $data);
            }
            
        } catch (Exception $e) {
            error_log("Auto Notifications Error: " . $e->getMessage());
        }
    }
    
    /**
     * إشعار بتحديثات المفضلة
     */
    private function notifyFavoriteUpdates() {
        // يمكن إضافة منطق لإشعار المستخدمين بتحديثات على المحتوى المفضل
    }
    
    /**
     * إرسال إشعار فوري (WebSocket أو Server-Sent Events)
     */
    private function sendRealTimeNotification($userId, $notification) {
        // في التطبيق الحقيقي، يمكن استخدام WebSocket أو SSE
        // هنا سنحفظ في ملف مؤقت للمحاكاة
        
        $notificationsFile = __DIR__ . '/temp/realtime_notifications.json';
        $notificationsDir = dirname($notificationsFile);
        
        if (!is_dir($notificationsDir)) {
            mkdir($notificationsDir, 0755, true);
        }
        
        $existingNotifications = [];
        if (file_exists($notificationsFile)) {
            $existingNotifications = json_decode(file_get_contents($notificationsFile), true) ?: [];
        }
        
        if (!isset($existingNotifications[$userId])) {
            $existingNotifications[$userId] = [];
        }
        
        $existingNotifications[$userId][] = $notification;
        
        // الاحتفاظ بآخر 50 إشعار فقط لكل مستخدم
        if (count($existingNotifications[$userId]) > 50) {
            $existingNotifications[$userId] = array_slice($existingNotifications[$userId], -50);
        }
        
        file_put_contents($notificationsFile, json_encode($existingNotifications));
    }
    
    /**
     * الحصول على الإشعارات الفورية
     */
    public function getRealTimeNotifications($userId) {
        $notificationsFile = __DIR__ . '/temp/realtime_notifications.json';
        
        if (!file_exists($notificationsFile)) {
            return [];
        }
        
        $notifications = json_decode(file_get_contents($notificationsFile), true) ?: [];
        
        return $notifications[$userId] ?? [];
    }
    
    /**
     * مسح الإشعارات الفورية
     */
    public function clearRealTimeNotifications($userId) {
        $notificationsFile = __DIR__ . '/temp/realtime_notifications.json';
        
        if (!file_exists($notificationsFile)) {
            return;
        }
        
        $notifications = json_decode(file_get_contents($notificationsFile), true) ?: [];
        
        if (isset($notifications[$userId])) {
            unset($notifications[$userId]);
            file_put_contents($notificationsFile, json_encode($notifications));
        }
    }
    
    /**
     * تنسيق التاريخ
     */
    private function formatDate($date) {
        if (!$date) return '';
        
        $timestamp = strtotime($date);
        $now = time();
        $diff = $now - $timestamp;
        
        if ($diff < 60) {
            return 'الآن';
        } elseif ($diff < 3600) {
            $minutes = floor($diff / 60);
            return "منذ {$minutes} دقيقة";
        } elseif ($diff < 86400) {
            $hours = floor($diff / 3600);
            return "منذ {$hours} ساعة";
        } elseif ($diff < 2592000) {
            $days = floor($diff / 86400);
            return "منذ {$days} يوم";
        } else {
            return date('Y/m/d H:i', $timestamp);
        }
    }
}

// معالجة طلبات API
if ($_SERVER['REQUEST_METHOD'] === 'POST' || $_SERVER['REQUEST_METHOD'] === 'GET') {
    header('Content-Type: application/json');
    
    $action = $_REQUEST['action'] ?? '';
    
    if ($action) {
        $notifications = new NotificationsSystem();
        
        switch ($action) {
            case 'send':
                $userId = $_POST['user_id'] ?? 0;
                $title = $_POST['title'] ?? '';
                $message = $_POST['message'] ?? '';
                $type = $_POST['type'] ?? 'info';
                $data = $_POST['data'] ?? [];
                
                echo json_encode($notifications->sendNotification($userId, $title, $message, $type, $data));
                break;
                
            case 'send_bulk':
                $userIds = $_POST['user_ids'] ?? [];
                $title = $_POST['title'] ?? '';
                $message = $_POST['message'] ?? '';
                $type = $_POST['type'] ?? 'info';
                $data = $_POST['data'] ?? [];
                
                echo json_encode($notifications->sendBulkNotification($userIds, $title, $message, $type, $data));
                break;
                
            case 'send_all':
                $title = $_POST['title'] ?? '';
                $message = $_POST['message'] ?? '';
                $type = $_POST['type'] ?? 'info';
                $data = $_POST['data'] ?? [];
                
                echo json_encode($notifications->sendNotificationToAll($title, $message, $type, $data));
                break;
                
            case 'get_notifications':
                $userId = $_GET['user_id'] ?? 1;
                $limit = $_GET['limit'] ?? 20;
                $offset = $_GET['offset'] ?? 0;
                $unreadOnly = $_GET['unread_only'] ?? false;
                
                echo json_encode($notifications->getUserNotifications($userId, $limit, $offset, $unreadOnly));
                break;
                
            case 'mark_read':
                $notificationId = $_POST['notification_id'] ?? 0;
                $userId = $_POST['user_id'] ?? 1;
                
                echo json_encode($notifications->markAsRead($notificationId, $userId));
                break;
                
            case 'mark_all_read':
                $userId = $_POST['user_id'] ?? 1;
                
                echo json_encode($notifications->markAllAsRead($userId));
                break;
                
            case 'delete':
                $notificationId = $_POST['notification_id'] ?? 0;
                $userId = $_POST['user_id'] ?? 1;
                
                echo json_encode($notifications->deleteNotification($notificationId, $userId));
                break;
                
            case 'unread_count':
                $userId = $_GET['user_id'] ?? 1;
                
                echo json_encode($notifications->getUnreadCount($userId));
                break;
                
            case 'get_realtime':
                $userId = $_GET['user_id'] ?? 1;
                
                echo json_encode([
                    'success' => true,
                    'notifications' => $notifications->getRealTimeNotifications($userId)
                ]);
                break;
                
            case 'clear_realtime':
                $userId = $_POST['user_id'] ?? 1;
                
                $notifications->clearRealTimeNotifications($userId);
                echo json_encode([
                    'success' => true,
                    'message' => 'تم مسح الإشعارات الفورية'
                ]);
                break;
                
            default:
                echo json_encode([
                    'success' => false,
                    'error' => 'Invalid action'
                ]);
        }
        exit;
    }
}
?>
