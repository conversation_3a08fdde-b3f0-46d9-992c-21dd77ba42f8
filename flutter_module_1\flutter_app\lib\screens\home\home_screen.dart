import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:shimmer/shimmer.dart';

import '../../core/app_config.dart';
import '../../core/theme/app_theme.dart';
import '../../providers/movie_provider.dart';
import '../../providers/series_provider.dart';
import '../../providers/auth_provider.dart';
import '../../core/models/movie.dart';
import '../../widgets/movie_card.dart';
import '../../widgets/series_card.dart';
import '../../widgets/custom_app_bar.dart';
import '../../widgets/section_header.dart';
import '../../widgets/loading_shimmer.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen>
    with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  bool _showAppBarBackground = false;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _initializeData();
    _setupScrollListener();
  }

  void _initializeData() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final movieProvider = Provider.of<MovieProvider>(context, listen: false);
      final seriesProvider = Provider.of<SeriesProvider>(context, listen: false);

      // Load featured content
      movieProvider.loadFeaturedMovies();
      seriesProvider.loadFeaturedSeries();

      // Load popular content
      movieProvider.loadPopularMovies();
      seriesProvider.loadPopularSeries();

      // Load latest content
      movieProvider.loadLatestMovies();
      seriesProvider.loadLatestSeries();
    });
  }

  void _setupScrollListener() {
    _scrollController.addListener(() {
      final showBackground = _scrollController.offset > 100;
      if (showBackground != _showAppBarBackground) {
        setState(() {
          _showAppBarBackground = showBackground;
        });
      }
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    
    return Scaffold(
      backgroundColor: AppTheme.darkBackgroundColor,
      extendBodyBehindAppBar: true,
      appBar: _buildAppBar(),
      body: RefreshIndicator(
        onRefresh: _refreshData,
        color: AppTheme.primaryColor,
        backgroundColor: AppTheme.darkSurfaceColor,
        child: CustomScrollView(
          controller: _scrollController,
          slivers: [
            // Featured Content Carousel
            SliverToBoxAdapter(
              child: _buildFeaturedCarousel(),
            ),

            // Continue Watching
            SliverToBoxAdapter(
              child: _buildContinueWatching(),
            ),

            // Popular Movies
            SliverToBoxAdapter(
              child: _buildPopularMovies(),
            ),

            // Latest Series
            SliverToBoxAdapter(
              child: _buildLatestSeries(),
            ),

            // Trending Now
            SliverToBoxAdapter(
              child: _buildTrendingContent(),
            ),

            // Recommended for You
            SliverToBoxAdapter(
              child: _buildRecommendedContent(),
            ),

            // Bottom Padding
            const SliverToBoxAdapter(
              child: SizedBox(height: 100),
            ),
          ],
        ),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return CustomAppBar(
      backgroundColor: _showAppBarBackground 
          ? AppTheme.darkBackgroundColor.withOpacity(0.9)
          : Colors.transparent,
      title: Row(
        children: [
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              gradient: AppTheme.primaryGradient,
            ),
            child: const Icon(
              Icons.play_arrow_rounded,
              size: 20,
              color: Colors.white,
            ),
          ),
          const SizedBox(width: 12),
          Text(
            AppConfig.appName,
            style: AppTheme.titleLarge.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
      actions: [
        IconButton(
          onPressed: () {
            // Navigate to search
          },
          icon: const Icon(Icons.search, color: Colors.white),
        ),
        IconButton(
          onPressed: () {
            // Navigate to notifications
          },
          icon: const Icon(Icons.notifications_outlined, color: Colors.white),
        ),
        Consumer<AuthProvider>(
          builder: (context, authProvider, child) {
            return GestureDetector(
              onTap: () {
                // Navigate to profile
              },
              child: Container(
                width: 32,
                height: 32,
                margin: const EdgeInsets.only(right: 16),
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(color: AppTheme.primaryColor, width: 2),
                ),
                child: ClipOval(
                  child: CachedNetworkImage(
                    imageUrl: authProvider.user?.avatarUrl ?? '',
                    fit: BoxFit.cover,
                    placeholder: (context, url) => Container(
                      color: AppTheme.darkSurfaceColor,
                      child: const Icon(
                        Icons.person,
                        size: 16,
                        color: Colors.white54,
                      ),
                    ),
                    errorWidget: (context, url, error) => Container(
                      color: AppTheme.darkSurfaceColor,
                      child: const Icon(
                        Icons.person,
                        size: 16,
                        color: Colors.white54,
                      ),
                    ),
                  ),
                ),
              ),
            );
          },
        ),
      ],
    );
  }

  Widget _buildFeaturedCarousel() {
    return Consumer<MovieProvider>(
      builder: (context, movieProvider, child) {
        if (movieProvider.isLoadingFeatured) {
          return _buildCarouselShimmer();
        }

        if (movieProvider.featuredMovies.isEmpty) {
          return const SizedBox.shrink();
        }

        return Container(
          height: 500,
          child: CarouselSlider.builder(
            itemCount: movieProvider.featuredMovies.length,
            itemBuilder: (context, index, realIndex) {
              final movie = movieProvider.featuredMovies[index];
              return _buildFeaturedItem(movie);
            },
            options: CarouselOptions(
              height: 500,
              viewportFraction: 1.0,
              autoPlay: true,
              autoPlayInterval: const Duration(seconds: 5),
              autoPlayAnimationDuration: const Duration(milliseconds: 800),
              autoPlayCurve: Curves.fastOutSlowIn,
              enlargeCenterPage: false,
              scrollDirection: Axis.horizontal,
            ),
          ),
        );
      },
    );
  }

  Widget _buildFeaturedItem(Movie movie) {
    return GestureDetector(
      onTap: () {
        // Navigate to movie details
      },
      child: Container(
        width: double.infinity,
        decoration: BoxDecoration(
          image: DecorationImage(
            image: CachedNetworkImageProvider(
              AppConfig.getImageUrl(movie.backdropUrl),
            ),
            fit: BoxFit.cover,
          ),
        ),
        child: Container(
          decoration: const BoxDecoration(
            gradient: AppTheme.darkOverlayGradient,
          ),
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.end,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (movie.premium)
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: AppTheme.accentColor,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      'مميز',
                      style: AppTheme.labelSmall.copyWith(
                        color: Colors.black,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                const SizedBox(height: 12),
                Text(
                  movie.title,
                  style: AppTheme.headlineMedium.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 8),
                if (movie.description != null)
                  Text(
                    movie.description!,
                    style: AppTheme.bodyMedium.copyWith(
                      color: Colors.white70,
                    ),
                    maxLines: 3,
                    overflow: TextOverflow.ellipsis,
                  ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    if (movie.rating != null) ...[
                      const Icon(
                        Icons.star,
                        color: AppTheme.accentColor,
                        size: 16,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        movie.ratingString,
                        style: AppTheme.bodySmall.copyWith(
                          color: Colors.white70,
                        ),
                      ),
                      const SizedBox(width: 16),
                    ],
                    if (movie.year != null) ...[
                      Text(
                        movie.year.toString(),
                        style: AppTheme.bodySmall.copyWith(
                          color: Colors.white70,
                        ),
                      ),
                      const SizedBox(width: 16),
                    ],
                    if (movie.durationString.isNotEmpty)
                      Text(
                        movie.durationString,
                        style: AppTheme.bodySmall.copyWith(
                          color: Colors.white70,
                        ),
                      ),
                  ],
                ),
                const SizedBox(height: 20),
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () {
                          // Play movie
                        },
                        icon: const Icon(Icons.play_arrow),
                        label: const Text('تشغيل'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppTheme.primaryColor,
                          padding: const EdgeInsets.symmetric(vertical: 12),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    IconButton(
                      onPressed: () {
                        // Add to watchlist
                      },
                      icon: Icon(
                        movie.isInWatchlist == true
                            ? Icons.bookmark
                            : Icons.bookmark_border,
                        color: Colors.white,
                      ),
                      style: IconButton.styleFrom(
                        backgroundColor: Colors.black54,
                        padding: const EdgeInsets.all(12),
                      ),
                    ),
                    const SizedBox(width: 8),
                    IconButton(
                      onPressed: () {
                        // Share movie
                      },
                      icon: const Icon(
                        Icons.share,
                        color: Colors.white,
                      ),
                      style: IconButton.styleFrom(
                        backgroundColor: Colors.black54,
                        padding: const EdgeInsets.all(12),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildContinueWatching() {
    return Consumer<MovieProvider>(
      builder: (context, movieProvider, child) {
        // This would typically come from a separate provider or API
        // For now, we'll use movies with watch progress
        final continueWatching = movieProvider.movies
            .where((movie) => movie.hasWatchProgress)
            .take(10)
            .toList();

        if (continueWatching.isEmpty) {
          return const SizedBox.shrink();
        }

        return Column(
          children: [
            SectionHeader(
              title: 'متابعة المشاهدة',
              onSeeAll: () {
                // Navigate to continue watching page
              },
            ),
            SizedBox(
              height: 200,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                padding: const EdgeInsets.symmetric(horizontal: 16),
                itemCount: continueWatching.length,
                itemBuilder: (context, index) {
                  final movie = continueWatching[index];
                  return Container(
                    width: 140,
                    margin: const EdgeInsets.only(right: 12),
                    child: MovieCard(
                      movie: movie,
                      showProgress: true,
                    ),
                  );
                },
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildPopularMovies() {
    return Consumer<MovieProvider>(
      builder: (context, movieProvider, child) {
        return Column(
          children: [
            SectionHeader(
              title: 'الأفلام الشائعة',
              onSeeAll: () {
                // Navigate to popular movies
              },
            ),
            if (movieProvider.isLoadingPopular)
              const LoadingShimmer(height: 200)
            else
              SizedBox(
                height: 200,
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  itemCount: movieProvider.popularMovies.length,
                  itemBuilder: (context, index) {
                    final movie = movieProvider.popularMovies[index];
                    return Container(
                      width: 140,
                      margin: const EdgeInsets.only(right: 12),
                      child: MovieCard(movie: movie),
                    );
                  },
                ),
              ),
          ],
        );
      },
    );
  }

  Widget _buildLatestSeries() {
    return Consumer<SeriesProvider>(
      builder: (context, seriesProvider, child) {
        return Column(
          children: [
            SectionHeader(
              title: 'أحدث المسلسلات',
              onSeeAll: () {
                // Navigate to latest series
              },
            ),
            if (seriesProvider.isLoadingLatest)
              const LoadingShimmer(height: 200)
            else
              SizedBox(
                height: 200,
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  itemCount: seriesProvider.latestSeries.length,
                  itemBuilder: (context, index) {
                    final series = seriesProvider.latestSeries[index];
                    return Container(
                      width: 140,
                      margin: const EdgeInsets.only(right: 12),
                      child: SeriesCard(series: series),
                    );
                  },
                ),
              ),
          ],
        );
      },
    );
  }

  Widget _buildTrendingContent() {
    return Consumer<MovieProvider>(
      builder: (context, movieProvider, child) {
        return Column(
          children: [
            SectionHeader(
              title: 'الأكثر رواجاً',
              onSeeAll: () {
                // Navigate to trending content
              },
            ),
            SizedBox(
              height: 200,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                padding: const EdgeInsets.symmetric(horizontal: 16),
                itemCount: movieProvider.movies.length,
                itemBuilder: (context, index) {
                  final movie = movieProvider.movies[index];
                  return Container(
                    width: 140,
                    margin: const EdgeInsets.only(right: 12),
                    child: MovieCard(movie: movie),
                  );
                },
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildRecommendedContent() {
    return Consumer<MovieProvider>(
      builder: (context, movieProvider, child) {
        return Column(
          children: [
            SectionHeader(
              title: 'مقترح لك',
              onSeeAll: () {
                // Navigate to recommended content
              },
            ),
            SizedBox(
              height: 200,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                padding: const EdgeInsets.symmetric(horizontal: 16),
                itemCount: movieProvider.movies.length,
                itemBuilder: (context, index) {
                  final movie = movieProvider.movies[index];
                  return Container(
                    width: 140,
                    margin: const EdgeInsets.only(right: 12),
                    child: MovieCard(movie: movie),
                  );
                },
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildCarouselShimmer() {
    return Container(
      height: 500,
      child: Shimmer.fromColors(
        baseColor: AppTheme.darkSurfaceColor,
        highlightColor: AppTheme.darkCardColor,
        child: Container(
          width: double.infinity,
          color: AppTheme.darkSurfaceColor,
        ),
      ),
    );
  }

  Future<void> _refreshData() async {
    final movieProvider = Provider.of<MovieProvider>(context, listen: false);
    final seriesProvider = Provider.of<SeriesProvider>(context, listen: false);

    await Future.wait([
      movieProvider.loadFeaturedMovies(),
      movieProvider.loadPopularMovies(),
      movieProvider.loadLatestMovies(),
      seriesProvider.loadFeaturedSeries(),
      seriesProvider.loadLatestSeries(),
    ]);
  }
}
