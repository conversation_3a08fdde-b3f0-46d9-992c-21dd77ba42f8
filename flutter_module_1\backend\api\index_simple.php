<?php
/**
 * Shahid API - Simple REST API
 * Professional Video Streaming Platform
 */

// CORS Headers
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Content-Type: application/json');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Check if installation is complete
if (!file_exists('../config/installed.lock')) {
    http_response_code(503);
    echo json_encode([
        'success' => false,
        'error' => 'System not installed. Please complete installation first.',
        'redirect' => '../install_simple.php'
    ]);
    exit();
}

// Check required files
$requiredFiles = [
    '../config/database.php',
    '../core/View.php'
];

foreach ($requiredFiles as $file) {
    if (!file_exists($file)) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'error' => "Required file missing: $file",
            'action' => 'Please complete installation'
        ]);
        exit();
    }
}

// Simple API Router
class SimpleAPI {
    private $pdo;
    private $method;
    private $path;
    private $data;

    public function __construct() {
        $this->method = $_SERVER['REQUEST_METHOD'];
        $this->path = $this->getPath();
        $this->data = $this->getData();
        $this->initDatabase();
    }

    private function getPath() {
        $uri = $_SERVER['REQUEST_URI'];
        $path = parse_url($uri, PHP_URL_PATH);

        // Remove /api/ prefix and any base path
        $path = preg_replace('#^.*/api/?#', '', $path);

        // Handle direct access to index_simple.php
        if (strpos($path, 'index_simple.php') !== false) {
            // Check for query parameters that might indicate the endpoint
            if (isset($_GET['endpoint'])) {
                $path = $_GET['endpoint'];
            } else {
                $path = '';
            }
        }

        return trim($path, '/');
    }

    private function getData() {
        $input = file_get_contents('php://input');
        return json_decode($input, true) ?: [];
    }

    private function initDatabase() {
        try {
            $config = include '../config/database.php';
            $dsn = "mysql:host={$config['host']};dbname={$config['name']};charset=utf8mb4";
            $this->pdo = new PDO($dsn, $config['username'], $config['password']);
            $this->pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        } catch (Exception $e) {
            $this->error('Database connection failed: ' . $e->getMessage(), 500);
        }
    }

    public function handleRequest() {
        try {
            $pathParts = explode('/', $this->path);
            $endpoint = $pathParts[0] ?: 'status';

            switch ($endpoint) {
                case 'status':
                    $this->getStatus();
                    break;
                case 'movies':
                    $this->handleMovies($pathParts);
                    break;
                case 'series':
                    $this->handleSeries($pathParts);
                    break;
                case 'search':
                    $this->handleSearch();
                    break;
                case 'auth':
                    $this->handleAuth($pathParts);
                    break;
                case 'user':
                    $this->handleUser($pathParts);
                    break;
                default:
                    $this->error('Endpoint not found', 404);
            }
        } catch (Exception $e) {
            $this->error('Internal server error: ' . $e->getMessage(), 500);
        }
    }

    private function getStatus() {
        $status = [
            'success' => true,
            'message' => 'Shahid API is running',
            'version' => '1.0.0',
            'timestamp' => date('Y-m-d H:i:s'),
            'endpoints' => [
                'GET /api/status' => 'API status',
                'GET /api/movies' => 'List movies',
                'GET /api/movies/{id}' => 'Get movie details',
                'GET /api/series' => 'List series',
                'GET /api/series/{id}' => 'Get series details',
                'GET /api/search?q={query}' => 'Search content',
                'POST /api/auth/login' => 'User login',
                'POST /api/auth/register' => 'User registration',
                'GET /api/user/profile' => 'User profile (requires auth)'
            ]
        ];

        // Check database connection
        try {
            $stmt = $this->pdo->query("SELECT COUNT(*) as count FROM movies");
            $movieCount = $stmt->fetch()['count'];
            
            $stmt = $this->pdo->query("SELECT COUNT(*) as count FROM series");
            $seriesCount = $stmt->fetch()['count'];
            
            $stmt = $this->pdo->query("SELECT COUNT(*) as count FROM users");
            $userCount = $stmt->fetch()['count'];

            $status['database'] = [
                'connected' => true,
                'movies' => (int)$movieCount,
                'series' => (int)$seriesCount,
                'users' => (int)$userCount
            ];
        } catch (Exception $e) {
            $status['database'] = [
                'connected' => false,
                'error' => $e->getMessage()
            ];
        }

        $this->success($status);
    }

    private function handleMovies($pathParts) {
        if ($this->method !== 'GET') {
            $this->error('Method not allowed', 405);
        }

        $id = isset($pathParts[1]) ? (int)$pathParts[1] : null;

        if ($id) {
            // Get specific movie
            $stmt = $this->pdo->prepare("SELECT * FROM movies WHERE id = ? AND status = 'published'");
            $stmt->execute([$id]);
            $movie = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$movie) {
                $this->error('Movie not found', 404);
            }

            $this->success(['movie' => $movie]);
        } else {
            // Get movies list
            $page = (int)($_GET['page'] ?? 1);
            $limit = (int)($_GET['limit'] ?? 20);
            $offset = ($page - 1) * $limit;

            // Validate limits
            $limit = max(1, min(100, $limit));
            $offset = max(0, $offset);

            $stmt = $this->pdo->prepare("SELECT * FROM movies WHERE status = 'published' ORDER BY created_at DESC LIMIT $limit OFFSET $offset");
            $stmt->execute();
            $movies = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Get total count
            $stmt = $this->pdo->query("SELECT COUNT(*) as total FROM movies WHERE status = 'published'");
            $total = $stmt->fetch()['total'];

            $this->success([
                'movies' => $movies,
                'pagination' => [
                    'current_page' => $page,
                    'per_page' => $limit,
                    'total' => (int)$total,
                    'total_pages' => ceil($total / $limit)
                ]
            ]);
        }
    }

    private function handleSeries($pathParts) {
        if ($this->method !== 'GET') {
            $this->error('Method not allowed', 405);
        }

        $id = isset($pathParts[1]) ? (int)$pathParts[1] : null;

        if ($id) {
            // Get specific series
            $stmt = $this->pdo->prepare("SELECT * FROM series WHERE id = ? AND status = 'published'");
            $stmt->execute([$id]);
            $series = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$series) {
                $this->error('Series not found', 404);
            }

            // Get episodes
            $stmt = $this->pdo->prepare("SELECT * FROM episodes WHERE series_id = ? AND status = 'published' ORDER BY season_number, episode_number");
            $stmt->execute([$id]);
            $episodes = $stmt->fetchAll(PDO::FETCH_ASSOC);

            $series['episodes'] = $episodes;

            $this->success(['series' => $series]);
        } else {
            // Get series list
            $page = (int)($_GET['page'] ?? 1);
            $limit = (int)($_GET['limit'] ?? 20);
            $offset = ($page - 1) * $limit;

            // Validate limits
            $limit = max(1, min(100, $limit));
            $offset = max(0, $offset);

            $stmt = $this->pdo->prepare("SELECT * FROM series WHERE status = 'published' ORDER BY created_at DESC LIMIT $limit OFFSET $offset");
            $stmt->execute();
            $series = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Get total count
            $stmt = $this->pdo->query("SELECT COUNT(*) as total FROM series WHERE status = 'published'");
            $total = $stmt->fetch()['total'];

            $this->success([
                'series' => $series,
                'pagination' => [
                    'current_page' => $page,
                    'per_page' => $limit,
                    'total' => (int)$total,
                    'total_pages' => ceil($total / $limit)
                ]
            ]);
        }
    }

    private function handleSearch() {
        if ($this->method !== 'GET') {
            $this->error('Method not allowed', 405);
        }

        $query = $_GET['q'] ?? '';
        if (empty($query)) {
            $this->error('Search query is required', 400);
        }

        $searchTerm = "%$query%";

        // Search movies
        $stmt = $this->pdo->prepare("SELECT 'movie' as type, id, title, poster, year, rating FROM movies WHERE (title LIKE ? OR description LIKE ?) AND status = 'published' LIMIT 10");
        $stmt->execute([$searchTerm, $searchTerm]);
        $movies = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Search series
        $stmt = $this->pdo->prepare("SELECT 'series' as type, id, title, poster, year, rating FROM series WHERE (title LIKE ? OR description LIKE ?) AND status = 'published' LIMIT 10");
        $stmt->execute([$searchTerm, $searchTerm]);
        $series = $stmt->fetchAll(PDO::FETCH_ASSOC);

        $results = array_merge($movies, $series);

        $this->success([
            'query' => $query,
            'results' => $results,
            'total' => count($results)
        ]);
    }

    private function handleAuth($pathParts) {
        $action = $pathParts[1] ?? '';

        switch ($action) {
            case 'login':
                $this->login();
                break;
            case 'register':
                $this->register();
                break;
            default:
                $this->error('Auth action not found', 404);
        }
    }

    private function login() {
        if ($this->method !== 'POST') {
            $this->error('Method not allowed', 405);
        }

        $email = $this->data['email'] ?? '';
        $password = $this->data['password'] ?? '';

        if (empty($email) || empty($password)) {
            $this->error('Email and password are required', 400);
        }

        $stmt = $this->pdo->prepare("SELECT * FROM users WHERE email = ? AND status = 'active'");
        $stmt->execute([$email]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$user || !password_verify($password, $user['password'])) {
            $this->error('Invalid credentials', 401);
        }

        // Update last login
        $stmt = $this->pdo->prepare("UPDATE users SET last_login = NOW() WHERE id = ?");
        $stmt->execute([$user['id']]);

        // Remove password from response
        unset($user['password']);

        $this->success([
            'message' => 'Login successful',
            'user' => $user,
            'token' => 'demo_token_' . $user['id'] // In real app, generate JWT
        ]);
    }

    private function register() {
        if ($this->method !== 'POST') {
            $this->error('Method not allowed', 405);
        }

        $name = $this->data['name'] ?? '';
        $email = $this->data['email'] ?? '';
        $password = $this->data['password'] ?? '';

        if (empty($name) || empty($email) || empty($password)) {
            $this->error('Name, email and password are required', 400);
        }

        // Check if email exists
        $stmt = $this->pdo->prepare("SELECT id FROM users WHERE email = ?");
        $stmt->execute([$email]);
        if ($stmt->fetch()) {
            $this->error('Email already exists', 409);
        }

        // Create user
        $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
        $stmt = $this->pdo->prepare("INSERT INTO users (name, email, password, role, status, created_at) VALUES (?, ?, ?, 'user', 'active', NOW())");
        $stmt->execute([$name, $email, $hashedPassword]);

        $userId = $this->pdo->lastInsertId();

        $this->success([
            'message' => 'Registration successful',
            'user_id' => (int)$userId
        ], 201);
    }

    private function handleUser($pathParts) {
        $action = $pathParts[1] ?? 'profile';

        switch ($action) {
            case 'profile':
                $this->getUserProfile();
                break;
            default:
                $this->error('User action not found', 404);
        }
    }

    private function getUserProfile() {
        if ($this->method !== 'GET') {
            $this->error('Method not allowed', 405);
        }

        // In real app, validate JWT token
        $this->success([
            'message' => 'Authentication required',
            'note' => 'This endpoint requires valid JWT token'
        ], 401);
    }

    private function success($data, $code = 200) {
        http_response_code($code);
        echo json_encode([
            'success' => true,
            'data' => $data,
            'timestamp' => date('Y-m-d H:i:s')
        ], JSON_PRETTY_PRINT);
        exit();
    }

    private function error($message, $code = 400) {
        http_response_code($code);
        echo json_encode([
            'success' => false,
            'error' => $message,
            'code' => $code,
            'timestamp' => date('Y-m-d H:i:s')
        ], JSON_PRETTY_PRINT);
        exit();
    }
}

// Initialize and handle request
try {
    $api = new SimpleAPI();
    $api->handleRequest();
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Internal server error',
        'message' => $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_PRETTY_PRINT);
}
?>
