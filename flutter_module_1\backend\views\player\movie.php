<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= htmlspecialchars($page_title ?? 'مشاهدة - Shahid') ?></title>
    
    <!-- CSS -->
    <link rel="stylesheet" href="/assets/css/bootstrap.min.css">
    <link rel="stylesheet" href="/assets/css/player.css">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Video.js CSS -->
    <link href="https://vjs.zencdn.net/8.6.1/video-js.css" rel="stylesheet">
    
    <!-- CSRF Token -->
    <meta name="csrf-token" content="<?= $csrf_token ?? '' ?>">
</head>
<body class="player-body">
    
    <!-- Player Container -->
    <div class="player-container">
        <!-- Top Bar -->
        <div class="player-topbar">
            <div class="topbar-left">
                <button class="btn btn-link back-btn" onclick="history.back()">
                    <i class="fas fa-arrow-right me-2"></i>
                    العودة
                </button>
                <div class="movie-info">
                    <h5 class="movie-title"><?= htmlspecialchars($movie['title']) ?></h5>
                    <span class="movie-meta">
                        <?= $movie['year'] ?> • 
                        <?= floor($movie['duration'] / 60) ?>س <?= $movie['duration'] % 60 ?>د
                    </span>
                </div>
            </div>
            
            <div class="topbar-right">
                <button class="btn btn-link settings-btn" data-bs-toggle="modal" data-bs-target="#settingsModal">
                    <i class="fas fa-cog"></i>
                </button>
                <button class="btn btn-link fullscreen-btn" onclick="toggleFullscreen()">
                    <i class="fas fa-expand"></i>
                </button>
            </div>
        </div>
        
        <!-- Video Player -->
        <div class="video-wrapper">
            <video
                id="shahid-player"
                class="video-js vjs-default-skin"
                controls
                preload="auto"
                width="100%"
                height="100%"
                data-setup='{"fluid": true, "responsive": true}'
                poster="<?= $movie['poster'] ?? '/assets/images/no-poster.jpg' ?>">
                
                <!-- Video Sources -->
                <?php if (!empty($movie['video_quality'])): ?>
                    <?php foreach ($movie['video_quality'] as $quality): ?>
                        <source src="/api/stream/<?= $movie['id'] ?>?quality=<?= $quality ?>&token=<?= $video_token ?>" 
                                type="video/mp4" 
                                label="<?= $quality ?>"
                                data-quality="<?= $quality ?>">
                    <?php endforeach; ?>
                <?php else: ?>
                    <source src="/api/stream/<?= $movie['id'] ?>?token=<?= $video_token ?>" 
                            type="video/mp4">
                <?php endif; ?>
                
                <!-- Subtitles -->
                <?php if (!empty($movie['subtitles'])): ?>
                    <?php foreach ($movie['subtitles'] as $subtitle): ?>
                        <track kind="subtitles" 
                               src="/api/subtitles/<?= $subtitle['id'] ?>?token=<?= $video_token ?>"
                               srclang="<?= $subtitle['language'] ?>"
                               label="<?= htmlspecialchars($subtitle['language_name']) ?>"
                               <?= $subtitle['is_default'] ? 'default' : '' ?>>
                    <?php endforeach; ?>
                <?php endif; ?>
                
                <p class="vjs-no-js">
                    لمشاهدة هذا الفيديو، يرجى تفعيل JavaScript أو ترقية متصفحك إلى
                    <a href="https://videojs.com/html5-video-support/" target="_blank">
                        متصفح يدعم HTML5 video
                    </a>.
                </p>
            </video>
            
            <!-- Loading Overlay -->
            <div class="loading-overlay" id="loadingOverlay">
                <div class="loading-spinner">
                    <div class="spinner"></div>
                    <p>جاري التحميل...</p>
                </div>
            </div>
            
            <!-- Error Overlay -->
            <div class="error-overlay" id="errorOverlay" style="display: none;">
                <div class="error-content">
                    <i class="fas fa-exclamation-triangle"></i>
                    <h4>حدث خطأ في تشغيل الفيديو</h4>
                    <p id="errorMessage">يرجى المحاولة مرة أخرى أو التواصل مع الدعم الفني</p>
                    <button class="btn btn-primary" onclick="retryVideo()">
                        <i class="fas fa-redo me-2"></i>
                        إعادة المحاولة
                    </button>
                </div>
            </div>
            
            <!-- Skip Intro Button -->
            <button class="skip-intro-btn" id="skipIntroBtn" style="display: none;" onclick="skipIntro()">
                <i class="fas fa-forward me-2"></i>
                تخطي المقدمة
            </button>
            
            <!-- Next Episode Button -->
            <button class="next-episode-btn" id="nextEpisodeBtn" style="display: none;">
                <i class="fas fa-step-forward me-2"></i>
                الحلقة التالية
            </button>
        </div>
        
        <!-- Bottom Controls -->
        <div class="player-controls">
            <div class="controls-left">
                <button class="btn btn-link favorite-btn" data-id="<?= $movie['id'] ?>" data-type="movie">
                    <i class="far fa-heart me-2"></i>
                    إضافة للمفضلة
                </button>
                <button class="btn btn-link download-btn" onclick="downloadMovie()">
                    <i class="fas fa-download me-2"></i>
                    تحميل
                </button>
            </div>
            
            <div class="controls-center">
                <div class="playback-speed">
                    <label>سرعة التشغيل:</label>
                    <select id="speedSelect" onchange="changePlaybackSpeed(this.value)">
                        <option value="0.5">0.5x</option>
                        <option value="0.75">0.75x</option>
                        <option value="1" selected>1x</option>
                        <option value="1.25">1.25x</option>
                        <option value="1.5">1.5x</option>
                        <option value="2">2x</option>
                    </select>
                </div>
            </div>
            
            <div class="controls-right">
                <button class="btn btn-link share-btn" data-bs-toggle="modal" data-bs-target="#shareModal">
                    <i class="fas fa-share-alt me-2"></i>
                    مشاركة
                </button>
                <button class="btn btn-link report-btn" data-bs-toggle="modal" data-bs-target="#reportModal">
                    <i class="fas fa-flag me-2"></i>
                    إبلاغ
                </button>
            </div>
        </div>
    </div>
    
    <!-- Settings Modal -->
    <div class="modal fade" id="settingsModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إعدادات المشغل</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="setting-group">
                        <label class="setting-label">جودة الفيديو</label>
                        <select class="form-select" id="qualitySelect">
                            <option value="auto">تلقائي</option>
                            <?php if (!empty($movie['video_quality'])): ?>
                                <?php foreach ($movie['video_quality'] as $quality): ?>
                                    <option value="<?= $quality ?>"><?= $quality ?></option>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </select>
                    </div>
                    
                    <div class="setting-group">
                        <label class="setting-label">الترجمة</label>
                        <select class="form-select" id="subtitleSelect">
                            <option value="off">بدون ترجمة</option>
                            <?php if (!empty($movie['subtitles'])): ?>
                                <?php foreach ($movie['subtitles'] as $subtitle): ?>
                                    <option value="<?= $subtitle['language'] ?>" 
                                            <?= $subtitle['is_default'] ? 'selected' : '' ?>>
                                        <?= htmlspecialchars($subtitle['language_name']) ?>
                                    </option>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </select>
                    </div>
                    
                    <div class="setting-group">
                        <label class="setting-label">المسار الصوتي</label>
                        <select class="form-select" id="audioSelect">
                            <option value="default">افتراضي</option>
                            <?php if (!empty($movie['audio_tracks'])): ?>
                                <?php foreach ($movie['audio_tracks'] as $audio): ?>
                                    <option value="<?= $audio['language'] ?>"
                                            <?= $audio['is_default'] ? 'selected' : '' ?>>
                                        <?= htmlspecialchars($audio['language_name']) ?>
                                    </option>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </select>
                    </div>
                    
                    <div class="setting-group">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="autoPlayNext">
                            <label class="form-check-label" for="autoPlayNext">
                                تشغيل تلقائي للحلقة التالية
                            </label>
                        </div>
                    </div>
                    
                    <div class="setting-group">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="skipIntroAuto">
                            <label class="form-check-label" for="skipIntroAuto">
                                تخطي المقدمة تلقائياً
                            </label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                    <button type="button" class="btn btn-primary" onclick="saveSettings()">حفظ الإعدادات</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Share Modal -->
    <div class="modal fade" id="shareModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">مشاركة الفيلم</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="share-options">
                        <button class="btn btn-social btn-facebook" onclick="shareOn('facebook')">
                            <i class="fab fa-facebook-f me-2"></i>
                            Facebook
                        </button>
                        <button class="btn btn-social btn-twitter" onclick="shareOn('twitter')">
                            <i class="fab fa-twitter me-2"></i>
                            Twitter
                        </button>
                        <button class="btn btn-social btn-whatsapp" onclick="shareOn('whatsapp')">
                            <i class="fab fa-whatsapp me-2"></i>
                            WhatsApp
                        </button>
                        <button class="btn btn-social btn-telegram" onclick="shareOn('telegram')">
                            <i class="fab fa-telegram me-2"></i>
                            Telegram
                        </button>
                    </div>
                    
                    <div class="share-link">
                        <label class="form-label">رابط المشاركة</label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="shareLink" 
                                   value="<?= htmlspecialchars($_SERVER['REQUEST_URI']) ?>" readonly>
                            <button class="btn btn-outline-primary" onclick="copyShareLink()">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Report Modal -->
    <div class="modal fade" id="reportModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">الإبلاغ عن مشكلة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form id="reportForm">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label class="form-label">نوع المشكلة</label>
                            <select class="form-select" name="reason" required>
                                <option value="">اختر نوع المشكلة</option>
                                <option value="broken_link">رابط معطل</option>
                                <option value="poor_quality">جودة ضعيفة</option>
                                <option value="wrong_subtitle">ترجمة خاطئة</option>
                                <option value="audio_sync">عدم تزامن الصوت</option>
                                <option value="buffering">مشاكل في التحميل</option>
                                <option value="other">أخرى</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">تفاصيل المشكلة</label>
                            <textarea class="form-control" name="description" rows="3" 
                                      placeholder="اشرح المشكلة بالتفصيل..."></textarea>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">الوقت في الفيديو (اختياري)</label>
                            <input type="text" class="form-control" name="timestamp" 
                                   placeholder="مثال: 15:30" id="currentTimeInput">
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-danger">إرسال البلاغ</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/assets/js/jquery.min.js"></script>
    <script src="https://vjs.zencdn.net/8.6.1/video.min.js"></script>
    <script src="/assets/js/player.js"></script>
    
    <script>
        // Initialize player with movie data
        const movieData = {
            id: <?= $movie['id'] ?>,
            title: '<?= htmlspecialchars($movie['title']) ?>',
            duration: <?= $movie['duration'] * 60 ?>, // Convert to seconds
            watchProgress: <?= $watch_progress ? $watch_progress['progress'] : 0 ?>,
            token: '<?= $video_token ?>'
        };
        
        // Initialize the video player
        initializePlayer(movieData);
    </script>
    
</body>
</html>
