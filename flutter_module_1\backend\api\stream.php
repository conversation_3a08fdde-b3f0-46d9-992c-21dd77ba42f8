<?php
/**
 * Shahid Video Streaming API
 * Secure video streaming with token validation
 */

require_once '../core/Database.php';
require_once '../core/Security.php';
require_once '../models/User.php';
require_once '../models/Movie.php';

class VideoStreamAPI {
    private $db;
    private $security;
    
    public function __construct() {
        $this->db = Database::getInstance()->getConnection();
        $this->security = new Security();
    }
    
    public function stream() {
        try {
            // Get parameters
            $contentId = intval($_GET['id'] ?? 0);
            $token = $_GET['token'] ?? '';
            $quality = $_GET['quality'] ?? 'auto';
            $range = $_SERVER['HTTP_RANGE'] ?? '';
            
            // Validate token
            if (!$this->validateStreamToken($token, $contentId)) {
                $this->sendError(403, 'Invalid or expired token');
                return;
            }
            
            // Get content info
            $content = $this->getContentInfo($contentId);
            if (!$content) {
                $this->sendError(404, 'Content not found');
                return;
            }
            
            // Check user access
            if (!$this->checkUserAccess($content)) {
                $this->sendError(403, 'Access denied');
                return;
            }
            
            // Get video file path
            $videoPath = $this->getVideoPath($content, $quality);
            if (!$videoPath || !file_exists($videoPath)) {
                $this->sendError(404, 'Video file not found');
                return;
            }
            
            // Stream the video
            $this->streamVideo($videoPath, $range);
            
        } catch (Exception $e) {
            error_log('Video Stream Error: ' . $e->getMessage());
            $this->sendError(500, 'Internal server error');
        }
    }
    
    private function validateStreamToken($token, $contentId) {
        try {
            $payload = $this->security->validateJWT($token);
            
            if (!$payload || 
                !isset($payload['content_id']) || 
                !isset($payload['user_id']) ||
                !isset($payload['exp'])) {
                return false;
            }
            
            // Check if token is for this content
            if ($payload['content_id'] != $contentId) {
                return false;
            }
            
            // Check if token is expired
            if ($payload['exp'] < time()) {
                return false;
            }
            
            return $payload;
            
        } catch (Exception $e) {
            return false;
        }
    }
    
    private function getContentInfo($contentId) {
        $sql = "SELECT * FROM movies WHERE id = :id AND status = 'active'";
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':id', $contentId);
        $stmt->execute();
        
        $movie = $stmt->fetch();
        if ($movie) {
            $movie['type'] = 'movie';
            return $movie;
        }
        
        // Check episodes table
        $sql = "SELECT e.*, s.title as series_title 
                FROM episodes e 
                JOIN series s ON e.series_id = s.id 
                WHERE e.id = :id AND e.status = 'active'";
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':id', $contentId);
        $stmt->execute();
        
        $episode = $stmt->fetch();
        if ($episode) {
            $episode['type'] = 'episode';
            return $episode;
        }
        
        return null;
    }
    
    private function checkUserAccess($content) {
        // Get user from token
        $token = $_GET['token'] ?? '';
        $payload = $this->security->validateJWT($token);
        
        if (!$payload || !isset($payload['user_id'])) {
            return false;
        }
        
        $userId = $payload['user_id'];
        
        // Check if content is premium
        if (!$content['premium']) {
            return true; // Free content
        }
        
        // Check user subscription
        $userModel = new User();
        return $userModel->hasActiveSubscription($userId);
    }
    
    private function getVideoPath($content, $quality) {
        $baseDir = '../storage/videos/';
        $contentDir = $content['type'] === 'movie' ? 'movies/' : 'episodes/';
        
        // Try to get specific quality
        if ($quality !== 'auto') {
            $qualityPath = $baseDir . $contentDir . $content['id'] . '/' . $quality . '.mp4';
            if (file_exists($qualityPath)) {
                return $qualityPath;
            }
        }
        
        // Fallback to default quality
        $defaultPath = $baseDir . $contentDir . $content['id'] . '/default.mp4';
        if (file_exists($defaultPath)) {
            return $defaultPath;
        }
        
        // Try common quality files
        $qualities = ['1080p', '720p', '480p', '360p'];
        foreach ($qualities as $q) {
            $path = $baseDir . $contentDir . $content['id'] . '/' . $q . '.mp4';
            if (file_exists($path)) {
                return $path;
            }
        }
        
        return null;
    }
    
    private function streamVideo($videoPath, $range) {
        $fileSize = filesize($videoPath);
        $start = 0;
        $end = $fileSize - 1;
        
        // Parse range header for partial content
        if ($range) {
            if (preg_match('/bytes=(\d+)-(\d*)/', $range, $matches)) {
                $start = intval($matches[1]);
                if (!empty($matches[2])) {
                    $end = intval($matches[2]);
                }
            }
        }
        
        // Validate range
        if ($start > $end || $start >= $fileSize || $end >= $fileSize) {
            $this->sendError(416, 'Range not satisfiable');
            return;
        }
        
        $contentLength = $end - $start + 1;
        
        // Set headers
        header('Content-Type: video/mp4');
        header('Accept-Ranges: bytes');
        header('Content-Length: ' . $contentLength);
        
        if ($range) {
            header('HTTP/1.1 206 Partial Content');
            header("Content-Range: bytes $start-$end/$fileSize");
        } else {
            header('HTTP/1.1 200 OK');
        }
        
        // Security headers
        header('X-Content-Type-Options: nosniff');
        header('X-Frame-Options: SAMEORIGIN');
        
        // Cache headers
        header('Cache-Control: private, max-age=3600');
        header('Expires: ' . gmdate('D, d M Y H:i:s', time() + 3600) . ' GMT');
        
        // Stream the video
        $this->outputVideoChunk($videoPath, $start, $contentLength);
    }
    
    private function outputVideoChunk($videoPath, $start, $length) {
        $chunkSize = 8192; // 8KB chunks
        $handle = fopen($videoPath, 'rb');
        
        if (!$handle) {
            $this->sendError(500, 'Cannot open video file');
            return;
        }
        
        // Seek to start position
        fseek($handle, $start);
        
        $bytesRemaining = $length;
        
        while ($bytesRemaining > 0 && !feof($handle)) {
            $readSize = min($chunkSize, $bytesRemaining);
            $chunk = fread($handle, $readSize);
            
            if ($chunk === false) {
                break;
            }
            
            echo $chunk;
            flush();
            
            $bytesRemaining -= strlen($chunk);
            
            // Check if client disconnected
            if (connection_aborted()) {
                break;
            }
        }
        
        fclose($handle);
    }
    
    private function sendError($code, $message) {
        http_response_code($code);
        header('Content-Type: application/json');
        echo json_encode(['error' => $message]);
        exit;
    }
}

// Handle the request
if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    $api = new VideoStreamAPI();
    $api->stream();
} else {
    http_response_code(405);
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Method not allowed']);
}
?>
