/// إعدادات التطبيق الشاملة - Shahid Platform Configuration
/// يحتوي على جميع الإعدادات والثوابت المستخدمة في التطبيق

import 'package:flutter/material.dart';

// ===== إعدادات API =====
class ApiConfig {
  static const String baseUrl = 'http://127.0.0.1/amr2/flutter_module_1/backend';
  static const String apiUrl = '$baseUrl/api';
  static const String uploadsUrl = '$baseUrl/uploads';
  static const String assetsUrl = '$baseUrl/assets';
  
  // نقاط النهاية الأساسية
  static const String moviesEndpoint = '$apiUrl/index.php?endpoint=movies';
  static const String seriesEndpoint = '$apiUrl/index.php?endpoint=series';
  static const String searchEndpoint = '$apiUrl/index.php?endpoint=search';
  static const String loginEndpoint = '$apiUrl/index.php?endpoint=login';
  static const String registerEndpoint = '$apiUrl/index.php?endpoint=register';
  static const String userEndpoint = '$apiUrl/index.php?endpoint=user';
  
  // نقاط النهاية المتقدمة
  static const String advancedApiUrl = '$apiUrl/advanced.php';
  static const String favoritesEndpoint = '$advancedApiUrl?endpoint=favorites';
  static const String ratingsEndpoint = '$advancedApiUrl?endpoint=ratings';
  static const String watchHistoryEndpoint = '$advancedApiUrl?endpoint=watch_history';
  static const String notificationsEndpoint = '$advancedApiUrl?endpoint=notifications';
  static const String recommendationsEndpoint = '$advancedApiUrl?endpoint=recommendations';
  static const String trendingEndpoint = '$advancedApiUrl?endpoint=trending';
  static const String recentEndpoint = '$advancedApiUrl?endpoint=recent';
  
  // إعدادات الطلبات
  static const int connectionTimeout = 30000; // 30 ثانية
  static const int receiveTimeout = 30000; // 30 ثانية
  static const int maxRetries = 3;
  static const int retryDelay = 1000; // 1 ثانية
}

// ===== ألوان التطبيق =====
class AppColors {
  // الألوان الأساسية
  static const Color primary = Color(0xFFE50914);
  static const Color primaryDark = Color(0xFFB8070F);
  static const Color primaryLight = Color(0xFFFF4757);
  static const Color secondary = Color(0xFF564D4D);
  static const Color accent = Color(0xFFFFD700);
  
  // ألوان الخلفية
  static const Color background = Color(0xFF0F0F0F);
  static const Color backgroundLight = Color(0xFF1A1A1A);
  static const Color surface = Color(0xFF2D2D2D);
  static const Color surfaceLight = Color(0xFF3D3D3D);
  static const Color cardBackground = Color(0xFF2A2A2A);
  
  // ألوان النص
  static const Color textPrimary = Color(0xFFFFFFFF);
  static const Color textSecondary = Color(0xFFCCCCCC);
  static const Color textMuted = Color(0xFF999999);
  static const Color textDisabled = Color(0xFF666666);
  
  // ألوان الحالة
  static const Color success = Color(0xFF4CAF50);
  static const Color warning = Color(0xFFFF9800);
  static const Color error = Color(0xFFF44336);
  static const Color info = Color(0xFF2196F3);
  
  // ألوان إضافية
  static const Color gold = Color(0xFFFFD700);
  static const Color silver = Color(0xFFC0C0C0);
  static const Color bronze = Color(0xFFCD7F32);
  
  // تدرجات لونية
  static const LinearGradient primaryGradient = LinearGradient(
    colors: [primary, primaryDark],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient backgroundGradient = LinearGradient(
    colors: [background, backgroundLight],
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
  );
  
  static const LinearGradient cardGradient = LinearGradient(
    colors: [surface, surfaceLight],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
}

// ===== أحجام وأبعاد التطبيق =====
class AppDimensions {
  // المسافات الأساسية
  static const double paddingXS = 4.0;
  static const double paddingS = 8.0;
  static const double paddingM = 16.0;
  static const double paddingL = 24.0;
  static const double paddingXL = 32.0;
  static const double paddingXXL = 48.0;
  
  // الهوامش
  static const double marginXS = 4.0;
  static const double marginS = 8.0;
  static const double marginM = 16.0;
  static const double marginL = 24.0;
  static const double marginXL = 32.0;
  
  // نصف القطر للحواف
  static const double radiusS = 4.0;
  static const double radiusM = 8.0;
  static const double radiusL = 12.0;
  static const double radiusXL = 16.0;
  static const double radiusXXL = 24.0;
  static const double radiusCircle = 50.0;
  
  // أحجام الأيقونات
  static const double iconXS = 16.0;
  static const double iconS = 20.0;
  static const double iconM = 24.0;
  static const double iconL = 32.0;
  static const double iconXL = 48.0;
  static const double iconXXL = 64.0;
  
  // أحجام الصور
  static const double imageThumbWidth = 120.0;
  static const double imageThumbHeight = 180.0;
  static const double imageCardWidth = 150.0;
  static const double imageCardHeight = 220.0;
  static const double imageBannerHeight = 200.0;
  static const double imageAvatarSize = 40.0;
  
  // أحجام الأزرار
  static const double buttonHeight = 48.0;
  static const double buttonHeightS = 36.0;
  static const double buttonHeightL = 56.0;
  static const double buttonMinWidth = 120.0;
  
  // أحجام شريط التطبيق
  static const double appBarHeight = 56.0;
  static const double tabBarHeight = 48.0;
  static const double bottomNavHeight = 60.0;
  
  // أحجام النوافذ المنبثقة
  static const double dialogMaxWidth = 400.0;
  static const double bottomSheetMaxHeight = 600.0;
  
  // الظلال
  static const double elevationS = 2.0;
  static const double elevationM = 4.0;
  static const double elevationL = 8.0;
  static const double elevationXL = 16.0;
}

// ===== أنماط النصوص =====
class AppTextStyles {
  // العناوين الرئيسية
  static const TextStyle h1 = TextStyle(
    fontSize: 32,
    fontWeight: FontWeight.bold,
    color: AppColors.textPrimary,
    height: 1.2,
  );
  
  static const TextStyle h2 = TextStyle(
    fontSize: 28,
    fontWeight: FontWeight.bold,
    color: AppColors.textPrimary,
    height: 1.3,
  );
  
  static const TextStyle h3 = TextStyle(
    fontSize: 24,
    fontWeight: FontWeight.bold,
    color: AppColors.textPrimary,
    height: 1.3,
  );
  
  static const TextStyle h4 = TextStyle(
    fontSize: 20,
    fontWeight: FontWeight.w600,
    color: AppColors.textPrimary,
    height: 1.4,
  );
  
  static const TextStyle h5 = TextStyle(
    fontSize: 18,
    fontWeight: FontWeight.w600,
    color: AppColors.textPrimary,
    height: 1.4,
  );
  
  static const TextStyle h6 = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w600,
    color: AppColors.textPrimary,
    height: 1.4,
  );
  
  // النصوص العادية
  static const TextStyle bodyLarge = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.normal,
    color: AppColors.textPrimary,
    height: 1.5,
  );
  
  static const TextStyle bodyMedium = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.normal,
    color: AppColors.textPrimary,
    height: 1.5,
  );
  
  static const TextStyle bodySmall = TextStyle(
    fontSize: 12,
    fontWeight: FontWeight.normal,
    color: AppColors.textSecondary,
    height: 1.5,
  );
  
  // النصوص الثانوية
  static const TextStyle caption = TextStyle(
    fontSize: 12,
    fontWeight: FontWeight.normal,
    color: AppColors.textMuted,
    height: 1.4,
  );
  
  static const TextStyle overline = TextStyle(
    fontSize: 10,
    fontWeight: FontWeight.w500,
    color: AppColors.textMuted,
    height: 1.6,
    letterSpacing: 1.5,
  );
  
  // نصوص الأزرار
  static const TextStyle button = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w600,
    color: AppColors.textPrimary,
    letterSpacing: 1.25,
  );
  
  static const TextStyle buttonLarge = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w600,
    color: AppColors.textPrimary,
    letterSpacing: 1.25,
  );
  
  // نصوص خاصة
  static const TextStyle rating = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.bold,
    color: AppColors.gold,
  );
  
  static const TextStyle price = TextStyle(
    fontSize: 18,
    fontWeight: FontWeight.bold,
    color: AppColors.primary,
  );
  
  static const TextStyle error = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.normal,
    color: AppColors.error,
  );
  
  static const TextStyle success = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.normal,
    color: AppColors.success,
  );
}

// ===== إعدادات الرسوم المتحركة =====
class AppAnimations {
  // مدد الرسوم المتحركة
  static const Duration durationFast = Duration(milliseconds: 150);
  static const Duration durationNormal = Duration(milliseconds: 300);
  static const Duration durationSlow = Duration(milliseconds: 500);
  static const Duration durationVerySlow = Duration(milliseconds: 1000);
  
  // منحنيات الرسوم المتحركة
  static const Curve curveDefault = Curves.easeInOut;
  static const Curve curveEaseIn = Curves.easeIn;
  static const Curve curveEaseOut = Curves.easeOut;
  static const Curve curveBounce = Curves.bounceOut;
  static const Curve curveElastic = Curves.elasticOut;
  
  // تأثيرات الانتقال
  static const Offset slideFromRight = Offset(1.0, 0.0);
  static const Offset slideFromLeft = Offset(-1.0, 0.0);
  static const Offset slideFromTop = Offset(0.0, -1.0);
  static const Offset slideFromBottom = Offset(0.0, 1.0);
}

// ===== إعدادات التطبيق العامة =====
class AppSettings {
  // معلومات التطبيق
  static const String appName = 'Shahid Platform';
  static const String appVersion = '2.0.0';
  static const String appDescription = 'منصة البث الاحترافية الكاملة';
  
  // إعدادات اللغة
  static const String defaultLanguage = 'ar';
  static const List<String> supportedLanguages = ['ar', 'en'];
  static const bool isRTL = true;
  
  // إعدادات التخزين المحلي
  static const String userTokenKey = 'user_token';
  static const String userDataKey = 'user_data';
  static const String settingsKey = 'app_settings';
  static const String favoritesKey = 'favorites';
  static const String watchHistoryKey = 'watch_history';
  
  // إعدادات التشغيل
  static const bool autoPlay = false;
  static const double defaultVolume = 1.0;
  static const double defaultPlaybackSpeed = 1.0;
  static const List<double> playbackSpeeds = [0.5, 0.75, 1.0, 1.25, 1.5, 2.0];
  
  // إعدادات الجودة
  static const String defaultQuality = 'auto';
  static const List<String> videoQualities = ['240p', '360p', '480p', '720p', '1080p', 'auto'];
  
  // إعدادات التحميل
  static const int itemsPerPage = 20;
  static const int maxSearchResults = 50;
  static const int cacheSize = 100; // عدد العناصر في الذاكرة المؤقتة
  
  // إعدادات الإشعارات
  static const bool notificationsEnabled = true;
  static const bool soundEnabled = true;
  static const bool vibrationEnabled = true;
  
  // إعدادات الأمان
  static const int sessionTimeout = 7200; // ساعتان بالثواني
  static const int maxLoginAttempts = 5;
  static const int lockoutDuration = 900; // 15 دقيقة بالثواني
  
  // إعدادات الشبكة
  static const int connectionTimeout = 30;
  static const int readTimeout = 30;
  static const int maxRetries = 3;
  static const bool enableLogging = true;
}

// ===== ثوابت التطبيق =====
class AppConstants {
  // أنواع المحتوى
  static const String contentTypeMovie = 'movie';
  static const String contentTypeSeries = 'series';
  static const String contentTypeEpisode = 'episode';
  
  // حالات المحتوى
  static const String statusActive = 'active';
  static const String statusInactive = 'inactive';
  static const String statusPending = 'pending';
  static const String statusDraft = 'draft';
  
  // أنواع المستخدمين
  static const String roleAdmin = 'admin';
  static const String roleModerator = 'moderator';
  static const String roleUser = 'user';
  static const String roleGuest = 'guest';
  
  // أنواع الاشتراك
  static const String subscriptionFree = 'free';
  static const String subscriptionBasic = 'basic';
  static const String subscriptionPremium = 'premium';
  static const String subscriptionVip = 'vip';
  
  // أنواع الإشعارات
  static const String notificationInfo = 'info';
  static const String notificationSuccess = 'success';
  static const String notificationWarning = 'warning';
  static const String notificationError = 'error';
  
  // تصنيفات المحتوى
  static const List<String> movieCategories = [
    'action', 'comedy', 'drama', 'horror', 'romance', 'thriller',
    'sci-fi', 'fantasy', 'adventure', 'crime', 'mystery', 'war',
    'history', 'biography', 'sport', 'music', 'family', 'kids'
  ];
  
  // اللغات المدعومة
  static const Map<String, String> languages = {
    'ar': 'العربية',
    'en': 'English',
    'fr': 'Français',
    'es': 'Español',
    'de': 'Deutsch',
    'tr': 'Türkçe',
  };
  
  // البلدان المدعومة
  static const Map<String, String> countries = {
    'SA': 'السعودية',
    'AE': 'الإمارات',
    'EG': 'مصر',
    'JO': 'الأردن',
    'LB': 'لبنان',
    'SY': 'سوريا',
    'IQ': 'العراق',
    'KW': 'الكويت',
    'QA': 'قطر',
    'BH': 'البحرين',
    'OM': 'عمان',
    'YE': 'اليمن',
    'MA': 'المغرب',
    'TN': 'تونس',
    'DZ': 'الجزائر',
    'LY': 'ليبيا',
    'SD': 'السودان',
    'PS': 'فلسطين',
  };
  
  // رسائل الخطأ الشائعة
  static const Map<String, String> errorMessages = {
    'network_error': 'خطأ في الاتصال بالشبكة',
    'server_error': 'خطأ في الخادم',
    'invalid_credentials': 'بيانات الدخول غير صحيحة',
    'session_expired': 'انتهت صلاحية الجلسة',
    'permission_denied': 'ليس لديك صلاحية للوصول',
    'content_not_found': 'المحتوى غير موجود',
    'invalid_input': 'البيانات المدخلة غير صحيحة',
    'upload_failed': 'فشل في رفع الملف',
    'download_failed': 'فشل في تحميل الملف',
    'unknown_error': 'خطأ غير معروف',
  };
  
  // رسائل النجاح
  static const Map<String, String> successMessages = {
    'login_success': 'تم تسجيل الدخول بنجاح',
    'logout_success': 'تم تسجيل الخروج بنجاح',
    'register_success': 'تم إنشاء الحساب بنجاح',
    'update_success': 'تم التحديث بنجاح',
    'delete_success': 'تم الحذف بنجاح',
    'upload_success': 'تم رفع الملف بنجاح',
    'download_success': 'تم تحميل الملف بنجاح',
    'save_success': 'تم الحفظ بنجاح',
  };
}

// ===== إعدادات البيئة =====
class Environment {
  static const bool isDevelopment = true;
  static const bool isProduction = false;
  static const bool enableDebugMode = true;
  static const bool enableAnalytics = false;
  static const bool enableCrashReporting = false;
  
  // إعدادات التسجيل
  static const bool enableLogging = true;
  static const String logLevel = 'debug'; // debug, info, warning, error
  
  // إعدادات الأداء
  static const bool enablePerformanceMonitoring = true;
  static const int performanceThreshold = 100; // milliseconds
}
