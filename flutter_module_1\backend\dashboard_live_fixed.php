<?php
/**
 * لوحة المراقبة المباشرة المحدثة - بدون أخطاء
 * Updated Live Dashboard - Error Free
 */

// بدء الجلسة بشكل آمن
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// تسجيل دخول تلقائي للاختبار
if (!isset($_SESSION["user_id"])) {
    $_SESSION["user_id"] = 1;
    $_SESSION["user_name"] = "مدير النظام";
    $_SESSION["user_role"] = "admin";
}

// Function to check API status
function checkAPIStatus() {
    try {
        $testEndpoints = [
            "test.php" => "اختبار API",
            "homepage.php" => "الصفحة الرئيسية"
        ];

        $workingEndpoints = 0;
        $totalEndpoints = count($testEndpoints);

        foreach ($testEndpoints as $endpoint => $name) {
            $url = "http://localhost" . dirname($_SERVER["REQUEST_URI"]) . "/$endpoint";
            $headers = @get_headers($url);
            if ($headers && strpos($headers[0], "200") !== false) {
                $workingEndpoints++;
            }
        }

        $percentage = ($workingEndpoints / $totalEndpoints) * 100;

        return [
            "status" => $percentage >= 80 ? "success" : ($percentage >= 50 ? "warning" : "error"),
            "working" => $workingEndpoints,
            "total" => $totalEndpoints,
            "percentage" => round($percentage, 1),
            "message" => "$workingEndpoints من $totalEndpoints نقطة نهاية تعمل"
        ];
    } catch (Exception $e) {
        return [
            "status" => "error",
            "working" => 0,
            "total" => 0,
            "percentage" => 0,
            "message" => "خطأ في فحص API: " . $e->getMessage()
        ];
    }
}

// Function to check database status
function checkDatabaseStatus() {
    try {
        $pdo = new PDO("mysql:host=localhost;dbname=shahid_platform;charset=utf8mb4", "root", "");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

        // Test basic operations
        $stmt = $pdo->query("SELECT 1");
        $result = $stmt->fetch();

        // Get database size
        $stmt = $pdo->query("
            SELECT ROUND(SUM(data_length + index_length) / 1024 / 1024, 1) AS size_mb
            FROM information_schema.tables
            WHERE table_schema = \"shahid_platform\"
        ");
        $sizeResult = $stmt->fetch();
        $dbSize = $sizeResult["size_mb"] ?? 0;

        return [
            "status" => "success",
            "connected" => true,
            "size" => $dbSize . " MB",
            "message" => "قاعدة البيانات متصلة وتعمل بشكل طبيعي"
        ];
    } catch (Exception $e) {
        return [
            "status" => "error",
            "connected" => false,
            "size" => "غير معروف",
            "message" => "خطأ في الاتصال: " . $e->getMessage()
        ];
    }
}

// Function to check admin accounts (محدثة)
function checkAdminAccounts() {
    try {
        $pdo = new PDO("mysql:host=localhost;dbname=shahid_platform;charset=utf8mb4", "root", "");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

        // فحص وجود جدول users
        $stmt = $pdo->query("SHOW TABLES LIKE \"users\"");
        if ($stmt->rowCount() == 0) {
            return [
                "status" => "warning",
                "exists" => false,
                "count" => 0,
                "active_sessions" => 0,
                "last_activity" => "جدول المستخدمين غير موجود",
                "message" => "جدول users غير موجود"
            ];
        }

        $stmt = $pdo->prepare("SELECT COUNT(*) as count, MAX(created_at) as last_login FROM users WHERE role = \"admin\"");
        $stmt->execute();
        $result = $stmt->fetch();

        $adminCount = $result["count"] ?? 0;
        $lastLogin = $result["last_login"];

        // Check for active sessions (مع فحص وجود الجدول)
        $activeSessions = 0;
        $stmt = $pdo->query("SHOW TABLES LIKE \"user_sessions\"");
        if ($stmt->rowCount() > 0) {
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM user_sessions WHERE user_id IN (SELECT id FROM users WHERE role = \"admin\") AND expires_at > NOW()");
            $stmt->execute();
            $activeSessions = $stmt->fetchColumn();
        }

        return [
            "status" => $adminCount > 0 ? "success" : "warning",
            "exists" => $adminCount > 0,
            "count" => $adminCount,
            "active_sessions" => $activeSessions,
            "last_activity" => $lastLogin ? date("Y-m-d H:i", strtotime($lastLogin)) : "غير محدد",
            "message" => $adminCount > 0 ? "حسابات المديرين متاحة" : "لا توجد حسابات مديرين"
        ];
    } catch (Exception $e) {
        return [
            "status" => "error",
            "exists" => false,
            "count" => 0,
            "active_sessions" => 0,
            "last_activity" => "غير متاح",
            "message" => "خطأ في فحص المديرين: " . $e->getMessage()
        ];
    }
}

// Function to check tables status
function checkTablesStatus() {
    try {
        $pdo = new PDO("mysql:host=localhost;dbname=shahid_platform;charset=utf8mb4", "root", "");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

        $requiredTables = ["users", "movies", "series", "watch_history", "user_sessions"];
        $existingTables = [];

        $stmt = $pdo->query("SHOW TABLES");
        while ($table = $stmt->fetchColumn()) {
            $existingTables[] = $table;
        }

        $missingTables = array_diff($requiredTables, $existingTables);
        $existingCount = count($existingTables);
        $requiredCount = count($requiredTables);

        return [
            "status" => empty($missingTables) ? "success" : (count($missingTables) <= 2 ? "warning" : "error"),
            "existing" => $existingCount,
            "required" => $requiredCount,
            "missing" => $missingTables,
            "message" => empty($missingTables) ? "جميع الجداول موجودة" : "بعض الجداول مفقودة: " . implode(", ", $missingTables)
        ];
    } catch (Exception $e) {
        return [
            "status" => "error",
            "existing" => 0,
            "required" => 0,
            "missing" => [],
            "message" => "خطأ في فحص الجداول: " . $e->getMessage()
        ];
    }
}

// Get all system status
$apiStatus = checkAPIStatus();
$dbStatus = checkDatabaseStatus();
$adminStatus = checkAdminAccounts();
$tablesStatus = checkTablesStatus();

// Calculate overall system health
$healthScore = 0;
if (($apiStatus["status"] ?? "error") === "success") $healthScore += 25;
elseif (($apiStatus["status"] ?? "error") === "warning") $healthScore += 15;

if (($dbStatus["status"] ?? "error") === "success") $healthScore += 25;
if (($adminStatus["status"] ?? "error") === "success") $healthScore += 25;
if (($tablesStatus["status"] ?? "error") === "success") $healthScore += 25;

$overallStatus = $healthScore >= 90 ? "excellent" : ($healthScore >= 70 ? "good" : ($healthScore >= 50 ? "warning" : "critical"));

// Return JSON for AJAX requests
if (isset($_GET["ajax"])) {
    header("Content-Type: application/json");
    echo json_encode([
        "api" => $apiStatus,
        "database" => $dbStatus,
        "admin" => $adminStatus,
        "tables" => $tablesStatus,
        "overall" => [
            "status" => $overallStatus,
            "score" => $healthScore
        ],
        "timestamp" => date("Y-m-d H:i:s")
    ]);
    exit;
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📊 لوحة المراقبة المباشرة - Shahid Platform</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>📊</text></svg>">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', 'Cairo', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #2d2d2d 100%);
            color: #fff;
            min-height: 100vh;
            overflow-x: hidden;
        }

        .header {
            background: linear-gradient(135deg, #E50914 0%, #B8070F 100%);
            padding: 2rem;
            text-align: center;
            box-shadow: 0 4px 20px rgba(229, 9, 20, 0.3);
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="white" opacity="0.1"/></svg>') repeat;
            animation: sparkle 3s linear infinite;
        }

        @keyframes sparkle {
            0% { transform: translateX(0) translateY(0); }
            100% { transform: translateX(-100px) translateY(-100px); }
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
            position: relative;
            z-index: 2;
        }

        .header .subtitle {
            opacity: 0.9;
            margin-top: 0.5rem;
            font-size: 1.2rem;
            position: relative;
            z-index: 2;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        .system-health {
            background: linear-gradient(135deg, rgba(47, 47, 47, 0.9) 0%, rgba(35, 35, 35, 0.9) 100%);
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            border: 2px solid rgba(229, 9, 20, 0.3);
            text-align: center;
        }

        .health-score {
            font-size: 4rem;
            font-weight: bold;
            margin-bottom: 1rem;
        }

        .health-score.excellent { color: #28a745; }
        .health-score.good { color: #20c997; }
        .health-score.warning { color: #ffc107; }
        .health-score.critical { color: #dc3545; }

        .health-label {
            font-size: 1.5rem;
            opacity: 0.9;
            margin-bottom: 1rem;
        }

        .monitors-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .monitor-card {
            background: linear-gradient(135deg, rgba(47, 47, 47, 0.9) 0%, rgba(35, 35, 35, 0.9) 100%);
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
            border: 2px solid;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .monitor-card.success { border-color: rgba(40, 167, 69, 0.5); }
        .monitor-card.warning { border-color: rgba(255, 193, 7, 0.5); }
        .monitor-card.error { border-color: rgba(220, 53, 69, 0.5); }

        .monitor-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent 30%, rgba(229, 9, 20, 0.1) 50%, transparent 70%);
            transform: translateX(-100%);
            transition: transform 0.6s ease;
        }

        .monitor-card:hover::before {
            transform: translateX(100%);
        }

        .monitor-card:hover {
            transform: translateY(-8px) scale(1.02);
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }

        .card-title {
            font-size: 1.3rem;
            font-weight: bold;
            color: #E50914;
        }

        .status-indicator {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            position: relative;
            z-index: 2;
        }

        .status-indicator.success {
            background: #28a745;
            box-shadow: 0 0 10px rgba(40, 167, 69, 0.5);
        }

        .status-indicator.warning {
            background: #ffc107;
            box-shadow: 0 0 10px rgba(255, 193, 7, 0.5);
        }

        .status-indicator.error {
            background: #dc3545;
            box-shadow: 0 0 10px rgba(220, 53, 69, 0.5);
        }

        .card-content {
            position: relative;
            z-index: 2;
        }

        .status-text {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 1rem;
        }

        .status-details {
            opacity: 0.8;
            line-height: 1.6;
        }

        .refresh-info {
            text-align: center;
            margin: 2rem 0;
            opacity: 0.7;
        }

        .controls-section {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
            justify-content: center;
            margin: 2rem 0;
        }

        .btn {
            background: linear-gradient(45deg, #E50914, #B8070F);
            color: white;
            padding: 1rem 2rem;
            text-decoration: none;
            border-radius: 12px;
            font-weight: bold;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            font-size: 1rem;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(229, 9, 20, 0.3);
        }

        .btn.secondary {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .btn.success { background: linear-gradient(45deg, #28a745, #20c997); }
        .btn.info { background: linear-gradient(45deg, #17a2b8, #20c997); }
        .btn.warning { background: linear-gradient(45deg, #ffc107, #fd7e14); color: #000; }

        @media (max-width: 768px) {
            .container { padding: 1rem; }
            .header h1 { font-size: 2rem; }
            .monitors-grid { grid-template-columns: 1fr; gap: 1rem; }
            .health-score { font-size: 3rem; }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>📊 لوحة المراقبة المباشرة</h1>
        <div class="subtitle">مراقبة حالة النظام في الوقت الفعلي - منصة شاهد</div>
    </div>

    <div class="container">
        <!-- نتيجة الصحة العامة -->
        <div class="system-health">
            <div class="health-score <?php echo $overallStatus; ?>" id="health-score">
                <?php echo $healthScore; ?>%
            </div>
            <div class="health-label" id="health-label">
                <?php
                $healthLabels = [
                    'excellent' => '🟢 ممتاز - النظام يعمل بكفاءة عالية',
                    'good' => '🟡 جيد - النظام يعمل بشكل طبيعي',
                    'warning' => '🟠 تحذير - يحتاج انتباه',
                    'critical' => '🔴 حرج - يحتاج تدخل فوري'
                ];
                echo $healthLabels[$overallStatus] ?? 'غير محدد';
                ?>
            </div>
            <div style="opacity: 0.7;">آخر تحديث: <span id="last-update"><?php echo date('Y-m-d H:i:s'); ?></span></div>
        </div>

        <!-- مراقبات النظام -->
        <div class="monitors-grid">
            <!-- API Status -->
            <div class="monitor-card <?php echo $apiStatus['status'] ?? 'error'; ?>" id="api-card">
                <div class="card-header">
                    <div class="card-title">🔗 حالة API</div>
                    <div class="status-indicator <?php echo $apiStatus['status'] ?? 'error'; ?>"></div>
                </div>
                <div class="card-content">
                    <div class="status-text" id="api-status">
                        <?php echo $apiStatus['message'] ?? 'غير متاح'; ?>
                    </div>
                    <div class="status-details" id="api-details">
                        نقاط النهاية العاملة: <span id="api-working"><?php echo $apiStatus['working'] ?? 0; ?></span> من <span id="api-total"><?php echo $apiStatus['total'] ?? 0; ?></span><br>
                        نسبة النجاح: <span id="api-percentage"><?php echo $apiStatus['percentage'] ?? 0; ?>%</span>
                    </div>
                </div>
            </div>

            <!-- Database Status -->
            <div class="monitor-card <?php echo $dbStatus['status'] ?? 'error'; ?>" id="db-card">
                <div class="card-header">
                    <div class="card-title">🗄️ قاعدة البيانات</div>
                    <div class="status-indicator <?php echo $dbStatus['status'] ?? 'error'; ?>"></div>
                </div>
                <div class="card-content">
                    <div class="status-text" id="db-status">
                        <?php echo $dbStatus['connected'] ?? false ? 'متصلة' : 'غير متصلة'; ?>
                    </div>
                    <div class="status-details" id="db-details">
                        الحجم: <span id="db-size"><?php echo $dbStatus['size'] ?? 'غير معروف'; ?></span><br>
                        الرسالة: <span id="db-message"><?php echo $dbStatus['message'] ?? 'غير متاح'; ?></span>
                    </div>
                </div>
            </div>

            <!-- Admin Account Status -->
            <div class="monitor-card <?php echo $adminStatus['status'] ?? 'error'; ?>" id="admin-card">
                <div class="card-header">
                    <div class="card-title">👤 حساب المدير</div>
                    <div class="status-indicator <?php echo $adminStatus['status'] ?? 'error'; ?>"></div>
                </div>
                <div class="card-content">
                    <div class="status-text" id="admin-status">
                        <?php echo isset($adminStatus['exists']) && $adminStatus['exists'] ? 'موجود' : 'غير موجود'; ?>
                    </div>
                    <div class="status-details" id="admin-details">
                        عدد المديرين: <span id="admin-count"><?php echo $adminStatus['count'] ?? 0; ?></span><br>
                        الجلسات النشطة: <span id="admin-sessions"><?php echo $adminStatus['active_sessions'] ?? 0; ?></span><br>
                        آخر نشاط: <span id="admin-activity"><?php echo $adminStatus['last_activity'] ?? 'غير متاح'; ?></span>
                    </div>
                </div>
            </div>

            <!-- Tables Status -->
            <div class="monitor-card <?php echo $tablesStatus['status'] ?? 'error'; ?>" id="tables-card">
                <div class="card-header">
                    <div class="card-title">📊 الجداول</div>
                    <div class="status-indicator <?php echo $tablesStatus['status'] ?? 'error'; ?>"></div>
                </div>
                <div class="card-content">
                    <div class="status-text" id="tables-status">
                        <?php echo $tablesStatus['message'] ?? 'غير متاح'; ?>
                    </div>
                    <div class="status-details" id="tables-details">
                        الجداول الموجودة: <span id="tables-existing"><?php echo $tablesStatus['existing'] ?? 0; ?></span> من <span id="tables-required"><?php echo $tablesStatus['required'] ?? 0; ?></span><br>
                        <?php if (!empty($tablesStatus['missing'])): ?>
                        الجداول المفقودة: <?php echo implode(', ', $tablesStatus['missing']); ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <div class="refresh-info">
            🔄 يتم تحديث البيانات تلقائياً كل 30 ثانية
        </div>

        <!-- أزرار التحكم -->
        <div class="controls-section">
            <button class="btn" onclick="refreshData()">🔄 تحديث فوري</button>
            <button class="btn secondary" onclick="toggleAutoRefresh()">
                <span id="auto-refresh-text">⏸️ إيقاف التحديث التلقائي</span>
            </button>
            <a href="admin/simple_dashboard.php" class="btn info">🎛️ لوحة الإدارة</a>
            <a href="homepage.php" class="btn success">🏠 الصفحة الرئيسية</a>
            <a href="fix_dashboard_live_issues.php" class="btn warning">🔧 إصلاح المشاكل</a>
        </div>
    </div>

    <script>
        let autoRefreshEnabled = true;
        let refreshInterval;

        // تحديث البيانات
        function refreshData() {
            console.log('🔄 تحديث البيانات...');

            fetch('?ajax=1')
                .then(response => response.json())
                .then(data => {
                    updateUI(data);
                    document.getElementById('last-update').textContent = data.timestamp || new Date().toLocaleString('ar-EG');
                    console.log('✅ تم تحديث البيانات بنجاح');
                })
                .catch(error => {
                    console.error('❌ خطأ في تحديث البيانات:', error);
                });
        }

        // تحديث واجهة المستخدم
        function updateUI(data) {
            // تحديث النتيجة العامة
            const healthScore = document.getElementById('health-score');
            const healthLabel = document.getElementById('health-label');

            if (data.overall) {
                healthScore.textContent = data.overall.score + '%';
                healthScore.className = 'health-score ' + data.overall.status;

                const healthLabels = {
                    'excellent': '🟢 ممتاز - النظام يعمل بكفاءة عالية',
                    'good': '🟡 جيد - النظام يعمل بشكل طبيعي',
                    'warning': '🟠 تحذير - يحتاج انتباه',
                    'critical': '🔴 حرج - يحتاج تدخل فوري'
                };
                healthLabel.textContent = healthLabels[data.overall.status] || 'غير محدد';
            }

            // تحديث بطاقات المراقبة
            updateCard('api', data.api);
            updateCard('db', data.database);
            updateCard('admin', data.admin);
            updateCard('tables', data.tables);
        }

        // تحديث بطاقة مراقبة
        function updateCard(type, data) {
            if (!data) return;

            const card = document.getElementById(type + '-card');
            const indicator = card.querySelector('.status-indicator');

            // تحديث الحالة
            card.className = 'monitor-card ' + (data.status || 'error');
            indicator.className = 'status-indicator ' + (data.status || 'error');

            // تحديث المحتوى حسب النوع
            if (type === 'api') {
                document.getElementById('api-status').textContent = data.message || 'غير متاح';
                document.getElementById('api-working').textContent = data.working || 0;
                document.getElementById('api-total').textContent = data.total || 0;
                document.getElementById('api-percentage').textContent = data.percentage || 0;
            } else if (type === 'db') {
                document.getElementById('db-status').textContent = data.connected ? 'متصلة' : 'غير متصلة';
                document.getElementById('db-size').textContent = data.size || 'غير معروف';
                document.getElementById('db-message').textContent = data.message || 'غير متاح';
            } else if (type === 'admin') {
                document.getElementById('admin-status').textContent = data.exists ? 'موجود' : 'غير موجود';
                document.getElementById('admin-count').textContent = data.count || 0;
                document.getElementById('admin-sessions').textContent = data.active_sessions || 0;
                document.getElementById('admin-activity').textContent = data.last_activity || 'غير متاح';
            } else if (type === 'tables') {
                document.getElementById('tables-status').textContent = data.message || 'غير متاح';
                document.getElementById('tables-existing').textContent = data.existing || 0;
                document.getElementById('tables-required').textContent = data.required || 0;
            }
        }

        // تبديل التحديث التلقائي
        function toggleAutoRefresh() {
            autoRefreshEnabled = !autoRefreshEnabled;
            const button = document.getElementById('auto-refresh-text');

            if (autoRefreshEnabled) {
                button.textContent = '⏸️ إيقاف التحديث التلقائي';
                startAutoRefresh();
            } else {
                button.textContent = '▶️ تشغيل التحديث التلقائي';
                clearInterval(refreshInterval);
            }
        }

        // بدء التحديث التلقائي
        function startAutoRefresh() {
            refreshInterval = setInterval(refreshData, 30000); // كل 30 ثانية
        }

        // بدء التطبيق
        document.addEventListener('DOMContentLoaded', function() {
            console.log('📊 لوحة المراقبة المباشرة جاهزة!');
            startAutoRefresh();

            // تحديث فوري عند التحميل
            setTimeout(refreshData, 1000);
        });

        // تنظيف عند إغلاق الصفحة
        window.addEventListener('beforeunload', function() {
            clearInterval(refreshInterval);
        });
    </script>
</body>
</html>
?>