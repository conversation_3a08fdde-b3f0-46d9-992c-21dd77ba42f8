<?php
/**
 * لوحة المراقبة المباشرة المحدثة - بدون أخطاء
 * Updated Live Dashboard - Error Free
 */

// بدء الجلسة بشكل آمن
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// تسجيل دخول تلقائي للاختبار
if (!isset($_SESSION["user_id"])) {
    $_SESSION["user_id"] = 1;
    $_SESSION["user_name"] = "مدير النظام";
    $_SESSION["user_role"] = "admin";
}

// Function to check API status
function checkAPIStatus() {
    try {
        $testEndpoints = [
            "test.php" => "اختبار API",
            "homepage.php" => "الصفحة الرئيسية"
        ];

        $workingEndpoints = 0;
        $totalEndpoints = count($testEndpoints);

        foreach ($testEndpoints as $endpoint => $name) {
            $url = "http://localhost" . dirname($_SERVER["REQUEST_URI"]) . "/$endpoint";
            $headers = @get_headers($url);
            if ($headers && strpos($headers[0], "200") !== false) {
                $workingEndpoints++;
            }
        }

        $percentage = ($workingEndpoints / $totalEndpoints) * 100;

        return [
            "status" => $percentage >= 80 ? "success" : ($percentage >= 50 ? "warning" : "error"),
            "working" => $workingEndpoints,
            "total" => $totalEndpoints,
            "percentage" => round($percentage, 1),
            "message" => "$workingEndpoints من $totalEndpoints نقطة نهاية تعمل"
        ];
    } catch (Exception $e) {
        return [
            "status" => "error",
            "working" => 0,
            "total" => 0,
            "percentage" => 0,
            "message" => "خطأ في فحص API: " . $e->getMessage()
        ];
    }
}

// Function to check database status
function checkDatabaseStatus() {
    try {
        $pdo = new PDO("mysql:host=localhost;dbname=shahid_platform;charset=utf8mb4", "root", "");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

        // Test basic operations
        $stmt = $pdo->query("SELECT 1");
        $result = $stmt->fetch();

        // Get database size
        $stmt = $pdo->query("
            SELECT ROUND(SUM(data_length + index_length) / 1024 / 1024, 1) AS size_mb
            FROM information_schema.tables
            WHERE table_schema = \"shahid_platform\"
        ");
        $sizeResult = $stmt->fetch();
        $dbSize = $sizeResult["size_mb"] ?? 0;

        return [
            "status" => "success",
            "connected" => true,
            "size" => $dbSize . " MB",
            "message" => "قاعدة البيانات متصلة وتعمل بشكل طبيعي"
        ];
    } catch (Exception $e) {
        return [
            "status" => "error",
            "connected" => false,
            "size" => "غير معروف",
            "message" => "خطأ في الاتصال: " . $e->getMessage()
        ];
    }
}

// Function to check admin accounts (محدثة)
function checkAdminAccounts() {
    try {
        $pdo = new PDO("mysql:host=localhost;dbname=shahid_platform;charset=utf8mb4", "root", "");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

        // فحص وجود جدول users
        $stmt = $pdo->query("SHOW TABLES LIKE \"users\"");
        if ($stmt->rowCount() == 0) {
            return [
                "status" => "warning",
                "exists" => false,
                "count" => 0,
                "active_sessions" => 0,
                "last_activity" => "جدول المستخدمين غير موجود",
                "message" => "جدول users غير موجود"
            ];
        }

        $stmt = $pdo->prepare("SELECT COUNT(*) as count, MAX(created_at) as last_login FROM users WHERE role = \"admin\"");
        $stmt->execute();
        $result = $stmt->fetch();

        $adminCount = $result["count"] ?? 0;
        $lastLogin = $result["last_login"];

        // Check for active sessions (مع فحص وجود الجدول)
        $activeSessions = 0;
        $stmt = $pdo->query("SHOW TABLES LIKE \"user_sessions\"");
        if ($stmt->rowCount() > 0) {
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM user_sessions WHERE user_id IN (SELECT id FROM users WHERE role = \"admin\") AND expires_at > NOW()");
            $stmt->execute();
            $activeSessions = $stmt->fetchColumn();
        }

        return [
            "status" => $adminCount > 0 ? "success" : "warning",
            "exists" => $adminCount > 0,
            "count" => $adminCount,
            "active_sessions" => $activeSessions,
            "last_activity" => $lastLogin ? date("Y-m-d H:i", strtotime($lastLogin)) : "غير محدد",
            "message" => $adminCount > 0 ? "حسابات المديرين متاحة" : "لا توجد حسابات مديرين"
        ];
    } catch (Exception $e) {
        return [
            "status" => "error",
            "exists" => false,
            "count" => 0,
            "active_sessions" => 0,
            "last_activity" => "غير متاح",
            "message" => "خطأ في فحص المديرين: " . $e->getMessage()
        ];
    }
}

// Function to check tables status
function checkTablesStatus() {
    try {
        $pdo = new PDO("mysql:host=localhost;dbname=shahid_platform;charset=utf8mb4", "root", "");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

        $requiredTables = ["users", "movies", "series", "watch_history", "user_sessions"];
        $existingTables = [];

        $stmt = $pdo->query("SHOW TABLES");
        while ($table = $stmt->fetchColumn()) {
            $existingTables[] = $table;
        }

        $missingTables = array_diff($requiredTables, $existingTables);
        $existingCount = count($existingTables);
        $requiredCount = count($requiredTables);

        return [
            "status" => empty($missingTables) ? "success" : (count($missingTables) <= 2 ? "warning" : "error"),
            "existing" => $existingCount,
            "required" => $requiredCount,
            "missing" => $missingTables,
            "message" => empty($missingTables) ? "جميع الجداول موجودة" : "بعض الجداول مفقودة: " . implode(", ", $missingTables)
        ];
    } catch (Exception $e) {
        return [
            "status" => "error",
            "existing" => 0,
            "required" => 0,
            "missing" => [],
            "message" => "خطأ في فحص الجداول: " . $e->getMessage()
        ];
    }
}

// Get all system status
$apiStatus = checkAPIStatus();
$dbStatus = checkDatabaseStatus();
$adminStatus = checkAdminAccounts();
$tablesStatus = checkTablesStatus();

// Calculate overall system health
$healthScore = 0;
if (($apiStatus["status"] ?? "error") === "success") $healthScore += 25;
elseif (($apiStatus["status"] ?? "error") === "warning") $healthScore += 15;

if (($dbStatus["status"] ?? "error") === "success") $healthScore += 25;
if (($adminStatus["status"] ?? "error") === "success") $healthScore += 25;
if (($tablesStatus["status"] ?? "error") === "success") $healthScore += 25;

$overallStatus = $healthScore >= 90 ? "excellent" : ($healthScore >= 70 ? "good" : ($healthScore >= 50 ? "warning" : "critical"));

// Return JSON for AJAX requests
if (isset($_GET["ajax"])) {
    header("Content-Type: application/json");
    echo json_encode([
        "api" => $apiStatus,
        "database" => $dbStatus,
        "admin" => $adminStatus,
        "tables" => $tablesStatus,
        "overall" => [
            "status" => $overallStatus,
            "score" => $healthScore
        ],
        "timestamp" => date("Y-m-d H:i:s")
    ]);
    exit;
}
?>