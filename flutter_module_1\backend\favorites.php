<?php
session_start();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

$user_id = $_SESSION['user_id'];
$favorites = [];
$totalFavorites = 0;

try {
    $pdo = new PDO("mysql:host=localhost;dbname=shahid_platform;charset=utf8mb4", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // جلب المفضلة مع تفاصيل المحتوى
    $stmt = $pdo->prepare("
        SELECT 
            f.id as favorite_id,
            f.content_type,
            f.content_id,
            f.created_at as added_date,
            CASE 
                WHEN f.content_type = 'movie' THEN m.title
                WHEN f.content_type = 'series' THEN s.title
            END as title,
            CASE 
                WHEN f.content_type = 'movie' THEN m.description
                WHEN f.content_type = 'series' THEN s.description
            END as description,
            CASE 
                WHEN f.content_type = 'movie' THEN m.poster
                WHEN f.content_type = 'series' THEN s.poster
            END as poster,
            CASE 
                WHEN f.content_type = 'movie' THEN m.rating
                WHEN f.content_type = 'series' THEN s.rating
            END as rating,
            CASE 
                WHEN f.content_type = 'movie' THEN m.release_year
                WHEN f.content_type = 'series' THEN s.release_year
            END as release_year,
            CASE 
                WHEN f.content_type = 'movie' THEN m.views
                WHEN f.content_type = 'series' THEN s.views
            END as views,
            s.total_seasons,
            s.total_episodes
        FROM favorites f
        LEFT JOIN movies m ON f.content_type = 'movie' AND f.content_id = m.id
        LEFT JOIN series s ON f.content_type = 'series' AND f.content_id = s.id
        WHERE f.user_id = ?
        ORDER BY f.created_at DESC
    ");
    
    $stmt->execute([$user_id]);
    $favorites = $stmt->fetchAll(PDO::FETCH_ASSOC);
    $totalFavorites = count($favorites);
    
} catch (Exception $e) {
    $favorites = [];
    $totalFavorites = 0;
}

// معالجة حذف من المفضلة
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['remove_favorite'])) {
    try {
        $favorite_id = intval($_POST['favorite_id']);
        $deleteStmt = $pdo->prepare("DELETE FROM favorites WHERE id = ? AND user_id = ?");
        $deleteStmt->execute([$favorite_id, $user_id]);
        
        // إعادة توجيه لتحديث الصفحة
        header('Location: favorites.php');
        exit;
    } catch (Exception $e) {
        // تجاهل الأخطاء
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>المفضلة - Shahid Platform</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', 'Cairo', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #2d2d2d 100%);
            color: #fff;
            min-height: 100vh;
        }
        
        .header {
            background: rgba(47, 47, 47, 0.95);
            padding: 1rem 0;
            border-bottom: 2px solid rgba(229, 9, 20, 0.3);
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }
        
        .nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }
        
        .logo {
            color: #E50914;
            font-size: 1.8rem;
            font-weight: bold;
            text-decoration: none;
        }
        
        .nav-links {
            display: flex;
            gap: 2rem;
            align-items: center;
        }
        
        .nav-links a {
            color: #fff;
            text-decoration: none;
            transition: color 0.3s ease;
        }
        
        .nav-links a:hover, .nav-links a.active {
            color: #E50914;
        }
        
        .page-header {
            background: rgba(47, 47, 47, 0.8);
            padding: 3rem 0;
            text-align: center;
        }
        
        .page-title {
            font-size: 2.5rem;
            color: #E50914;
            margin-bottom: 1rem;
        }
        
        .page-subtitle {
            color: #ccc;
            font-size: 1.1rem;
        }
        
        .favorites-section {
            padding: 3rem 0;
        }
        
        .favorites-stats {
            text-align: center;
            margin-bottom: 2rem;
            color: #ccc;
        }
        
        .favorites-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 2rem;
        }
        
        .favorite-card {
            background: rgba(47, 47, 47, 0.8);
            border-radius: 15px;
            overflow: hidden;
            transition: all 0.3s ease;
            border: 2px solid transparent;
            position: relative;
        }
        
        .favorite-card:hover {
            transform: translateY(-5px);
            border-color: rgba(229, 9, 20, 0.5);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.5);
        }
        
        .favorite-poster {
            width: 100%;
            height: 200px;
            background: linear-gradient(45deg, #333, #555);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            color: #666;
            position: relative;
        }
        
        .favorite-poster img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .content-type {
            position: absolute;
            top: 0.5rem;
            left: 0.5rem;
            background: rgba(229, 9, 20, 0.9);
            color: #fff;
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: bold;
        }
        
        .remove-btn {
            position: absolute;
            top: 0.5rem;
            right: 0.5rem;
            background: rgba(244, 67, 54, 0.9);
            color: #fff;
            border: none;
            padding: 0.5rem;
            border-radius: 50%;
            cursor: pointer;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        
        .remove-btn:hover {
            background: #F44336;
            transform: scale(1.1);
        }
        
        .favorite-info {
            padding: 1.5rem;
        }
        
        .favorite-title {
            font-size: 1.2rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
            color: #fff;
        }
        
        .favorite-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            font-size: 0.9rem;
            color: #ccc;
        }
        
        .favorite-rating {
            display: flex;
            align-items: center;
            gap: 0.25rem;
            color: #FFD700;
        }
        
        .favorite-stats {
            display: flex;
            gap: 1rem;
            margin-bottom: 1rem;
            font-size: 0.85rem;
            color: #ccc;
        }
        
        .favorite-description {
            color: #ccc;
            font-size: 0.9rem;
            line-height: 1.4;
            margin-bottom: 1rem;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        
        .favorite-actions {
            display: flex;
            gap: 0.5rem;
        }
        
        .btn-small {
            padding: 0.5rem 1rem;
            font-size: 0.9rem;
            flex: 1;
            text-align: center;
            background: linear-gradient(45deg, #E50914, #B8070F);
            color: white;
            border: none;
            border-radius: 8px;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        
        .btn-small:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(229, 9, 20, 0.3);
        }
        
        .added-date {
            font-size: 0.8rem;
            color: #999;
            margin-top: 0.5rem;
            text-align: center;
        }
        
        .empty-favorites {
            text-align: center;
            padding: 4rem 2rem;
            color: #ccc;
        }
        
        .empty-favorites h3 {
            color: #E50914;
            margin-bottom: 1rem;
            font-size: 2rem;
        }
        
        .empty-favorites p {
            font-size: 1.1rem;
            margin-bottom: 2rem;
        }
        
        .quick-links {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 0 1rem;
            }
            
            .nav {
                flex-direction: column;
                gap: 1rem;
            }
            
            .favorites-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }
            
            .page-title {
                font-size: 2rem;
            }
            
            .quick-links {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="container">
            <nav class="nav">
                <a href="index.php" class="logo">🎬 شاهد</a>
                <div class="nav-links">
                    <a href="index.php">الرئيسية</a>
                    <a href="movies.php">الأفلام</a>
                    <a href="series.php">المسلسلات</a>
                    <a href="search.php">البحث</a>
                    <a href="profile.php">الملف الشخصي</a>
                    <a href="favorites.php" class="active">المفضلة</a>
                    <a href="logout.php">تسجيل الخروج</a>
                </div>
            </nav>
        </div>
    </header>
    
    <section class="page-header">
        <div class="container">
            <h1 class="page-title">❤️ المفضلة</h1>
            <p class="page-subtitle">الأفلام والمسلسلات المفضلة لديك</p>
        </div>
    </section>
    
    <section class="favorites-section">
        <div class="container">
            <div class="favorites-stats">
                <p>لديك <?php echo $totalFavorites; ?> عنصر في المفضلة</p>
            </div>
            
            <?php if (empty($favorites)): ?>
                <div class="empty-favorites">
                    <h3>💔 لا توجد مفضلة</h3>
                    <p>لم تقم بإضافة أي أفلام أو مسلسلات إلى المفضلة بعد</p>
                    
                    <div class="quick-links">
                        <a href="movies.php" class="btn-small">🎬 تصفح الأفلام</a>
                        <a href="series.php" class="btn-small">📺 تصفح المسلسلات</a>
                        <a href="search.php" class="btn-small">🔍 البحث</a>
                        <a href="index.php" class="btn-small">🏠 الصفحة الرئيسية</a>
                    </div>
                </div>
            <?php else: ?>
                <div class="favorites-grid">
                    <?php foreach ($favorites as $item): ?>
                        <div class="favorite-card">
                            <div class="favorite-poster">
                                <div class="content-type">
                                    <?php echo $item['content_type'] === 'movie' ? '🎬 فيلم' : '📺 مسلسل'; ?>
                                </div>
                                
                                <form method="POST" style="display: inline;">
                                    <input type="hidden" name="favorite_id" value="<?php echo $item['favorite_id']; ?>">
                                    <button type="submit" name="remove_favorite" class="remove-btn" 
                                            onclick="return confirm('هل تريد حذف هذا العنصر من المفضلة؟')">
                                        ❌
                                    </button>
                                </form>
                                
                                <?php if (!empty($item['poster'])): ?>
                                    <img src="<?php echo htmlspecialchars($item['poster']); ?>" 
                                         alt="<?php echo htmlspecialchars($item['title']); ?>">
                                <?php else: ?>
                                    <?php echo $item['content_type'] === 'movie' ? '🎬' : '📺'; ?>
                                <?php endif; ?>
                            </div>
                            
                            <div class="favorite-info">
                                <h3 class="favorite-title"><?php echo htmlspecialchars($item['title']); ?></h3>
                                
                                <div class="favorite-meta">
                                    <span><?php echo $item['release_year'] ?? 'غير محدد'; ?></span>
                                    <div class="favorite-rating">
                                        <span>⭐</span>
                                        <span><?php echo number_format($item['rating'], 1); ?></span>
                                    </div>
                                </div>
                                
                                <?php if ($item['content_type'] === 'series'): ?>
                                    <div class="favorite-stats">
                                        <span>🎬 <?php echo $item['total_seasons'] ?? 1; ?> موسم</span>
                                        <span>📺 <?php echo $item['total_episodes'] ?? 0; ?> حلقة</span>
                                    </div>
                                <?php endif; ?>
                                
                                <?php if (!empty($item['description'])): ?>
                                    <p class="favorite-description">
                                        <?php echo htmlspecialchars($item['description']); ?>
                                    </p>
                                <?php endif; ?>
                                
                                <div class="favorite-actions">
                                    <a href="watch.php?type=<?php echo $item['content_type']; ?>&id=<?php echo $item['content_id']; ?>" 
                                       class="btn-small">
                                        ▶️ مشاهدة
                                    </a>
                                    <a href="<?php echo $item['content_type']; ?>_details.php?id=<?php echo $item['content_id']; ?>" 
                                       class="btn-small">
                                        📋 التفاصيل
                                    </a>
                                </div>
                                
                                <div class="added-date">
                                    أُضيف في: <?php echo date('Y-m-d', strtotime($item['added_date'])); ?>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </section>
    
    <script>
        // تأثيرات تفاعلية للبطاقات
        document.querySelectorAll('.favorite-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-5px) scale(1.02)';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });
        
        // تأكيد الحذف
        document.querySelectorAll('.remove-btn').forEach(btn => {
            btn.addEventListener('click', function(e) {
                if (!confirm('هل أنت متأكد من حذف هذا العنصر من المفضلة؟')) {
                    e.preventDefault();
                }
            });
        });
    </script>
</body>
</html>
