<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 فهرس أدوات المراقبة</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', 'Cairo', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #2d2d2d 100%);
            color: #fff;
            min-height: 100vh;
            padding: 2rem;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(47, 47, 47, 0.95);
            border-radius: 20px;
            padding: 3rem;
            border: 2px solid rgba(229, 9, 20, 0.3);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
        }
        
        .header {
            text-align: center;
            margin-bottom: 3rem;
            padding-bottom: 2rem;
            border-bottom: 3px solid #E50914;
        }
        
        .header h1 {
            color: #E50914;
            font-size: 3rem;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }
        
        .header p {
            color: #ccc;
            font-size: 1.2rem;
        }
        
        .tools-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }
        
        .tool-card {
            background: linear-gradient(145deg, rgba(0, 0, 0, 0.4), rgba(47, 47, 47, 0.6));
            border-radius: 15px;
            padding: 2rem;
            border: 2px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
            text-decoration: none;
            color: inherit;
            display: block;
        }
        
        .tool-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(229, 9, 20, 0.2);
            border-color: rgba(229, 9, 20, 0.5);
            color: inherit;
            text-decoration: none;
        }
        
        .tool-header {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
        }
        
        .tool-icon {
            font-size: 2.5rem;
            margin-left: 1rem;
        }
        
        .tool-title {
            font-size: 1.5rem;
            font-weight: bold;
            color: #E50914;
        }
        
        .tool-description {
            color: #ccc;
            line-height: 1.6;
            margin-bottom: 1rem;
        }
        
        .tool-features {
            list-style: none;
            padding: 0;
        }
        
        .tool-features li {
            color: #aaa;
            margin-bottom: 0.5rem;
            padding-right: 1rem;
            position: relative;
        }
        
        .tool-features li::before {
            content: '✓';
            color: #4CAF50;
            font-weight: bold;
            position: absolute;
            right: 0;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 0.5rem;
        }
        
        .status-online {
            background: #4CAF50;
            box-shadow: 0 0 10px rgba(76, 175, 80, 0.5);
        }
        
        .status-new {
            background: #2196F3;
            box-shadow: 0 0 10px rgba(33, 150, 243, 0.5);
        }
        
        .status-updated {
            background: #FF9800;
            box-shadow: 0 0 10px rgba(255, 152, 0, 0.5);
        }
        
        .footer {
            text-align: center;
            padding-top: 2rem;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            color: #666;
        }
        
        .quick-links {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin-bottom: 2rem;
            flex-wrap: wrap;
        }
        
        .quick-link {
            background: rgba(229, 9, 20, 0.1);
            border: 1px solid rgba(229, 9, 20, 0.3);
            color: #E50914;
            padding: 0.5rem 1rem;
            border-radius: 25px;
            text-decoration: none;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }
        
        .quick-link:hover {
            background: rgba(229, 9, 20, 0.2);
            color: #fff;
            text-decoration: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 فهرس أدوات المراقبة</h1>
            <p>جميع أدوات مراقبة منصة شاهد في مكان واحد</p>
        </div>

        <div class="quick-links">
            <a href="../homepage.php" class="quick-link">🏠 الرئيسية</a>
            <a href="admin/dashboard.php" class="quick-link">🎛️ لوحة الإدارة</a>
            <a href="api/test.php" class="quick-link">🔗 اختبار API</a>
            <a href="http://localhost/phpmyadmin/" target="_blank" class="quick-link">📊 phpMyAdmin</a>
        </div>

        <div class="tools-grid">
            <!-- Dashboard Live - الأحدث والأفضل -->
            <a href="dashboard_live.php" class="tool-card">
                <div class="tool-header">
                    <div class="tool-icon">🚀</div>
                    <div>
                        <div class="tool-title">
                            <span class="status-indicator status-new"></span>
                            لوحة المراقبة المباشرة المحسنة
                        </div>
                    </div>
                </div>
                <div class="tool-description">
                    أحدث وأفضل أداة مراقبة مع تصميم متطور وميزات متقدمة
                </div>
                <ul class="tool-features">
                    <li>مراقبة شاملة لجميع المكونات</li>
                    <li>تحديث تلقائي كل 5 ثوانٍ</li>
                    <li>تصميم متجاوب وحديث</li>
                    <li>مؤشر الصحة العامة</li>
                    <li>تفاصيل متقدمة لكل مكون</li>
                </ul>
            </a>

            <!-- Live Monitoring - النسخة الأساسية -->
            <a href="live_monitoring.php" class="tool-card">
                <div class="tool-header">
                    <div class="tool-icon">📊</div>
                    <div>
                        <div class="tool-title">
                            <span class="status-indicator status-new"></span>
                            المراقبة المباشرة الأساسية
                        </div>
                    </div>
                </div>
                <div class="tool-description">
                    نسخة مبسطة من المراقبة المباشرة مع الميزات الأساسية
                </div>
                <ul class="tool-features">
                    <li>مراقبة API وقاعدة البيانات</li>
                    <li>فحص الجداول وحساب المدير</li>
                    <li>تحديث تلقائي</li>
                    <li>واجهة بسيطة وسريعة</li>
                </ul>
            </a>

            <!-- System Status - المحدث -->
            <a href="system_status.php" class="tool-card">
                <div class="tool-header">
                    <div class="tool-icon">⚙️</div>
                    <div>
                        <div class="tool-title">
                            <span class="status-indicator status-updated"></span>
                            حالة النظام المحدثة
                        </div>
                    </div>
                </div>
                <div class="tool-description">
                    أداة فحص حالة النظام التقليدية مع تحسينات جديدة
                </div>
                <ul class="tool-features">
                    <li>فحص شامل للنظام</li>
                    <li>معلومات تفصيلية</li>
                    <li>روابط الإصلاح السريع</li>
                    <li>تحديث كل 30 ثانية</li>
                </ul>
            </a>

            <!-- System Health Checker -->
            <a href="system_health_checker.php" class="tool-card">
                <div class="tool-header">
                    <div class="tool-icon">🏥</div>
                    <div>
                        <div class="tool-title">
                            <span class="status-indicator status-online"></span>
                            فاحص صحة النظام
                        </div>
                    </div>
                </div>
                <div class="tool-description">
                    أداة متخصصة لفحص صحة النظام بالتفصيل
                </div>
                <ul class="tool-features">
                    <li>فحص عميق للمكونات</li>
                    <li>تقارير مفصلة</li>
                    <li>اكتشاف المشاكل المحتملة</li>
                    <li>اقتراحات الإصلاح</li>
                </ul>
            </a>

            <!-- Admin System Report -->
            <a href="admin/system_report.php" class="tool-card">
                <div class="tool-header">
                    <div class="tool-icon">📋</div>
                    <div>
                        <div class="tool-title">
                            <span class="status-indicator status-online"></span>
                            تقرير النظام الإداري
                        </div>
                    </div>
                </div>
                <div class="tool-description">
                    تقرير شامل لحالة النظام من لوحة الإدارة
                </div>
                <ul class="tool-features">
                    <li>تقرير شامل ومفصل</li>
                    <li>إحصائيات متقدمة</li>
                    <li>تحليل الأداء</li>
                    <li>بيانات التحليلات</li>
                </ul>
            </a>

            <!-- API Dashboard -->
            <a href="api/dashboard.php" class="tool-card">
                <div class="tool-header">
                    <div class="tool-icon">🔗</div>
                    <div>
                        <div class="tool-title">
                            <span class="status-indicator status-online"></span>
                            لوحة API
                        </div>
                    </div>
                </div>
                <div class="tool-description">
                    مراقبة وإدارة جميع نقاط API في النظام
                </div>
                <ul class="tool-features">
                    <li>قائمة جميع نقاط API</li>
                    <li>اختبار مباشر للنقاط</li>
                    <li>إحصائيات الاستخدام</li>
                    <li>وثائق تفاعلية</li>
                </ul>
            </a>
        </div>

        <div class="footer">
            <p>📅 آخر تحديث: <?php echo date('Y-m-d H:i:s'); ?></p>
            <p>🔧 جميع الأدوات جاهزة للاستخدام</p>
            <p style="margin-top: 1rem; font-size: 0.9rem;">
                💡 <strong>نصيحة:</strong> ابدأ بـ "لوحة المراقبة المباشرة المحسنة" للحصول على أفضل تجربة
            </p>
        </div>
    </div>

    <script>
        // Add some interactivity
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.tool-card');
            
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-8px) scale(1.02)';
                });
                
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(-5px)';
                });
            });
            
            console.log('🔍 فهرس أدوات المراقبة تم تحميله بنجاح');
        });
    </script>
</body>
</html>
