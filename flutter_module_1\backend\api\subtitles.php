<?php
/**
 * Shahid Subtitles API
 * Secure subtitle delivery with token validation
 */

require_once '../core/Database.php';
require_once '../core/Security.php';

class SubtitlesAPI {
    private $db;
    private $security;
    
    public function __construct() {
        $this->db = Database::getInstance()->getConnection();
        $this->security = new Security();
    }
    
    public function getSubtitles() {
        try {
            // Get parameters
            $subtitleId = intval($_GET['id'] ?? 0);
            $token = $_GET['token'] ?? '';
            
            // Validate token
            if (!$this->validateToken($token)) {
                $this->sendError(403, 'Invalid or expired token');
                return;
            }
            
            // Get subtitle info
            $subtitle = $this->getSubtitleInfo($subtitleId);
            if (!$subtitle) {
                $this->sendError(404, 'Subtitle not found');
                return;
            }
            
            // Get subtitle file path
            $subtitlePath = $this->getSubtitlePath($subtitle);
            if (!$subtitlePath || !file_exists($subtitlePath)) {
                $this->sendError(404, 'Subtitle file not found');
                return;
            }
            
            // Serve the subtitle file
            $this->serveSubtitle($subtitlePath, $subtitle);
            
        } catch (Exception $e) {
            error_log('Subtitles API Error: ' . $e->getMessage());
            $this->sendError(500, 'Internal server error');
        }
    }
    
    private function validateToken($token) {
        try {
            $payload = $this->security->validateJWT($token);
            
            if (!$payload || 
                !isset($payload['user_id']) ||
                !isset($payload['exp'])) {
                return false;
            }
            
            // Check if token is expired
            if ($payload['exp'] < time()) {
                return false;
            }
            
            return true;
            
        } catch (Exception $e) {
            return false;
        }
    }
    
    private function getSubtitleInfo($subtitleId) {
        $sql = "SELECT * FROM subtitles WHERE id = :id";
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':id', $subtitleId);
        $stmt->execute();
        
        return $stmt->fetch();
    }
    
    private function getSubtitlePath($subtitle) {
        $baseDir = '../storage/subtitles/';
        
        // Determine content type directory
        $contentDir = $subtitle['content_type'] === 'movie' ? 'movies/' : 'episodes/';
        
        // Build file path
        $fileName = $subtitle['language'] . '.vtt';
        $subtitlePath = $baseDir . $contentDir . $subtitle['content_id'] . '/' . $fileName;
        
        // Fallback to SRT format if VTT not found
        if (!file_exists($subtitlePath)) {
            $fileName = $subtitle['language'] . '.srt';
            $subtitlePath = $baseDir . $contentDir . $subtitle['content_id'] . '/' . $fileName;
        }
        
        return file_exists($subtitlePath) ? $subtitlePath : null;
    }
    
    private function serveSubtitle($subtitlePath, $subtitle) {
        $fileExtension = pathinfo($subtitlePath, PATHINFO_EXTENSION);
        
        // Set appropriate content type
        switch (strtolower($fileExtension)) {
            case 'vtt':
                header('Content-Type: text/vtt; charset=utf-8');
                break;
            case 'srt':
                header('Content-Type: text/plain; charset=utf-8');
                // Convert SRT to VTT on the fly
                $this->serveSrtAsVtt($subtitlePath);
                return;
            default:
                header('Content-Type: text/plain; charset=utf-8');
                break;
        }
        
        // Security headers
        header('X-Content-Type-Options: nosniff');
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Methods: GET');
        header('Access-Control-Allow-Headers: Content-Type');
        
        // Cache headers
        header('Cache-Control: public, max-age=3600');
        header('Expires: ' . gmdate('D, d M Y H:i:s', time() + 3600) . ' GMT');
        
        // Content disposition
        $filename = $subtitle['language_name'] . '.' . $fileExtension;
        header('Content-Disposition: inline; filename="' . $filename . '"');
        
        // Output file
        readfile($subtitlePath);
    }
    
    private function serveSrtAsVtt($srtPath) {
        header('Content-Type: text/vtt; charset=utf-8');
        
        // Output VTT header
        echo "WEBVTT\n\n";
        
        $srtContent = file_get_contents($srtPath);
        
        // Convert SRT format to VTT format
        $vttContent = $this->convertSrtToVtt($srtContent);
        
        echo $vttContent;
    }
    
    private function convertSrtToVtt($srtContent) {
        // Split into subtitle blocks
        $blocks = preg_split('/\n\s*\n/', trim($srtContent));
        $vttBlocks = [];
        
        foreach ($blocks as $block) {
            $lines = explode("\n", trim($block));
            
            if (count($lines) < 3) {
                continue; // Skip invalid blocks
            }
            
            // Skip the sequence number (first line)
            $timeLine = $lines[1];
            $textLines = array_slice($lines, 2);
            
            // Convert time format from SRT to VTT
            // SRT: 00:00:20,000 --> 00:00:24,400
            // VTT: 00:00:20.000 --> 00:00:24.400
            $timeLine = str_replace(',', '.', $timeLine);
            
            // Clean up text (remove HTML tags if any)
            $text = implode("\n", $textLines);
            $text = $this->cleanSubtitleText($text);
            
            $vttBlocks[] = $timeLine . "\n" . $text;
        }
        
        return implode("\n\n", $vttBlocks);
    }
    
    private function cleanSubtitleText($text) {
        // Remove common SRT formatting tags
        $text = preg_replace('/<[^>]*>/', '', $text);
        
        // Clean up common formatting
        $text = str_replace(['{\\i1}', '{\\i0}', '{\\b1}', '{\\b0}'], '', $text);
        
        // Convert HTML entities
        $text = html_entity_decode($text, ENT_QUOTES, 'UTF-8');
        
        // Trim whitespace
        $text = trim($text);
        
        return $text;
    }
    
    public function listSubtitles() {
        try {
            $contentId = intval($_GET['content_id'] ?? 0);
            $contentType = $_GET['content_type'] ?? 'movie';
            $token = $_GET['token'] ?? '';
            
            // Validate token
            if (!$this->validateToken($token)) {
                $this->sendError(403, 'Invalid or expired token');
                return;
            }
            
            // Get available subtitles
            $sql = "SELECT id, language, language_name, is_default 
                    FROM subtitles 
                    WHERE content_id = :content_id AND content_type = :content_type
                    ORDER BY is_default DESC, language_name ASC";
            
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':content_id', $contentId);
            $stmt->bindParam(':content_type', $contentType);
            $stmt->execute();
            
            $subtitles = $stmt->fetchAll();
            
            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'subtitles' => $subtitles
            ]);
            
        } catch (Exception $e) {
            error_log('List Subtitles Error: ' . $e->getMessage());
            $this->sendError(500, 'Internal server error');
        }
    }
    
    private function sendError($code, $message) {
        http_response_code($code);
        header('Content-Type: application/json');
        echo json_encode(['error' => $message]);
        exit;
    }
}

// Handle the request
$requestMethod = $_SERVER['REQUEST_METHOD'];
$requestUri = $_SERVER['REQUEST_URI'];

if ($requestMethod === 'GET') {
    $api = new SubtitlesAPI();
    
    // Check if this is a list request
    if (strpos($requestUri, '/list') !== false) {
        $api->listSubtitles();
    } else {
        $api->getSubtitles();
    }
} else {
    http_response_code(405);
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Method not allowed']);
}
?>
