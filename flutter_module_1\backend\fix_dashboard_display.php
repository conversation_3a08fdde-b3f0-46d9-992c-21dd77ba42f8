<?php
/**
 * إصلاح مشكلة عرض HTML بدلاً من تنفيذ PHP
 * Fix HTML Display Issue in Dashboard
 */

try {
    echo "<h1>🔧 إصلاح مشكلة عرض لوحة الإدارة</h1>";
    
    $fixedIssues = [];
    $errors = [];
    
    // 1. فحص إعدادات PHP
    echo "<h2>🔍 فحص إعدادات PHP...</h2>";
    
    $phpInfo = [
        'PHP Version' => phpversion(),
        'Short Open Tags' => ini_get('short_open_tag') ? 'Enabled' : 'Disabled',
        'Display Errors' => ini_get('display_errors') ? 'Enabled' : 'Disabled',
        'Error Reporting' => error_reporting(),
        'Max Execution Time' => ini_get('max_execution_time') . ' seconds'
    ];
    
    echo "<div style='background: #e3f2fd; border: 1px solid #2196f3; color: #0d47a1; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>📋 معلومات PHP:</h3>";
    echo "<ul>";
    foreach ($phpInfo as $key => $value) {
        echo "<li><strong>$key:</strong> $value</li>";
    }
    echo "</ul>";
    echo "</div>";
    
    // 2. فحص ملف dashboard.php الأصلي
    echo "<h2>📁 فحص ملف dashboard.php...</h2>";
    
    $dashboardFile = 'admin/dashboard.php';
    if (file_exists($dashboardFile)) {
        $fileSize = filesize($dashboardFile);
        $fileContent = file_get_contents($dashboardFile);
        
        // فحص بداية الملف
        $firstLine = substr($fileContent, 0, 100);
        $hasPhpTag = strpos($firstLine, '<?php') !== false;
        
        echo "<div style='background: #fff3e0; border: 1px solid #ff9800; color: #e65100; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h3>📄 معلومات الملف:</h3>";
        echo "<ul>";
        echo "<li><strong>الحجم:</strong> " . number_format($fileSize) . " بايت</li>";
        echo "<li><strong>يبدأ بـ PHP Tag:</strong> " . ($hasPhpTag ? 'نعم ✅' : 'لا ❌') . "</li>";
        echo "<li><strong>أول 100 حرف:</strong> <code>" . htmlspecialchars(substr($firstLine, 0, 100)) . "</code></li>";
        echo "</ul>";
        echo "</div>";
        
        if (!$hasPhpTag) {
            $errors[] = "الملف لا يبدأ بـ <?php";
        }
        
        // فحص أخطاء PHP في الملف
        $syntaxCheck = shell_exec("php -l $dashboardFile 2>&1");
        if (strpos($syntaxCheck, 'No syntax errors') === false) {
            $errors[] = "خطأ في بناء الجملة: " . $syntaxCheck;
        } else {
            $fixedIssues[] = "بناء الجملة صحيح في الملف";
        }
        
    } else {
        $errors[] = "ملف dashboard.php غير موجود";
    }
    
    // 3. إنشاء نسخة احتياطية وإصلاح الملف
    echo "<h2>🔄 إصلاح الملف...</h2>";
    
    if (file_exists($dashboardFile)) {
        // إنشاء نسخة احتياطية
        $backupFile = $dashboardFile . '.backup.' . date('Y-m-d-H-i-s');
        if (copy($dashboardFile, $backupFile)) {
            $fixedIssues[] = "تم إنشاء نسخة احتياطية: $backupFile";
        }
        
        // نسخ الملف الذي يعمل
        if (file_exists('admin/working_dashboard.php')) {
            if (copy('admin/working_dashboard.php', $dashboardFile)) {
                $fixedIssues[] = "تم استبدال dashboard.php بالنسخة التي تعمل";
            } else {
                $errors[] = "فشل في استبدال الملف";
            }
        }
    }
    
    // 4. فحص إعدادات Apache
    echo "<h2>🌐 فحص إعدادات Apache...</h2>";
    
    $htaccessFile = '.htaccess';
    if (file_exists($htaccessFile)) {
        $htaccessContent = file_get_contents($htaccessFile);
        
        // فحص إعدادات PHP في .htaccess
        $phpSettings = [
            'AddType application/x-httpd-php .php',
            'php_flag short_open_tag On',
            'php_flag display_errors On'
        ];
        
        $missingSettings = [];
        foreach ($phpSettings as $setting) {
            if (strpos($htaccessContent, $setting) === false) {
                $missingSettings[] = $setting;
            }
        }
        
        if (!empty($missingSettings)) {
            // إضافة الإعدادات المفقودة
            $newHtaccess = $htaccessContent . "\n\n# PHP Settings for Dashboard\n";
            foreach ($missingSettings as $setting) {
                $newHtaccess .= $setting . "\n";
            }
            
            if (file_put_contents($htaccessFile, $newHtaccess)) {
                $fixedIssues[] = "تم تحديث إعدادات PHP في .htaccess";
            }
        } else {
            $fixedIssues[] = "إعدادات PHP في .htaccess صحيحة";
        }
    }
    
    // 5. إنشاء ملف اختبار PHP
    $testFile = 'admin/test_php.php';
    $testContent = '<?php
echo "<!DOCTYPE html>";
echo "<html><head><title>PHP Test</title></head><body>";
echo "<h1>✅ PHP يعمل بشكل صحيح!</h1>";
echo "<p>الوقت الحالي: " . date("Y-m-d H:i:s") . "</p>";
echo "<p>إصدار PHP: " . phpversion() . "</p>";
echo "</body></html>";
?>';
    
    if (file_put_contents($testFile, $testContent)) {
        $fixedIssues[] = "تم إنشاء ملف اختبار PHP";
    }
    
    // 6. فحص صلاحيات الملفات
    echo "<h2>🔐 فحص صلاحيات الملفات...</h2>";
    
    $files = ['admin/dashboard.php', 'admin/working_dashboard.php', $testFile];
    foreach ($files as $file) {
        if (file_exists($file)) {
            $perms = fileperms($file);
            $readable = is_readable($file);
            $executable = is_executable($file);
            
            echo "<p><strong>$file:</strong> ";
            echo "صلاحيات: " . substr(sprintf('%o', $perms), -4) . " | ";
            echo "قابل للقراءة: " . ($readable ? 'نعم ✅' : 'لا ❌') . " | ";
            echo "قابل للتنفيذ: " . ($executable ? 'نعم ✅' : 'لا ❌');
            echo "</p>";
            
            if (!$readable) {
                chmod($file, 0644);
                $fixedIssues[] = "تم إصلاح صلاحيات $file";
            }
        }
    }
    
    echo "<br><h2>🎉 تم إصلاح مشكلة عرض لوحة الإدارة!</h2>";
    
    if (!empty($fixedIssues)) {
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h3>✅ المشاكل التي تم إصلاحها:</h3>";
        echo "<ul>";
        foreach ($fixedIssues as $issue) {
            echo "<li>$issue</li>";
        }
        echo "</ul>";
        echo "</div>";
    }
    
    if (!empty($errors)) {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h3>⚠️ مشاكل تحتاج انتباه:</h3>";
        echo "<ul>";
        foreach ($errors as $error) {
            echo "<li>$error</li>";
        }
        echo "</ul>";
        echo "</div>";
    }
    
    echo "<div style='background: #cce5ff; border: 1px solid #99ccff; color: #004085; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>📋 الخطوات التالية:</h3>";
    echo "<ol>";
    echo "<li>أعد تشغيل Apache في XAMPP</li>";
    echo "<li>امسح كاش المتصفح (Ctrl+F5)</li>";
    echo "<li>جرب لوحة الإدارة مرة أخرى</li>";
    echo "<li>إذا استمرت المشكلة، استخدم لوحة الإدارة البديلة</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<div style='text-align: center; margin: 2rem 0;'>";
    echo "<a href='admin/test_php.php' style='background: #28a745; color: white; padding: 1rem 2rem; text-decoration: none; border-radius: 8px; font-weight: bold; margin: 0.5rem;'>🧪 اختبار PHP</a>";
    echo "<a href='admin/dashboard.php' style='background: #E50914; color: white; padding: 1rem 2rem; text-decoration: none; border-radius: 8px; font-weight: bold; margin: 0.5rem;'>🎛️ لوحة الإدارة الأصلية</a>";
    echo "<a href='admin/working_dashboard.php' style='background: #007bff; color: white; padding: 1rem 2rem; text-decoration: none; border-radius: 8px; font-weight: bold; margin: 0.5rem;'>🎛️ لوحة الإدارة البديلة</a>";
    echo "<a href='admin/simple_dashboard.php' style='background: #6f42c1; color: white; padding: 1rem 2rem; text-decoration: none; border-radius: 8px; font-weight: bold; margin: 0.5rem;'>🎛️ لوحة الإدارة البسيطة</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px;'>";
    echo "<h3>❌ خطأ في الإصلاح:</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح مشكلة عرض لوحة الإدارة</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #2d2d2d 100%);
            color: #fff;
            min-height: 100vh;
            padding: 2rem;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: rgba(47, 47, 47, 0.95);
            border-radius: 20px;
            padding: 3rem;
            border: 2px solid rgba(229, 9, 20, 0.3);
        }
        h1, h2, h3 { color: #E50914; }
        ul { margin: 1rem 0; padding-right: 2rem; }
        li { margin: 0.5rem 0; }
        ol { margin: 1rem 0; padding-right: 2rem; }
        code {
            background: rgba(0, 0, 0, 0.3);
            padding: 0.2rem 0.5rem;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
        }
        p { margin: 0.5rem 0; }
    </style>
</head>
<body>
    <div class="container">
        <!-- المحتوى يتم عرضه من PHP أعلاه -->
    </div>
</body>
</html>
