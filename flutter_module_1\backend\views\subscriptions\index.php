<?php include 'views/layout/header.php'; ?>

<!-- Hero Section -->
<div class="subscription-hero">
    <div class="container">
        <div class="row align-items-center min-vh-75">
            <div class="col-lg-6">
                <h1 class="hero-title">اختر الخطة المناسبة لك</h1>
                <p class="hero-subtitle">
                    استمتع بمشاهدة آلاف الأفلام والمسلسلات بجودة عالية مع خطط اشتراك مرنة تناسب احتياجاتك
                </p>
                <div class="hero-features">
                    <div class="feature-item">
                        <i class="fas fa-check-circle"></i>
                        <span>مشاهدة بدون إعلانات</span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-check-circle"></i>
                        <span>جودة عالية حتى 4K</span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-check-circle"></i>
                        <span>تحميل للمشاهدة بدون إنترنت</span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-check-circle"></i>
                        <span>مشاهدة على عدة أجهزة</span>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="hero-image">
                    <img src="/assets/images/subscription-hero.png" alt="Shahid Subscription" class="img-fluid">
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Current Subscription -->
<?php if ($current_subscription): ?>
<div class="current-subscription-section">
    <div class="container">
        <div class="current-subscription-card">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h4>اشتراكك الحالي</h4>
                    <div class="subscription-info">
                        <span class="plan-name"><?= htmlspecialchars($current_subscription['plan_name']) ?></span>
                        <span class="plan-price">$<?= number_format($current_subscription['price'], 2) ?>/شهر</span>
                    </div>
                    <div class="subscription-status">
                        <span class="status-badge status-<?= $current_subscription['status'] ?>">
                            <?= $current_subscription['status'] === 'active' ? 'نشط' : 'غير نشط' ?>
                        </span>
                        <span class="expires-at">
                            ينتهي في: <?= date('d/m/Y', strtotime($current_subscription['expires_at'])) ?>
                        </span>
                    </div>
                </div>
                <div class="col-md-4 text-md-end">
                    <div class="subscription-actions">
                        <button class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#upgradeModal">
                            <i class="fas fa-arrow-up me-2"></i>ترقية الخطة
                        </button>
                        <button class="btn btn-outline-danger" data-bs-toggle="modal" data-bs-target="#cancelModal">
                            <i class="fas fa-times me-2"></i>إلغاء الاشتراك
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Subscription Plans -->
<div class="subscription-plans-section">
    <div class="container">
        <div class="section-header text-center mb-5">
            <h2 class="section-title">خطط الاشتراك</h2>
            <p class="section-subtitle">اختر الخطة التي تناسب احتياجاتك ومشاهدتك</p>
        </div>
        
        <div class="row justify-content-center">
            <?php foreach ($plans as $index => $plan): ?>
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="plan-card <?= $index === 1 ? 'featured' : '' ?>">
                        <?php if ($index === 1): ?>
                            <div class="plan-badge">الأكثر شعبية</div>
                        <?php endif; ?>
                        
                        <div class="plan-header">
                            <h3 class="plan-name"><?= htmlspecialchars($plan['name']) ?></h3>
                            <div class="plan-price">
                                <span class="currency">$</span>
                                <span class="amount"><?= number_format($plan['price'], 0) ?></span>
                                <span class="period">/شهر</span>
                            </div>
                            <p class="plan-description"><?= htmlspecialchars($plan['description']) ?></p>
                        </div>
                        
                        <div class="plan-features">
                            <ul class="features-list">
                                <?php 
                                $features = json_decode($plan['features'], true) ?: [];
                                foreach ($features as $feature): 
                                ?>
                                    <li>
                                        <i class="fas fa-check"></i>
                                        <?= htmlspecialchars($feature) ?>
                                    </li>
                                <?php endforeach; ?>
                                
                                <li>
                                    <i class="fas fa-check"></i>
                                    مشاهدة على <?= $plan['max_devices'] ?> <?= $plan['max_devices'] > 1 ? 'أجهزة' : 'جهاز' ?>
                                </li>
                                <li>
                                    <i class="fas fa-check"></i>
                                    جودة حتى <?= $plan['max_quality'] ?>
                                </li>
                                <?php if ($plan['download_enabled']): ?>
                                    <li>
                                        <i class="fas fa-check"></i>
                                        تحميل للمشاهدة بدون إنترنت
                                    </li>
                                <?php endif; ?>
                                <?php if ($plan['ads_free']): ?>
                                    <li>
                                        <i class="fas fa-check"></i>
                                        مشاهدة بدون إعلانات
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </div>
                        
                        <div class="plan-footer">
                            <?php if ($current_subscription && $current_subscription['subscription_id'] == $plan['id']): ?>
                                <button class="btn btn-success btn-lg w-100" disabled>
                                    <i class="fas fa-check me-2"></i>خطتك الحالية
                                </button>
                            <?php elseif ($current_subscription): ?>
                                <button class="btn btn-primary btn-lg w-100 upgrade-btn" 
                                        data-plan-id="<?= $plan['id'] ?>"
                                        data-plan-name="<?= htmlspecialchars($plan['name']) ?>"
                                        data-plan-price="<?= $plan['price'] ?>">
                                    <i class="fas fa-arrow-up me-2"></i>ترقية إلى هذه الخطة
                                </button>
                            <?php else: ?>
                                <button class="btn btn-primary btn-lg w-100 subscribe-btn" 
                                        data-plan-id="<?= $plan['id'] ?>"
                                        data-plan-name="<?= htmlspecialchars($plan['name']) ?>"
                                        data-plan-price="<?= $plan['price'] ?>">
                                    <i class="fas fa-credit-card me-2"></i>اشترك الآن
                                </button>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
</div>

<!-- FAQ Section -->
<div class="faq-section">
    <div class="container">
        <div class="section-header text-center mb-5">
            <h2 class="section-title">الأسئلة الشائعة</h2>
        </div>
        
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="accordion" id="faqAccordion">
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#faq1">
                                هل يمكنني إلغاء الاشتراك في أي وقت؟
                            </button>
                        </h2>
                        <div id="faq1" class="accordion-collapse collapse show" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                نعم، يمكنك إلغاء اشتراكك في أي وقت بدون أي رسوم إضافية. ستتمكن من الاستمرار في المشاهدة حتى نهاية فترة الاشتراك المدفوعة.
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq2">
                                هل يمكنني تغيير خطة الاشتراك؟
                            </button>
                        </h2>
                        <div id="faq2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                نعم، يمكنك ترقية أو تخفيض خطة اشتراكك في أي وقت. سيتم احتساب الفرق في السعر بشكل تناسبي.
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq3">
                                ما هي طرق الدفع المتاحة؟
                            </button>
                        </h2>
                        <div id="faq3" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                نقبل جميع بطاقات الائتمان الرئيسية (Visa, Mastercard, American Express) وPayPal والتحويل البنكي.
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq4">
                                هل يمكنني مشاهدة المحتوى على عدة أجهزة؟
                            </button>
                        </h2>
                        <div id="faq4" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                نعم، يمكنك مشاهدة المحتوى على عدة أجهزة حسب خطة اشتراكك. الخطة الأساسية تسمح بجهاز واحد، والخطط الأعلى تسمح بأجهزة متعددة.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Subscribe Modal -->
<div class="modal fade" id="subscribeModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">اشتراك في خطة <span id="modalPlanName"></span></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="subscribeForm" method="POST" action="/subscriptions/subscribe">
                <div class="modal-body">
                    <input type="hidden" name="csrf_token" value="<?= $csrf_token ?? '' ?>">
                    <input type="hidden" name="plan_id" id="modalPlanId">
                    
                    <div class="subscription-summary">
                        <h6>ملخص الاشتراك</h6>
                        <div class="summary-item">
                            <span>الخطة:</span>
                            <span id="summaryPlanName"></span>
                        </div>
                        <div class="summary-item">
                            <span>السعر:</span>
                            <span id="summaryPlanPrice"></span>
                        </div>
                        <div class="summary-item total">
                            <span>المجموع:</span>
                            <span id="summaryTotal"></span>
                        </div>
                    </div>
                    
                    <div class="payment-methods">
                        <h6>طريقة الدفع</h6>
                        <div class="payment-method-options">
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="payment_method" id="stripe" value="stripe" checked>
                                <label class="form-check-label" for="stripe">
                                    <i class="fab fa-cc-stripe me-2"></i>
                                    بطاقة ائتمان
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="payment_method" id="paypal" value="paypal">
                                <label class="form-check-label" for="paypal">
                                    <i class="fab fa-paypal me-2"></i>
                                    PayPal
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="payment_method" id="bank_transfer" value="bank_transfer">
                                <label class="form-check-label" for="bank_transfer">
                                    <i class="fas fa-university me-2"></i>
                                    تحويل بنكي
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="terms-agreement">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="agreeTerms" required>
                            <label class="form-check-label" for="agreeTerms">
                                أوافق على <a href="/terms" target="_blank">شروط الاستخدام</a> و 
                                <a href="/privacy" target="_blank">سياسة الخصوصية</a>
                            </label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-credit-card me-2"></i>متابعة الدفع
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Upgrade Modal -->
<?php if ($current_subscription): ?>
<div class="modal fade" id="upgradeModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">ترقية الاشتراك</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="/subscriptions/upgrade">
                <div class="modal-body">
                    <input type="hidden" name="csrf_token" value="<?= $csrf_token ?? '' ?>">
                    
                    <div class="current-plan">
                        <h6>خطتك الحالية</h6>
                        <p><?= htmlspecialchars($current_subscription['plan_name']) ?> - $<?= number_format($current_subscription['price'], 2) ?>/شهر</p>
                    </div>
                    
                    <div class="new-plan-selection">
                        <h6>اختر الخطة الجديدة</h6>
                        <?php foreach ($plans as $plan): ?>
                            <?php if ($plan['id'] != $current_subscription['subscription_id']): ?>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="new_plan_id" 
                                           id="upgrade_plan_<?= $plan['id'] ?>" value="<?= $plan['id'] ?>">
                                    <label class="form-check-label" for="upgrade_plan_<?= $plan['id'] ?>">
                                        <?= htmlspecialchars($plan['name']) ?> - $<?= number_format($plan['price'], 2) ?>/شهر
                                    </label>
                                </div>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">ترقية الاشتراك</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Cancel Modal -->
<div class="modal fade" id="cancelModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إلغاء الاشتراك</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="/subscriptions/cancel">
                <div class="modal-body">
                    <input type="hidden" name="csrf_token" value="<?= $csrf_token ?? '' ?>">
                    
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        هل أنت متأكد من إلغاء اشتراكك؟ ستفقد الوصول إلى المحتوى المميز في نهاية فترة الاشتراك الحالية.
                    </div>
                    
                    <p>سيبقى اشتراكك نشطاً حتى <?= date('d/m/Y', strtotime($current_subscription['expires_at'])) ?></p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">الاحتفاظ بالاشتراك</button>
                    <button type="submit" class="btn btn-danger">إلغاء الاشتراك</button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php endif; ?>

<script>
// Subscribe button functionality
$('.subscribe-btn, .upgrade-btn').click(function() {
    const planId = $(this).data('plan-id');
    const planName = $(this).data('plan-name');
    const planPrice = $(this).data('plan-price');
    
    $('#modalPlanId').val(planId);
    $('#modalPlanName').text(planName);
    $('#summaryPlanName').text(planName);
    $('#summaryPlanPrice').text('$' + planPrice + '/شهر');
    $('#summaryTotal').text('$' + planPrice + '/شهر');
    
    $('#subscribeModal').modal('show');
});

// Form validation
$('#subscribeForm').submit(function(e) {
    if (!$('#agreeTerms').is(':checked')) {
        e.preventDefault();
        alert('يجب الموافقة على شروط الاستخدام');
        return false;
    }
});

// Show loading on form submit
$('#subscribeForm').submit(function() {
    $(this).find('button[type="submit"]').prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-2"></i>جاري المعالجة...');
});
</script>

<?php 
$additional_css = ['/assets/css/subscriptions.css'];
include 'views/layout/footer.php'; 
?>
