<?php
/**
 * إصلاح شامل لقاعدة البيانات - Shahid Platform
 * Complete Database Structure Fix
 */

echo "<h1>🗄️ إصلاح شامل لقاعدة البيانات</h1>";

try {
    $pdo = new PDO("mysql:host=localhost;dbname=shahid_platform;charset=utf8mb4", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>✅ تم الاتصال بقاعدة البيانات بنجاح</h3>";
    echo "</div>";
    
    // 1. إنشاء جدول المستخدمين إذا لم يكن موجوداً
    echo "<h2>👥 فحص وإصلاح جدول المستخدمين</h2>";
    
    $userTableSQL = "
        CREATE TABLE IF NOT EXISTS `users` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `username` varchar(50) NOT NULL UNIQUE,
            `email` varchar(100) NOT NULL UNIQUE,
            `password` varchar(255) NOT NULL,
            `full_name` varchar(100) DEFAULT NULL,
            `phone` varchar(20) DEFAULT NULL,
            `avatar` varchar(255) DEFAULT NULL,
            `birth_date` date DEFAULT NULL,
            `gender` enum('male','female','other') DEFAULT NULL,
            `country` varchar(50) DEFAULT NULL,
            `language` varchar(10) DEFAULT 'ar',
            `subscription_type` enum('free','premium','vip') DEFAULT 'free',
            `subscription_start` datetime DEFAULT NULL,
            `subscription_end` datetime DEFAULT NULL,
            `is_active` tinyint(1) DEFAULT 1,
            `is_verified` tinyint(1) DEFAULT 0,
            `verification_token` varchar(255) DEFAULT NULL,
            `reset_token` varchar(255) DEFAULT NULL,
            `last_login` datetime DEFAULT NULL,
            `login_attempts` int(11) DEFAULT 0,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `idx_email` (`email`),
            KEY `idx_username` (`username`),
            KEY `idx_subscription` (`subscription_type`),
            KEY `idx_active` (`is_active`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    try {
        $pdo->exec($userTableSQL);
        echo "<p style='color: #4CAF50;'>✅ جدول المستخدمين جاهز</p>";
        
        // إنشاء مستخدم إداري تجريبي
        $adminCheck = $pdo->query("SELECT COUNT(*) FROM users WHERE username = 'admin'")->fetchColumn();
        if ($adminCheck == 0) {
            $adminPassword = password_hash('admin123', PASSWORD_DEFAULT);
            $stmt = $pdo->prepare("INSERT INTO users (username, email, password, full_name, subscription_type, is_active, is_verified) VALUES (?, ?, ?, ?, ?, ?, ?)");
            $stmt->execute(['admin', '<EMAIL>', $adminPassword, 'مدير النظام', 'vip', 1, 1]);
            echo "<p style='color: #2196F3;'>👤 تم إنشاء حساب إداري: admin / admin123</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: #F44336;'>❌ خطأ في جدول المستخدمين: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
    
    // 2. تشغيل أداة إنشاء الجداول الأساسية
    echo "<h2>🔧 تشغيل أداة إنشاء الجداول الأساسية</h2>";
    
    // تضمين ملف إنشاء الجداول الأساسية
    ob_start();
    include 'create_missing_tables.php';
    $output = ob_get_clean();
    
    // عرض ملخص مبسط
    if (strpos($output, 'تم إنشاء') !== false) {
        echo "<p style='color: #4CAF50;'>✅ تم تشغيل أداة إنشاء الجداول الأساسية بنجاح</p>";
    } else {
        echo "<p style='color: #2196F3;'>ℹ️ الجداول الأساسية موجودة مسبقاً</p>";
    }
    
    // 3. إنشاء جدول الإعدادات
    echo "<h2>⚙️ فحص وإصلاح جدول الإعدادات</h2>";
    
    $settingsTableSQL = "
        CREATE TABLE IF NOT EXISTS `settings` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `setting_key` varchar(100) NOT NULL UNIQUE,
            `setting_value` text DEFAULT NULL,
            `setting_type` enum('string','number','boolean','json') DEFAULT 'string',
            `description` varchar(255) DEFAULT NULL,
            `category` varchar(50) DEFAULT 'general',
            `is_public` tinyint(1) DEFAULT 0,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `idx_key` (`setting_key`),
            KEY `idx_category` (`category`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    try {
        $pdo->exec($settingsTableSQL);
        echo "<p style='color: #4CAF50;'>✅ جدول الإعدادات جاهز</p>";
        
        // إدراج إعدادات افتراضية
        $defaultSettings = [
            ['site_name', 'Shahid Platform', 'string', 'اسم الموقع', 'general', 1],
            ['site_description', 'منصة شاهد للأفلام والمسلسلات', 'string', 'وصف الموقع', 'general', 1],
            ['site_logo', '/assets/images/logo.png', 'string', 'شعار الموقع', 'general', 1],
            ['maintenance_mode', '0', 'boolean', 'وضع الصيانة', 'system', 0],
            ['registration_enabled', '1', 'boolean', 'تفعيل التسجيل', 'user', 0],
            ['max_login_attempts', '5', 'number', 'محاولات تسجيل الدخول القصوى', 'security', 0],
            ['session_timeout', '3600', 'number', 'انتهاء صلاحية الجلسة (ثانية)', 'security', 0],
            ['default_language', 'ar', 'string', 'اللغة الافتراضية', 'general', 1],
            ['items_per_page', '20', 'number', 'عدد العناصر في الصفحة', 'display', 1],
            ['video_quality_default', 'HD', 'string', 'جودة الفيديو الافتراضية', 'video', 1]
        ];
        
        $stmt = $pdo->prepare("INSERT IGNORE INTO settings (setting_key, setting_value, setting_type, description, category, is_public) VALUES (?, ?, ?, ?, ?, ?)");
        
        foreach ($defaultSettings as $setting) {
            $stmt->execute($setting);
        }
        echo "<p style='color: #2196F3;'>⚙️ تم إدراج الإعدادات الافتراضية</p>";
        
    } catch (Exception $e) {
        echo "<p style='color: #F44336;'>❌ خطأ في جدول الإعدادات: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
    
    // 4. إنشاء جدول السجلات
    echo "<h2>📝 فحص وإصلاح جدول السجلات</h2>";
    
    $logsTableSQL = "
        CREATE TABLE IF NOT EXISTS `logs` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `user_id` int(11) DEFAULT NULL,
            `action` varchar(100) NOT NULL,
            `table_name` varchar(50) DEFAULT NULL,
            `record_id` int(11) DEFAULT NULL,
            `old_values` json DEFAULT NULL,
            `new_values` json DEFAULT NULL,
            `ip_address` varchar(45) DEFAULT NULL,
            `user_agent` text DEFAULT NULL,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `idx_user` (`user_id`),
            KEY `idx_action` (`action`),
            KEY `idx_table` (`table_name`),
            KEY `idx_created` (`created_at`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    try {
        $pdo->exec($logsTableSQL);
        echo "<p style='color: #4CAF50;'>✅ جدول السجلات جاهز</p>";
    } catch (Exception $e) {
        echo "<p style='color: #F44336;'>❌ خطأ في جدول السجلات: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
    
    // 5. إنشاء جدول الإشعارات
    echo "<h2>🔔 فحص وإصلاح جدول الإشعارات</h2>";
    
    $notificationsTableSQL = "
        CREATE TABLE IF NOT EXISTS `notifications` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `user_id` int(11) NOT NULL,
            `title` varchar(255) NOT NULL,
            `message` text NOT NULL,
            `type` enum('info','success','warning','error') DEFAULT 'info',
            `is_read` tinyint(1) DEFAULT 0,
            `action_url` varchar(500) DEFAULT NULL,
            `expires_at` datetime DEFAULT NULL,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `idx_user` (`user_id`),
            KEY `idx_read` (`is_read`),
            KEY `idx_type` (`type`),
            KEY `idx_created` (`created_at`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    try {
        $pdo->exec($notificationsTableSQL);
        echo "<p style='color: #4CAF50;'>✅ جدول الإشعارات جاهز</p>";
    } catch (Exception $e) {
        echo "<p style='color: #F44336;'>❌ خطأ في جدول الإشعارات: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
    
    // 6. إنشاء جدول التعليقات
    echo "<h2>💬 فحص وإصلاح جدول التعليقات</h2>";
    
    $commentsTableSQL = "
        CREATE TABLE IF NOT EXISTS `comments` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `user_id` int(11) NOT NULL,
            `content_type` enum('movie','series','episode') NOT NULL,
            `content_id` int(11) NOT NULL,
            `parent_id` int(11) DEFAULT NULL,
            `comment` text NOT NULL,
            `is_approved` tinyint(1) DEFAULT 1,
            `likes` int(11) DEFAULT 0,
            `dislikes` int(11) DEFAULT 0,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `idx_user` (`user_id`),
            KEY `idx_content` (`content_type`, `content_id`),
            KEY `idx_parent` (`parent_id`),
            KEY `idx_approved` (`is_approved`),
            KEY `idx_created` (`created_at`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    try {
        $pdo->exec($commentsTableSQL);
        echo "<p style='color: #4CAF50;'>✅ جدول التعليقات جاهز</p>";
    } catch (Exception $e) {
        echo "<p style='color: #F44336;'>❌ خطأ في جدول التعليقات: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
    
    // 7. إنشاء جدول قوائم التشغيل
    echo "<h2>📋 فحص وإصلاح جدول قوائم التشغيل</h2>";
    
    $playlistsTableSQL = "
        CREATE TABLE IF NOT EXISTS `playlists` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `user_id` int(11) NOT NULL,
            `name` varchar(100) NOT NULL,
            `description` text DEFAULT NULL,
            `is_public` tinyint(1) DEFAULT 0,
            `thumbnail` varchar(255) DEFAULT NULL,
            `items_count` int(11) DEFAULT 0,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `idx_user` (`user_id`),
            KEY `idx_public` (`is_public`),
            KEY `idx_created` (`created_at`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    $playlistItemsTableSQL = "
        CREATE TABLE IF NOT EXISTS `playlist_items` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `playlist_id` int(11) NOT NULL,
            `content_type` enum('movie','series','episode') NOT NULL,
            `content_id` int(11) NOT NULL,
            `sort_order` int(11) DEFAULT 0,
            `added_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `unique_playlist_content` (`playlist_id`, `content_type`, `content_id`),
            KEY `idx_playlist` (`playlist_id`),
            KEY `idx_content` (`content_type`, `content_id`),
            KEY `idx_sort` (`sort_order`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    try {
        $pdo->exec($playlistsTableSQL);
        $pdo->exec($playlistItemsTableSQL);
        echo "<p style='color: #4CAF50;'>✅ جداول قوائم التشغيل جاهزة</p>";
    } catch (Exception $e) {
        echo "<p style='color: #F44336;'>❌ خطأ في جداول قوائم التشغيل: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>❌ خطأ في الاتصال بقاعدة البيانات:</h3>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🗄️ إصلاح شامل لقاعدة البيانات - Shahid Platform</title>
    <style>
        body {
            font-family: 'Segoe UI', 'Cairo', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #2d2d2d 100%);
            color: #fff;
            min-height: 100vh;
            padding: 2rem;
            margin: 0;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(47, 47, 47, 0.95);
            border-radius: 20px;
            padding: 3rem;
            border: 2px solid rgba(229, 9, 20, 0.3);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
        }
        h1, h2, h3 { 
            color: #E50914; 
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
        }
        h1 {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 2rem;
            background: linear-gradient(45deg, #E50914, #FF6B35);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        p {
            line-height: 1.6;
            margin: 0.5rem 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- المحتوى يتم عرضه من PHP أعلاه -->
    </div>
</body>
</html>
