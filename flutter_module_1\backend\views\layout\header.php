<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= htmlspecialchars($page_title ?? 'Shahid') ?></title>
    
    <!-- SEO Meta Tags -->
    <meta name="description" content="<?= htmlspecialchars($meta_description ?? 'Professional Video Streaming Platform') ?>">
    <meta name="keywords" content="<?= htmlspecialchars($meta_keywords ?? 'movies, series, streaming, video, entertainment') ?>">
    <meta name="author" content="Shahid">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="<?= htmlspecialchars($page_title ?? 'Shahid') ?>">
    <meta property="og:description" content="<?= htmlspecialchars($meta_description ?? 'Professional Video Streaming Platform') ?>">
    <meta property="og:image" content="<?= $og_image ?? '/assets/images/logo-og.jpg' ?>">
    <meta property="og:url" content="<?= $canonical_url ?? '' ?>">
    <meta property="og:type" content="website">
    <meta property="og:site_name" content="Shahid">
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="<?= htmlspecialchars($page_title ?? 'Shahid') ?>">
    <meta name="twitter:description" content="<?= htmlspecialchars($meta_description ?? 'Professional Video Streaming Platform') ?>">
    <meta name="twitter:image" content="<?= $og_image ?? '/assets/images/logo-og.jpg' ?>">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/assets/images/favicon.ico">
    <link rel="apple-touch-icon" href="/assets/images/apple-touch-icon.png">
    
    <!-- CSS -->
    <link rel="stylesheet" href="/assets/css/bootstrap.min.css">
    <link rel="stylesheet" href="/assets/css/style.css">
    <link rel="stylesheet" href="/assets/css/responsive.css">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- CSRF Token -->
    <meta name="csrf-token" content="<?= $csrf_token ?? '' ?>">
    
    <?php if (isset($additional_css)): ?>
        <?php foreach ($additional_css as $css): ?>
            <link rel="stylesheet" href="<?= $css ?>">
        <?php endforeach; ?>
    <?php endif; ?>
</head>
<body class="<?= $body_class ?? '' ?>">
    
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top">
        <div class="container">
            <!-- Logo -->
            <a class="navbar-brand" href="/">
                <img src="/assets/images/logo.png" alt="Shahid" height="40">
            </a>
            
            <!-- Mobile Toggle -->
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <!-- Navigation Links -->
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">الرئيسية</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/movies">الأفلام</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/series">المسلسلات</a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            التصنيفات
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/movies?genre=action">أكشن</a></li>
                            <li><a class="dropdown-item" href="/movies?genre=drama">دراما</a></li>
                            <li><a class="dropdown-item" href="/movies?genre=comedy">كوميديا</a></li>
                            <li><a class="dropdown-item" href="/movies?genre=horror">رعب</a></li>
                            <li><a class="dropdown-item" href="/movies?genre=romance">رومانسي</a></li>
                        </ul>
                    </li>
                </ul>
                
                <!-- Search Form -->
                <form class="d-flex me-3" action="/search" method="GET">
                    <div class="search-container">
                        <input class="form-control" type="search" name="q" placeholder="البحث..." 
                               value="<?= htmlspecialchars($_GET['q'] ?? '') ?>" autocomplete="off">
                        <div class="search-suggestions"></div>
                    </div>
                    <button class="btn btn-outline-light" type="submit">
                        <i class="fas fa-search"></i>
                    </button>
                </form>
                
                <!-- User Menu -->
                <ul class="navbar-nav">
                    <?php if (isset($user) && $user): ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <img src="<?= $user['avatar'] ?? '/assets/images/default-avatar.png' ?>" 
                                     alt="<?= htmlspecialchars($user['name']) ?>" 
                                     class="rounded-circle me-1" width="30" height="30">
                                <?= htmlspecialchars($user['name']) ?>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li><a class="dropdown-item" href="/profile">الملف الشخصي</a></li>
                                <li><a class="dropdown-item" href="/favorites">المفضلة</a></li>
                                <li><a class="dropdown-item" href="/watch-history">سجل المشاهدة</a></li>
                                <li><a class="dropdown-item" href="/subscriptions">الاشتراكات</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="/settings">الإعدادات</a></li>
                                <li><a class="dropdown-item" href="/logout">تسجيل الخروج</a></li>
                            </ul>
                        </li>
                    <?php else: ?>
                        <li class="nav-item">
                            <a class="nav-link" href="/login">تسجيل الدخول</a>
                        </li>
                        <li class="nav-item">
                            <a class="btn btn-primary ms-2" href="/register">إنشاء حساب</a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>
    
    <!-- Main Content -->
    <main class="main-content">
        <?php if (isset($_SESSION['flash_message'])): ?>
            <div class="container mt-3">
                <div class="alert alert-<?= $_SESSION['flash_type'] ?? 'info' ?> alert-dismissible fade show">
                    <?= htmlspecialchars($_SESSION['flash_message']) ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            </div>
            <?php 
            unset($_SESSION['flash_message']);
            unset($_SESSION['flash_type']);
            ?>
        <?php endif; ?>
