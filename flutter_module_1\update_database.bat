@echo off
chcp 65001 >nul
title Shahid Platform - Database Update

echo.
echo ========================================
echo 🎬 Shahid Platform - Database Update
echo ========================================
echo.

echo 🔍 Checking XAMPP installation...

REM Check if XAMPP is installed
if not exist "C:\xampp\php\php.exe" (
    echo ❌ XAMPP not found at C:\xampp\
    echo Please install XAMPP or update the path in this script
    pause
    exit /b 1
)

echo ✅ XAMPP found!
echo.

echo 🚀 Starting database update...
echo.

REM Run the PHP script
"C:\xampp\php\php.exe" "backend\database\update_database.php"

echo.
echo ========================================
echo 🎉 Database update completed!
echo ========================================
echo.

echo 🔗 Quick Links:
echo   📊 phpMyAdmin: http://localhost/phpmyadmin/
echo   🌐 Website: http://localhost/amr2/flutter_module_1/backend/homepage.php
echo   🎛️ Admin Panel: http://localhost/amr2/flutter_module_1/backend/admin/dashboard.php
echo   🔗 API Test: http://localhost/amr2/flutter_module_1/backend/api/test.php
echo.

echo Press any key to open phpMyAdmin...
pause >nul

start http://localhost/phpmyadmin/

echo.
echo ✅ Done! Database is ready for use.
pause
