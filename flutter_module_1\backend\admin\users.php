<?php
/**
 * Users Management - Shahid Admin Panel
 */

session_start();

// Check admin login
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: index.php');
    exit();
}

// Database connection
try {
    $config = include '../config/database.php';
    $pdo = new PDO("mysql:host={$config['host']};dbname={$config['name']};charset=utf8mb4", 
                   $config['username'], $config['password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (Exception $e) {
    die('خطأ في الاتصال بقاعدة البيانات: ' . $e->getMessage());
}

// Handle actions
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['add_user'])) {
        // Add new user
        $name = $_POST['name'] ?? '';
        $email = $_POST['email'] ?? '';
        $password = $_POST['password'] ?? '';
        $role = $_POST['role'] ?? 'user';
        $status = $_POST['status'] ?? 'active';
        
        if (!empty($name) && !empty($email) && !empty($password)) {
            try {
                // Check if email exists
                $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ?");
                $stmt->execute([$email]);
                if ($stmt->fetch()) {
                    $message = 'البريد الإلكتروني موجود مسبقاً';
                    $messageType = 'error';
                } else {
                    $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
                    $stmt = $pdo->prepare("INSERT INTO users (name, email, password, role, status, created_at) VALUES (?, ?, ?, ?, ?, NOW())");
                    $stmt->execute([$name, $email, $hashedPassword, $role, $status]);
                    $message = 'تم إضافة المستخدم بنجاح';
                    $messageType = 'success';
                }
            } catch (Exception $e) {
                $message = 'خطأ في إضافة المستخدم: ' . $e->getMessage();
                $messageType = 'error';
            }
        } else {
            $message = 'جميع البيانات مطلوبة';
            $messageType = 'error';
        }
    } elseif (isset($_POST['update_user'])) {
        // Update user
        $userId = $_POST['user_id'] ?? '';
        $name = $_POST['name'] ?? '';
        $email = $_POST['email'] ?? '';
        $role = $_POST['role'] ?? 'user';
        $status = $_POST['status'] ?? 'active';
        
        if (!empty($userId) && !empty($name) && !empty($email)) {
            try {
                $stmt = $pdo->prepare("UPDATE users SET name = ?, email = ?, role = ?, status = ? WHERE id = ?");
                $stmt->execute([$name, $email, $role, $status, $userId]);
                $message = 'تم تحديث المستخدم بنجاح';
                $messageType = 'success';
            } catch (Exception $e) {
                $message = 'خطأ في تحديث المستخدم: ' . $e->getMessage();
                $messageType = 'error';
            }
        }
    } elseif (isset($_POST['delete_user'])) {
        // Delete user
        $userId = $_POST['user_id'] ?? '';
        if (!empty($userId)) {
            try {
                $stmt = $pdo->prepare("DELETE FROM users WHERE id = ?");
                $stmt->execute([$userId]);
                $message = 'تم حذف المستخدم بنجاح';
                $messageType = 'success';
            } catch (Exception $e) {
                $message = 'خطأ في حذف المستخدم: ' . $e->getMessage();
                $messageType = 'error';
            }
        }
    }
}

// Get users
$page = (int)($_GET['page'] ?? 1);
$limit = 15;
$offset = ($page - 1) * $limit;
$search = $_GET['search'] ?? '';

try {
    if (!empty($search)) {
        $stmt = $pdo->prepare("SELECT * FROM users WHERE name LIKE ? OR email LIKE ? ORDER BY created_at DESC LIMIT $limit OFFSET $offset");
        $searchTerm = "%$search%";
        $stmt->execute([$searchTerm, $searchTerm]);
        $users = $stmt->fetchAll(PDO::FETCH_ASSOC);

        $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM users WHERE name LIKE ? OR email LIKE ?");
        $stmt->execute([$searchTerm, $searchTerm]);
        $total = $stmt->fetch()['total'];
    } else {
        $stmt = $pdo->prepare("SELECT * FROM users ORDER BY created_at DESC LIMIT $limit OFFSET $offset");
        $stmt->execute();
        $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $stmt = $pdo->query("SELECT COUNT(*) as total FROM users");
        $total = $stmt->fetch()['total'];
    }
    
    $totalPages = ceil($total / $limit);
} catch (Exception $e) {
    $users = [];
    $total = 0;
    $totalPages = 0;
    $message = 'خطأ في جلب المستخدمين: ' . $e->getMessage();
    $messageType = 'error';
}

// Get statistics
$stats = [];
try {
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM users");
    $stats['total'] = $stmt->fetch()['total'];
    
    $stmt = $pdo->query("SELECT COUNT(*) as active FROM users WHERE status = 'active'");
    $stats['active'] = $stmt->fetch()['active'];
    
    $stmt = $pdo->query("SELECT COUNT(*) as admins FROM users WHERE role = 'admin'");
    $stats['admins'] = $stmt->fetch()['admins'];
    
    $stmt = $pdo->query("SELECT COUNT(*) as today FROM users WHERE DATE(created_at) = CURDATE()");
    $stats['today'] = $stmt->fetch()['today'];
} catch (Exception $e) {
    $stats = ['total' => 0, 'active' => 0, 'admins' => 0, 'today' => 0];
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المستخدمين - Shahid Admin</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            color: #fff;
            min-height: 100vh;
        }
        
        .header {
            background: #E50914;
            padding: 1rem 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .header h1 {
            font-size: 1.8rem;
        }
        
        .header a {
            color: #fff;
            text-decoration: none;
            padding: 0.5rem 1rem;
            border: 1px solid #fff;
            border-radius: 5px;
            transition: all 0.3s;
        }
        
        .header a:hover {
            background: #fff;
            color: #E50914;
        }
        
        .container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 2rem;
        }
        
        .message {
            padding: 1rem;
            border-radius: 5px;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .message.success {
            background: #28a745;
            color: #fff;
        }
        
        .message.error {
            background: #dc3545;
            color: #fff;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: #2d2d2d;
            padding: 1.5rem;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }
        
        .stat-card .number {
            font-size: 2rem;
            font-weight: bold;
            color: #E50914;
            margin-bottom: 0.5rem;
        }
        
        .stat-card .label {
            color: #ccc;
        }
        
        .search-section {
            background: #2d2d2d;
            padding: 1.5rem;
            border-radius: 10px;
            margin-bottom: 2rem;
            display: flex;
            gap: 1rem;
            align-items: center;
        }
        
        .search-section input {
            flex: 1;
            padding: 0.75rem;
            border: 1px solid #555;
            border-radius: 5px;
            background: #1a1a1a;
            color: #fff;
            font-size: 1rem;
        }
        
        .search-section input:focus {
            outline: none;
            border-color: #E50914;
        }
        
        .btn {
            padding: 0.75rem 1.5rem;
            background: #E50914;
            color: #fff;
            border: none;
            border-radius: 5px;
            font-size: 1rem;
            cursor: pointer;
            transition: background 0.3s;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn:hover {
            background: #b8070f;
        }
        
        .btn.danger {
            background: #dc3545;
        }
        
        .btn.danger:hover {
            background: #c82333;
        }
        
        .btn.secondary {
            background: #6c757d;
        }
        
        .btn.secondary:hover {
            background: #5a6268;
        }
        
        .btn.small {
            padding: 0.5rem 1rem;
            font-size: 0.9rem;
        }
        
        .users-table {
            background: #2d2d2d;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }
        
        .table-header {
            background: #E50914;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .table-header h2 {
            font-size: 1.3rem;
        }
        
        .table-content {
            overflow-x: auto;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
        }
        
        th, td {
            padding: 1rem;
            text-align: right;
            border-bottom: 1px solid #555;
        }
        
        th {
            background: #3d3d3d;
            font-weight: bold;
        }
        
        tr:hover {
            background: #3d3d3d;
        }
        
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: bold;
        }
        
        .status-active {
            background: #28a745;
            color: #fff;
        }
        
        .status-inactive {
            background: #dc3545;
            color: #fff;
        }
        
        .status-pending {
            background: #ffc107;
            color: #000;
        }
        
        .role-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: bold;
        }
        
        .role-admin {
            background: #E50914;
            color: #fff;
        }
        
        .role-user {
            background: #6c757d;
            color: #fff;
        }
        
        .actions {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 2rem;
            gap: 1rem;
        }
        
        .pagination a {
            color: #fff;
            text-decoration: none;
            padding: 0.5rem 1rem;
            border: 1px solid #555;
            border-radius: 5px;
            transition: all 0.3s;
        }
        
        .pagination a:hover,
        .pagination a.active {
            background: #E50914;
            border-color: #E50914;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 0 1rem;
            }
            
            .header {
                flex-direction: column;
                gap: 1rem;
            }
            
            .search-section {
                flex-direction: column;
            }
            
            .actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>👥 إدارة المستخدمين</h1>
        <a href="index.php">← العودة للوحة الرئيسية</a>
    </div>
    
    <div class="container">
        <?php if (!empty($message)): ?>
            <div class="message <?php echo $messageType; ?>">
                <?php echo htmlspecialchars($message); ?>
            </div>
        <?php endif; ?>
        
        <!-- Statistics -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="number"><?php echo number_format($stats['total']); ?></div>
                <div class="label">إجمالي المستخدمين</div>
            </div>
            <div class="stat-card">
                <div class="number"><?php echo number_format($stats['active']); ?></div>
                <div class="label">المستخدمون النشطون</div>
            </div>
            <div class="stat-card">
                <div class="number"><?php echo number_format($stats['admins']); ?></div>
                <div class="label">المديرون</div>
            </div>
            <div class="stat-card">
                <div class="number"><?php echo number_format($stats['today']); ?></div>
                <div class="label">تسجيلات اليوم</div>
            </div>
        </div>
        
        <!-- Search Section -->
        <div class="search-section">
            <form method="GET" style="display: flex; gap: 1rem; width: 100%;">
                <input type="text" name="search" placeholder="البحث بالاسم أو البريد الإلكتروني..." 
                       value="<?php echo htmlspecialchars($search); ?>">
                <button type="submit" class="btn">بحث</button>
                <?php if (!empty($search)): ?>
                    <a href="users.php" class="btn secondary">مسح</a>
                <?php endif; ?>
            </form>
        </div>
        
        <!-- Users Table -->
        <div class="users-table">
            <div class="table-header">
                <h2>📋 قائمة المستخدمين (<?php echo number_format($total); ?> مستخدم)</h2>
                <button onclick="showAddUserForm()" class="btn">➕ إضافة مستخدم</button>
            </div>
            
            <div class="table-content">
                <table>
                    <thead>
                        <tr>
                            <th>الاسم</th>
                            <th>البريد الإلكتروني</th>
                            <th>الدور</th>
                            <th>الحالة</th>
                            <th>تاريخ التسجيل</th>
                            <th>آخر دخول</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (!empty($users)): ?>
                            <?php foreach ($users as $user): ?>
                                <tr>
                                    <td><strong><?php echo htmlspecialchars($user['name']); ?></strong></td>
                                    <td><?php echo htmlspecialchars($user['email']); ?></td>
                                    <td>
                                        <span class="role-badge role-<?php echo $user['role']; ?>">
                                            <?php echo $user['role'] === 'admin' ? 'مدير' : 'مستخدم'; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="status-badge status-<?php echo $user['status']; ?>">
                                            <?php 
                                            switch($user['status']) {
                                                case 'active': echo 'نشط'; break;
                                                case 'inactive': echo 'غير نشط'; break;
                                                case 'pending': echo 'في الانتظار'; break;
                                                default: echo $user['status'];
                                            }
                                            ?>
                                        </span>
                                    </td>
                                    <td><?php echo date('Y-m-d', strtotime($user['created_at'])); ?></td>
                                    <td><?php echo isset($user['last_login']) && $user['last_login'] ? date('Y-m-d H:i', strtotime($user['last_login'])) : 'لم يدخل بعد'; ?></td>
                                    <td>
                                        <div class="actions">
                                            <button onclick="editUser(<?php echo htmlspecialchars(json_encode($user)); ?>)" class="btn small secondary">تعديل</button>
                                            <form method="POST" style="display: inline;" onsubmit="return confirm('هل أنت متأكد من حذف هذا المستخدم؟')">
                                                <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                                <button type="submit" name="delete_user" class="btn small danger">حذف</button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="7" style="text-align: center; color: #ccc; padding: 2rem;">
                                    <?php echo !empty($search) ? 'لا توجد نتائج للبحث' : 'لا يوجد مستخدمون'; ?>
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
        
        <!-- Pagination -->
        <?php if ($totalPages > 1): ?>
            <div class="pagination">
                <?php if ($page > 1): ?>
                    <a href="?page=<?php echo $page - 1; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?>">← السابق</a>
                <?php endif; ?>
                
                <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                    <a href="?page=<?php echo $i; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?>" 
                       <?php echo $i === $page ? 'class="active"' : ''; ?>>
                        <?php echo $i; ?>
                    </a>
                <?php endfor; ?>
                
                <?php if ($page < $totalPages): ?>
                    <a href="?page=<?php echo $page + 1; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?>">التالي →</a>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>
    
    <script>
        function showAddUserForm() {
            const name = prompt('اسم المستخدم:');
            if (!name) return;
            
            const email = prompt('البريد الإلكتروني:');
            if (!email) return;
            
            const password = prompt('كلمة المرور:');
            if (!password) return;
            
            const role = confirm('هل تريد جعل هذا المستخدم مديراً؟') ? 'admin' : 'user';
            
            // Create form and submit
            const form = document.createElement('form');
            form.method = 'POST';
            form.innerHTML = `
                <input type="hidden" name="add_user" value="1">
                <input type="hidden" name="name" value="${name}">
                <input type="hidden" name="email" value="${email}">
                <input type="hidden" name="password" value="${password}">
                <input type="hidden" name="role" value="${role}">
                <input type="hidden" name="status" value="active">
            `;
            document.body.appendChild(form);
            form.submit();
        }
        
        function editUser(user) {
            const name = prompt('اسم المستخدم:', user.name);
            if (!name) return;
            
            const email = prompt('البريد الإلكتروني:', user.email);
            if (!email) return;
            
            const role = confirm('هل تريد جعل هذا المستخدم مديراً؟\n(اختر إلغاء للمستخدم العادي)') ? 'admin' : 'user';
            const status = confirm('هل تريد تفعيل هذا المستخدم؟\n(اختر إلغاء لإلغاء التفعيل)') ? 'active' : 'inactive';
            
            // Create form and submit
            const form = document.createElement('form');
            form.method = 'POST';
            form.innerHTML = `
                <input type="hidden" name="update_user" value="1">
                <input type="hidden" name="user_id" value="${user.id}">
                <input type="hidden" name="name" value="${name}">
                <input type="hidden" name="email" value="${email}">
                <input type="hidden" name="role" value="${role}">
                <input type="hidden" name="status" value="${status}">
            `;
            document.body.appendChild(form);
            form.submit();
        }
    </script>
</body>
</html>
