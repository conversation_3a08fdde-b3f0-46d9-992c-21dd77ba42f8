<?php
/**
 * Shahid - Configuration File
 * Generated by installation script
 */

return [
    // Database Configuration
    'database' => [
        'host' => 'localhost',
        'name' => 'shahid_db',
        'username' => 'root',
        'password' => '',
        'charset' => 'utf8mb4'
    ],

    // Site Configuration
    'site' => [
        'name' => 'Shahid',
        'url' => '',
        'description' => 'Professional Video Streaming Platform',
        'keywords' => 'movies, series, streaming, video, entertainment',
        'admin_email' => '<EMAIL>',
        'timezone' => 'Asia/Riyadh'
    ],

    // Security Configuration
    'security' => [
        'jwt_secret' => '1c99377cfb510b2ea9024cb29eda24e7a38c7f210866975be2f2b1a2829fd309',
        'csrf_token_name' => 'csrf_token',
        'session_lifetime' => 3600,
        'password_min_length' => 8,
        'max_login_attempts' => 5,
        'lockout_duration' => 900
    ],

    // Payment Configuration
    'payment' => [
        'stripe' => [
            'public_key' => '',
            'secret_key' => '',
            'webhook_secret' => ''
        ],
        'paypal' => [
            'client_id' => '',
            'client_secret' => '',
            'mode' => 'sandbox'
        ]
    ],

    // Video Configuration
    'video' => [
        'upload_path' => 'uploads/videos/',
        'subtitle_path' => 'uploads/subtitles/',
        'thumbnail_path' => 'uploads/thumbnails/',
        'max_file_size' => 2147483648,
        'allowed_formats' => ['mp4', 'mkv', 'avi', 'mov'],
        'quality_levels' => ['360p', '480p', '720p', '1080p', 'auto']
    ],

    // API Configuration
    'api' => [
        'version' => 'v1',
        'rate_limit' => 100,
        'pagination_limit' => 20
    ],

    // SEO Configuration
    'seo' => [
        'google_analytics_id' => '',
        'google_search_console_code' => '',
        'facebook_app_id' => '',
        'twitter_site' => '@shahid'
    ],

    // Email Configuration
    'email' => [
        'smtp_host' => 'smtp.gmail.com',
        'smtp_port' => 587,
        'smtp_username' => '',
        'smtp_password' => '',
        'from_email' => '<EMAIL>',
        'from_name' => 'Shahid Platform'
    ],

    // Firebase Configuration
    'firebase' => [
        'server_key' => '',
        'sender_id' => ''
    ]
];
?>