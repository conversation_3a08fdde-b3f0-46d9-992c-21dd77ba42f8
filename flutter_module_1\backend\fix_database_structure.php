<?php
/**
 * إصلاح بنية قاعدة البيانات - إضافة الأعمدة المفقودة
 * Fix Database Structure - Add Missing Columns
 */

try {
    echo "<h1>🗄️ إصلاح بنية قاعدة البيانات</h1>";
    
    $fixedIssues = [];
    $errors = [];
    
    // الاتصال بقاعدة البيانات
    $pdo = new PDO("mysql:host=localhost;dbname=shahid_platform;charset=utf8mb4", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>📊 فحص بنية الجداول الحالية...</h2>";
    
    // 1. فحص جدول watch_history
    echo "<h3>🔍 فحص جدول watch_history...</h3>";
    
    try {
        $stmt = $pdo->query("DESCRIBE watch_history");
        $columns = [];
        $columnDetails = [];
        
        echo "<div style='background: #e3f2fd; border: 1px solid #2196f3; color: #0d47a1; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h4>📋 الأعمدة الموجودة حالياً:</h4>";
        echo "<ul>";
        
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $columns[] = $row['Field'];
            $columnDetails[$row['Field']] = $row;
            echo "<li><strong>{$row['Field']}</strong> - {$row['Type']} " . 
                 ($row['Null'] === 'YES' ? '(NULL)' : '(NOT NULL)') . 
                 ($row['Default'] ? " DEFAULT: {$row['Default']}" : '') . "</li>";
        }
        echo "</ul>";
        echo "</div>";
        
        // الأعمدة المطلوبة
        $requiredColumns = [
            'id' => [
                'sql' => "ALTER TABLE watch_history ADD COLUMN id INT AUTO_INCREMENT PRIMARY KEY FIRST",
                'check' => "INT AUTO_INCREMENT PRIMARY KEY"
            ],
            'user_id' => [
                'sql' => "ALTER TABLE watch_history ADD COLUMN user_id INT NOT NULL AFTER id",
                'check' => "INT NOT NULL"
            ],
            'movie_id' => [
                'sql' => "ALTER TABLE watch_history ADD COLUMN movie_id INT NULL AFTER user_id",
                'check' => "INT NULL"
            ],
            'series_id' => [
                'sql' => "ALTER TABLE watch_history ADD COLUMN series_id INT NULL AFTER movie_id",
                'check' => "INT NULL"
            ],
            'content_id' => [
                'sql' => "ALTER TABLE watch_history ADD COLUMN content_id INT NULL AFTER series_id",
                'check' => "INT NULL"
            ],
            'content_type' => [
                'sql' => "ALTER TABLE watch_history ADD COLUMN content_type VARCHAR(20) NULL AFTER content_id",
                'check' => "VARCHAR(20) NULL"
            ],
            'watch_time' => [
                'sql' => "ALTER TABLE watch_history ADD COLUMN watch_time INT DEFAULT 0 AFTER content_type",
                'check' => "INT DEFAULT 0"
            ],
            'total_time' => [
                'sql' => "ALTER TABLE watch_history ADD COLUMN total_time INT DEFAULT 0 AFTER watch_time",
                'check' => "INT DEFAULT 0"
            ],
            'duration' => [
                'sql' => "ALTER TABLE watch_history ADD COLUMN duration INT DEFAULT 0 AFTER total_time",
                'check' => "INT DEFAULT 0"
            ],
            'created_at' => [
                'sql' => "ALTER TABLE watch_history ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP AFTER duration",
                'check' => "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"
            ],
            'updated_at' => [
                'sql' => "ALTER TABLE watch_history ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP AFTER created_at",
                'check' => "TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"
            ],
            'last_watched' => [
                'sql' => "ALTER TABLE watch_history ADD COLUMN last_watched TIMESTAMP NULL AFTER updated_at",
                'check' => "TIMESTAMP NULL"
            ]
        ];
        
        echo "<h4>🔧 إضافة الأعمدة المفقودة...</h4>";
        
        foreach ($requiredColumns as $column => $config) {
            if (!in_array($column, $columns)) {
                try {
                    $pdo->exec($config['sql']);
                    $fixedIssues[] = "تم إضافة عمود '$column' إلى جدول watch_history";
                    echo "<p style='color: green;'>✅ تم إضافة عمود: <strong>$column</strong></p>";
                } catch (Exception $e) {
                    $errors[] = "فشل في إضافة عمود '$column': " . $e->getMessage();
                    echo "<p style='color: red;'>❌ فشل في إضافة عمود: <strong>$column</strong> - {$e->getMessage()}</p>";
                }
            } else {
                echo "<p style='color: blue;'>ℹ️ العمود موجود بالفعل: <strong>$column</strong></p>";
            }
        }
        
    } catch (Exception $e) {
        if (strpos($e->getMessage(), "doesn't exist") !== false) {
            // إنشاء جدول watch_history من الصفر
            echo "<h4>🆕 إنشاء جدول watch_history جديد...</h4>";
            
            $createTableSQL = "
            CREATE TABLE watch_history (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                movie_id INT NULL,
                series_id INT NULL,
                content_id INT NULL,
                content_type VARCHAR(20) NULL,
                watch_time INT DEFAULT 0,
                total_time INT DEFAULT 0,
                duration INT DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                last_watched TIMESTAMP NULL,
                INDEX idx_user_id (user_id),
                INDEX idx_movie_id (movie_id),
                INDEX idx_series_id (series_id),
                INDEX idx_content_id (content_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ";
            
            try {
                $pdo->exec($createTableSQL);
                $fixedIssues[] = "تم إنشاء جدول watch_history جديد بجميع الأعمدة المطلوبة";
                echo "<p style='color: green;'>✅ تم إنشاء جدول watch_history بنجاح!</p>";
            } catch (Exception $e) {
                $errors[] = "فشل في إنشاء جدول watch_history: " . $e->getMessage();
                echo "<p style='color: red;'>❌ فشل في إنشاء الجدول: {$e->getMessage()}</p>";
            }
        } else {
            $errors[] = "خطأ في فحص جدول watch_history: " . $e->getMessage();
        }
    }
    
    // 2. إنشاء فهارس للأداء
    echo "<h3>📈 إنشاء فهارس الأداء...</h3>";
    
    $indexes = [
        "CREATE INDEX IF NOT EXISTS idx_watch_history_user ON watch_history(user_id)",
        "CREATE INDEX IF NOT EXISTS idx_watch_history_movie ON watch_history(movie_id)",
        "CREATE INDEX IF NOT EXISTS idx_watch_history_series ON watch_history(series_id)",
        "CREATE INDEX IF NOT EXISTS idx_watch_history_content ON watch_history(content_id)",
        "CREATE INDEX IF NOT EXISTS idx_watch_history_created ON watch_history(created_at)",
        "CREATE INDEX IF NOT EXISTS idx_watch_history_updated ON watch_history(updated_at)"
    ];
    
    foreach ($indexes as $index) {
        try {
            $pdo->exec($index);
            echo "<p style='color: green;'>✅ تم إنشاء فهرس</p>";
        } catch (Exception $e) {
            // الفهرس موجود بالفعل أو خطأ آخر
            echo "<p style='color: blue;'>ℹ️ الفهرس موجود بالفعل أو تم تخطيه</p>";
        }
    }
    $fixedIssues[] = "تم إنشاء جميع الفهارس المطلوبة";
    
    // 3. إضافة بيانات تجريبية
    echo "<h3>📝 إضافة بيانات تجريبية...</h3>";
    
    try {
        // فحص وجود بيانات
        $stmt = $pdo->query("SELECT COUNT(*) FROM watch_history");
        $count = $stmt->fetchColumn();
        
        if ($count == 0) {
            $sampleData = [
                [1, 1, null, 1, 'movie', 45, 120, 120],
                [1, 2, null, 2, 'movie', 30, 95, 95],
                [1, null, 1, 1, 'series', 20, 45, 45],
                [1, null, 2, 2, 'series', 35, 45, 45]
            ];
            
            $stmt = $pdo->prepare("
                INSERT INTO watch_history 
                (user_id, movie_id, series_id, content_id, content_type, watch_time, total_time, duration, created_at) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())
            ");
            
            foreach ($sampleData as $data) {
                $stmt->execute($data);
            }
            
            $fixedIssues[] = "تم إضافة بيانات تجريبية إلى جدول watch_history";
            echo "<p style='color: green;'>✅ تم إضافة " . count($sampleData) . " سجل تجريبي</p>";
        } else {
            echo "<p style='color: blue;'>ℹ️ الجدول يحتوي على $count سجل بالفعل</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: orange;'>⚠️ تم تخطي البيانات التجريبية: {$e->getMessage()}</p>";
    }
    
    // 4. فحص الجدول النهائي
    echo "<h3>🔍 فحص البنية النهائية...</h3>";
    
    try {
        $stmt = $pdo->query("DESCRIBE watch_history");
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h4>📋 البنية النهائية لجدول watch_history:</h4>";
        echo "<table style='width: 100%; border-collapse: collapse;'>";
        echo "<tr style='background: rgba(0,0,0,0.1);'><th style='padding: 8px; border: 1px solid #ccc;'>العمود</th><th style='padding: 8px; border: 1px solid #ccc;'>النوع</th><th style='padding: 8px; border: 1px solid #ccc;'>NULL</th><th style='padding: 8px; border: 1px solid #ccc;'>افتراضي</th></tr>";
        
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            echo "<tr>";
            echo "<td style='padding: 8px; border: 1px solid #ccc;'><strong>{$row['Field']}</strong></td>";
            echo "<td style='padding: 8px; border: 1px solid #ccc;'>{$row['Type']}</td>";
            echo "<td style='padding: 8px; border: 1px solid #ccc;'>{$row['Null']}</td>";
            echo "<td style='padding: 8px; border: 1px solid #ccc;'>{$row['Default']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        echo "</div>";
        
    } catch (Exception $e) {
        $errors[] = "فشل في فحص البنية النهائية: " . $e->getMessage();
    }
    
    echo "<br><h2>🎉 تم إصلاح بنية قاعدة البيانات!</h2>";
    
    if (!empty($fixedIssues)) {
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h3>✅ الإصلاحات التي تمت:</h3>";
        echo "<ul>";
        foreach ($fixedIssues as $issue) {
            echo "<li>$issue</li>";
        }
        echo "</ul>";
        echo "</div>";
    }
    
    if (!empty($errors)) {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h3>⚠️ مشاكل تحتاج انتباه:</h3>";
        echo "<ul>";
        foreach ($errors as $error) {
            echo "<li>$error</li>";
        }
        echo "</ul>";
        echo "</div>";
    }
    
    echo "<div style='background: #cce5ff; border: 1px solid #99ccff; color: #004085; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>📋 الخطوات التالية:</h3>";
    echo "<ol>";
    echo "<li>جرب مشغل الفيديو مرة أخرى</li>";
    echo "<li>تأكد من عدم وجود أخطاء في قاعدة البيانات</li>";
    echo "<li>اختبر تسجيل المشاهدات</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<div style='text-align: center; margin: 2rem 0;'>";
    echo "<a href='streaming/video_player.php?id=1&type=movie' style='background: #E50914; color: white; padding: 1rem 2rem; text-decoration: none; border-radius: 8px; font-weight: bold; margin: 0.5rem;'>🎬 اختبار مشغل الفيديو الأصلي</a>";
    echo "<a href='streaming/fixed_video_player.php?id=1&type=movie' style='background: #28a745; color: white; padding: 1rem 2rem; text-decoration: none; border-radius: 8px; font-weight: bold; margin: 0.5rem;'>🎬 اختبار مشغل الفيديو المحدث</a>";
    echo "<a href='admin/simple_dashboard.php' style='background: #007bff; color: white; padding: 1rem 2rem; text-decoration: none; border-radius: 8px; font-weight: bold; margin: 0.5rem;'>🎛️ لوحة الإدارة</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px;'>";
    echo "<h3>❌ خطأ في الإصلاح:</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "<p><strong>تفاصيل الخطأ:</strong></p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
    echo "</div>";
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح بنية قاعدة البيانات</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #2d2d2d 100%);
            color: #fff;
            min-height: 100vh;
            padding: 2rem;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(47, 47, 47, 0.95);
            border-radius: 20px;
            padding: 3rem;
            border: 2px solid rgba(229, 9, 20, 0.3);
        }
        h1, h2, h3, h4 { color: #E50914; }
        ul { margin: 1rem 0; padding-right: 2rem; }
        ol { margin: 1rem 0; padding-right: 2rem; }
        li { margin: 0.5rem 0; }
        table { margin: 1rem 0; }
        th, td { text-align: right; }
        pre {
            background: rgba(0, 0, 0, 0.3);
            padding: 1rem;
            border-radius: 8px;
            overflow-x: auto;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- المحتوى يتم عرضه من PHP أعلاه -->
    </div>
</body>
</html>
