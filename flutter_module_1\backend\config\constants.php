<?php
/**
 * ثوابت النظام الشاملة - Shahid Platform Constants
 * يحتوي على جميع الثوابت المستخدمة في النظام
 */

// منع الوصول المباشر
if (!defined('SHAHID_PLATFORM')) {
    die('Access Denied');
}

// ===== معلومات التطبيق الأساسية =====
define('APP_NAME', 'Shahid Platform');
define('APP_VERSION', '2.0.0');
define('APP_DESCRIPTION', 'منصة البث الاحترافية الكاملة');
define('APP_AUTHOR', 'Shahid Development Team');
define('APP_URL', 'http://localhost/amr2/flutter_module_1');
define('APP_EMAIL', '<EMAIL>');

// ===== إعدادات قاعدة البيانات =====
define('DB_HOST', 'localhost');
define('DB_NAME', 'shahid_platform');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_CHARSET', 'utf8mb4');
define('DB_PREFIX', 'shahid_');

// ===== مسارات النظام =====
define('ROOT_PATH', dirname(__DIR__));
define('CONFIG_PATH', ROOT_PATH . '/config');
define('CONTROLLERS_PATH', ROOT_PATH . '/controllers');
define('MODELS_PATH', ROOT_PATH . '/models');
define('VIEWS_PATH', ROOT_PATH . '/views');
define('CORE_PATH', ROOT_PATH . '/core');
define('API_PATH', ROOT_PATH . '/api');
define('ADMIN_PATH', ROOT_PATH . '/admin');
define('ASSETS_PATH', ROOT_PATH . '/assets');
define('UPLOADS_PATH', ROOT_PATH . '/uploads');
define('LOGS_PATH', ROOT_PATH . '/logs');
define('TEMP_PATH', ROOT_PATH . '/temp');
define('BACKUPS_PATH', ROOT_PATH . '/backups');

// ===== URLs الأساسية =====
define('BASE_URL', 'http://localhost/amr2/flutter_module_1/backend');
define('API_URL', BASE_URL . '/api');
define('ADMIN_URL', BASE_URL . '/admin');
define('ASSETS_URL', BASE_URL . '/assets');
define('UPLOADS_URL', BASE_URL . '/uploads');

// ===== إعدادات الأمان =====
define('SECURITY_SALT', 'shahid_platform_2024_secure_salt_key');
define('JWT_SECRET', 'shahid_jwt_secret_key_2024');
define('CSRF_TOKEN_NAME', 'shahid_csrf_token');
define('SESSION_NAME', 'SHAHID_SESSION');
define('COOKIE_PREFIX', 'shahid_');
define('PASSWORD_MIN_LENGTH', 8);
define('MAX_LOGIN_ATTEMPTS', 5);
define('LOGIN_LOCKOUT_TIME', 900); // 15 دقيقة

// ===== إعدادات الجلسة =====
define('SESSION_LIFETIME', 7200); // ساعتان
define('SESSION_REGENERATE_TIME', 300); // 5 دقائق
define('REMEMBER_ME_TIME', 2592000); // 30 يوم

// ===== إعدادات رفع الملفات =====
define('MAX_FILE_SIZE', 100 * 1024 * 1024); // 100 MB
define('ALLOWED_IMAGE_TYPES', 'jpg,jpeg,png,gif,webp');
define('ALLOWED_VIDEO_TYPES', 'mp4,avi,mkv,mov,wmv');
define('ALLOWED_DOCUMENT_TYPES', 'pdf,doc,docx,txt');
define('IMAGE_MAX_WIDTH', 1920);
define('IMAGE_MAX_HEIGHT', 1080);
define('IMAGE_QUALITY', 85);

// ===== إعدادات التخزين المؤقت =====
define('CACHE_ENABLED', true);
define('CACHE_LIFETIME', 3600); // ساعة واحدة
define('CACHE_PATH', ROOT_PATH . '/cache');

// ===== إعدادات البريد الإلكتروني =====
define('MAIL_HOST', 'smtp.gmail.com');
define('MAIL_PORT', 587);
define('MAIL_USERNAME', '<EMAIL>');
define('MAIL_PASSWORD', 'your-app-password');
define('MAIL_FROM_EMAIL', '<EMAIL>');
define('MAIL_FROM_NAME', 'Shahid Platform');
define('MAIL_ENCRYPTION', 'tls');

// ===== إعدادات API =====
define('API_VERSION', 'v1');
define('API_RATE_LIMIT', 1000); // طلب في الساعة
define('API_RATE_WINDOW', 3600); // نافزة زمنية بالثواني
define('API_KEY_LENGTH', 32);
define('API_TOKEN_LIFETIME', 86400); // 24 ساعة

// ===== إعدادات المحتوى =====
define('MOVIES_PER_PAGE', 20);
define('SERIES_PER_PAGE', 15);
define('EPISODES_PER_PAGE', 30);
define('SEARCH_MIN_LENGTH', 2);
define('SEARCH_MAX_RESULTS', 50);
define('RATING_SCALE_MAX', 10);
define('COMMENT_MAX_LENGTH', 1000);

// ===== إعدادات الإشعارات =====
define('NOTIFICATION_TYPES', [
    'info' => 'معلومات',
    'success' => 'نجاح',
    'warning' => 'تحذير',
    'error' => 'خطأ'
]);

// ===== أنواع المحتوى =====
define('CONTENT_TYPES', [
    'movie' => 'فيلم',
    'series' => 'مسلسل',
    'episode' => 'حلقة',
    'documentary' => 'وثائقي',
    'animation' => 'رسوم متحركة'
]);

// ===== تصنيفات المحتوى =====
define('CONTENT_CATEGORIES', [
    'action' => 'أكشن',
    'comedy' => 'كوميديا',
    'drama' => 'دراما',
    'horror' => 'رعب',
    'romance' => 'رومانسي',
    'thriller' => 'إثارة',
    'sci-fi' => 'خيال علمي',
    'fantasy' => 'فانتازيا',
    'adventure' => 'مغامرة',
    'crime' => 'جريمة',
    'mystery' => 'غموض',
    'war' => 'حرب',
    'history' => 'تاريخي',
    'biography' => 'سيرة ذاتية',
    'sport' => 'رياضي',
    'music' => 'موسيقي',
    'family' => 'عائلي',
    'kids' => 'أطفال'
]);

// ===== أنواع المستخدمين =====
define('USER_ROLES', [
    'admin' => 'مدير',
    'moderator' => 'مشرف',
    'premium' => 'مميز',
    'user' => 'مستخدم عادي',
    'guest' => 'زائر'
]);

// ===== حالات المحتوى =====
define('CONTENT_STATUS', [
    'active' => 'نشط',
    'inactive' => 'غير نشط',
    'pending' => 'في الانتظار',
    'draft' => 'مسودة',
    'archived' => 'مؤرشف'
]);

// ===== جودات الفيديو =====
define('VIDEO_QUALITIES', [
    '240p' => '240p',
    '360p' => '360p',
    '480p' => '480p',
    '720p' => '720p HD',
    '1080p' => '1080p Full HD',
    '1440p' => '1440p 2K',
    '2160p' => '2160p 4K'
]);

// ===== اللغات المدعومة =====
define('SUPPORTED_LANGUAGES', [
    'ar' => 'العربية',
    'en' => 'English',
    'fr' => 'Français',
    'es' => 'Español',
    'de' => 'Deutsch',
    'tr' => 'Türkçe'
]);

// ===== إعدادات SEO =====
define('SEO_TITLE_MAX_LENGTH', 60);
define('SEO_DESCRIPTION_MAX_LENGTH', 160);
define('SEO_KEYWORDS_MAX_COUNT', 10);
define('SITEMAP_MAX_URLS', 50000);

// ===== إعدادات التحليلات =====
define('ANALYTICS_ENABLED', true);
define('GOOGLE_ANALYTICS_ID', 'G-XXXXXXXXXX');
define('ANALYTICS_DATA_RETENTION', 90); // أيام

// ===== إعدادات النسخ الاحتياطي =====
define('BACKUP_MAX_COUNT', 10);
define('BACKUP_COMPRESSION', true);
define('BACKUP_INCLUDE_FILES', true);
define('BACKUP_INCLUDE_DATABASE', true);

// ===== إعدادات الأداء =====
define('PERFORMANCE_MONITORING', true);
define('SLOW_QUERY_THRESHOLD', 0.1); // ثانية
define('MEMORY_LIMIT_WARNING', 80); // نسبة مئوية
define('DISK_SPACE_WARNING', 85); // نسبة مئوية

// ===== إعدادات التطوير =====
define('DEBUG_MODE', true);
define('ERROR_REPORTING_LEVEL', E_ALL);
define('DISPLAY_ERRORS', true);
define('LOG_ERRORS', true);
define('LOG_QUERIES', true);

// ===== إعدادات الإنتاج =====
define('PRODUCTION_MODE', false);
define('MINIFY_CSS', false);
define('MINIFY_JS', false);
define('GZIP_COMPRESSION', true);

// ===== رسائل النظام =====
define('MESSAGES', [
    'success' => [
        'login' => 'تم تسجيل الدخول بنجاح',
        'logout' => 'تم تسجيل الخروج بنجاح',
        'register' => 'تم إنشاء الحساب بنجاح',
        'update' => 'تم التحديث بنجاح',
        'delete' => 'تم الحذف بنجاح',
        'upload' => 'تم رفع الملف بنجاح'
    ],
    'error' => [
        'login' => 'خطأ في اسم المستخدم أو كلمة المرور',
        'permission' => 'ليس لديك صلاحية للوصول',
        'not_found' => 'العنصر المطلوب غير موجود',
        'server' => 'خطأ في الخادم، يرجى المحاولة لاحقاً',
        'validation' => 'يرجى التحقق من البيانات المدخلة'
    ],
    'warning' => [
        'session_expired' => 'انتهت صلاحية الجلسة، يرجى تسجيل الدخول مرة أخرى',
        'maintenance' => 'الموقع تحت الصيانة حالياً',
        'quota_exceeded' => 'تم تجاوز الحد المسموح'
    ]
]);

// ===== إعدادات التاريخ والوقت =====
define('DEFAULT_TIMEZONE', 'Asia/Riyadh');
define('DATE_FORMAT', 'Y-m-d');
define('TIME_FORMAT', 'H:i:s');
define('DATETIME_FORMAT', 'Y-m-d H:i:s');
define('DISPLAY_DATE_FORMAT', 'd/m/Y');
define('DISPLAY_TIME_FORMAT', 'h:i A');

// ===== إعدادات العملة =====
define('DEFAULT_CURRENCY', 'SAR');
define('CURRENCY_SYMBOL', 'ر.س');
define('CURRENCY_POSITION', 'after'); // before أو after

// ===== إعدادات الاشتراكات =====
define('SUBSCRIPTION_PLANS', [
    'basic' => [
        'name' => 'أساسي',
        'price' => 29.99,
        'duration' => 30, // أيام
        'features' => ['جودة عادية', 'جهاز واحد', 'إعلانات']
    ],
    'premium' => [
        'name' => 'مميز',
        'price' => 49.99,
        'duration' => 30,
        'features' => ['جودة عالية', '3 أجهزة', 'بدون إعلانات']
    ],
    'vip' => [
        'name' => 'VIP',
        'price' => 79.99,
        'duration' => 30,
        'features' => ['جودة 4K', '5 أجهزة', 'محتوى حصري']
    ]
]);

// ===== إعدادات الصيانة =====
define('MAINTENANCE_MODE', false);
define('MAINTENANCE_MESSAGE', 'الموقع تحت الصيانة. سنعود قريباً!');
define('MAINTENANCE_ALLOWED_IPS', ['127.0.0.1', '::1']);

// ===== إعدادات متنوعة =====
define('ITEMS_PER_PAGE_OPTIONS', [10, 20, 50, 100]);
define('MAX_SEARCH_HISTORY', 10);
define('MAX_WATCH_HISTORY', 100);
define('MAX_FAVORITES', 500);
define('TRENDING_PERIOD_DAYS', 7);
define('POPULAR_THRESHOLD', 1000); // عدد المشاهدات

// تعيين المنطقة الزمنية
date_default_timezone_set(DEFAULT_TIMEZONE);

// تعيين الترميز
mb_internal_encoding('UTF-8');

// إعدادات PHP
ini_set('memory_limit', '256M');
ini_set('max_execution_time', 300);
ini_set('upload_max_filesize', '100M');
ini_set('post_max_size', '100M');

// تعريف ثابت للتحقق من التحميل الصحيح
define('CONSTANTS_LOADED', true);
?>
