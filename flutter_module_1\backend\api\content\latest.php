<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// معالجة طلبات OPTIONS
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => 'طريقة الطلب غير مدعومة',
        'error_code' => 'METHOD_NOT_ALLOWED'
    ]);
    exit;
}

try {
    // الاتصال بقاعدة البيانات
    $pdo = new PDO("mysql:host=localhost;dbname=shahid_platform;charset=utf8mb4", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // معاملات الاستعلام
    $limit = isset($_GET['limit']) ? min(max(1, intval($_GET['limit'])), 50) : 20;
    $offset = isset($_GET['offset']) ? max(0, intval($_GET['offset'])) : 0;
    $type = isset($_GET['type']) ? $_GET['type'] : 'all'; // all, movies, series
    $category = isset($_GET['category']) ? intval($_GET['category']) : null;
    
    // التحقق من رمز الوصول (اختياري)
    $userId = null;
    $userSubscription = 'free';
    
    if (isset($_SERVER['HTTP_AUTHORIZATION'])) {
        $token = str_replace('Bearer ', '', $_SERVER['HTTP_AUTHORIZATION']);
        $payload = json_decode(base64_decode($token), true);
        
        if ($payload && isset($payload['user_id']) && $payload['expires_at'] > time()) {
            $userId = $payload['user_id'];
            $userSubscription = $payload['subscription_type'] ?? 'free';
        }
    }
    
    // بناء الاستعلام حسب النوع
    $movies = [];
    $series = [];
    
    if ($type === 'all' || $type === 'movies') {
        $movieQuery = "
            SELECT 
                m.id,
                m.title,
                m.description,
                m.poster,
                m.video_url,
                m.release_year,
                m.rating,
                m.views,
                m.duration,
                m.created_at,
                c.name as category_name,
                c.id as category_id
            FROM movies m
            LEFT JOIN categories c ON m.category_id = c.id
            WHERE m.status = 'active'
        ";
        
        $params = [];
        
        if ($category) {
            $movieQuery .= " AND m.category_id = ?";
            $params[] = $category;
        }
        
        $movieQuery .= " ORDER BY m.created_at DESC LIMIT ? OFFSET ?";
        $params[] = $limit;
        $params[] = $offset;
        
        $stmt = $pdo->prepare($movieQuery);
        $stmt->execute($params);
        $movies = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // إضافة معلومات إضافية للأفلام
        foreach ($movies as &$movie) {
            $movie['type'] = 'movie';
            $movie['is_premium'] = $movie['rating'] > 8.0; // افتراض أن الأفلام عالية التقييم مدفوعة
            $movie['can_watch'] = !$movie['is_premium'] || $userSubscription !== 'free';
            
            // إخفاء رابط الفيديو للمحتوى المدفوع
            if ($movie['is_premium'] && $userSubscription === 'free') {
                $movie['video_url'] = null;
            }
            
            // تنسيق التاريخ
            $movie['created_at'] = date('Y-m-d H:i:s', strtotime($movie['created_at']));
        }
    }
    
    if ($type === 'all' || $type === 'series') {
        $seriesQuery = "
            SELECT 
                s.id,
                s.title,
                s.description,
                s.poster,
                s.release_year,
                s.rating,
                s.views,
                s.total_seasons,
                s.created_at,
                c.name as category_name,
                c.id as category_id,
                COUNT(e.id) as episodes_count
            FROM series s
            LEFT JOIN categories c ON s.category_id = c.id
            LEFT JOIN episodes e ON s.id = e.series_id AND e.status = 'active'
            WHERE s.status = 'active'
        ";
        
        $params = [];
        
        if ($category) {
            $seriesQuery .= " AND s.category_id = ?";
            $params[] = $category;
        }
        
        $seriesQuery .= " GROUP BY s.id ORDER BY s.created_at DESC LIMIT ? OFFSET ?";
        $params[] = $limit;
        $params[] = $offset;
        
        $stmt = $pdo->prepare($seriesQuery);
        $stmt->execute($params);
        $series = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // إضافة معلومات إضافية للمسلسلات
        foreach ($series as &$serie) {
            $serie['type'] = 'series';
            $serie['is_premium'] = $serie['rating'] > 8.0;
            $serie['can_watch'] = !$serie['is_premium'] || $userSubscription !== 'free';
            
            // تنسيق التاريخ
            $serie['created_at'] = date('Y-m-d H:i:s', strtotime($serie['created_at']));
        }
    }
    
    // دمج النتائج إذا كان النوع "all"
    $content = [];
    if ($type === 'all') {
        $content = array_merge($movies, $series);
        // ترتيب حسب تاريخ الإنشاء
        usort($content, function($a, $b) {
            return strtotime($b['created_at']) - strtotime($a['created_at']);
        });
        // تطبيق الحد
        $content = array_slice($content, 0, $limit);
    } elseif ($type === 'movies') {
        $content = $movies;
    } elseif ($type === 'series') {
        $content = $series;
    }
    
    // الحصول على إجمالي العدد للصفحات
    $totalQuery = "SELECT ";
    if ($type === 'all') {
        $totalQuery .= "(SELECT COUNT(*) FROM movies WHERE status = 'active'";
        if ($category) $totalQuery .= " AND category_id = $category";
        $totalQuery .= ") + (SELECT COUNT(*) FROM series WHERE status = 'active'";
        if ($category) $totalQuery .= " AND category_id = $category";
        $totalQuery .= ") as total";
    } elseif ($type === 'movies') {
        $totalQuery .= "COUNT(*) as total FROM movies WHERE status = 'active'";
        if ($category) $totalQuery .= " AND category_id = $category";
    } elseif ($type === 'series') {
        $totalQuery .= "COUNT(*) as total FROM series WHERE status = 'active'";
        if ($category) $totalQuery .= " AND category_id = $category";
    }
    
    $totalResult = $pdo->query($totalQuery)->fetch(PDO::FETCH_ASSOC);
    $total = $totalResult['total'];
    
    // الحصول على التصنيفات
    $categories = $pdo->query("
        SELECT id, name, icon 
        FROM categories 
        WHERE status = 'active' 
        ORDER BY sort_order ASC, name ASC
    ")->fetchAll(PDO::FETCH_ASSOC);
    
    // إعداد الاستجابة
    $response = [
        'success' => true,
        'message' => 'تم جلب المحتوى بنجاح',
        'data' => [
            'content' => $content,
            'pagination' => [
                'total' => intval($total),
                'limit' => $limit,
                'offset' => $offset,
                'has_more' => ($offset + $limit) < $total
            ],
            'categories' => $categories,
            'user_info' => $userId ? [
                'user_id' => $userId,
                'subscription_type' => $userSubscription,
                'can_access_premium' => $userSubscription !== 'free'
            ] : null
        ]
    ];
    
    http_response_code(200);
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    
} catch (PDOException $e) {
    error_log("Database error in latest content API: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'خطأ في الخادم',
        'error_code' => 'DATABASE_ERROR'
    ]);
    
} catch (Exception $e) {
    error_log("General error in latest content API: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'خطأ غير متوقع',
        'error_code' => 'GENERAL_ERROR'
    ]);
}
?>
