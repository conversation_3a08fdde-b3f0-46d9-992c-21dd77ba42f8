<?php
session_start();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

try {
    $pdo = new PDO("mysql:host=localhost;dbname=shahid_platform;charset=utf8mb4", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // الحصول على معلومات المستخدم
    $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // الحصول على خطط الاشتراك
    $stmt = $pdo->query("SELECT * FROM subscriptions WHERE status = 'active' ORDER BY price ASC");
    $subscriptions = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // الحصول على اشتراك المستخدم الحالي
    $stmt = $pdo->prepare("
        SELECT us.*, s.name as plan_name, s.price, s.duration_days 
        FROM user_subscriptions us 
        JOIN subscriptions s ON us.subscription_id = s.id 
        WHERE us.user_id = ? AND us.status = 'active' 
        ORDER BY us.end_date DESC 
        LIMIT 1
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $currentSubscription = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // معالجة طلب الاشتراك
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['subscribe'])) {
        $subscriptionId = $_POST['subscription_id'];
        $paymentMethod = $_POST['payment_method'];
        
        // التحقق من صحة البيانات
        $stmt = $pdo->prepare("SELECT * FROM subscriptions WHERE id = ? AND status = 'active'");
        $stmt->execute([$subscriptionId]);
        $subscription = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($subscription) {
            // إنهاء الاشتراك الحالي إن وجد
            $pdo->prepare("UPDATE user_subscriptions SET status = 'cancelled' WHERE user_id = ? AND status = 'active'")->execute([$_SESSION['user_id']]);
            
            // إنشاء اشتراك جديد
            $startDate = date('Y-m-d H:i:s');
            $endDate = date('Y-m-d H:i:s', strtotime("+{$subscription['duration_days']} days"));
            
            $stmt = $pdo->prepare("
                INSERT INTO user_subscriptions (user_id, subscription_id, start_date, end_date, status, payment_method) 
                VALUES (?, ?, ?, ?, 'active', ?)
            ");
            $stmt->execute([$_SESSION['user_id'], $subscriptionId, $startDate, $endDate, $paymentMethod]);
            
            // تحديث نوع اشتراك المستخدم
            $subscriptionType = strtolower($subscription['name']);
            if (strpos($subscriptionType, 'premium') !== false) {
                $subscriptionType = 'premium';
            } elseif (strpos($subscriptionType, 'vip') !== false) {
                $subscriptionType = 'vip';
            } else {
                $subscriptionType = 'free';
            }
            
            $pdo->prepare("UPDATE users SET subscription_type = ? WHERE id = ?")->execute([$subscriptionType, $_SESSION['user_id']]);
            
            // تسجيل عملية الدفع
            $pdo->prepare("
                INSERT INTO payments (user_id, subscription_id, amount, payment_method, status, transaction_id) 
                VALUES (?, ?, ?, ?, 'completed', ?)
            ")->execute([$_SESSION['user_id'], $subscriptionId, $subscription['price'], $paymentMethod, 'TXN_' . time()]);
            
            $success = "تم تفعيل اشتراكك بنجاح!";
            
            // إعادة تحميل بيانات الاشتراك الحالي
            $stmt = $pdo->prepare("
                SELECT us.*, s.name as plan_name, s.price, s.duration_days 
                FROM user_subscriptions us 
                JOIN subscriptions s ON us.subscription_id = s.id 
                WHERE us.user_id = ? AND us.status = 'active' 
                ORDER BY us.end_date DESC 
                LIMIT 1
            ");
            $stmt->execute([$_SESSION['user_id']]);
            $currentSubscription = $stmt->fetch(PDO::FETCH_ASSOC);
        } else {
            $error = "خطة الاشتراك غير صحيحة";
        }
    }
    
} catch (Exception $e) {
    $error = "حدث خطأ: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>خطط الاشتراك - Shahid Platform</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', 'Cairo', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #2d2d2d 100%);
            color: #fff;
            min-height: 100vh;
        }
        
        .header {
            background: rgba(47, 47, 47, 0.95);
            padding: 1rem 0;
            border-bottom: 2px solid rgba(229, 9, 20, 0.3);
            position: sticky;
            top: 0;
            z-index: 1000;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }
        
        .nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            color: #E50914;
            font-size: 1.8rem;
            font-weight: bold;
            text-decoration: none;
        }
        
        .nav-links {
            display: flex;
            gap: 2rem;
            list-style: none;
        }
        
        .nav-links a {
            color: #fff;
            text-decoration: none;
            transition: color 0.3s ease;
        }
        
        .nav-links a:hover {
            color: #E50914;
        }
        
        .main-content {
            padding: 3rem 0;
        }
        
        .page-header {
            text-align: center;
            margin-bottom: 3rem;
        }
        
        .page-title {
            font-size: 2.5rem;
            color: #E50914;
            margin-bottom: 1rem;
        }
        
        .page-subtitle {
            font-size: 1.2rem;
            color: #ccc;
        }
        
        .current-subscription {
            background: rgba(47, 47, 47, 0.8);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 3rem;
            border: 2px solid rgba(229, 9, 20, 0.3);
        }
        
        .subscription-plans {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }
        
        .plan-card {
            background: rgba(47, 47, 47, 0.8);
            border-radius: 15px;
            padding: 2rem;
            text-align: center;
            transition: all 0.3s ease;
            border: 2px solid rgba(255, 255, 255, 0.1);
            position: relative;
            overflow: hidden;
        }
        
        .plan-card:hover {
            transform: translateY(-10px);
            border-color: rgba(229, 9, 20, 0.5);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }
        
        .plan-card.featured {
            border-color: rgba(229, 9, 20, 0.5);
            background: rgba(229, 9, 20, 0.1);
        }
        
        .plan-card.featured::before {
            content: 'الأكثر شعبية';
            position: absolute;
            top: 1rem;
            right: -2rem;
            background: #E50914;
            color: white;
            padding: 0.5rem 3rem;
            transform: rotate(45deg);
            font-size: 0.8rem;
            font-weight: bold;
        }
        
        .plan-name {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 1rem;
            color: #E50914;
        }
        
        .plan-price {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        
        .plan-duration {
            color: #ccc;
            margin-bottom: 2rem;
        }
        
        .plan-features {
            list-style: none;
            margin-bottom: 2rem;
            text-align: right;
        }
        
        .plan-features li {
            padding: 0.5rem 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .plan-features li:last-child {
            border-bottom: none;
        }
        
        .plan-features i {
            color: #4CAF50;
            margin-left: 0.5rem;
        }
        
        .btn {
            background: linear-gradient(45deg, #E50914, #B8070F);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            width: 100%;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(229, 9, 20, 0.3);
        }
        
        .btn:disabled {
            background: #666;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        .payment-methods {
            background: rgba(47, 47, 47, 0.8);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
        }
        
        .payment-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }
        
        .payment-method {
            background: rgba(255, 255, 255, 0.05);
            border: 2px solid rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 1rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .payment-method:hover,
        .payment-method.selected {
            border-color: #E50914;
            background: rgba(229, 9, 20, 0.1);
        }
        
        .payment-method input {
            display: none;
        }
        
        .payment-method i {
            font-size: 2rem;
            margin-bottom: 0.5rem;
            color: #E50914;
        }
        
        .alert {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .alert-success {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid rgba(76, 175, 80, 0.5);
            color: #4CAF50;
        }
        
        .alert-error {
            background: rgba(244, 67, 54, 0.2);
            border: 1px solid rgba(244, 67, 54, 0.5);
            color: #F44336;
        }
        
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            z-index: 2000;
        }
        
        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(47, 47, 47, 0.95);
            border-radius: 15px;
            padding: 2rem;
            max-width: 500px;
            width: 90%;
            border: 2px solid rgba(229, 9, 20, 0.3);
        }
        
        .modal-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .modal-title {
            color: #E50914;
            font-size: 1.5rem;
            margin-bottom: 1rem;
        }
        
        .close {
            position: absolute;
            top: 1rem;
            left: 1rem;
            background: none;
            border: none;
            color: #ccc;
            font-size: 1.5rem;
            cursor: pointer;
        }
        
        @media (max-width: 768px) {
            .subscription-plans {
                grid-template-columns: 1fr;
            }
            
            .payment-grid {
                grid-template-columns: 1fr;
            }
            
            .nav {
                flex-direction: column;
                gap: 1rem;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="container">
            <nav class="nav">
                <a href="index.php" class="logo">
                    <i class="fas fa-play-circle"></i> شاهد
                </a>
                <ul class="nav-links">
                    <li><a href="index.php">الرئيسية</a></li>
                    <li><a href="movies.php">الأفلام</a></li>
                    <li><a href="series.php">المسلسلات</a></li>
                    <li><a href="profile.php">حسابي</a></li>
                    <li><a href="logout.php">تسجيل الخروج</a></li>
                </ul>
            </nav>
        </div>
    </header>
    
    <main class="main-content">
        <div class="container">
            <div class="page-header">
                <h1 class="page-title">خطط الاشتراك</h1>
                <p class="page-subtitle">اختر الخطة المناسبة لك واستمتع بمشاهدة غير محدودة</p>
            </div>
            
            <?php if (isset($success)): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i> <?php echo $success; ?>
                </div>
            <?php endif; ?>
            
            <?php if (isset($error)): ?>
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-circle"></i> <?php echo $error; ?>
                </div>
            <?php endif; ?>
            
            <?php if ($currentSubscription): ?>
                <div class="current-subscription">
                    <h3 style="color: #E50914; margin-bottom: 1rem;">
                        <i class="fas fa-crown"></i> اشتراكك الحالي
                    </h3>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                        <div>
                            <strong>الخطة:</strong> <?php echo htmlspecialchars($currentSubscription['plan_name']); ?>
                        </div>
                        <div>
                            <strong>تاريخ البداية:</strong> <?php echo date('Y-m-d', strtotime($currentSubscription['start_date'])); ?>
                        </div>
                        <div>
                            <strong>تاريخ الانتهاء:</strong> <?php echo date('Y-m-d', strtotime($currentSubscription['end_date'])); ?>
                        </div>
                        <div>
                            <strong>الحالة:</strong> 
                            <span style="color: #4CAF50;">
                                <i class="fas fa-check-circle"></i> نشط
                            </span>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
            
            <div class="subscription-plans">
                <?php foreach ($subscriptions as $index => $subscription): ?>
                    <div class="plan-card <?php echo $index === 1 ? 'featured' : ''; ?>">
                        <h3 class="plan-name"><?php echo htmlspecialchars($subscription['name']); ?></h3>
                        <div class="plan-price">
                            <?php echo number_format($subscription['price'], 2); ?> ر.س
                        </div>
                        <div class="plan-duration">
                            لمدة <?php echo $subscription['duration_days']; ?> يوم
                        </div>
                        
                        <ul class="plan-features">
                            <?php 
                            $features = json_decode($subscription['features'], true) ?: [];
                            foreach ($features as $feature): 
                            ?>
                                <li><i class="fas fa-check"></i> <?php echo htmlspecialchars($feature); ?></li>
                            <?php endforeach; ?>
                        </ul>
                        
                        <button class="btn" onclick="openSubscriptionModal(<?php echo $subscription['id']; ?>, '<?php echo htmlspecialchars($subscription['name']); ?>', <?php echo $subscription['price']; ?>)">
                            <?php if ($currentSubscription && $currentSubscription['subscription_id'] == $subscription['id']): ?>
                                <i class="fas fa-crown"></i> خطتك الحالية
                            <?php else: ?>
                                <i class="fas fa-credit-card"></i> اشترك الآن
                            <?php endif; ?>
                        </button>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </main>
    
    <!-- نافذة الاشتراك -->
    <div id="subscriptionModal" class="modal">
        <div class="modal-content">
            <button class="close" onclick="closeSubscriptionModal()">&times;</button>
            <div class="modal-header">
                <h3 class="modal-title">تأكيد الاشتراك</h3>
                <p id="subscriptionDetails"></p>
            </div>
            
            <form method="POST" id="subscriptionForm">
                <input type="hidden" name="subscription_id" id="subscriptionId">
                <input type="hidden" name="subscribe" value="1">
                
                <div class="payment-methods">
                    <h4 style="color: #E50914; margin-bottom: 1rem;">اختر طريقة الدفع</h4>
                    <div class="payment-grid">
                        <label class="payment-method">
                            <input type="radio" name="payment_method" value="credit_card" required>
                            <i class="fas fa-credit-card"></i>
                            <div>بطاقة ائتمان</div>
                        </label>
                        <label class="payment-method">
                            <input type="radio" name="payment_method" value="paypal" required>
                            <i class="fab fa-paypal"></i>
                            <div>PayPal</div>
                        </label>
                        <label class="payment-method">
                            <input type="radio" name="payment_method" value="apple_pay" required>
                            <i class="fab fa-apple-pay"></i>
                            <div>Apple Pay</div>
                        </label>
                        <label class="payment-method">
                            <input type="radio" name="payment_method" value="stc_pay" required>
                            <i class="fas fa-mobile-alt"></i>
                            <div>STC Pay</div>
                        </label>
                    </div>
                </div>
                
                <button type="submit" class="btn">
                    <i class="fas fa-lock"></i> تأكيد الدفع
                </button>
            </form>
        </div>
    </div>
    
    <script>
        function openSubscriptionModal(id, name, price) {
            document.getElementById('subscriptionId').value = id;
            document.getElementById('subscriptionDetails').textContent = `خطة ${name} - ${price} ر.س`;
            document.getElementById('subscriptionModal').style.display = 'block';
        }
        
        function closeSubscriptionModal() {
            document.getElementById('subscriptionModal').style.display = 'none';
        }
        
        // إغلاق النافذة عند النقر خارجها
        window.onclick = function(event) {
            const modal = document.getElementById('subscriptionModal');
            if (event.target === modal) {
                closeSubscriptionModal();
            }
        }
        
        // تفعيل اختيار طريقة الدفع
        document.querySelectorAll('.payment-method').forEach(method => {
            method.addEventListener('click', function() {
                document.querySelectorAll('.payment-method').forEach(m => m.classList.remove('selected'));
                this.classList.add('selected');
                this.querySelector('input').checked = true;
            });
        });
        
        console.log('صفحة الاشتراكات جاهزة!');
    </script>
</body>
</html>
