import 'package:flutter/material.dart';
import '../services/api_service.dart';
import '../main.dart';

class SeriesScreen extends StatefulWidget {
  const SeriesScreen({super.key});

  @override
  State<SeriesScreen> createState() => _SeriesScreenState();
}

class _SeriesScreenState extends State<SeriesScreen> {
  bool _isLoading = true;
  String? _error;
  List<Series> _series = [];
  String _selectedGenre = 'الكل';
  List<String> _genres = ['الكل'];

  @override
  void initState() {
    super.initState();
    _loadSeries();
  }

  Future<void> _loadSeries() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final response = await ApiService.getSeries();
      
      if (response['success']) {
        final series = (response['data'] as List)
            .map((json) => Series.fromJson(json))
            .toList();
        
        // Extract unique genres
        final genres = {'الكل'};
        for (final serie in series) {
          if (serie.genre.isNotEmpty) {
            final serieGenres = serie.genre.split('،').map((g) => g.trim());
            genres.addAll(serieGenres);
          }
        }

        setState(() {
          _series = series;
          _genres = genres.toList();
          _isLoading = false;
        });
      } else {
        throw Exception(response['error'] ?? 'Failed to load series');
      }
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  List<Series> get _filteredSeries {
    if (_selectedGenre == 'الكل') {
      return _series;
    }
    return _series.where((serie) => serie.genre.contains(_selectedGenre)).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const ShahidAppBar(title: 'المسلسلات'),
      body: _isLoading
          ? const LoadingWidget(message: 'جاري تحميل المسلسلات...')
          : _error != null
              ? ErrorWidget(
                  message: _error!,
                  onRetry: _loadSeries,
                )
              : Column(
                  children: [
                    // Genre Filter
                    _buildGenreFilter(),
                    
                    // Series Grid
                    Expanded(
                      child: RefreshIndicator(
                        onRefresh: _loadSeries,
                        color: const Color(0xFFE50914),
                        child: _filteredSeries.isEmpty
                            ? const Center(
                                child: Text(
                                  'لا توجد مسلسلات متاحة',
                                  style: TextStyle(
                                    color: Colors.grey,
                                    fontSize: 16,
                                  ),
                                ),
                              )
                            : GridView.builder(
                                padding: const EdgeInsets.all(16),
                                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                                  crossAxisCount: 2,
                                  childAspectRatio: 0.7,
                                  crossAxisSpacing: 12,
                                  mainAxisSpacing: 12,
                                ),
                                itemCount: _filteredSeries.length,
                                itemBuilder: (context, index) {
                                  final serie = _filteredSeries[index];
                                  return _buildSeriesCard(serie);
                                },
                              ),
                      ),
                    ),
                  ],
                ),
    );
  }

  Widget _buildGenreFilter() {
    return Container(
      height: 50,
      margin: const EdgeInsets.symmetric(vertical: 8),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: _genres.length,
        itemBuilder: (context, index) {
          final genre = _genres[index];
          final isSelected = genre == _selectedGenre;
          
          return Container(
            margin: const EdgeInsets.only(left: 8),
            child: FilterChip(
              label: Text(genre),
              selected: isSelected,
              onSelected: (selected) {
                setState(() {
                  _selectedGenre = genre;
                });
              },
              backgroundColor: const Color(0xFF2F2F2F),
              selectedColor: const Color(0xFFE50914),
              labelStyle: TextStyle(
                color: isSelected ? Colors.white : Colors.grey,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              ),
              side: BorderSide(
                color: isSelected ? const Color(0xFFE50914) : Colors.grey,
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildSeriesCard(Series serie) {
    return GestureDetector(
      onTap: () => _showSeriesDetails(serie),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          color: const Color(0xFF2F2F2F),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Series Poster
            Expanded(
              flex: 3,
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: const BorderRadius.vertical(
                    top: Radius.circular(12),
                  ),
                  color: Colors.grey[800],
                ),
                child: ClipRRect(
                  borderRadius: const BorderRadius.vertical(
                    top: Radius.circular(12),
                  ),
                  child: Stack(
                    children: [
                      // Image
                      Image.network(
                        serie.poster,
                        fit: BoxFit.cover,
                        width: double.infinity,
                        height: double.infinity,
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            color: Colors.grey[800],
                            child: const Center(
                              child: Icon(
                                Icons.tv,
                                color: Colors.grey,
                                size: 48,
                              ),
                            ),
                          );
                        },
                      ),
                      
                      // Play button overlay
                      Positioned.fill(
                        child: Container(
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.center,
                              end: Alignment.bottomCenter,
                              colors: [
                                Colors.transparent,
                                Colors.black.withOpacity(0.7),
                              ],
                            ),
                          ),
                          child: const Center(
                            child: Icon(
                              Icons.play_circle_filled,
                              color: Colors.white,
                              size: 48,
                            ),
                          ),
                        ),
                      ),
                      
                      // Rating badge
                      Positioned(
                        top: 8,
                        right: 8,
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 6,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.black.withOpacity(0.7),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              const Icon(
                                Icons.star,
                                color: Colors.amber,
                                size: 12,
                              ),
                              const SizedBox(width: 2),
                              Text(
                                serie.rating.toStringAsFixed(1),
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 10,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      
                      // Status badge
                      Positioned(
                        top: 8,
                        left: 8,
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 6,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color: _getStatusColor(serie.status),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            _getStatusText(serie.status),
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            
            // Series Info
            Expanded(
              flex: 1,
              child: Padding(
                padding: const EdgeInsets.all(8),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Title
                    Text(
                      serie.title,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    
                    const Spacer(),
                    
                    // Year and Seasons
                    Row(
                      children: [
                        Text(
                          '${serie.year}',
                          style: const TextStyle(
                            color: Colors.grey,
                            fontSize: 12,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            '${serie.seasons} مواسم',
                            style: const TextStyle(
                              color: Colors.grey,
                              fontSize: 12,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'ongoing':
        return Colors.green;
      case 'completed':
        return Colors.blue;
      case 'cancelled':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  String _getStatusText(String status) {
    switch (status.toLowerCase()) {
      case 'ongoing':
        return 'مستمر';
      case 'completed':
        return 'مكتمل';
      case 'cancelled':
        return 'ملغي';
      default:
        return 'نشط';
    }
  }

  void _showSeriesDetails(Series serie) {
    showModalBottomSheet(
      context: context,
      backgroundColor: const Color(0xFF2F2F2F),
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.8,
        maxChildSize: 0.95,
        minChildSize: 0.5,
        builder: (context, scrollController) => SeriesDetailsSheet(
          serie: serie,
          scrollController: scrollController,
        ),
      ),
    );
  }
}

class SeriesDetailsSheet extends StatefulWidget {
  final Series serie;
  final ScrollController scrollController;

  const SeriesDetailsSheet({
    super.key,
    required this.serie,
    required this.scrollController,
  });

  @override
  State<SeriesDetailsSheet> createState() => _SeriesDetailsSheetState();
}

class _SeriesDetailsSheetState extends State<SeriesDetailsSheet>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  List<Episode> _episodes = [];
  bool _loadingEpisodes = false;
  String? _episodesError;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadEpisodes();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadEpisodes() async {
    setState(() {
      _loadingEpisodes = true;
      _episodesError = null;
    });

    try {
      final response = await ApiService.getEpisodes(widget.serie.id);
      
      if (response['success']) {
        final episodes = (response['data'] as List)
            .map((json) => Episode.fromJson(json))
            .toList();
        
        setState(() {
          _episodes = episodes;
          _loadingEpisodes = false;
        });
      } else {
        throw Exception(response['error'] ?? 'Failed to load episodes');
      }
    } catch (e) {
      setState(() {
        _episodesError = e.toString();
        _loadingEpisodes = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Handle bar
        Container(
          margin: const EdgeInsets.only(top: 8),
          width: 40,
          height: 4,
          decoration: BoxDecoration(
            color: Colors.grey,
            borderRadius: BorderRadius.circular(2),
          ),
        ),
        
        // Series header
        Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Poster
              Container(
                width: 100,
                height: 150,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  color: Colors.grey[800],
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Image.network(
                    widget.serie.poster,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        color: Colors.grey[800],
                        child: const Icon(
                          Icons.tv,
                          color: Colors.grey,
                          size: 48,
                        ),
                      );
                    },
                  ),
                ),
              ),
              
              const SizedBox(width: 16),
              
              // Basic info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.serie.title,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    
                    Row(
                      children: [
                        const Icon(Icons.star, color: Colors.amber, size: 16),
                        const SizedBox(width: 4),
                        Text(
                          widget.serie.rating.toStringAsFixed(1),
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        if (widget.serie.ratingCount > 0) ...[
                          const SizedBox(width: 4),
                          Text(
                            '(${widget.serie.ratingCount})',
                            style: const TextStyle(color: Colors.grey),
                          ),
                        ],
                      ],
                    ),
                    const SizedBox(height: 8),
                    
                    Text(
                      '${widget.serie.year} • ${widget.serie.seasons} مواسم',
                      style: const TextStyle(color: Colors.grey),
                    ),
                    const SizedBox(height: 4),
                    
                    Text(
                      widget.serie.genre,
                      style: const TextStyle(color: Colors.grey),
                    ),
                    const SizedBox(height: 12),
                    
                    // Action buttons
                    Row(
                      children: [
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: () {
                              Navigator.pop(context);
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  content: Text('سيتم تشغيل المسلسل قريباً'),
                                  backgroundColor: Color(0xFFE50914),
                                ),
                              );
                            },
                            icon: const Icon(Icons.play_arrow, size: 16),
                            label: const Text('تشغيل', style: TextStyle(fontSize: 12)),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: const Color(0xFFE50914),
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 8),
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: OutlinedButton.icon(
                            onPressed: () {
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  content: Text('تم إضافة المسلسل للمفضلة'),
                                ),
                              );
                            },
                            icon: const Icon(Icons.favorite_border, size: 16),
                            label: const Text('المفضلة', style: TextStyle(fontSize: 12)),
                            style: OutlinedButton.styleFrom(
                              foregroundColor: Colors.white,
                              side: const BorderSide(color: Colors.white),
                              padding: const EdgeInsets.symmetric(vertical: 8),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        
        // Tabs
        TabBar(
          controller: _tabController,
          indicatorColor: const Color(0xFFE50914),
          labelColor: Colors.white,
          unselectedLabelColor: Colors.grey,
          tabs: const [
            Tab(text: 'التفاصيل'),
            Tab(text: 'الحلقات'),
          ],
        ),
        
        // Tab content
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [
              // Details tab
              SingleChildScrollView(
                controller: widget.scrollController,
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'القصة',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      widget.serie.description,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        height: 1.5,
                      ),
                    ),
                    
                    const SizedBox(height: 24),
                    
                    // Additional details
                    if (widget.serie.director != null || widget.serie.cast != null) ...[
                      const Text(
                        'تفاصيل إضافية',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 12),
                      
                      if (widget.serie.director != null) ...[
                        _buildDetailRow('الإخراج', widget.serie.director!),
                        const SizedBox(height: 8),
                      ],
                      
                      if (widget.serie.cast != null) ...[
                        _buildDetailRow('التمثيل', widget.serie.cast!),
                        const SizedBox(height: 8),
                      ],
                      
                      if (widget.serie.country != null) ...[
                        _buildDetailRow('البلد', widget.serie.country!),
                        const SizedBox(height: 8),
                      ],
                      
                      if (widget.serie.language != null) ...[
                        _buildDetailRow('اللغة', widget.serie.language!),
                      ],
                    ],
                  ],
                ),
              ),
              
              // Episodes tab
              _loadingEpisodes
                  ? const LoadingWidget(message: 'جاري تحميل الحلقات...')
                  : _episodesError != null
                      ? Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              const Icon(
                                Icons.error_outline,
                                color: Colors.red,
                                size: 48,
                              ),
                              const SizedBox(height: 16),
                              Text(
                                _episodesError!,
                                style: const TextStyle(color: Colors.white),
                                textAlign: TextAlign.center,
                              ),
                              const SizedBox(height: 16),
                              ElevatedButton(
                                onPressed: _loadEpisodes,
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: const Color(0xFFE50914),
                                ),
                                child: const Text('إعادة المحاولة'),
                              ),
                            ],
                          ),
                        )
                      : _episodes.isEmpty
                          ? const Center(
                              child: Text(
                                'لا توجد حلقات متاحة',
                                style: TextStyle(
                                  color: Colors.grey,
                                  fontSize: 16,
                                ),
                              ),
                            )
                          : ListView.builder(
                              controller: widget.scrollController,
                              padding: const EdgeInsets.all(16),
                              itemCount: _episodes.length,
                              itemBuilder: (context, index) {
                                final episode = _episodes[index];
                                return _buildEpisodeCard(episode);
                              },
                            ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 80,
          child: Text(
            '$label:',
            style: const TextStyle(
              color: Colors.grey,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: const TextStyle(color: Colors.white),
          ),
        ),
      ],
    );
  }

  Widget _buildEpisodeCard(Episode episode) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: const Color(0xFF1F1F1F),
        borderRadius: BorderRadius.circular(8),
      ),
      child: ListTile(
        leading: Container(
          width: 60,
          height: 40,
          decoration: BoxDecoration(
            color: Colors.grey[800],
            borderRadius: BorderRadius.circular(4),
          ),
          child: episode.thumbnail != null
              ? ClipRRect(
                  borderRadius: BorderRadius.circular(4),
                  child: Image.network(
                    episode.thumbnail!,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return const Icon(
                        Icons.play_circle_outline,
                        color: Colors.grey,
                      );
                    },
                  ),
                )
              : const Icon(
                  Icons.play_circle_outline,
                  color: Colors.grey,
                ),
        ),
        title: Text(
          episode.title,
          style: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'الموسم ${episode.season} • الحلقة ${episode.episode}',
              style: const TextStyle(color: Colors.grey),
            ),
            if (episode.description.isNotEmpty) ...[
              const SizedBox(height: 4),
              Text(
                episode.description,
                style: const TextStyle(color: Colors.grey),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ],
        ),
        trailing: Text(
          '${episode.duration} د',
          style: const TextStyle(color: Colors.grey),
        ),
        onTap: () {
          Navigator.pop(context);
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('سيتم تشغيل ${episode.title} قريباً'),
              backgroundColor: const Color(0xFFE50914),
            ),
          );
        },
      ),
    );
  }
}
