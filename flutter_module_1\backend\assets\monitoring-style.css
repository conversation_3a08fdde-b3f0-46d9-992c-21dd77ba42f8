/* حالة النظام المباشرة - Live System Monitoring Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', 'Cairo', 'Taja<PERSON>', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 25%, #2d2d2d 75%, #0f0f0f 100%);
    color: #ffffff;
    min-height: 100vh;
    padding: 1.5rem;
    overflow-x: hidden;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    background: rgba(30, 30, 30, 0.95);
    border-radius: 25px;
    padding: 2.5rem;
    border: 2px solid rgba(229, 9, 20, 0.4);
    box-shadow: 
        0 20px 60px rgba(0, 0, 0, 0.7),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

.header {
    text-align: center;
    margin-bottom: 3rem;
    padding-bottom: 2rem;
    border-bottom: 3px solid #E50914;
    position: relative;
}

.header::after {
    content: '';
    position: absolute;
    bottom: -3px;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 3px;
    background: linear-gradient(90deg, transparent, #E50914, transparent);
}

.header h1 {
    color: #E50914;
    font-size: 3.2rem;
    margin-bottom: 0.8rem;
    text-shadow: 
        2px 2px 4px rgba(0, 0, 0, 0.8),
        0 0 20px rgba(229, 9, 20, 0.3);
    font-weight: 700;
    letter-spacing: 1px;
}

.header p {
    color: #cccccc;
    font-size: 1.3rem;
    opacity: 0.9;
    font-weight: 300;
}

.monitoring-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 2.5rem;
    margin-bottom: 3rem;
}

.monitor-card {
    background: linear-gradient(145deg, 
        rgba(20, 20, 20, 0.8), 
        rgba(40, 40, 40, 0.6));
    border-radius: 20px;
    padding: 2.5rem;
    border: 2px solid rgba(255, 255, 255, 0.1);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(5px);
}

.monitor-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: #666666;
    transition: all 0.4s ease;
}

.monitor-card.success::before {
    background: linear-gradient(90deg, #4CAF50, #66BB6A, #4CAF50);
    box-shadow: 0 0 15px rgba(76, 175, 80, 0.5);
}

.monitor-card.error::before {
    background: linear-gradient(90deg, #F44336, #EF5350, #F44336);
    box-shadow: 0 0 15px rgba(244, 67, 54, 0.5);
}

.monitor-card.warning::before {
    background: linear-gradient(90deg, #FF9800, #FFB74D, #FF9800);
    box-shadow: 0 0 15px rgba(255, 152, 0, 0.5);
}

.monitor-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 
        0 25px 50px rgba(0, 0, 0, 0.4),
        0 0 30px rgba(229, 9, 20, 0.2);
    border-color: rgba(229, 9, 20, 0.3);
}

.card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 2rem;
}

.card-title {
    font-size: 1.6rem;
    font-weight: 600;
    color: #ffffff;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.status-indicator {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    position: relative;
    animation: pulse 2s infinite ease-in-out;
}

.status-indicator::after {
    content: '';
    position: absolute;
    top: -4px;
    left: -4px;
    right: -4px;
    bottom: -4px;
    border-radius: 50%;
    border: 2px solid currentColor;
    opacity: 0.3;
    animation: ripple 2s infinite ease-out;
}

.status-indicator.success {
    background: radial-gradient(circle, #4CAF50, #2E7D32);
    color: #4CAF50;
    box-shadow: 
        0 0 15px rgba(76, 175, 80, 0.6),
        inset 0 2px 4px rgba(255, 255, 255, 0.2);
}

.status-indicator.error {
    background: radial-gradient(circle, #F44336, #C62828);
    color: #F44336;
    box-shadow: 
        0 0 15px rgba(244, 67, 54, 0.6),
        inset 0 2px 4px rgba(255, 255, 255, 0.2);
}

.status-indicator.warning {
    background: radial-gradient(circle, #FF9800, #E65100);
    color: #FF9800;
    box-shadow: 
        0 0 15px rgba(255, 152, 0, 0.6),
        inset 0 2px 4px rgba(255, 255, 255, 0.2);
}

@keyframes pulse {
    0%, 100% { 
        opacity: 1; 
        transform: scale(1);
    }
    50% { 
        opacity: 0.7; 
        transform: scale(1.1);
    }
}

@keyframes ripple {
    0% {
        transform: scale(1);
        opacity: 0.3;
    }
    100% {
        transform: scale(2);
        opacity: 0;
    }
}

.card-content {
    color: #e0e0e0;
    line-height: 1.8;
}

.status-text {
    font-size: 1.2rem;
    margin-bottom: 0.8rem;
    font-weight: 500;
    color: #ffffff;
}

.status-details {
    font-size: 1rem;
    opacity: 0.85;
    color: #cccccc;
    font-weight: 400;
}

.refresh-info {
    text-align: center;
    margin-top: 3rem;
    padding-top: 1.5rem;
    border-top: 1px solid rgba(255, 255, 255, 0.15);
    color: #888888;
    font-size: 1rem;
}

.search-icon {
    position: absolute;
    top: 2rem;
    left: 2rem;
    font-size: 2rem;
    color: #E50914;
    text-shadow: 0 0 10px rgba(229, 9, 20, 0.5);
    animation: glow 3s ease-in-out infinite alternate;
}

@keyframes glow {
    from {
        text-shadow: 0 0 10px rgba(229, 9, 20, 0.5);
    }
    to {
        text-shadow: 0 0 20px rgba(229, 9, 20, 0.8);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 1.5rem;
        margin: 1rem;
    }
    
    .header h1 {
        font-size: 2.5rem;
    }
    
    .monitoring-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .monitor-card {
        padding: 2rem;
    }
    
    .search-icon {
        top: 1rem;
        left: 1rem;
        font-size: 1.5rem;
    }
}

/* Dark theme enhancements */
.monitor-card.success {
    background: linear-gradient(145deg, 
        rgba(20, 40, 20, 0.8), 
        rgba(30, 60, 30, 0.6));
}

.monitor-card.error {
    background: linear-gradient(145deg, 
        rgba(40, 20, 20, 0.8), 
        rgba(60, 30, 30, 0.6));
}

.monitor-card.warning {
    background: linear-gradient(145deg, 
        rgba(40, 30, 20, 0.8), 
        rgba(60, 45, 30, 0.6));
}

/* Loading animation for updates */
.updating {
    opacity: 0.7;
    transition: opacity 0.3s ease;
}

.updating::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, 
        transparent, 
        rgba(255, 255, 255, 0.1), 
        transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { left: -100%; }
    100% { left: 100%; }
}
