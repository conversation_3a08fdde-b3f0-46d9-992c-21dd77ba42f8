<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// معالجة طلبات OPTIONS
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => 'طريقة الطلب غير مدعومة',
        'error_code' => 'METHOD_NOT_ALLOWED'
    ]);
    exit;
}

try {
    // الاتصال بقاعدة البيانات
    $pdo = new PDO("mysql:host=localhost;dbname=shahid_platform;charset=utf8mb4", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // قراءة البيانات المرسلة
    $input = json_decode(file_get_contents('php://input'), true);
    
    // التحقق من وجود البيانات المطلوبة
    $requiredFields = ['username', 'email', 'password', 'full_name'];
    foreach ($requiredFields as $field) {
        if (!isset($input[$field]) || empty(trim($input[$field]))) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => "الحقل '$field' مطلوب",
                'error_code' => 'MISSING_FIELD'
            ]);
            exit;
        }
    }
    
    $username = trim($input['username']);
    $email = trim($input['email']);
    $password = $input['password'];
    $fullName = trim($input['full_name']);
    $phone = isset($input['phone']) ? trim($input['phone']) : null;
    
    // التحقق من صحة البيانات
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'message' => 'البريد الإلكتروني غير صحيح',
            'error_code' => 'INVALID_EMAIL'
        ]);
        exit;
    }
    
    if (strlen($username) < 3 || strlen($username) > 50) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'message' => 'اسم المستخدم يجب أن يكون بين 3 و 50 حرف',
            'error_code' => 'INVALID_USERNAME_LENGTH'
        ]);
        exit;
    }
    
    if (!preg_match('/^[a-zA-Z0-9_]+$/', $username)) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'message' => 'اسم المستخدم يجب أن يحتوي على أحرف وأرقام و _ فقط',
            'error_code' => 'INVALID_USERNAME_FORMAT'
        ]);
        exit;
    }
    
    if (strlen($password) < 6) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'message' => 'كلمة المرور يجب أن تكون 6 أحرف على الأقل',
            'error_code' => 'WEAK_PASSWORD'
        ]);
        exit;
    }
    
    if (strlen($fullName) < 2 || strlen($fullName) > 100) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'message' => 'الاسم الكامل يجب أن يكون بين 2 و 100 حرف',
            'error_code' => 'INVALID_FULLNAME_LENGTH'
        ]);
        exit;
    }
    
    // التحقق من عدم وجود المستخدم مسبقاً
    $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ? OR username = ?");
    $stmt->execute([$email, $username]);
    $existingUser = $stmt->fetch();
    
    if ($existingUser) {
        http_response_code(409);
        echo json_encode([
            'success' => false,
            'message' => 'البريد الإلكتروني أو اسم المستخدم مستخدم مسبقاً',
            'error_code' => 'USER_EXISTS'
        ]);
        exit;
    }
    
    // تشفير كلمة المرور
    $hashedPassword = password_hash($password, PASSWORD_ARGON2ID);
    
    // إنشاء المستخدم الجديد
    $stmt = $pdo->prepare("
        INSERT INTO users (username, email, password, full_name, phone, subscription_type, is_active, created_at) 
        VALUES (?, ?, ?, ?, ?, 'free', 1, NOW())
    ");
    
    if ($stmt->execute([$username, $email, $hashedPassword, $fullName, $phone])) {
        $userId = $pdo->lastInsertId();
        
        // تسجيل النشاط
        $stmt = $pdo->prepare("
            INSERT INTO activity_logs (user_id, action, description, ip_address, created_at) 
            VALUES (?, 'register', 'إنشاء حساب جديد عبر API', ?, NOW())
        ");
        $stmt->execute([$userId, $_SERVER['REMOTE_ADDR']]);
        
        // إنشاء رمز الوصول
        $payload = [
            'user_id' => $userId,
            'email' => $email,
            'subscription_type' => 'free',
            'issued_at' => time(),
            'expires_at' => time() + (24 * 60 * 60) // 24 ساعة
        ];
        
        $token = base64_encode(json_encode($payload));
        
        // الحصول على بيانات المستخدم الجديد
        $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
        $stmt->execute([$userId]);
        $newUser = $stmt->fetch(PDO::FETCH_ASSOC);
        
        // إرسال إشعار ترحيب (اختياري)
        $stmt = $pdo->prepare("
            INSERT INTO notifications (user_id, title, message, type, created_at) 
            VALUES (?, 'مرحباً بك!', 'مرحباً بك في منصة شاهد. استمتع بمشاهدة أفضل الأفلام والمسلسلات.', 'welcome', NOW())
        ");
        $stmt->execute([$userId]);
        
        // الاستجابة الناجحة
        http_response_code(201);
        echo json_encode([
            'success' => true,
            'message' => 'تم إنشاء الحساب بنجاح',
            'data' => [
                'user' => [
                    'id' => $newUser['id'],
                    'username' => $newUser['username'],
                    'email' => $newUser['email'],
                    'full_name' => $newUser['full_name'],
                    'subscription_type' => $newUser['subscription_type'],
                    'created_at' => $newUser['created_at']
                ],
                'token' => $token
            ]
        ], JSON_UNESCAPED_UNICODE);
        
    } else {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'فشل في إنشاء الحساب',
            'error_code' => 'CREATION_FAILED'
        ]);
    }
    
} catch (PDOException $e) {
    error_log("Database error in register API: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'خطأ في الخادم',
        'error_code' => 'DATABASE_ERROR'
    ]);
    
} catch (Exception $e) {
    error_log("General error in register API: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'خطأ غير متوقع',
        'error_code' => 'GENERAL_ERROR'
    ]);
}
?>
