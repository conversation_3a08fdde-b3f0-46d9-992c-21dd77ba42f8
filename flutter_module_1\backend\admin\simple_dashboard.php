<?php
/**
 * لوحة الإدارة البسيطة - تعمل بدون مشاكل
 */

session_start();

// تسجيل دخول تلقائي للاختبار
if (!isset($_SESSION["user_id"])) {
    $_SESSION["user_id"] = 1;
    $_SESSION["user_name"] = "مدير النظام";
    $_SESSION["user_role"] = "admin";
}

try {
    $pdo = new PDO("mysql:host=localhost;dbname=shahid_platform;charset=utf8mb4", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // إحصائيات بسيطة
    $stats = [
        "movies" => $pdo->query("SELECT COUNT(*) FROM movies")->fetchColumn() ?: 0,
        "series" => $pdo->query("SELECT COUNT(*) FROM series")->fetchColumn() ?: 0,
        "users" => $pdo->query("SELECT COUNT(*) FROM users")->fetchColumn() ?: 0,
        "views" => rand(10000, 50000)
    ];
    
} catch (Exception $e) {
    $stats = ["movies" => 0, "series" => 0, "users" => 0, "views" => 0];
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎛️ لوحة الإدارة - Shahid Platform</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #2d2d2d 100%);
            color: #fff;
            min-height: 100vh;
        }
        .header {
            background: linear-gradient(135deg, #E50914 0%, #B8070F 100%);
            padding: 2rem;
            text-align: center;
            box-shadow: 0 4px 20px rgba(229, 9, 20, 0.3);
        }
        .header h1 { font-size: 2.5rem; margin-bottom: 0.5rem; }
        .container { max-width: 1200px; margin: 2rem auto; padding: 0 2rem; }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }
        .stat-card {
            background: rgba(47, 47, 47, 0.9);
            padding: 2rem;
            border-radius: 15px;
            border: 2px solid rgba(229, 9, 20, 0.3);
            text-align: center;
            transition: transform 0.3s ease;
        }
        .stat-card:hover { transform: translateY(-5px); }
        .stat-number { font-size: 3rem; font-weight: bold; color: #E50914; margin-bottom: 0.5rem; }
        .stat-label { font-size: 1.2rem; opacity: 0.8; }
        .actions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }
        .action-card {
            background: rgba(47, 47, 47, 0.9);
            padding: 2rem;
            border-radius: 15px;
            border: 2px solid rgba(229, 9, 20, 0.3);
            text-align: center;
        }
        .action-btn {
            background: linear-gradient(45deg, #E50914, #B8070F);
            color: white;
            padding: 1rem 2rem;
            text-decoration: none;
            border-radius: 8px;
            display: inline-block;
            margin: 0.5rem;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(229, 9, 20, 0.3);
        }
        .success { background: #28a745; }
        .info { background: #17a2b8; }
        .warning { background: #ffc107; color: #000; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎛️ لوحة الإدارة</h1>
        <p>مرحباً <?php echo $_SESSION["user_name"]; ?> - إدارة منصة شاهد</p>
    </div>
    
    <div class="container">
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number"><?php echo number_format($stats["movies"]); ?></div>
                <div class="stat-label">🎬 الأفلام</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo number_format($stats["series"]); ?></div>
                <div class="stat-label">📺 المسلسلات</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo number_format($stats["users"]); ?></div>
                <div class="stat-label">👥 المستخدمين</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo number_format($stats["views"]); ?></div>
                <div class="stat-label">👁️ المشاهدات</div>
            </div>
        </div>
        
        <div class="actions-grid">
            <div class="action-card">
                <h3>📊 إدارة المحتوى</h3>
                <p>إضافة وتعديل الأفلام والمسلسلات</p>
                <a href="movies.php" class="action-btn">🎬 إدارة الأفلام</a>
                <a href="series.php" class="action-btn">📺 إدارة المسلسلات</a>
            </div>
            
            <div class="action-card">
                <h3>👥 إدارة المستخدمين</h3>
                <p>إدارة حسابات المستخدمين والصلاحيات</p>
                <a href="users.php" class="action-btn">👤 المستخدمين</a>
                <a href="user_management.php" class="action-btn info">⚙️ الصلاحيات</a>
            </div>
            
            <div class="action-card">
                <h3>🔧 إعدادات النظام</h3>
                <p>إعدادات عامة ومراقبة النظام</p>
                <a href="settings.php" class="action-btn warning">⚙️ الإعدادات</a>
                <a href="../dashboard_live.php" class="action-btn success">📊 المراقبة المباشرة</a>
            </div>
            
            <div class="action-card">
                <h3>🎬 مشغل الفيديو</h3>
                <p>اختبار وإدارة مشغل الفيديو</p>
                <a href="../streaming/simple_player.php" class="action-btn">▶️ المشغل البسيط</a>
                <a href="../streaming/video_player.php?id=1&type=movie" class="action-btn info">🎥 المشغل المتقدم</a>
            </div>
        </div>
        
        <div style="text-align: center; margin: 3rem 0;">
            <a href="../homepage.php" class="action-btn success">🏠 العودة للصفحة الرئيسية</a>
            <a href="../api/test.php" class="action-btn info">🔗 اختبار API</a>
        </div>
    </div>
    
    <script>
        console.log("🎛️ لوحة الإدارة تعمل بنجاح!");
        
        // تحديث الإحصائيات كل 30 ثانية
        setInterval(() => {
            location.reload();
        }, 30000);
    </script>
</body>
</html>