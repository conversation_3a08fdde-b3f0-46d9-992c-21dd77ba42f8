<?php
session_start();

// التحقق من تسجيل الدخول والصلاحيات
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit;
}

try {
    $pdo = new PDO("mysql:host=localhost;dbname=shahid_platform;charset=utf8mb4", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // التحقق من صلاحيات الإدارة
    $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ? AND (subscription_type = 'vip' OR email LIKE '%admin%')");
    $stmt->execute([$_SESSION['user_id']]);
    $admin = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$admin) {
        header('Location: ../index.php');
        exit;
    }
    
    $message = '';
    $error = '';
    
    // معالجة العمليات
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'send_notification':
                    $title = $_POST['title'];
                    $messageText = $_POST['message'];
                    $type = $_POST['type'];
                    $target = $_POST['target'];
                    
                    if ($target === 'all') {
                        // إرسال لجميع المستخدمين
                        $stmt = $pdo->prepare("
                            INSERT INTO notifications (user_id, title, message, type, created_at)
                            SELECT id, ?, ?, ?, NOW() FROM users WHERE is_active = 1
                        ");
                        $stmt->execute([$title, $messageText, $type]);
                        $message = "تم إرسال الإشعار لجميع المستخدمين";
                    } elseif ($target === 'premium') {
                        // إرسال للمشتركين المدفوعين
                        $stmt = $pdo->prepare("
                            INSERT INTO notifications (user_id, title, message, type, created_at)
                            SELECT id, ?, ?, ?, NOW() FROM users 
                            WHERE is_active = 1 AND subscription_type IN ('premium', 'vip')
                        ");
                        $stmt->execute([$title, $messageText, $type]);
                        $message = "تم إرسال الإشعار للمشتركين المدفوعين";
                    } elseif ($target === 'free') {
                        // إرسال للمستخدمين المجانيين
                        $stmt = $pdo->prepare("
                            INSERT INTO notifications (user_id, title, message, type, created_at)
                            SELECT id, ?, ?, ?, NOW() FROM users 
                            WHERE is_active = 1 AND subscription_type = 'free'
                        ");
                        $stmt->execute([$title, $messageText, $type]);
                        $message = "تم إرسال الإشعار للمستخدمين المجانيين";
                    } else {
                        // إرسال لمستخدم محدد
                        $userId = intval($target);
                        $stmt = $pdo->prepare("
                            INSERT INTO notifications (user_id, title, message, type, created_at) 
                            VALUES (?, ?, ?, ?, NOW())
                        ");
                        $stmt->execute([$userId, $title, $messageText, $type]);
                        $message = "تم إرسال الإشعار للمستخدم المحدد";
                    }
                    break;
                    
                case 'delete_notification':
                    $notificationId = $_POST['notification_id'];
                    
                    $stmt = $pdo->prepare("DELETE FROM notifications WHERE id = ?");
                    if ($stmt->execute([$notificationId])) {
                        $message = "تم حذف الإشعار";
                    } else {
                        $error = "فشل في حذف الإشعار";
                    }
                    break;
                    
                case 'mark_as_read':
                    $notificationId = $_POST['notification_id'];
                    
                    $stmt = $pdo->prepare("UPDATE notifications SET is_read = 1 WHERE id = ?");
                    if ($stmt->execute([$notificationId])) {
                        $message = "تم تحديث حالة الإشعار";
                    } else {
                        $error = "فشل في تحديث حالة الإشعار";
                    }
                    break;
            }
        }
    }
    
    // معاملات الاستعلام
    $limit = 50;
    $offset = isset($_GET['page']) ? (max(1, intval($_GET['page'])) - 1) * $limit : 0;
    $type_filter = isset($_GET['type']) ? $_GET['type'] : '';
    $read_filter = isset($_GET['read']) ? $_GET['read'] : '';
    
    // بناء الاستعلام
    $where_conditions = [];
    $params = [];
    
    if ($type_filter) {
        $where_conditions[] = "n.type = ?";
        $params[] = $type_filter;
    }
    
    if ($read_filter !== '') {
        $where_conditions[] = "n.is_read = ?";
        $params[] = $read_filter === 'read' ? 1 : 0;
    }
    
    $where_clause = $where_conditions ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
    
    // الحصول على الإشعارات
    $notifications_query = "
        SELECT n.*, u.username, u.email, u.full_name
        FROM notifications n 
        LEFT JOIN users u ON n.user_id = u.id 
        $where_clause
        ORDER BY n.created_at DESC 
        LIMIT ? OFFSET ?
    ";
    
    $params[] = $limit;
    $params[] = $offset;
    
    $stmt = $pdo->prepare($notifications_query);
    $stmt->execute($params);
    $notifications = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // الحصول على إجمالي العدد
    $count_params = array_slice($params, 0, -2);
    $count_query = "
        SELECT COUNT(*) 
        FROM notifications n 
        LEFT JOIN users u ON n.user_id = u.id 
        $where_clause
    ";
    
    $stmt = $pdo->prepare($count_query);
    $stmt->execute($count_params);
    $total_notifications = $stmt->fetchColumn();
    
    // إحصائيات
    $stats = [
        'total_notifications' => $pdo->query("SELECT COUNT(*) FROM notifications")->fetchColumn(),
        'unread_notifications' => $pdo->query("SELECT COUNT(*) FROM notifications WHERE is_read = 0")->fetchColumn(),
        'today_notifications' => $pdo->query("SELECT COUNT(*) FROM notifications WHERE DATE(created_at) = CURDATE()")->fetchColumn(),
        'week_notifications' => $pdo->query("SELECT COUNT(*) FROM notifications WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)")->fetchColumn()
    ];
    
    // الحصول على المستخدمين للإرسال المحدد
    $users = $pdo->query("
        SELECT id, username, email, full_name 
        FROM users 
        WHERE is_active = 1 
        ORDER BY full_name ASC 
        LIMIT 100
    ")->fetchAll(PDO::FETCH_ASSOC);
    
} catch (Exception $e) {
    $error = "خطأ في قاعدة البيانات: " . $e->getMessage();
}

function getNotificationTypeText($type) {
    $types = [
        'info' => 'معلومات',
        'warning' => 'تحذير',
        'success' => 'نجاح',
        'error' => 'خطأ',
        'promotion' => 'عرض ترويجي',
        'update' => 'تحديث',
        'welcome' => 'ترحيب'
    ];
    return $types[$type] ?? $type;
}

function getNotificationTypeColor($type) {
    $colors = [
        'info' => '#2196F3',
        'warning' => '#FF9800',
        'success' => '#4CAF50',
        'error' => '#F44336',
        'promotion' => '#E91E63',
        'update' => '#9C27B0',
        'welcome' => '#00BCD4'
    ];
    return $colors[$type] ?? '#666';
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الإشعارات - Shahid Platform</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', 'Cairo', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #2d2d2d 100%);
            color: #fff;
            min-height: 100vh;
        }
        
        .admin-container {
            display: flex;
            min-height: 100vh;
        }
        
        .sidebar {
            width: 280px;
            background: rgba(47, 47, 47, 0.95);
            border-left: 2px solid rgba(229, 9, 20, 0.3);
            padding: 2rem 0;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
        }
        
        .sidebar-header {
            padding: 0 2rem 2rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            margin-bottom: 2rem;
        }
        
        .logo {
            color: #E50914;
            font-size: 1.8rem;
            font-weight: bold;
            text-decoration: none;
        }
        
        .sidebar-menu {
            list-style: none;
            padding: 0 1rem;
        }
        
        .sidebar-menu li {
            margin-bottom: 0.5rem;
        }
        
        .sidebar-menu a {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            color: #ccc;
            text-decoration: none;
            border-radius: 10px;
            transition: all 0.3s ease;
        }
        
        .sidebar-menu a:hover,
        .sidebar-menu a.active {
            background: rgba(229, 9, 20, 0.2);
            color: #fff;
        }
        
        .main-content {
            flex: 1;
            margin-right: 280px;
            padding: 2rem;
        }
        
        .header {
            background: rgba(47, 47, 47, 0.8);
            padding: 1.5rem 2rem;
            border-radius: 15px;
            margin-bottom: 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .header h1 {
            color: #E50914;
            font-size: 2rem;
        }
        
        .btn {
            padding: 0.75rem 1.5rem;
            background: linear-gradient(45deg, #E50914, #B8070F);
            color: white;
            border: none;
            border-radius: 8px;
            text-decoration: none;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(229, 9, 20, 0.3);
        }
        
        .btn-secondary {
            background: linear-gradient(45deg, #2196F3, #1976D2);
        }
        
        .btn-danger {
            background: linear-gradient(45deg, #F44336, #D32F2F);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: rgba(47, 47, 47, 0.8);
            padding: 1.5rem;
            border-radius: 15px;
            border: 2px solid rgba(229, 9, 20, 0.1);
            text-align: center;
            transition: all 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
            border-color: rgba(229, 9, 20, 0.3);
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #E50914;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            color: #ccc;
        }
        
        .notification-form {
            background: rgba(47, 47, 47, 0.8);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            border: 2px solid rgba(229, 9, 20, 0.1);
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-bottom: 1rem;
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
        }
        
        .form-group label {
            margin-bottom: 0.5rem;
            color: #E50914;
            font-weight: bold;
        }
        
        .form-group input,
        .form-group select,
        .form-group textarea {
            padding: 0.75rem;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.1);
            color: #fff;
            font-size: 1rem;
        }
        
        .form-group textarea {
            resize: vertical;
            min-height: 100px;
        }
        
        .notifications-section {
            background: rgba(47, 47, 47, 0.8);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            border: 2px solid rgba(229, 9, 20, 0.1);
        }
        
        .notifications-list {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            margin-top: 1rem;
        }
        
        .notification-item {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 1.5rem;
            border-left: 4px solid;
            transition: all 0.3s ease;
        }
        
        .notification-item:hover {
            transform: translateX(-5px);
            background: rgba(255, 255, 255, 0.08);
        }
        
        .notification-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
        }
        
        .notification-title {
            font-weight: bold;
            color: #fff;
            font-size: 1.1rem;
        }
        
        .notification-meta {
            display: flex;
            gap: 1rem;
            color: #ccc;
            font-size: 0.9rem;
            margin-bottom: 1rem;
        }
        
        .notification-message {
            color: #ddd;
            line-height: 1.6;
            margin-bottom: 1rem;
        }
        
        .notification-actions {
            display: flex;
            gap: 0.5rem;
        }
        
        .type-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: bold;
            color: white;
        }
        
        .read-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: bold;
        }
        
        .read-badge.unread {
            background: rgba(244, 67, 54, 0.2);
            color: #F44336;
        }
        
        .read-badge.read {
            background: rgba(76, 175, 80, 0.2);
            color: #4CAF50;
        }
        
        .filters-section {
            background: rgba(47, 47, 47, 0.8);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            border: 2px solid rgba(229, 9, 20, 0.1);
        }
        
        .filters-form {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            align-items: end;
        }
        
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            z-index: 1000;
        }
        
        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(47, 47, 47, 0.95);
            border-radius: 15px;
            padding: 2rem;
            max-width: 600px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }
        
        .close {
            position: absolute;
            top: 1rem;
            left: 1rem;
            background: none;
            border: none;
            color: #ccc;
            font-size: 1.5rem;
            cursor: pointer;
        }
        
        .alert {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 2rem;
        }
        
        .alert-success {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid rgba(76, 175, 80, 0.5);
            color: #4CAF50;
        }
        
        .alert-error {
            background: rgba(244, 67, 54, 0.2);
            border: 1px solid rgba(244, 67, 54, 0.5);
            color: #F44336;
        }
        
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }
            
            .main-content {
                margin-right: 0;
                padding: 1rem;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .form-grid {
                grid-template-columns: 1fr;
            }
            
            .filters-form {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <aside class="sidebar">
            <div class="sidebar-header">
                <a href="../index.php" class="logo">
                    <i class="fas fa-play-circle"></i> شاهد
                </a>
            </div>

            <ul class="sidebar-menu">
                <li><a href="dashboard.php"><i class="fas fa-tachometer-alt"></i> لوحة التحكم</a></li>
                <li><a href="content_manager.php"><i class="fas fa-film"></i> مدير المحتوى</a></li>
                <li><a href="upload_center.php"><i class="fas fa-upload"></i> مركز الرفع</a></li>
                <li><a href="users.php"><i class="fas fa-users"></i> إدارة المستخدمين</a></li>
                <li><a href="subscription_manager.php"><i class="fas fa-crown"></i> مدير الاشتراكات</a></li>
                <li><a href="payments.php"><i class="fas fa-credit-card"></i> المدفوعات</a></li>
                <li><a href="reports.php"><i class="fas fa-chart-bar"></i> التقارير</a></li>
                <li><a href="notifications.php" class="active"><i class="fas fa-bell"></i> الإشعارات</a></li>
                <li><a href="settings.php"><i class="fas fa-cog"></i> الإعدادات</a></li>
                <li><a href="../logout.php"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a></li>
            </ul>
        </aside>

        <main class="main-content">
            <div class="header">
                <h1><i class="fas fa-bell"></i> إدارة الإشعارات</h1>
                <button class="btn" onclick="openModal('notificationModal')">
                    <i class="fas fa-plus"></i> إرسال إشعار جديد
                </button>
            </div>

            <?php if ($message): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i> <?php echo $message; ?>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-circle"></i> <?php echo $error; ?>
                </div>
            <?php endif; ?>

            <!-- إحصائيات -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number"><?php echo number_format($stats['total_notifications']); ?></div>
                    <div class="stat-label">إجمالي الإشعارات</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo number_format($stats['unread_notifications']); ?></div>
                    <div class="stat-label">غير مقروءة</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo number_format($stats['today_notifications']); ?></div>
                    <div class="stat-label">اليوم</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo number_format($stats['week_notifications']); ?></div>
                    <div class="stat-label">هذا الأسبوع</div>
                </div>
            </div>

            <!-- فلاتر البحث -->
            <div class="filters-section">
                <h3 style="color: #E50914; margin-bottom: 1rem;">
                    <i class="fas fa-filter"></i> فلاتر البحث
                </h3>

                <form method="GET" class="filters-form">
                    <div class="form-group">
                        <label>نوع الإشعار</label>
                        <select name="type">
                            <option value="">جميع الأنواع</option>
                            <option value="info" <?php echo $type_filter === 'info' ? 'selected' : ''; ?>>معلومات</option>
                            <option value="warning" <?php echo $type_filter === 'warning' ? 'selected' : ''; ?>>تحذير</option>
                            <option value="success" <?php echo $type_filter === 'success' ? 'selected' : ''; ?>>نجاح</option>
                            <option value="error" <?php echo $type_filter === 'error' ? 'selected' : ''; ?>>خطأ</option>
                            <option value="promotion" <?php echo $type_filter === 'promotion' ? 'selected' : ''; ?>>عرض ترويجي</option>
                            <option value="update" <?php echo $type_filter === 'update' ? 'selected' : ''; ?>>تحديث</option>
                            <option value="welcome" <?php echo $type_filter === 'welcome' ? 'selected' : ''; ?>>ترحيب</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label>حالة القراءة</label>
                        <select name="read">
                            <option value="">جميع الحالات</option>
                            <option value="unread" <?php echo $read_filter === 'unread' ? 'selected' : ''; ?>>غير مقروء</option>
                            <option value="read" <?php echo $read_filter === 'read' ? 'selected' : ''; ?>>مقروء</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <button type="submit" class="btn">
                            <i class="fas fa-search"></i> بحث
                        </button>
                    </div>
                </form>
            </div>

            <!-- قائمة الإشعارات -->
            <div class="notifications-section">
                <h3 style="color: #E50914; margin-bottom: 1rem;">
                    <i class="fas fa-list"></i> الإشعارات المرسلة
                </h3>

                <div class="notifications-list">
                    <?php foreach ($notifications as $notification): ?>
                        <div class="notification-item" style="border-left-color: <?php echo getNotificationTypeColor($notification['type']); ?>">
                            <div class="notification-header">
                                <div class="notification-title"><?php echo htmlspecialchars($notification['title']); ?></div>
                                <div style="display: flex; gap: 0.5rem;">
                                    <span class="type-badge" style="background-color: <?php echo getNotificationTypeColor($notification['type']); ?>">
                                        <?php echo getNotificationTypeText($notification['type']); ?>
                                    </span>
                                    <span class="read-badge <?php echo $notification['is_read'] ? 'read' : 'unread'; ?>">
                                        <?php echo $notification['is_read'] ? 'مقروء' : 'غير مقروء'; ?>
                                    </span>
                                </div>
                            </div>

                            <div class="notification-meta">
                                <span><i class="fas fa-user"></i> <?php echo htmlspecialchars($notification['full_name'] ?? $notification['username']); ?></span>
                                <span><i class="fas fa-envelope"></i> <?php echo htmlspecialchars($notification['email']); ?></span>
                                <span><i class="fas fa-clock"></i> <?php echo date('Y-m-d H:i', strtotime($notification['created_at'])); ?></span>
                            </div>

                            <div class="notification-message">
                                <?php echo nl2br(htmlspecialchars($notification['message'])); ?>
                            </div>

                            <div class="notification-actions">
                                <?php if (!$notification['is_read']): ?>
                                    <form method="POST" style="display: inline;">
                                        <input type="hidden" name="action" value="mark_as_read">
                                        <input type="hidden" name="notification_id" value="<?php echo $notification['id']; ?>">
                                        <button type="submit" class="btn btn-secondary" style="padding: 0.5rem 1rem;">
                                            <i class="fas fa-check"></i> تحديد كمقروء
                                        </button>
                                    </form>
                                <?php endif; ?>

                                <form method="POST" style="display: inline;" onsubmit="return confirm('هل أنت متأكد من حذف هذا الإشعار؟')">
                                    <input type="hidden" name="action" value="delete_notification">
                                    <input type="hidden" name="notification_id" value="<?php echo $notification['id']; ?>">
                                    <button type="submit" class="btn btn-danger" style="padding: 0.5rem 1rem;">
                                        <i class="fas fa-trash"></i> حذف
                                    </button>
                                </form>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </main>
    </div>

    <!-- نافذة إرسال إشعار جديد -->
    <div id="notificationModal" class="modal">
        <div class="modal-content">
            <button class="close" onclick="closeModal('notificationModal')">&times;</button>
            <h3 style="color: #E50914; margin-bottom: 2rem;">إرسال إشعار جديد</h3>

            <form method="POST">
                <input type="hidden" name="action" value="send_notification">

                <div class="form-group">
                    <label>عنوان الإشعار</label>
                    <input type="text" name="title" required placeholder="أدخل عنوان الإشعار">
                </div>

                <div class="form-group">
                    <label>نص الإشعار</label>
                    <textarea name="message" required placeholder="أدخل نص الإشعار"></textarea>
                </div>

                <div class="form-grid">
                    <div class="form-group">
                        <label>نوع الإشعار</label>
                        <select name="type" required>
                            <option value="info">معلومات</option>
                            <option value="warning">تحذير</option>
                            <option value="success">نجاح</option>
                            <option value="error">خطأ</option>
                            <option value="promotion">عرض ترويجي</option>
                            <option value="update">تحديث</option>
                            <option value="welcome">ترحيب</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label>المستهدفين</label>
                        <select name="target" required onchange="toggleUserSelect(this.value)">
                            <option value="all">جميع المستخدمين</option>
                            <option value="premium">المشتركين المدفوعين</option>
                            <option value="free">المستخدمين المجانيين</option>
                            <option value="specific">مستخدم محدد</option>
                        </select>
                    </div>
                </div>

                <div class="form-group" id="userSelect" style="display: none;">
                    <label>اختر المستخدم</label>
                    <select name="specific_user">
                        <option value="">اختر مستخدم</option>
                        <?php foreach ($users as $user): ?>
                            <option value="<?php echo $user['id']; ?>">
                                <?php echo htmlspecialchars($user['full_name'] ?? $user['username']) . ' (' . $user['email'] . ')'; ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <button type="submit" class="btn" style="width: 100%; margin-top: 1rem;">
                    <i class="fas fa-paper-plane"></i> إرسال الإشعار
                </button>
            </form>
        </div>
    </div>

    <script>
        function openModal(modalId) {
            document.getElementById(modalId).style.display = 'block';
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        function toggleUserSelect(target) {
            const userSelect = document.getElementById('userSelect');
            const specificUserSelect = document.querySelector('select[name="specific_user"]');

            if (target === 'specific') {
                userSelect.style.display = 'block';
                specificUserSelect.required = true;
            } else {
                userSelect.style.display = 'none';
                specificUserSelect.required = false;
            }
        }

        // إغلاق النافذة عند النقر خارجها
        window.onclick = function(event) {
            if (event.target.classList.contains('modal')) {
                event.target.style.display = 'none';
            }
        }

        // تحديث الصفحة كل دقيقة للإشعارات الجديدة
        setInterval(function() {
            if (!document.querySelector('.modal[style*="block"]')) {
                window.location.reload();
            }
        }, 60000);

        console.log('نظام إدارة الإشعارات جاهز!');
    </script>
</body>
</html>
