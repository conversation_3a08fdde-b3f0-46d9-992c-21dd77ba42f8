<?php
session_start();

// تسجيل آخر نشاط للمستخدم قبل تسجيل الخروج
if (isset($_SESSION['user_id'])) {
    try {
        $pdo = new PDO("mysql:host=localhost;dbname=shahid_platform;charset=utf8mb4", "root", "");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // تحديث آخر نشاط
        $stmt = $pdo->prepare("UPDATE users SET last_login = NOW() WHERE id = ?");
        $stmt->execute([$_SESSION['user_id']]);
        
    } catch (Exception $e) {
        // تجاهل الأخطاء
    }
}

// تدمير الجلسة
session_destroy();

// حذف ملفات تعريف الارتباط
if (isset($_COOKIE[session_name()])) {
    setcookie(session_name(), '', time() - 3600, '/');
}

// إعادة توجيه للصفحة الرئيسية
header('Location: index.php?logout=success');
exit;
?>
