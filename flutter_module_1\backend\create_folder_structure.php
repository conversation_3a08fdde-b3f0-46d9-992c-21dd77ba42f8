<?php
/**
 * إنشاء هيكل المجلدات المطلوب لمشروع Shahid Platform
 * Create Required Folder Structure for Shahid Platform
 */

echo "<h1>📁 إنشاء هيكل المجلدات المطلوب</h1>";

// قائمة المجلدات المطلوبة
$required_folders = [
    // المجلدات الرئيسية
    'app' => 'مجلد التطبيق الرئيسي',
    'app/controllers' => 'متحكمات التطبيق',
    'app/models' => 'نماذج البيانات',
    'app/views' => 'عروض التطبيق',
    'app/api' => 'واجهات برمجة التطبيقات',
    
    // مجلدات الأصول
    'assets' => 'الأصول العامة',
    'assets/css' => 'ملفات التصميم',
    'assets/js' => 'ملفات JavaScript',
    'assets/images' => 'الصور',
    'assets/fonts' => 'الخطوط',
    'assets/videos' => 'ملفات الفيديو التجريبية',
    
    // مجلدات الإعدادات
    'config' => 'ملفات الإعدادات',
    'config/database' => 'إعدادات قاعدة البيانات',
    'config/security' => 'إعدادات الأمان',
    
    // مجلدات قاعدة البيانات
    'database' => 'ملفات قاعدة البيانات',
    'database/migrations' => 'ملفات الترحيل',
    'database/seeds' => 'بيانات البذر',
    'database/backups' => 'النسخ الاحتياطية',
    
    // المجلد العام
    'public' => 'الملفات العامة',
    'public/css' => 'ملفات CSS العامة',
    'public/js' => 'ملفات JavaScript العامة',
    'public/images' => 'الصور العامة',
    
    // مجلد الإدارة
    'admin' => 'لوحة الإدارة',
    'admin/views' => 'عروض لوحة الإدارة',
    'admin/controllers' => 'متحكمات لوحة الإدارة',
    'admin/assets' => 'أصول لوحة الإدارة',
    
    // مجلدات المكتبات
    'vendor' => 'مكتبات خارجية',
    'vendor/composer' => 'مكتبات Composer',
    'vendor/custom' => 'مكتبات مخصصة',
    
    // مجلد التثبيت
    'install' => 'ملفات التثبيت',
    'install/steps' => 'خطوات التثبيت',
    'install/templates' => 'قوالب التثبيت',
    
    // مجلدات الرفع
    'uploads' => 'ملفات المستخدمين',
    'uploads/videos' => 'ملفات الفيديو',
    'uploads/images' => 'الصور المرفوعة',
    'uploads/subtitles' => 'ملفات الترجمة',
    'uploads/audio' => 'ملفات الصوت',
    'uploads/thumbnails' => 'الصور المصغرة',
    'uploads/temp' => 'ملفات مؤقتة',
    
    // مجلدات إضافية
    'logs' => 'ملفات السجلات',
    'cache' => 'ملفات التخزين المؤقت',
    'sessions' => 'ملفات الجلسات',
    'temp' => 'ملفات مؤقتة عامة',
    
    // مجلدات API
    'api' => 'واجهات برمجة التطبيقات',
    'api/v1' => 'الإصدار الأول من API',
    'api/v1/auth' => 'مصادقة API',
    'api/v1/content' => 'محتوى API',
    'api/v1/user' => 'مستخدم API',
    'api/v1/admin' => 'إدارة API',
    
    // مجلدات الأمان
    'security' => 'ملفات الأمان',
    'security/tokens' => 'رموز الأمان',
    'security/certificates' => 'الشهادات',
    
    // مجلدات التوثيق
    'docs' => 'التوثيق',
    'docs/api' => 'توثيق API',
    'docs/user' => 'دليل المستخدم',
    'docs/admin' => 'دليل الإدارة'
];

$created_folders = [];
$existing_folders = [];
$failed_folders = [];

echo "<h2>🔄 جاري إنشاء المجلدات...</h2>";

foreach ($required_folders as $folder => $description) {
    if (is_dir($folder)) {
        $existing_folders[] = $folder;
        echo "<p style='color: #4CAF50;'>✅ موجود مسبقاً: <strong>$folder</strong> - $description</p>";
    } else {
        if (mkdir($folder, 0755, true)) {
            $created_folders[] = $folder;
            echo "<p style='color: #2196F3;'>🆕 تم إنشاء: <strong>$folder</strong> - $description</p>";
        } else {
            $failed_folders[] = $folder;
            echo "<p style='color: #F44336;'>❌ فشل إنشاء: <strong>$folder</strong> - $description</p>";
        }
    }
}

// إنشاء ملفات .htaccess للحماية
$protected_folders = [
    'config' => 'deny from all',
    'database' => 'deny from all',
    'logs' => 'deny from all',
    'uploads/temp' => 'deny from all',
    'security' => 'deny from all',
    'sessions' => 'deny from all'
];

echo "<h2>🔒 إنشاء ملفات الحماية...</h2>";

foreach ($protected_folders as $folder => $content) {
    if (is_dir($folder)) {
        $htaccess_file = $folder . '/.htaccess';
        if (file_put_contents($htaccess_file, $content)) {
            echo "<p style='color: #FF9800;'>🛡️ تم إنشاء حماية: <strong>$htaccess_file</strong></p>";
        }
    }
}

// إنشاء ملفات index.php فارغة للحماية
echo "<h2>📄 إنشاء ملفات الحماية الإضافية...</h2>";

$index_content = "<?php\n// Access denied\nheader('HTTP/1.0 403 Forbidden');\nexit('Access denied');\n?>";

foreach ($required_folders as $folder => $description) {
    if (is_dir($folder) && !file_exists($folder . '/index.php')) {
        if (file_put_contents($folder . '/index.php', $index_content)) {
            echo "<p style='color: #9C27B0;'>🔐 تم إنشاء حماية: <strong>$folder/index.php</strong></p>";
        }
    }
}

// إنشاء ملفات README
echo "<h2>📚 إنشاء ملفات التوثيق...</h2>";

$readme_files = [
    'app/README.md' => "# App Directory\nThis directory contains the main application files including controllers, models, views, and API endpoints.",
    'uploads/README.md' => "# Uploads Directory\nThis directory contains user uploaded files including videos, images, subtitles, and audio files.",
    'admin/README.md' => "# Admin Directory\nThis directory contains the admin panel files and controllers.",
    'api/README.md' => "# API Directory\nThis directory contains the REST API endpoints for the mobile application.",
    'docs/README.md' => "# Documentation Directory\nThis directory contains project documentation and user guides."
];

foreach ($readme_files as $file => $content) {
    $dir = dirname($file);
    if (is_dir($dir) && !file_exists($file)) {
        if (file_put_contents($file, $content)) {
            echo "<p style='color: #00BCD4;'>📖 تم إنشاء توثيق: <strong>$file</strong></p>";
        }
    }
}

// ملخص النتائج
echo "<h2>📊 ملخص العملية</h2>";

echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem; margin: 2rem 0;'>";

// بطاقة المجلدات المنشأة
echo "<div style='background: rgba(33, 150, 243, 0.1); border: 1px solid rgba(33, 150, 243, 0.3); border-radius: 10px; padding: 1.5rem;'>";
echo "<h3 style='color: #2196F3; margin-bottom: 1rem;'>🆕 مجلدات جديدة</h3>";
echo "<div style='font-size: 2rem; font-weight: bold; color: #2196F3; margin-bottom: 0.5rem;'>" . count($created_folders) . "</div>";
echo "<div style='opacity: 0.8;'>مجلد تم إنشاؤه</div>";
echo "</div>";

// بطاقة المجلدات الموجودة
echo "<div style='background: rgba(76, 175, 80, 0.1); border: 1px solid rgba(76, 175, 80, 0.3); border-radius: 10px; padding: 1.5rem;'>";
echo "<h3 style='color: #4CAF50; margin-bottom: 1rem;'>✅ مجلدات موجودة</h3>";
echo "<div style='font-size: 2rem; font-weight: bold; color: #4CAF50; margin-bottom: 0.5rem;'>" . count($existing_folders) . "</div>";
echo "<div style='opacity: 0.8;'>مجلد موجود مسبقاً</div>";
echo "</div>";

// بطاقة المجلدات الفاشلة
echo "<div style='background: rgba(244, 67, 54, 0.1); border: 1px solid rgba(244, 67, 54, 0.3); border-radius: 10px; padding: 1.5rem;'>";
echo "<h3 style='color: #F44336; margin-bottom: 1rem;'>❌ فشل الإنشاء</h3>";
echo "<div style='font-size: 2rem; font-weight: bold; color: #F44336; margin-bottom: 0.5rem;'>" . count($failed_folders) . "</div>";
echo "<div style='opacity: 0.8;'>مجلد فشل إنشاؤه</div>";
echo "</div>";

// بطاقة الإجمالي
echo "<div style='background: rgba(229, 9, 20, 0.1); border: 1px solid rgba(229, 9, 20, 0.3); border-radius: 10px; padding: 1.5rem;'>";
echo "<h3 style='color: #E50914; margin-bottom: 1rem;'>📁 الإجمالي</h3>";
echo "<div style='font-size: 2rem; font-weight: bold; color: #E50914; margin-bottom: 0.5rem;'>" . count($required_folders) . "</div>";
echo "<div style='opacity: 0.8;'>مجلد مطلوب</div>";
echo "</div>";

echo "</div>";

// نسبة النجاح
$success_rate = count($required_folders) > 0 ? round(((count($created_folders) + count($existing_folders)) / count($required_folders)) * 100, 1) : 0;

echo "<div style='background: linear-gradient(135deg, rgba(47, 47, 47, 0.9) 0%, rgba(35, 35, 35, 0.9) 100%); border-radius: 15px; padding: 2rem; margin: 2rem 0; border: 1px solid rgba(229, 9, 20, 0.2);'>";
echo "<h3 style='text-align: center; color: #E50914; margin-bottom: 2rem;'>🎯 نسبة نجاح العملية</h3>";

$color = $success_rate >= 90 ? '#4CAF50' : ($success_rate >= 70 ? '#FF9800' : '#F44336');
echo "<div style='text-align: center;'>";
echo "<div style='font-size: 4rem; font-weight: bold; color: $color; margin-bottom: 1rem;'>$success_rate%</div>";
echo "<div style='font-size: 1.2rem; opacity: 0.8;'>تم إنشاء/التحقق من " . (count($created_folders) + count($existing_folders)) . " من " . count($required_folders) . " مجلد</div>";
echo "</div>";

// شريط التقدم
echo "<div style='background: rgba(0,0,0,0.3); border-radius: 10px; height: 20px; margin: 2rem 0; overflow: hidden;'>";
echo "<div style='background: linear-gradient(45deg, #E50914, #B8070F); height: 100%; width: $success_rate%; transition: width 0.5s ease;'></div>";
echo "</div>";
echo "</div>";

if (!empty($failed_folders)) {
    echo "<div style='background: rgba(244, 67, 54, 0.1); border: 1px solid rgba(244, 67, 54, 0.3); border-radius: 10px; padding: 2rem; margin: 2rem 0;'>";
    echo "<h3 style='color: #F44336; margin-bottom: 1rem;'>⚠️ مجلدات فشل إنشاؤها:</h3>";
    echo "<ul style='margin-right: 2rem;'>";
    foreach ($failed_folders as $folder) {
        echo "<li style='color: #F44336; margin: 0.5rem 0;'>$folder</li>";
    }
    echo "</ul>";
    echo "<p style='color: #ccc; margin-top: 1rem;'>تأكد من صلاحيات الكتابة في المجلد الجذر للمشروع.</p>";
    echo "</div>";
}

// روابط سريعة
echo "<div style='text-align: center; margin: 3rem 0;'>";
echo "<h3 style='color: #E50914; margin-bottom: 2rem;'>🔗 الخطوات التالية</h3>";

$next_steps = [
    ['url' => 'create_database_tables.php', 'title' => '🗄️ إنشاء جداول قاعدة البيانات', 'color' => '#2196F3'],
    ['url' => 'create_basic_pages.php', 'title' => '📄 إنشاء الصفحات الأساسية', 'color' => '#FF9800'],
    ['url' => 'create_api_endpoints.php', 'title' => '🔌 إنشاء API Endpoints', 'color' => '#9C27B0'],
    ['url' => 'project_audit_complete.php', 'title' => '📊 العودة للمراجعة الشاملة', 'color' => '#E50914'],
    ['url' => 'homepage_working.php', 'title' => '🏠 الصفحة الرئيسية', 'color' => '#4CAF50']
];

foreach ($next_steps as $step) {
    echo "<a href='{$step['url']}' style='background: {$step['color']}; color: white; padding: 1rem 2rem; text-decoration: none; border-radius: 8px; font-weight: bold; margin: 0.5rem; display: inline-block; transition: transform 0.3s ease;' onmouseover='this.style.transform=\"translateY(-3px)\"' onmouseout='this.style.transform=\"translateY(0)\"'>{$step['title']}</a>";
}

echo "</div>";
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📁 إنشاء هيكل المجلدات - Shahid Platform</title>
    <style>
        body {
            font-family: 'Segoe UI', 'Cairo', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #2d2d2d 100%);
            color: #fff;
            min-height: 100vh;
            padding: 2rem;
            margin: 0;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: rgba(47, 47, 47, 0.95);
            border-radius: 20px;
            padding: 3rem;
            border: 2px solid rgba(229, 9, 20, 0.3);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
        }
        h1, h2, h3 { 
            color: #E50914; 
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
        }
        h1 {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 2rem;
            background: linear-gradient(45deg, #E50914, #FF6B35);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        p {
            line-height: 1.6;
            margin: 0.5rem 0;
        }
        ul {
            line-height: 1.8;
            margin-right: 2rem;
        }
        li {
            margin: 0.5rem 0;
        }
        .btn {
            background: linear-gradient(45deg, #E50914, #B8070F);
            color: white;
            padding: 1rem 2rem;
            text-decoration: none;
            border-radius: 8px;
            font-weight: bold;
            margin: 0.5rem;
            display: inline-block;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }
        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(229, 9, 20, 0.4);
        }
        @media (max-width: 768px) {
            .container {
                padding: 1rem;
                margin: 1rem;
            }
            h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- المحتوى يتم عرضه من PHP أعلاه -->
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('📁 أداة إنشاء هيكل المجلدات جاهزة!');
            
            // تأثير شريط التقدم
            const progressBar = document.querySelector('[style*="width: <?php echo $success_rate; ?>%"]');
            if (progressBar) {
                progressBar.style.width = '0%';
                setTimeout(() => {
                    progressBar.style.width = '<?php echo $success_rate; ?>%';
                }, 500);
            }
        });
    </script>
</body>
</html>
