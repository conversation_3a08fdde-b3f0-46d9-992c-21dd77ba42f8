<?php
/**
 * Shahid API - Simple Working API
 * Professional Video Streaming Platform
 */

// CORS Headers
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Content-Type: application/json; charset=utf-8');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Get endpoint from URL or parameter
$endpoint = '';
if (isset($_GET['endpoint'])) {
    $endpoint = $_GET['endpoint'];
} else {
    $uri = $_SERVER['REQUEST_URI'];
    $path = parse_url($uri, PHP_URL_PATH);
    $path = preg_replace('#^.*/api/?#', '', $path);
    $path = str_replace('api.php', '', $path);
    $endpoint = trim($path, '/');
}

// Default to status if no endpoint
if (empty($endpoint)) {
    $endpoint = 'status';
}

// Simple API responses
switch ($endpoint) {
    case 'status':
        echo json_encode([
            'success' => true,
            'message' => 'Shahid API is working perfectly!',
            'data' => [
                'server' => 'Apache/2.4.58',
                'php' => PHP_VERSION,
                'timestamp' => date('Y-m-d H:i:s'),
                'memory_usage' => memory_get_usage(true),
                'endpoints' => [
                    'status' => 'System status',
                    'movies' => 'Movies list',
                    'series' => 'TV series list',
                    'search' => 'Search content'
                ]
            ]
        ]);
        break;
        
    case 'movies':
        echo json_encode([
            'success' => true,
            'message' => 'Movies retrieved successfully',
            'data' => [
                [
                    'id' => 1,
                    'title' => 'فيلم تجريبي 1',
                    'title_en' => 'Test Movie 1',
                    'description' => 'وصف الفيلم التجريبي الأول',
                    'year' => 2024,
                    'duration' => '120 دقيقة',
                    'genre' => 'دراما',
                    'rating' => 8.5,
                    'poster' => 'https://via.placeholder.com/300x450/E50914/FFFFFF?text=Movie+1',
                    'trailer' => 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
                    'status' => 'active'
                ],
                [
                    'id' => 2,
                    'title' => 'فيلم تجريبي 2',
                    'title_en' => 'Test Movie 2',
                    'description' => 'وصف الفيلم التجريبي الثاني',
                    'year' => 2024,
                    'duration' => '95 دقيقة',
                    'genre' => 'كوميديا',
                    'rating' => 7.8,
                    'poster' => 'https://via.placeholder.com/300x450/E50914/FFFFFF?text=Movie+2',
                    'trailer' => 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
                    'status' => 'active'
                ],
                [
                    'id' => 3,
                    'title' => 'فيلم تجريبي 3',
                    'title_en' => 'Test Movie 3',
                    'description' => 'وصف الفيلم التجريبي الثالث',
                    'year' => 2023,
                    'duration' => '110 دقيقة',
                    'genre' => 'أكشن',
                    'rating' => 9.2,
                    'poster' => 'https://via.placeholder.com/300x450/E50914/FFFFFF?text=Movie+3',
                    'trailer' => 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
                    'status' => 'active'
                ]
            ],
            'total' => 3,
            'page' => 1,
            'per_page' => 10
        ]);
        break;
        
    case 'series':
        echo json_encode([
            'success' => true,
            'message' => 'TV series retrieved successfully',
            'data' => [
                [
                    'id' => 1,
                    'title' => 'مسلسل تجريبي 1',
                    'title_en' => 'Test Series 1',
                    'description' => 'وصف المسلسل التجريبي الأول',
                    'year' => 2024,
                    'seasons' => 2,
                    'episodes' => 20,
                    'genre' => 'دراما',
                    'rating' => 8.7,
                    'poster' => 'https://via.placeholder.com/300x450/E50914/FFFFFF?text=Series+1',
                    'trailer' => 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
                    'status' => 'ongoing'
                ],
                [
                    'id' => 2,
                    'title' => 'مسلسل تجريبي 2',
                    'title_en' => 'Test Series 2',
                    'description' => 'وصف المسلسل التجريبي الثاني',
                    'year' => 2023,
                    'seasons' => 1,
                    'episodes' => 12,
                    'genre' => 'كوميديا',
                    'rating' => 7.9,
                    'poster' => 'https://via.placeholder.com/300x450/E50914/FFFFFF?text=Series+2',
                    'trailer' => 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
                    'status' => 'completed'
                ]
            ],
            'total' => 2,
            'page' => 1,
            'per_page' => 10
        ]);
        break;
        
    case 'search':
        $query = $_GET['q'] ?? '';
        if (empty($query)) {
            echo json_encode([
                'success' => false,
                'error' => 'Search query is required',
                'message' => 'Please provide a search query using ?q=your_search_term'
            ]);
        } else {
            echo json_encode([
                'success' => true,
                'message' => "Search results for: $query",
                'data' => [
                    'movies' => [
                        [
                            'id' => 1,
                            'title' => 'فيلم يحتوي على: ' . $query,
                            'type' => 'movie',
                            'year' => 2024,
                            'rating' => 8.5
                        ]
                    ],
                    'series' => [
                        [
                            'id' => 1,
                            'title' => 'مسلسل يحتوي على: ' . $query,
                            'type' => 'series',
                            'year' => 2024,
                            'rating' => 8.7
                        ]
                    ]
                ],
                'query' => $query,
                'total_results' => 2
            ]);
        }
        break;
        
    case 'episodes':
        $seriesId = $_GET['series_id'] ?? 1;
        echo json_encode([
            'success' => true,
            'message' => "Episodes for series ID: $seriesId",
            'data' => [
                [
                    'id' => 1,
                    'series_id' => $seriesId,
                    'season' => 1,
                    'episode' => 1,
                    'title' => 'الحلقة الأولى',
                    'description' => 'وصف الحلقة الأولى',
                    'duration' => '45 دقيقة',
                    'air_date' => '2024-01-01'
                ],
                [
                    'id' => 2,
                    'series_id' => $seriesId,
                    'season' => 1,
                    'episode' => 2,
                    'title' => 'الحلقة الثانية',
                    'description' => 'وصف الحلقة الثانية',
                    'duration' => '47 دقيقة',
                    'air_date' => '2024-01-08'
                ]
            ],
            'series_id' => $seriesId,
            'total_episodes' => 2
        ]);
        break;
        
    default:
        http_response_code(404);
        echo json_encode([
            'success' => false,
            'error' => 'Endpoint not found',
            'message' => "The endpoint '$endpoint' is not available",
            'available_endpoints' => [
                'status' => 'System status',
                'movies' => 'Movies list',
                'series' => 'TV series list',
                'episodes' => 'Episodes list (requires series_id)',
                'search' => 'Search content (requires q parameter)'
            ]
        ]);
        break;
}
?>
