<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مشغل الفيديو المتقدم - Shahid Platform</title>
    <style>
        :root {
            --primary-color: #E50914;
            --secondary-color: #B8070F;
            --dark-bg: #000000;
            --player-bg: #0f0f0f;
            --controls-bg: rgba(0, 0, 0, 0.8);
            --text-primary: #ffffff;
            --text-secondary: #cccccc;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', 'Cairo', 'Helvetica Neue', Arial, sans-serif;
            background: var(--dark-bg);
            color: var(--text-primary);
            overflow: hidden;
        }

        .player-container {
            position: relative;
            width: 100vw;
            height: 100vh;
            background: var(--player-bg);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .video-player {
            width: 100%;
            height: 100%;
            object-fit: contain;
            background: #000;
        }

        .controls-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(transparent, var(--controls-bg));
            padding: 2rem 1.5rem 1.5rem;
            transform: translateY(100%);
            transition: transform 0.3s ease;
            z-index: 100;
        }

        .player-container:hover .controls-overlay,
        .controls-overlay.show {
            transform: translateY(0);
        }

        .progress-container {
            margin-bottom: 1rem;
            cursor: pointer;
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
            position: relative;
            overflow: hidden;
        }

        .progress-bar:hover {
            height: 8px;
        }

        .progress-filled {
            height: 100%;
            background: var(--primary-color);
            border-radius: 3px;
            width: 0%;
            transition: width 0.1s ease;
        }

        .progress-buffered {
            position: absolute;
            top: 0;
            height: 100%;
            background: rgba(255, 255, 255, 0.5);
            border-radius: 3px;
            width: 0%;
        }

        .controls {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .controls-left {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .controls-center {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 1rem;
        }

        .controls-right {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .control-btn {
            background: none;
            border: none;
            color: var(--text-primary);
            font-size: 1.5rem;
            cursor: pointer;
            padding: 0.5rem;
            border-radius: 50%;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 48px;
            height: 48px;
        }

        .control-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: scale(1.1);
        }

        .control-btn.primary {
            background: var(--primary-color);
            font-size: 2rem;
            width: 56px;
            height: 56px;
        }

        .control-btn.primary:hover {
            background: var(--secondary-color);
        }

        .time-display {
            font-size: 0.9rem;
            color: var(--text-secondary);
            min-width: 100px;
            text-align: center;
        }

        .volume-control {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .volume-slider {
            width: 80px;
            height: 4px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 2px;
            outline: none;
            cursor: pointer;
            -webkit-appearance: none;
        }

        .volume-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            width: 16px;
            height: 16px;
            background: var(--primary-color);
            border-radius: 50%;
            cursor: pointer;
        }

        .loading-spinner {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 60px;
            height: 60px;
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-top: 4px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            z-index: 200;
            display: none;
        }

        @keyframes spin {
            0% { transform: translate(-50%, -50%) rotate(0deg); }
            100% { transform: translate(-50%, -50%) rotate(360deg); }
        }

        .center-play-btn {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 80px;
            height: 80px;
            background: rgba(229, 9, 20, 0.9);
            border: none;
            border-radius: 50%;
            color: white;
            font-size: 2.5rem;
            cursor: pointer;
            transition: all 0.3s ease;
            z-index: 150;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .center-play-btn:hover {
            transform: translate(-50%, -50%) scale(1.1);
            background: var(--primary-color);
        }

        .center-play-btn.hidden {
            opacity: 0;
            visibility: hidden;
        }

        .info-overlay {
            position: absolute;
            top: 2rem;
            left: 2rem;
            right: 2rem;
            background: rgba(0, 0, 0, 0.7);
            border-radius: 10px;
            padding: 1.5rem;
            transform: translateY(-100%);
            transition: transform 0.3s ease;
            backdrop-filter: blur(10px);
            z-index: 120;
        }

        .info-overlay.show {
            transform: translateY(0);
        }

        .video-title {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
            color: var(--primary-color);
        }

        .video-description {
            color: var(--text-secondary);
            line-height: 1.5;
            margin-bottom: 1rem;
        }

        .video-meta {
            display: flex;
            gap: 2rem;
            font-size: 0.9rem;
            color: var(--text-secondary);
        }

        @media (max-width: 768px) {
            .controls {
                gap: 0.5rem;
            }

            .control-btn {
                width: 40px;
                height: 40px;
                font-size: 1.2rem;
            }

            .control-btn.primary {
                width: 48px;
                height: 48px;
                font-size: 1.8rem;
            }

            .info-overlay {
                left: 1rem;
                right: 1rem;
                top: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="player-container" id="playerContainer">
        <video
            class="video-player"
            id="videoPlayer"
            preload="metadata"
            poster="https://via.placeholder.com/1920x1080/0f0f0f/ffffff?text=Shahid+Platform"
        >
            <source src="https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4" type="video/mp4">
            <source src="https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.webm" type="video/webm">
            المتصفح لا يدعم تشغيل الفيديو.
        </video>

        <div class="loading-spinner" id="loadingSpinner"></div>

        <button class="center-play-btn" id="centerPlayBtn">▶</button>

        <div class="info-overlay" id="infoOverlay">
            <div class="video-title">Big Buck Bunny</div>
            <div class="video-description">
                فيلم قصير مفتوح المصدر من إنتاج Blender Foundation. يحكي قصة أرنب كبير يواجه مجموعة من القوارض الصغيرة المؤذية.
            </div>
            <div class="video-meta">
                <span>المدة: 10:34</span>
                <span>الجودة: 1080p</span>
                <span>السنة: 2008</span>
            </div>
        </div>

        <div class="controls-overlay" id="controlsOverlay">
            <div class="progress-container" id="progressContainer">
                <div class="progress-bar" id="progressBar">
                    <div class="progress-buffered" id="progressBuffered"></div>
                    <div class="progress-filled" id="progressFilled"></div>
                </div>
            </div>

            <div class="controls">
                <div class="controls-left">
                    <button class="control-btn primary" id="playPauseBtn">▶</button>
                    <button class="control-btn" id="skipBackBtn" title="الرجوع 10 ثوان">⏪</button>
                    <button class="control-btn" id="skipForwardBtn" title="التقدم 10 ثوان">⏩</button>
                </div>

                <div class="controls-center">
                    <div class="time-display">
                        <span id="currentTime">00:00</span> / <span id="duration">00:00</span>
                    </div>
                </div>

                <div class="controls-right">
                    <div class="volume-control">
                        <button class="control-btn" id="muteBtn" title="كتم الصوت">🔊</button>
                        <input type="range" class="volume-slider" id="volumeSlider" min="0" max="100" value="100">
                    </div>
                    <button class="control-btn" id="infoBtn" title="معلومات الفيديو">ℹ️</button>
                    <button class="control-btn" id="fullscreenBtn" title="ملء الشاشة">⛶</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        class VideoPlayer {
            constructor() {
                this.video = document.getElementById('videoPlayer');
                this.playerContainer = document.getElementById('playerContainer');
                this.controlsOverlay = document.getElementById('controlsOverlay');
                this.loadingSpinner = document.getElementById('loadingSpinner');
                this.centerPlayBtn = document.getElementById('centerPlayBtn');
                this.infoOverlay = document.getElementById('infoOverlay');

                this.playPauseBtn = document.getElementById('playPauseBtn');
                this.skipBackBtn = document.getElementById('skipBackBtn');
                this.skipForwardBtn = document.getElementById('skipForwardBtn');
                this.muteBtn = document.getElementById('muteBtn');
                this.volumeSlider = document.getElementById('volumeSlider');
                this.fullscreenBtn = document.getElementById('fullscreenBtn');
                this.infoBtn = document.getElementById('infoBtn');

                this.progressContainer = document.getElementById('progressContainer');
                this.progressBar = document.getElementById('progressBar');
                this.progressFilled = document.getElementById('progressFilled');
                this.progressBuffered = document.getElementById('progressBuffered');

                this.currentTimeEl = document.getElementById('currentTime');
                this.durationEl = document.getElementById('duration');

                this.isPlaying = false;
                this.isMuted = false;
                this.isFullscreen = false;
                this.controlsTimeout = null;

                this.init();
            }

            init() {
                this.setupEventListeners();
                this.setupKeyboardControls();
                console.log('🎥 Video Player initialized');
            }

            setupEventListeners() {
                this.video.addEventListener('loadedmetadata', () => this.onLoadedMetadata());
                this.video.addEventListener('timeupdate', () => this.onTimeUpdate());
                this.video.addEventListener('progress', () => this.onProgress());
                this.video.addEventListener('play', () => this.onPlay());
                this.video.addEventListener('pause', () => this.onPause());
                this.video.addEventListener('ended', () => this.onEnded());
                this.video.addEventListener('waiting', () => this.showLoadingSpinner());
                this.video.addEventListener('canplay', () => this.hideLoadingSpinner());

                this.centerPlayBtn.addEventListener('click', () => this.togglePlay());
                this.playPauseBtn.addEventListener('click', () => this.togglePlay());
                this.skipBackBtn.addEventListener('click', () => this.skipBack());
                this.skipForwardBtn.addEventListener('click', () => this.skipForward());
                this.muteBtn.addEventListener('click', () => this.toggleMute());
                this.volumeSlider.addEventListener('input', (e) => this.setVolume(e.target.value));
                this.fullscreenBtn.addEventListener('click', () => this.toggleFullscreen());
                this.infoBtn.addEventListener('click', () => this.toggleInfo());

                this.progressContainer.addEventListener('click', (e) => this.seek(e));

                this.playerContainer.addEventListener('mousemove', () => this.showControls());
                this.playerContainer.addEventListener('mouseleave', () => this.hideControls());

                this.video.addEventListener('click', () => this.togglePlay());

                document.addEventListener('fullscreenchange', () => this.onFullscreenChange());
            }

            setupKeyboardControls() {
                document.addEventListener('keydown', (e) => {
                    if (e.target.tagName === 'INPUT') return;

                    switch(e.code) {
                        case 'Space':
                            e.preventDefault();
                            this.togglePlay();
                            break;
                        case 'ArrowLeft':
                            e.preventDefault();
                            this.skipBack();
                            break;
                        case 'ArrowRight':
                            e.preventDefault();
                            this.skipForward();
                            break;
                        case 'ArrowUp':
                            e.preventDefault();
                            this.changeVolume(10);
                            break;
                        case 'ArrowDown':
                            e.preventDefault();
                            this.changeVolume(-10);
                            break;
                        case 'KeyM':
                            this.toggleMute();
                            break;
                        case 'KeyF':
                            this.toggleFullscreen();
                            break;
                        case 'KeyI':
                            this.toggleInfo();
                            break;
                        case 'Escape':
                            this.hideInfo();
                            break;
                    }
                });
            }

            togglePlay() {
                if (this.video.paused) {
                    this.play();
                } else {
                    this.pause();
                }
            }

            play() {
                this.video.play();
                this.isPlaying = true;
                this.playPauseBtn.textContent = '⏸';
                this.centerPlayBtn.classList.add('hidden');
            }

            pause() {
                this.video.pause();
                this.isPlaying = false;
                this.playPauseBtn.textContent = '▶';
                this.centerPlayBtn.classList.remove('hidden');
            }

            skipBack() {
                this.video.currentTime = Math.max(0, this.video.currentTime - 10);
            }

            skipForward() {
                this.video.currentTime = Math.min(this.video.duration, this.video.currentTime + 10);
            }

            toggleMute() {
                if (this.video.muted) {
                    this.video.muted = false;
                    this.muteBtn.textContent = '🔊';
                    this.volumeSlider.value = this.video.volume * 100;
                } else {
                    this.video.muted = true;
                    this.muteBtn.textContent = '🔇';
                }
                this.isMuted = this.video.muted;
            }

            setVolume(value) {
                this.video.volume = value / 100;
                this.video.muted = false;
                this.muteBtn.textContent = value > 0 ? '🔊' : '🔇';
            }

            changeVolume(delta) {
                const newVolume = Math.max(0, Math.min(100, this.video.volume * 100 + delta));
                this.setVolume(newVolume);
                this.volumeSlider.value = newVolume;
            }

            seek(e) {
                const rect = this.progressBar.getBoundingClientRect();
                const pos = (e.clientX - rect.left) / rect.width;
                this.video.currentTime = pos * this.video.duration;
            }

            toggleFullscreen() {
                if (!this.isFullscreen) {
                    if (this.playerContainer.requestFullscreen) {
                        this.playerContainer.requestFullscreen();
                    } else if (this.playerContainer.webkitRequestFullscreen) {
                        this.playerContainer.webkitRequestFullscreen();
                    }
                } else {
                    if (document.exitFullscreen) {
                        document.exitFullscreen();
                    } else if (document.webkitExitFullscreen) {
                        document.webkitExitFullscreen();
                    }
                }
            }

            toggleInfo() {
                this.infoOverlay.classList.toggle('show');
            }

            hideInfo() {
                this.infoOverlay.classList.remove('show');
            }

            onLoadedMetadata() {
                this.durationEl.textContent = this.formatTime(this.video.duration);
                this.hideLoadingSpinner();
            }

            onTimeUpdate() {
                const progress = (this.video.currentTime / this.video.duration) * 100;
                this.progressFilled.style.width = progress + '%';
                this.currentTimeEl.textContent = this.formatTime(this.video.currentTime);
            }

            onProgress() {
                if (this.video.buffered.length > 0) {
                    const buffered = (this.video.buffered.end(0) / this.video.duration) * 100;
                    this.progressBuffered.style.width = buffered + '%';
                }
            }

            onPlay() {
                this.isPlaying = true;
                this.playPauseBtn.textContent = '⏸';
                this.centerPlayBtn.classList.add('hidden');
            }

            onPause() {
                this.isPlaying = false;
                this.playPauseBtn.textContent = '▶';
                this.centerPlayBtn.classList.remove('hidden');
            }

            onEnded() {
                this.isPlaying = false;
                this.playPauseBtn.textContent = '▶';
                this.centerPlayBtn.classList.remove('hidden');
                console.log('Video ended');
            }

            onFullscreenChange() {
                this.isFullscreen = !!document.fullscreenElement;
            }

            showControls() {
                this.controlsOverlay.classList.add('show');
                clearTimeout(this.controlsTimeout);
                this.controlsTimeout = setTimeout(() => {
                    if (this.isPlaying) {
                        this.hideControls();
                    }
                }, 3000);
            }

            hideControls() {
                if (!this.playerContainer.matches(':hover')) {
                    this.controlsOverlay.classList.remove('show');
                }
            }

            showLoadingSpinner() {
                this.loadingSpinner.style.display = 'block';
            }

            hideLoadingSpinner() {
                this.loadingSpinner.style.display = 'none';
            }

            formatTime(seconds) {
                const mins = Math.floor(seconds / 60);
                const secs = Math.floor(seconds % 60);
                return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
            }
        }

        document.addEventListener('DOMContentLoaded', function() {
            const player = new VideoPlayer();
            console.log('🎬 Video Player loaded successfully!');
        });

        window.addEventListener('load', function() {
            const loadTime = performance.now();
            console.log(`⚡ Player loaded in ${Math.round(loadTime)}ms`);
        });
    </script>
</body>
</html>

        .player-container:-webkit-full-screen {
            width: 100vw;
            height: 100vh;
        }

        .player-container:-moz-full-screen {
            width: 100vw;
            height: 100vh;
        }

        .player-container:fullscreen {
            width: 100vw;
            height: 100vh;
        }
    </style>
</head>
<body>
    <div class="player-container" id="playerContainer">
        <video
            class="video-player"
            id="videoPlayer"
            preload="metadata"
            poster="https://via.placeholder.com/1920x1080/0f0f0f/ffffff?text=Shahid+Platform"
        >
            <source src="https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4" type="video/mp4">
            <source src="https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.webm" type="video/webm">
            المتصفح لا يدعم تشغيل الفيديو.
        </video>

        <div class="loading-spinner" id="loadingSpinner"></div>

        <button class="center-play-btn" id="centerPlayBtn">▶</button>

        <div class="info-overlay" id="infoOverlay">
            <div class="video-title">Big Buck Bunny</div>
            <div class="video-description">
                فيلم قصير مفتوح المصدر من إنتاج Blender Foundation. يحكي قصة أرنب كبير يواجه مجموعة من القوارض الصغيرة المؤذية.
            </div>
            <div class="video-meta">
                <span>المدة: 10:34</span>
                <span>الجودة: 1080p</span>
                <span>السنة: 2008</span>
            </div>
        </div>
