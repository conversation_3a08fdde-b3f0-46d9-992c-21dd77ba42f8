<?php
session_start();

// التحقق من المعاملات
$type = $_GET['type'] ?? '';
$id = intval($_GET['id'] ?? 0);
$episode_id = intval($_GET['episode_id'] ?? 0);

if (!in_array($type, ['movie', 'series', 'episode']) || $id <= 0) {
    header('Location: index.php');
    exit;
}

try {
    $pdo = new PDO("mysql:host=localhost;dbname=shahid_platform;charset=utf8mb4", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $content = null;
    $episodes = [];
    $currentEpisode = null;
    
    if ($type === 'movie') {
        // جلب بيانات الفيلم
        $stmt = $pdo->prepare("SELECT * FROM movies WHERE id = ? AND status = 'active'");
        $stmt->execute([$id]);
        $content = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($content) {
            // تحديث عدد المشاهدات
            $updateStmt = $pdo->prepare("UPDATE movies SET views = views + 1 WHERE id = ?");
            $updateStmt->execute([$id]);
        }
        
    } elseif ($type === 'series') {
        // جلب بيانات المسلسل
        $stmt = $pdo->prepare("SELECT * FROM series WHERE id = ? AND status = 'active'");
        $stmt->execute([$id]);
        $content = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($content) {
            // جلب الحلقات
            $episodesStmt = $pdo->prepare("
                SELECT * FROM episodes 
                WHERE series_id = ? AND status = 'active' 
                ORDER BY season_number, episode_number
            ");
            $episodesStmt->execute([$id]);
            $episodes = $episodesStmt->fetchAll(PDO::FETCH_ASSOC);
            
            // إذا لم يتم تحديد حلقة، اختر الأولى
            if ($episode_id > 0) {
                $currentEpisode = array_filter($episodes, fn($ep) => $ep['id'] == $episode_id)[0] ?? null;
            } else {
                $currentEpisode = $episodes[0] ?? null;
            }
            
            if ($currentEpisode) {
                // تحديث عدد مشاهدات الحلقة
                $updateStmt = $pdo->prepare("UPDATE episodes SET views = views + 1 WHERE id = ?");
                $updateStmt->execute([$currentEpisode['id']]);
            }
        }
        
    } elseif ($type === 'episode') {
        // جلب بيانات الحلقة مباشرة
        $stmt = $pdo->prepare("
            SELECT e.*, s.title as series_title, s.id as series_id 
            FROM episodes e 
            JOIN series s ON e.series_id = s.id 
            WHERE e.id = ? AND e.status = 'active'
        ");
        $stmt->execute([$id]);
        $currentEpisode = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($currentEpisode) {
            $content = [
                'id' => $currentEpisode['series_id'],
                'title' => $currentEpisode['series_title']
            ];
            
            // جلب باقي حلقات المسلسل
            $episodesStmt = $pdo->prepare("
                SELECT * FROM episodes 
                WHERE series_id = ? AND status = 'active' 
                ORDER BY season_number, episode_number
            ");
            $episodesStmt->execute([$currentEpisode['series_id']]);
            $episodes = $episodesStmt->fetchAll(PDO::FETCH_ASSOC);
            
            // تحديث عدد المشاهدات
            $updateStmt = $pdo->prepare("UPDATE episodes SET views = views + 1 WHERE id = ?");
            $updateStmt->execute([$id]);
        }
    }
    
    if (!$content) {
        header('Location: index.php');
        exit;
    }
    
    // تسجيل سجل المشاهدة للمستخدمين المسجلين
    if (isset($_SESSION['user_id'])) {
        $watchType = $type === 'movie' ? 'movie' : 'episode';
        $watchId = $type === 'movie' ? $id : ($currentEpisode['id'] ?? 0);
        
        if ($watchId > 0) {
            $watchStmt = $pdo->prepare("
                INSERT INTO watch_history (user_id, content_type, content_id, episode_id, last_watched) 
                VALUES (?, ?, ?, ?, NOW()) 
                ON DUPLICATE KEY UPDATE last_watched = NOW()
            ");
            $watchStmt->execute([
                $_SESSION['user_id'], 
                $watchType, 
                $type === 'movie' ? $id : $currentEpisode['series_id'], 
                $type === 'movie' ? null : $currentEpisode['id']
            ]);
        }
    }
    
} catch (Exception $e) {
    header('Location: index.php');
    exit;
}

// تحديد الفيديو المراد تشغيله
$videoUrl = '';
$videoTitle = '';

if ($type === 'movie') {
    $videoUrl = $content['video_url'] ?? '';
    $videoTitle = $content['title'];
} else {
    $videoUrl = $currentEpisode['video_url'] ?? '';
    $videoTitle = $content['title'] . ' - ' . ($currentEpisode['title'] ?? 'الحلقة ' . $currentEpisode['episode_number']);
}

// إذا لم يكن هناك رابط فيديو، استخدم فيديو تجريبي
if (empty($videoUrl)) {
    $videoUrl = 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4';
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($videoTitle); ?> - Shahid Platform</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', 'Cairo', Tahoma, Geneva, Verdana, sans-serif;
            background: #000;
            color: #fff;
            overflow-x: hidden;
        }
        
        .player-container {
            position: relative;
            width: 100%;
            height: 100vh;
            background: #000;
        }
        
        .video-player {
            width: 100%;
            height: 100%;
            background: #000;
        }
        
        .video-player video {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }
        
        .player-controls {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(transparent, rgba(0,0,0,0.8));
            padding: 2rem;
            transform: translateY(100%);
            transition: transform 0.3s ease;
        }
        
        .player-container:hover .player-controls,
        .player-controls.show {
            transform: translateY(0);
        }
        
        .controls-row {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1rem;
        }
        
        .play-btn {
            background: none;
            border: none;
            color: #fff;
            font-size: 2rem;
            cursor: pointer;
            padding: 0.5rem;
            border-radius: 50%;
            transition: all 0.3s ease;
        }
        
        .play-btn:hover {
            background: rgba(255, 255, 255, 0.1);
        }
        
        .progress-container {
            flex: 1;
            height: 6px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
            cursor: pointer;
            position: relative;
        }
        
        .progress-bar {
            height: 100%;
            background: #E50914;
            border-radius: 3px;
            width: 0%;
            transition: width 0.1s ease;
        }
        
        .time-display {
            font-size: 0.9rem;
            color: #ccc;
            min-width: 100px;
            text-align: center;
        }
        
        .volume-container {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .volume-btn {
            background: none;
            border: none;
            color: #fff;
            font-size: 1.2rem;
            cursor: pointer;
            padding: 0.5rem;
        }
        
        .volume-slider {
            width: 80px;
            height: 4px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 2px;
            outline: none;
            cursor: pointer;
        }
        
        .fullscreen-btn {
            background: none;
            border: none;
            color: #fff;
            font-size: 1.2rem;
            cursor: pointer;
            padding: 0.5rem;
        }
        
        .video-info {
            position: absolute;
            top: 2rem;
            left: 2rem;
            right: 2rem;
            background: rgba(0, 0, 0, 0.7);
            padding: 1rem;
            border-radius: 10px;
            transform: translateY(-100%);
            transition: transform 0.3s ease;
        }
        
        .player-container:hover .video-info,
        .video-info.show {
            transform: translateY(0);
        }
        
        .video-title {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
            color: #E50914;
        }
        
        .video-meta {
            color: #ccc;
            font-size: 0.9rem;
        }
        
        .episodes-sidebar {
            position: fixed;
            top: 0;
            right: -400px;
            width: 400px;
            height: 100vh;
            background: rgba(47, 47, 47, 0.95);
            backdrop-filter: blur(10px);
            transition: right 0.3s ease;
            z-index: 1000;
            overflow-y: auto;
        }
        
        .episodes-sidebar.show {
            right: 0;
        }
        
        .episodes-header {
            padding: 1rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .episodes-list {
            padding: 1rem;
        }
        
        .episode-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 0.5rem;
            text-decoration: none;
            color: #fff;
        }
        
        .episode-item:hover {
            background: rgba(229, 9, 20, 0.1);
        }
        
        .episode-item.current {
            background: rgba(229, 9, 20, 0.2);
            border: 1px solid #E50914;
        }
        
        .episode-number {
            background: #E50914;
            color: #fff;
            padding: 0.5rem;
            border-radius: 50%;
            min-width: 40px;
            text-align: center;
            font-weight: bold;
        }
        
        .episode-info h4 {
            margin-bottom: 0.25rem;
        }
        
        .episode-info p {
            color: #ccc;
            font-size: 0.9rem;
        }
        
        .toggle-episodes {
            position: fixed;
            top: 50%;
            right: 1rem;
            transform: translateY(-50%);
            background: rgba(229, 9, 20, 0.8);
            color: #fff;
            border: none;
            padding: 1rem;
            border-radius: 50%;
            cursor: pointer;
            font-size: 1.2rem;
            z-index: 999;
            transition: all 0.3s ease;
        }
        
        .toggle-episodes:hover {
            background: #E50914;
            transform: translateY(-50%) scale(1.1);
        }
        
        .back-btn {
            position: fixed;
            top: 1rem;
            left: 1rem;
            background: rgba(0, 0, 0, 0.7);
            color: #fff;
            border: none;
            padding: 1rem;
            border-radius: 50%;
            cursor: pointer;
            font-size: 1.2rem;
            z-index: 999;
            transition: all 0.3s ease;
        }
        
        .back-btn:hover {
            background: rgba(229, 9, 20, 0.8);
        }
        
        @media (max-width: 768px) {
            .episodes-sidebar {
                width: 100%;
                right: -100%;
            }
            
            .video-info {
                left: 1rem;
                right: 1rem;
                top: 1rem;
            }
            
            .player-controls {
                padding: 1rem;
            }
            
            .controls-row {
                flex-wrap: wrap;
                gap: 0.5rem;
            }
            
            .volume-container {
                order: 3;
                width: 100%;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="player-container" id="playerContainer">
        <button class="back-btn" onclick="goBack()">
            ← العودة
        </button>
        
        <?php if (!empty($episodes)): ?>
            <button class="toggle-episodes" onclick="toggleEpisodes()">
                📺
            </button>
        <?php endif; ?>
        
        <div class="video-info" id="videoInfo">
            <div class="video-title"><?php echo htmlspecialchars($videoTitle); ?></div>
            <div class="video-meta">
                <?php if ($type === 'movie'): ?>
                    <span>🎬 فيلم</span>
                    <?php if ($content['duration']): ?>
                        <span> • ⏱️ <?php echo $content['duration']; ?> دقيقة</span>
                    <?php endif; ?>
                    <?php if ($content['release_year']): ?>
                        <span> • 📅 <?php echo $content['release_year']; ?></span>
                    <?php endif; ?>
                <?php else: ?>
                    <span>📺 مسلسل</span>
                    <?php if ($currentEpisode): ?>
                        <span> • الموسم <?php echo $currentEpisode['season_number']; ?></span>
                        <span> • الحلقة <?php echo $currentEpisode['episode_number']; ?></span>
                        <?php if ($currentEpisode['duration']): ?>
                            <span> • ⏱️ <?php echo $currentEpisode['duration']; ?> دقيقة</span>
                        <?php endif; ?>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
        
        <div class="video-player">
            <video id="videoElement" controls preload="metadata">
                <source src="<?php echo htmlspecialchars($videoUrl); ?>" type="video/mp4">
                متصفحك لا يدعم تشغيل الفيديو.
            </video>
        </div>
        
        <div class="player-controls" id="playerControls">
            <div class="controls-row">
                <button class="play-btn" id="playBtn" onclick="togglePlay()">
                    ▶️
                </button>
                
                <div class="progress-container" onclick="seek(event)">
                    <div class="progress-bar" id="progressBar"></div>
                </div>
                
                <div class="time-display" id="timeDisplay">
                    00:00 / 00:00
                </div>
                
                <div class="volume-container">
                    <button class="volume-btn" id="volumeBtn" onclick="toggleMute()">
                        🔊
                    </button>
                    <input type="range" class="volume-slider" id="volumeSlider" 
                           min="0" max="100" value="100" onchange="setVolume(this.value)">
                </div>
                
                <button class="fullscreen-btn" onclick="toggleFullscreen()">
                    ⛶
                </button>
            </div>
        </div>
    </div>
    
    <?php if (!empty($episodes)): ?>
        <div class="episodes-sidebar" id="episodesSidebar">
            <div class="episodes-header">
                <h3>الحلقات</h3>
                <button onclick="toggleEpisodes()" style="background: none; border: none; color: #fff; font-size: 1.5rem; cursor: pointer;">
                    ✕
                </button>
            </div>
            
            <div class="episodes-list">
                <?php 
                $currentSeason = null;
                foreach ($episodes as $episode): 
                    if ($currentSeason !== $episode['season_number']):
                        $currentSeason = $episode['season_number'];
                ?>
                        <h4 style="color: #E50914; margin: 1rem 0 0.5rem 0; padding: 0.5rem; border-bottom: 1px solid rgba(229, 9, 20, 0.3);">
                            الموسم <?php echo $currentSeason; ?>
                        </h4>
                <?php endif; ?>
                
                <a href="watch.php?type=series&id=<?php echo $content['id']; ?>&episode_id=<?php echo $episode['id']; ?>" 
                   class="episode-item <?php echo ($currentEpisode && $currentEpisode['id'] == $episode['id']) ? 'current' : ''; ?>">
                    <div class="episode-number"><?php echo $episode['episode_number']; ?></div>
                    <div class="episode-info">
                        <h4><?php echo htmlspecialchars($episode['title'] ?: 'الحلقة ' . $episode['episode_number']); ?></h4>
                        <p>
                            <?php if ($episode['duration']): ?>
                                ⏱️ <?php echo $episode['duration']; ?> دقيقة
                            <?php endif; ?>
                            <?php if ($episode['air_date']): ?>
                                • 📅 <?php echo date('Y-m-d', strtotime($episode['air_date'])); ?>
                            <?php endif; ?>
                        </p>
                    </div>
                </a>
                <?php endforeach; ?>
            </div>
        </div>
    <?php endif; ?>
    
    <script>
        const video = document.getElementById('videoElement');
        const playBtn = document.getElementById('playBtn');
        const progressBar = document.getElementById('progressBar');
        const timeDisplay = document.getElementById('timeDisplay');
        const volumeBtn = document.getElementById('volumeBtn');
        const volumeSlider = document.getElementById('volumeSlider');
        const playerContainer = document.getElementById('playerContainer');
        const playerControls = document.getElementById('playerControls');
        const videoInfo = document.getElementById('videoInfo');
        
        let controlsTimeout;
        
        // تشغيل/إيقاف الفيديو
        function togglePlay() {
            if (video.paused) {
                video.play();
                playBtn.textContent = '⏸️';
            } else {
                video.pause();
                playBtn.textContent = '▶️';
            }
        }
        
        // تحديث شريط التقدم
        video.addEventListener('timeupdate', function() {
            const progress = (video.currentTime / video.duration) * 100;
            progressBar.style.width = progress + '%';
            
            const currentTime = formatTime(video.currentTime);
            const duration = formatTime(video.duration);
            timeDisplay.textContent = currentTime + ' / ' + duration;
        });
        
        // البحث في الفيديو
        function seek(event) {
            const progressContainer = event.currentTarget;
            const clickX = event.offsetX;
            const width = progressContainer.offsetWidth;
            const duration = video.duration;
            
            video.currentTime = (clickX / width) * duration;
        }
        
        // تنسيق الوقت
        function formatTime(seconds) {
            if (isNaN(seconds)) return '00:00';
            
            const minutes = Math.floor(seconds / 60);
            const remainingSeconds = Math.floor(seconds % 60);
            
            return minutes.toString().padStart(2, '0') + ':' + 
                   remainingSeconds.toString().padStart(2, '0');
        }
        
        // كتم/إلغاء كتم الصوت
        function toggleMute() {
            if (video.muted) {
                video.muted = false;
                volumeBtn.textContent = '🔊';
                volumeSlider.value = video.volume * 100;
            } else {
                video.muted = true;
                volumeBtn.textContent = '🔇';
                volumeSlider.value = 0;
            }
        }
        
        // تعديل مستوى الصوت
        function setVolume(value) {
            video.volume = value / 100;
            video.muted = value == 0;
            volumeBtn.textContent = value == 0 ? '🔇' : '🔊';
        }
        
        // ملء الشاشة
        function toggleFullscreen() {
            if (!document.fullscreenElement) {
                playerContainer.requestFullscreen();
            } else {
                document.exitFullscreen();
            }
        }
        
        // إظهار/إخفاء التحكم
        function showControls() {
            playerControls.classList.add('show');
            videoInfo.classList.add('show');
            
            clearTimeout(controlsTimeout);
            controlsTimeout = setTimeout(() => {
                if (!video.paused) {
                    playerControls.classList.remove('show');
                    videoInfo.classList.remove('show');
                }
            }, 3000);
        }
        
        // إظهار/إخفاء قائمة الحلقات
        function toggleEpisodes() {
            const sidebar = document.getElementById('episodesSidebar');
            sidebar.classList.toggle('show');
        }
        
        // العودة للصفحة السابقة
        function goBack() {
            window.history.back();
        }
        
        // أحداث الماوس والحركة
        playerContainer.addEventListener('mousemove', showControls);
        playerContainer.addEventListener('click', function(e) {
            if (e.target === video) {
                togglePlay();
            }
        });
        
        // أحداث لوحة المفاتيح
        document.addEventListener('keydown', function(e) {
            switch(e.code) {
                case 'Space':
                    e.preventDefault();
                    togglePlay();
                    break;
                case 'ArrowLeft':
                    video.currentTime -= 10;
                    break;
                case 'ArrowRight':
                    video.currentTime += 10;
                    break;
                case 'ArrowUp':
                    e.preventDefault();
                    video.volume = Math.min(1, video.volume + 0.1);
                    volumeSlider.value = video.volume * 100;
                    break;
                case 'ArrowDown':
                    e.preventDefault();
                    video.volume = Math.max(0, video.volume - 0.1);
                    volumeSlider.value = video.volume * 100;
                    break;
                case 'KeyF':
                    toggleFullscreen();
                    break;
                case 'KeyM':
                    toggleMute();
                    break;
                case 'Escape':
                    const sidebar = document.getElementById('episodesSidebar');
                    if (sidebar && sidebar.classList.contains('show')) {
                        sidebar.classList.remove('show');
                    }
                    break;
            }
        });
        
        // إظهار التحكم في البداية
        showControls();
        
        // تشغيل تلقائي (اختياري)
        video.addEventListener('loadeddata', function() {
            // يمكن إضافة video.play() هنا للتشغيل التلقائي
        });
    </script>
</body>
</html>
