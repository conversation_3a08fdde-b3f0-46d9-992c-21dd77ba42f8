<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تحديث قاعدة البيانات - Shahid Platform</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', 'Cairo', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #2d2d2d 100%);
            color: #fff;
            line-height: 1.6;
            min-height: 100vh;
            padding: 2rem;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(47, 47, 47, 0.9);
            border-radius: 15px;
            padding: 2rem;
            border: 1px solid rgba(229, 9, 20, 0.2);
        }
        
        .header {
            text-align: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #E50914;
        }
        
        .header h1 {
            color: #E50914;
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }
        
        .status {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            border-left: 4px solid #E50914;
        }
        
        .success {
            border-left-color: #4CAF50;
            background: rgba(76, 175, 80, 0.1);
        }
        
        .error {
            border-left-color: #F44336;
            background: rgba(244, 67, 54, 0.1);
        }
        
        .warning {
            border-left-color: #FF9800;
            background: rgba(255, 152, 0, 0.1);
        }
        
        .btn {
            background: linear-gradient(45deg, #E50914, #B8070F);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1.1rem;
            font-weight: bold;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            margin: 0.5rem;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(229, 9, 20, 0.3);
            color: white;
        }
        
        .btn-secondary {
            background: linear-gradient(45deg, #555, #333);
        }
        
        .progress {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .progress-item {
            display: flex;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .progress-item:last-child {
            border-bottom: none;
        }
        
        .progress-icon {
            margin-left: 1rem;
            font-size: 1.2rem;
        }
        
        .links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 2rem;
        }
        
        .link-card {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 1.5rem;
            text-align: center;
            border: 1px solid rgba(229, 9, 20, 0.2);
            transition: all 0.3s ease;
        }
        
        .link-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(229, 9, 20, 0.3);
        }
        
        .link-card a {
            color: #E50914;
            text-decoration: none;
            font-weight: bold;
        }
        
        .link-card a:hover {
            color: #fff;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🗄️ تحديث قاعدة البيانات</h1>
            <p>Shahid Platform Database Update</p>
        </div>

        <?php
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update'])) {
            echo '<div class="progress">';
            echo '<h3>🚀 جاري تحديث قاعدة البيانات...</h3>';
            
            // إعدادات قاعدة البيانات
            $host = 'localhost';
            $username = 'root';
            $password = '';
            $database = 'shahid_platform';
            
            try {
                // الاتصال بـ MySQL
                echo '<div class="progress-item"><span class="progress-icon">📡</span> الاتصال بخادم MySQL...</div>';
                flush();
                
                $pdo = new PDO("mysql:host=$host;charset=utf8mb4", $username, $password);
                $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                
                echo '<div class="progress-item"><span class="progress-icon">✅</span> تم الاتصال بنجاح!</div>';
                flush();
                
                // حذف وإنشاء قاعدة البيانات
                echo '<div class="progress-item"><span class="progress-icon">🗑️</span> حذف قاعدة البيانات القديمة...</div>';
                flush();
                
                $pdo->exec("DROP DATABASE IF EXISTS $database");
                $pdo->exec("CREATE DATABASE $database CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
                
                echo '<div class="progress-item"><span class="progress-icon">✅</span> تم إنشاء قاعدة البيانات الجديدة!</div>';
                flush();
                
                // الاتصال بقاعدة البيانات الجديدة
                $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password);
                $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                
                // تنفيذ ملف Schema
                echo '<div class="progress-item"><span class="progress-icon">📋</span> تنفيذ ملف Schema...</div>';
                flush();
                
                $schemaFile = __DIR__ . '/schema.sql';
                if (file_exists($schemaFile)) {
                    $schema = file_get_contents($schemaFile);
                    $queries = array_filter(explode(';', $schema), 'trim');
                    
                    foreach ($queries as $query) {
                        if (!empty(trim($query))) {
                            $pdo->exec($query);
                        }
                    }
                    echo '<div class="progress-item"><span class="progress-icon">✅</span> تم تنفيذ ملف Schema بنجاح!</div>';
                } else {
                    echo '<div class="progress-item"><span class="progress-icon">⚠️</span> ملف Schema غير موجود!</div>';
                }
                flush();
                
                // تنفيذ ملف البيانات
                echo '<div class="progress-item"><span class="progress-icon">📊</span> إدراج البيانات الإنتاجية...</div>';
                flush();
                
                $dataFile = __DIR__ . '/production_data.sql';
                if (file_exists($dataFile)) {
                    $data = file_get_contents($dataFile);
                    $queries = array_filter(explode(';', $data), 'trim');
                    
                    foreach ($queries as $query) {
                        if (!empty(trim($query))) {
                            try {
                                $pdo->exec($query);
                            } catch (Exception $e) {
                                // تجاهل الأخطاء البسيطة
                            }
                        }
                    }
                    echo '<div class="progress-item"><span class="progress-icon">✅</span> تم إدراج البيانات بنجاح!</div>';
                } else {
                    echo '<div class="progress-item"><span class="progress-icon">⚠️</span> ملف البيانات غير موجود!</div>';
                }
                flush();
                
                // التحقق من البيانات
                echo '<div class="progress-item"><span class="progress-icon">🔍</span> التحقق من البيانات...</div>';
                flush();
                
                $tables = ['users', 'movies', 'series', 'categories'];
                $totalRecords = 0;
                
                foreach ($tables as $table) {
                    try {
                        $stmt = $pdo->query("SELECT COUNT(*) FROM $table");
                        $count = $stmt->fetchColumn();
                        $totalRecords += $count;
                        echo '<div class="progress-item"><span class="progress-icon">📊</span> ' . $table . ': ' . $count . ' سجل</div>';
                    } catch (Exception $e) {
                        echo '<div class="progress-item"><span class="progress-icon">❌</span> ' . $table . ': خطأ في القراءة</div>';
                    }
                    flush();
                }
                
                echo '</div>';
                echo '<div class="status success">';
                echo '<h3>🎉 تم تحديث قاعدة البيانات بنجاح!</h3>';
                echo '<p>تم إدراج ' . $totalRecords . ' سجل في قاعدة البيانات</p>';
                echo '<p><strong>قاعدة البيانات:</strong> ' . $database . '</p>';
                echo '<p><strong>الخادم:</strong> ' . $host . '</p>';
                echo '</div>';
                
            } catch (PDOException $e) {
                echo '</div>';
                echo '<div class="status error">';
                echo '<h3>❌ خطأ في قاعدة البيانات</h3>';
                echo '<p>' . htmlspecialchars($e->getMessage()) . '</p>';
                echo '<h4>حلول مقترحة:</h4>';
                echo '<ul>';
                echo '<li>تأكد من تشغيل XAMPP</li>';
                echo '<li>تأكد من تشغيل خدمة MySQL</li>';
                echo '<li>تحقق من إعدادات الاتصال</li>';
                echo '</ul>';
                echo '</div>';
            }
        } else {
        ?>
        
        <div class="status">
            <h3>📋 معلومات التحديث</h3>
            <p>سيتم تحديث قاعدة البيانات بالبيانات الإنتاجية الكاملة:</p>
            <ul style="margin: 1rem 0; padding-right: 2rem;">
                <li>🎬 أفلام ومسلسلات حقيقية</li>
                <li>👥 مستخدمين تجريبيين</li>
                <li>💳 اشتراكات ومدفوعات</li>
                <li>📊 تقييمات وإحصائيات</li>
                <li>🔒 بيانات أمان وسجلات</li>
            </ul>
        </div>
        
        <div style="text-align: center;">
            <form method="POST">
                <button type="submit" name="update" class="btn">🚀 تحديث قاعدة البيانات</button>
            </form>
            <a href="../homepage.php" class="btn btn-secondary">🏠 العودة للرئيسية</a>
        </div>
        
        <?php } ?>
        
        <div class="links">
            <div class="link-card">
                <h4>📊 phpMyAdmin</h4>
                <a href="http://localhost/phpmyadmin/" target="_blank">إدارة قاعدة البيانات</a>
            </div>
            <div class="link-card">
                <h4>🌐 الموقع</h4>
                <a href="../homepage.php" target="_blank">الصفحة الرئيسية</a>
            </div>
            <div class="link-card">
                <h4>🎛️ لوحة الإدارة</h4>
                <a href="../admin/dashboard.php" target="_blank">لوحة التحكم</a>
            </div>
            <div class="link-card">
                <h4>🔗 API</h4>
                <a href="../api/test.php" target="_blank">اختبار API</a>
            </div>
        </div>
    </div>
</body>
</html>
