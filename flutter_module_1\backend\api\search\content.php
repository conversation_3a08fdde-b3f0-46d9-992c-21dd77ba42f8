<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// معالجة طلبات OPTIONS
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => 'طريقة الطلب غير مدعومة',
        'error_code' => 'METHOD_NOT_ALLOWED'
    ]);
    exit;
}

try {
    // الاتصال بقاعدة البيانات
    $pdo = new PDO("mysql:host=localhost;dbname=shahid_platform;charset=utf8mb4", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // معاملات البحث
    $query = isset($_GET['q']) ? trim($_GET['q']) : '';
    $type = isset($_GET['type']) ? $_GET['type'] : 'all'; // all, movie, series
    $category = isset($_GET['category']) ? intval($_GET['category']) : 0;
    $year = isset($_GET['year']) ? intval($_GET['year']) : 0;
    $rating_min = isset($_GET['rating_min']) ? floatval($_GET['rating_min']) : 0;
    $sort = isset($_GET['sort']) ? $_GET['sort'] : 'relevance'; // relevance, rating, year, views
    $limit = isset($_GET['limit']) ? min(max(1, intval($_GET['limit'])), 50) : 20;
    $offset = isset($_GET['offset']) ? max(0, intval($_GET['offset'])) : 0;
    
    // التحقق من رمز الوصول (اختياري)
    $userId = null;
    $userSubscription = 'free';
    
    if (isset($_SERVER['HTTP_AUTHORIZATION'])) {
        $token = str_replace('Bearer ', '', $_SERVER['HTTP_AUTHORIZATION']);
        $payload = json_decode(base64_decode($token), true);
        
        if ($payload && isset($payload['user_id']) && $payload['expires_at'] > time()) {
            $userId = $payload['user_id'];
            $userSubscription = $payload['subscription_type'] ?? 'free';
        }
    }
    
    $results = [];
    $totalResults = 0;
    
    // البحث في الأفلام
    if ($type === 'all' || $type === 'movie') {
        $movieConditions = ["m.status = 'active'"];
        $movieParams = [];
        
        if ($query) {
            $movieConditions[] = "(m.title LIKE ? OR m.description LIKE ? OR m.director LIKE ? OR m.cast LIKE ?)";
            $searchTerm = "%$query%";
            $movieParams = array_merge($movieParams, [$searchTerm, $searchTerm, $searchTerm, $searchTerm]);
        }
        
        if ($category) {
            $movieConditions[] = "m.category_id = ?";
            $movieParams[] = $category;
        }
        
        if ($year) {
            $movieConditions[] = "YEAR(m.release_date) = ?";
            $movieParams[] = $year;
        }
        
        if ($rating_min) {
            $movieConditions[] = "m.rating >= ?";
            $movieParams[] = $rating_min;
        }
        
        $movieWhere = implode(' AND ', $movieConditions);
        
        // ترتيب النتائج
        $movieOrderBy = match($sort) {
            'rating' => 'ORDER BY m.rating DESC',
            'year' => 'ORDER BY m.release_date DESC',
            'views' => 'ORDER BY m.views DESC',
            'title' => 'ORDER BY m.title ASC',
            default => $query ? 'ORDER BY CASE 
                WHEN m.title LIKE ? THEN 1
                WHEN m.title LIKE ? THEN 2
                ELSE 3
            END, m.rating DESC' : 'ORDER BY m.created_at DESC'
        };
        
        if ($sort === 'relevance' && $query) {
            $movieParams = array_merge(["%$query%", "$query%"], $movieParams);
        }
        
        $movieQuery = "
            SELECT 
                m.id,
                m.title,
                m.description,
                m.poster,
                m.trailer_url,
                m.duration,
                m.rating,
                m.views,
                m.release_date,
                m.director,
                m.cast,
                c.name as category_name,
                'movie' as content_type
            FROM movies m
            LEFT JOIN categories c ON m.category_id = c.id
            WHERE $movieWhere
            $movieOrderBy
            LIMIT ? OFFSET ?
        ";
        
        $movieParams[] = $limit;
        $movieParams[] = $offset;
        
        $stmt = $pdo->prepare($movieQuery);
        $stmt->execute($movieParams);
        $movies = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // معالجة نتائج الأفلام
        foreach ($movies as &$movie) {
            $movie['can_watch'] = $userSubscription !== 'free' || $movie['rating'] < 8.0;
            $movie['is_premium'] = $movie['rating'] >= 8.0;
            $movie['duration_formatted'] = gmdate('H:i', $movie['duration'] * 60);
            $movie['release_year'] = date('Y', strtotime($movie['release_date']));
            
            // إضافة معلومات المشاهدة للمستخدم المسجل
            if ($userId) {
                $stmt = $pdo->prepare("
                    SELECT watch_time, completed 
                    FROM watch_history 
                    WHERE user_id = ? AND content_type = 'movie' AND content_id = ? 
                    ORDER BY watched_at DESC 
                    LIMIT 1
                ");
                $stmt->execute([$userId, $movie['id']]);
                $watchHistory = $stmt->fetch(PDO::FETCH_ASSOC);
                
                $movie['watch_progress'] = $watchHistory ? [
                    'watch_time' => intval($watchHistory['watch_time']),
                    'completed' => (bool)$watchHistory['completed'],
                    'progress_percentage' => $movie['duration'] > 0 ? 
                        min(100, ($watchHistory['watch_time'] / ($movie['duration'] * 60)) * 100) : 0
                ] : null;
                
                // التحقق من المفضلة
                $stmt = $pdo->prepare("SELECT id FROM favorites WHERE user_id = ? AND content_type = 'movie' AND content_id = ?");
                $stmt->execute([$userId, $movie['id']]);
                $movie['is_favorite'] = (bool)$stmt->fetch();
            }
        }
        
        $results = array_merge($results, $movies);
    }
    
    // البحث في المسلسلات
    if ($type === 'all' || $type === 'series') {
        $seriesConditions = ["s.status = 'active'"];
        $seriesParams = [];
        
        if ($query) {
            $seriesConditions[] = "(s.title LIKE ? OR s.description LIKE ? OR s.director LIKE ? OR s.cast LIKE ?)";
            $searchTerm = "%$query%";
            $seriesParams = array_merge($seriesParams, [$searchTerm, $searchTerm, $searchTerm, $searchTerm]);
        }
        
        if ($category) {
            $seriesConditions[] = "s.category_id = ?";
            $seriesParams[] = $category;
        }
        
        if ($year) {
            $seriesConditions[] = "YEAR(s.release_date) = ?";
            $seriesParams[] = $year;
        }
        
        if ($rating_min) {
            $seriesConditions[] = "s.rating >= ?";
            $seriesParams[] = $rating_min;
        }
        
        $seriesWhere = implode(' AND ', $seriesConditions);
        
        // ترتيب النتائج
        $seriesOrderBy = match($sort) {
            'rating' => 'ORDER BY s.rating DESC',
            'year' => 'ORDER BY s.release_date DESC',
            'views' => 'ORDER BY s.views DESC',
            'title' => 'ORDER BY s.title ASC',
            default => $query ? 'ORDER BY CASE 
                WHEN s.title LIKE ? THEN 1
                WHEN s.title LIKE ? THEN 2
                ELSE 3
            END, s.rating DESC' : 'ORDER BY s.created_at DESC'
        };
        
        if ($sort === 'relevance' && $query) {
            $seriesParams = array_merge(["%$query%", "$query%"], $seriesParams);
        }
        
        $seriesQuery = "
            SELECT 
                s.id,
                s.title,
                s.description,
                s.poster,
                s.trailer_url,
                s.total_seasons,
                s.rating,
                s.views,
                s.release_date,
                s.director,
                s.cast,
                s.status as series_status,
                c.name as category_name,
                'series' as content_type,
                COUNT(e.id) as total_episodes
            FROM series s
            LEFT JOIN categories c ON s.category_id = c.id
            LEFT JOIN episodes e ON s.id = e.series_id AND e.status = 'active'
            WHERE $seriesWhere
            GROUP BY s.id
            $seriesOrderBy
            LIMIT ? OFFSET ?
        ";
        
        $seriesParams[] = $limit;
        $seriesParams[] = $offset;
        
        $stmt = $pdo->prepare($seriesQuery);
        $stmt->execute($seriesParams);
        $series = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // معالجة نتائج المسلسلات
        foreach ($series as &$show) {
            $show['can_watch'] = $userSubscription !== 'free';
            $show['is_premium'] = true; // معظم المسلسلات مدفوعة
            $show['release_year'] = date('Y', strtotime($show['release_date']));
            
            // إضافة معلومات المشاهدة للمستخدم المسجل
            if ($userId) {
                // الحصول على آخر حلقة تم مشاهدتها
                $stmt = $pdo->prepare("
                    SELECT e.id, e.title, e.episode_number, e.season_number, wh.watch_time, wh.completed
                    FROM watch_history wh
                    JOIN episodes e ON wh.content_id = e.id
                    WHERE wh.user_id = ? AND wh.content_type = 'episode' AND e.series_id = ?
                    ORDER BY wh.watched_at DESC
                    LIMIT 1
                ");
                $stmt->execute([$userId, $show['id']]);
                $lastWatched = $stmt->fetch(PDO::FETCH_ASSOC);
                
                $show['last_watched_episode'] = $lastWatched;
                
                // التحقق من المفضلة
                $stmt = $pdo->prepare("SELECT id FROM favorites WHERE user_id = ? AND content_type = 'series' AND content_id = ?");
                $stmt->execute([$userId, $show['id']]);
                $show['is_favorite'] = (bool)$stmt->fetch();
                
                // حساب تقدم المشاهدة
                $stmt = $pdo->prepare("
                    SELECT 
                        COUNT(DISTINCT e.id) as total_episodes,
                        COUNT(DISTINCT wh.content_id) as watched_episodes
                    FROM episodes e
                    LEFT JOIN watch_history wh ON e.id = wh.content_id AND wh.user_id = ? AND wh.completed = 1
                    WHERE e.series_id = ? AND e.status = 'active'
                ");
                $stmt->execute([$userId, $show['id']]);
                $progress = $stmt->fetch(PDO::FETCH_ASSOC);
                
                $show['watch_progress'] = [
                    'total_episodes' => intval($progress['total_episodes']),
                    'watched_episodes' => intval($progress['watched_episodes']),
                    'completion_percentage' => $progress['total_episodes'] > 0 ? 
                        ($progress['watched_episodes'] / $progress['total_episodes']) * 100 : 0
                ];
            }
        }
        
        $results = array_merge($results, $series);
    }
    
    // ترتيب النتائج المدمجة حسب الصلة
    if ($sort === 'relevance' && $query) {
        usort($results, function($a, $b) use ($query) {
            $aScore = 0;
            $bScore = 0;
            
            // نقاط للعنوان
            if (stripos($a['title'], $query) === 0) $aScore += 10;
            elseif (stripos($a['title'], $query) !== false) $aScore += 5;
            
            if (stripos($b['title'], $query) === 0) $bScore += 10;
            elseif (stripos($b['title'], $query) !== false) $bScore += 5;
            
            // نقاط للتقييم
            $aScore += $a['rating'];
            $bScore += $b['rating'];
            
            return $bScore <=> $aScore;
        });
    }
    
    // حساب إجمالي النتائج
    $totalResults = count($results);
    
    // الحصول على التصنيفات للفلاتر
    $categories = $pdo->query("
        SELECT id, name, COUNT(*) as content_count
        FROM categories c
        LEFT JOIN (
            SELECT category_id FROM movies WHERE status = 'active'
            UNION ALL
            SELECT category_id FROM series WHERE status = 'active'
        ) content ON c.id = content.category_id
        GROUP BY c.id, c.name
        HAVING content_count > 0
        ORDER BY c.name ASC
    ")->fetchAll(PDO::FETCH_ASSOC);
    
    // إحصائيات البحث
    $searchStats = [
        'total_results' => $totalResults,
        'movies_count' => count(array_filter($results, fn($item) => $item['content_type'] === 'movie')),
        'series_count' => count(array_filter($results, fn($item) => $item['content_type'] === 'series')),
        'search_time' => microtime(true) - $_SERVER['REQUEST_TIME_FLOAT']
    ];
    
    // إعداد الاستجابة
    $response = [
        'success' => true,
        'message' => $query ? "تم العثور على $totalResults نتيجة للبحث عن \"$query\"" : 'تم جلب المحتوى بنجاح',
        'data' => [
            'results' => $results,
            'search_params' => [
                'query' => $query,
                'type' => $type,
                'category' => $category,
                'year' => $year,
                'rating_min' => $rating_min,
                'sort' => $sort
            ],
            'pagination' => [
                'limit' => $limit,
                'offset' => $offset,
                'total' => $totalResults,
                'has_more' => ($offset + $limit) < $totalResults
            ],
            'filters' => [
                'categories' => $categories,
                'available_years' => range(date('Y'), 2000),
                'content_types' => [
                    ['value' => 'all', 'label' => 'جميع الأنواع'],
                    ['value' => 'movie', 'label' => 'أفلام'],
                    ['value' => 'series', 'label' => 'مسلسلات']
                ],
                'sort_options' => [
                    ['value' => 'relevance', 'label' => 'الأكثر صلة'],
                    ['value' => 'rating', 'label' => 'الأعلى تقييماً'],
                    ['value' => 'year', 'label' => 'الأحدث'],
                    ['value' => 'views', 'label' => 'الأكثر مشاهدة'],
                    ['value' => 'title', 'label' => 'الترتيب الأبجدي']
                ]
            ],
            'statistics' => $searchStats,
            'user_info' => $userId ? [
                'user_id' => $userId,
                'subscription_type' => $userSubscription,
                'can_access_premium' => $userSubscription !== 'free'
            ] : null
        ]
    ];
    
    http_response_code(200);
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    
} catch (PDOException $e) {
    error_log("Database error in search API: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'خطأ في الخادم',
        'error_code' => 'DATABASE_ERROR'
    ]);
    
} catch (Exception $e) {
    error_log("General error in search API: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'خطأ غير متوقع',
        'error_code' => 'GENERAL_ERROR'
    ]);
}
?>
