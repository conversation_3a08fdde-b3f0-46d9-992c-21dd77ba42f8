<?php
/**
 * إصلاح بيانات لوحة التحكم
 * Fix Dashboard Data
 */

try {
    $pdo = new PDO("mysql:host=localhost;dbname=shahid_platform;charset=utf8mb4", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>🔧 إصلاح بيانات لوحة التحكم...</h2>";
    
    // إنشاء جدول episodes إذا لم يكن موجوداً
    $pdo->exec("CREATE TABLE IF NOT EXISTS episodes (
        id INT AUTO_INCREMENT PRIMARY KEY,
        series_id INT NOT NULL,
        season_number INT DEFAULT 1,
        episode_number INT NOT NULL,
        title VARCHAR(255) NOT NULL,
        description TEXT,
        duration INT,
        video_url VARCHAR(255),
        thumbnail VARCHAR(255),
        views INT DEFAULT 0,
        status ENUM('draft','published','archived') DEFAULT 'published',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_series (series_id),
        INDEX idx_season (season_number),
        INDEX idx_status (status)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    echo "✅ تم إنشاء جدول episodes<br>";
    
    // إنشاء جدول content_views إذا لم يكن موجوداً
    $pdo->exec("CREATE TABLE IF NOT EXISTS content_views (
        id INT AUTO_INCREMENT PRIMARY KEY,
        content_id INT NOT NULL,
        content_type ENUM('movie', 'series', 'episode') NOT NULL,
        user_id INT NULL,
        session_id VARCHAR(255) NOT NULL,
        duration INT DEFAULT 0,
        ip_address VARCHAR(45) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_content (content_id, content_type),
        INDEX idx_session (session_id),
        INDEX idx_created_at (created_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    echo "✅ تم إنشاء جدول content_views<br>";
    
    // إضافة أفلام تجريبية محسنة
    $movies = [
        [
            'title' => 'الأكشن المثير',
            'slug' => 'action-thriller-1',
            'description' => 'فيلم أكشن مليء بالإثارة والتشويق مع مؤثرات بصرية رائعة',
            'year' => 2023,
            'duration' => 120,
            'rating' => 4.5,
            'poster' => 'https://via.placeholder.com/300x450/E50914/FFFFFF?text=Action+Movie',
            'backdrop' => 'https://via.placeholder.com/1920x1080/E50914/FFFFFF?text=Action+Backdrop',
            'director' => 'أحمد محمد',
            'language' => 'ar',
            'country' => 'مصر',
            'views' => 15420,
            'likes' => 892,
            'featured' => 1
        ],
        [
            'title' => 'الدراما العائلية',
            'slug' => 'family-drama-1',
            'description' => 'قصة عائلية مؤثرة تحكي عن الحب والتضحية والأمل',
            'year' => 2023,
            'duration' => 110,
            'rating' => 4.2,
            'poster' => 'https://via.placeholder.com/300x450/2196F3/FFFFFF?text=Drama+Movie',
            'backdrop' => 'https://via.placeholder.com/1920x1080/2196F3/FFFFFF?text=Drama+Backdrop',
            'director' => 'فاطمة علي',
            'language' => 'ar',
            'country' => 'الأردن',
            'views' => 12350,
            'likes' => 756,
            'featured' => 1
        ],
        [
            'title' => 'الكوميديا الرائعة',
            'slug' => 'comedy-movie-1',
            'description' => 'فيلم كوميدي مضحك يناسب جميع أفراد العائلة',
            'year' => 2023,
            'duration' => 95,
            'rating' => 4.0,
            'poster' => 'https://via.placeholder.com/300x450/FF9800/FFFFFF?text=Comedy+Movie',
            'backdrop' => 'https://via.placeholder.com/1920x1080/FF9800/FFFFFF?text=Comedy+Backdrop',
            'director' => 'محمد حسن',
            'language' => 'ar',
            'country' => 'لبنان',
            'views' => 9870,
            'likes' => 623,
            'featured' => 0
        ]
    ];
    
    foreach ($movies as $movie) {
        $stmt = $pdo->prepare("INSERT IGNORE INTO movies (title, slug, description, year, duration, rating, poster, backdrop, director, language, country, views, likes, featured, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'published')");
        $stmt->execute([
            $movie['title'], $movie['slug'], $movie['description'], $movie['year'], 
            $movie['duration'], $movie['rating'], $movie['poster'], $movie['backdrop'],
            $movie['director'], $movie['language'], $movie['country'], 
            $movie['views'], $movie['likes'], $movie['featured']
        ]);
    }
    echo "✅ تم إضافة " . count($movies) . " فيلم<br>";
    
    // إضافة مسلسلات تجريبية
    $series = [
        [
            'title' => 'المسلسل الدرامي الكبير',
            'slug' => 'big-drama-series',
            'description' => 'مسلسل درامي اجتماعي يناقش قضايا المجتمع المعاصر',
            'year' => 2023,
            'rating' => 4.6,
            'poster' => 'https://via.placeholder.com/300x450/E50914/FFFFFF?text=Drama+Series',
            'backdrop' => 'https://via.placeholder.com/1920x1080/E50914/FFFFFF?text=Drama+Series+Backdrop',
            'total_seasons' => 2,
            'total_episodes' => 30,
            'director' => 'يوسف وهبي',
            'language' => 'ar',
            'country' => 'مصر',
            'views' => 25600,
            'likes' => 1420,
            'featured' => 1
        ],
        [
            'title' => 'الكوميديا الاجتماعية',
            'slug' => 'social-comedy-series',
            'description' => 'مسلسل كوميدي خفيف يعالج المواقف الاجتماعية بطريقة مضحكة',
            'year' => 2023,
            'rating' => 4.3,
            'poster' => 'https://via.placeholder.com/300x450/FF9800/FFFFFF?text=Comedy+Series',
            'backdrop' => 'https://via.placeholder.com/1920x1080/FF9800/FFFFFF?text=Comedy+Series+Backdrop',
            'total_seasons' => 1,
            'total_episodes' => 15,
            'director' => 'نادية لطفي',
            'language' => 'ar',
            'country' => 'الكويت',
            'views' => 18900,
            'likes' => 987,
            'featured' => 1
        ]
    ];
    
    foreach ($series as $serie) {
        $stmt = $pdo->prepare("INSERT IGNORE INTO series (title, slug, description, year, rating, poster, backdrop, total_seasons, total_episodes, director, language, country, views, likes, featured, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'published')");
        $stmt->execute([
            $serie['title'], $serie['slug'], $serie['description'], $serie['year'], 
            $serie['rating'], $serie['poster'], $serie['backdrop'], $serie['total_seasons'],
            $serie['total_episodes'], $serie['director'], $serie['language'], 
            $serie['country'], $serie['views'], $serie['likes'], $serie['featured']
        ]);
    }
    echo "✅ تم إضافة " . count($series) . " مسلسل<br>";
    
    // إضافة حلقات تجريبية
    $episodes = [
        [1, 1, 1, 'الحلقة الأولى', 'بداية القصة المثيرة', 45, 'https://example.com/episode1.mp4', 'https://via.placeholder.com/300x200/E50914/FFFFFF?text=Episode+1'],
        [1, 1, 2, 'الحلقة الثانية', 'تطور الأحداث', 47, 'https://example.com/episode2.mp4', 'https://via.placeholder.com/300x200/E50914/FFFFFF?text=Episode+2'],
        [1, 1, 3, 'الحلقة الثالثة', 'المفاجأة الكبرى', 50, 'https://example.com/episode3.mp4', 'https://via.placeholder.com/300x200/E50914/FFFFFF?text=Episode+3'],
        [2, 1, 1, 'الحلقة الأولى', 'بداية كوميدية رائعة', 30, 'https://example.com/comedy1.mp4', 'https://via.placeholder.com/300x200/FF9800/FFFFFF?text=Comedy+1'],
        [2, 1, 2, 'الحلقة الثانية', 'المزيد من الضحك', 32, 'https://example.com/comedy2.mp4', 'https://via.placeholder.com/300x200/FF9800/FFFFFF?text=Comedy+2']
    ];
    
    foreach ($episodes as $episode) {
        $stmt = $pdo->prepare("INSERT IGNORE INTO episodes (series_id, season_number, episode_number, title, description, duration, video_url, thumbnail) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
        $stmt->execute($episode);
    }
    echo "✅ تم إضافة " . count($episodes) . " حلقة<br>";
    
    // إضافة تقييمات تجريبية
    $ratings = [
        [1, 1, 'movie', 5, 'فيلم رائع جداً، أنصح بمشاهدته'],
        [1, 2, 'movie', 4, 'قصة مؤثرة وأداء ممتاز'],
        [1, 3, 'movie', 4, 'كوميديا خفيفة ومسلية'],
        [1, 1, 'series', 5, 'مسلسل ممتاز بكل المقاييس'],
        [1, 2, 'series', 4, 'كوميديا رائعة ومضحكة']
    ];
    
    foreach ($ratings as $rating) {
        $stmt = $pdo->prepare("INSERT IGNORE INTO ratings (user_id, content_id, content_type, rating, review) VALUES (?, ?, ?, ?, ?)");
        $stmt->execute($rating);
    }
    echo "✅ تم إضافة " . count($ratings) . " تقييم<br>";
    
    // إضافة مفضلة تجريبية
    $favorites = [
        [1, 1, 'movie'],
        [1, 2, 'movie'],
        [1, 1, 'series'],
        [1, 2, 'series']
    ];
    
    foreach ($favorites as $favorite) {
        $stmt = $pdo->prepare("INSERT IGNORE INTO favorites (user_id, content_id, content_type) VALUES (?, ?, ?)");
        $stmt->execute($favorite);
    }
    echo "✅ تم إضافة " . count($favorites) . " مفضلة<br>";
    
    // إضافة مشاهدات اليوم
    $today_views = [
        [1, 'movie', 1, session_id(), 120, '127.0.0.1'],
        [2, 'movie', 1, session_id(), 95, '127.0.0.1'],
        [1, 'series', 1, session_id(), 45, '127.0.0.1'],
        [2, 'series', 1, session_id(), 50, '127.0.0.1']
    ];
    
    foreach ($today_views as $view) {
        $stmt = $pdo->prepare("INSERT INTO content_views (content_id, content_type, user_id, session_id, duration, ip_address, created_at) VALUES (?, ?, ?, ?, ?, ?, NOW())");
        $stmt->execute($view);
    }
    echo "✅ تم إضافة " . count($today_views) . " مشاهدة لليوم<br>";
    
    echo "<br><h3>🎉 تم إصلاح جميع بيانات لوحة التحكم بنجاح!</h3>";
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>✅ تم إضافة/إصلاح:</h4>";
    echo "<ul>";
    echo "<li>جدول episodes</li>";
    echo "<li>جدول content_views</li>";
    echo "<li>" . count($movies) . " أفلام تجريبية</li>";
    echo "<li>" . count($series) . " مسلسلات تجريبية</li>";
    echo "<li>" . count($episodes) . " حلقات تجريبية</li>";
    echo "<li>" . count($ratings) . " تقييمات</li>";
    echo "<li>" . count($favorites) . " مفضلة</li>";
    echo "<li>" . count($today_views) . " مشاهدات لليوم</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div style='text-align: center; margin: 2rem 0;'>";
    echo "<a href='admin/dashboard.php' style='background: #E50914; color: white; padding: 1rem 2rem; text-decoration: none; border-radius: 8px; font-weight: bold; margin: 0.5rem;'>🎛️ عرض لوحة التحكم</a>";
    echo "<a href='dashboard_live.php' style='background: #4CAF50; color: white; padding: 1rem 2rem; text-decoration: none; border-radius: 8px; font-weight: bold; margin: 0.5rem;'>📊 المراقبة المباشرة</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px;'>";
    echo "<h3>❌ خطأ في إصلاح البيانات:</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح بيانات لوحة التحكم</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #2d2d2d 100%);
            color: #fff;
            min-height: 100vh;
            padding: 2rem;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(47, 47, 47, 0.95);
            border-radius: 20px;
            padding: 3rem;
            border: 2px solid rgba(229, 9, 20, 0.3);
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- المحتوى يتم عرضه من PHP أعلاه -->
    </div>
</body>
</html>
