# 🎬 **Shahid Platform - منصة البث الاحترافية**

<div align="center">

![Shahid Platform](https://img.shields.io/badge/Shahid-Platform-E50914?style=for-the-badge&logo=netflix)
![PHP](https://img.shields.io/badge/PHP-8.0+-777BB4?style=for-the-badge&logo=php)
![MySQL](https://img.shields.io/badge/MySQL-8.0+-4479A1?style=for-the-badge&logo=mysql)
![Flutter](https://img.shields.io/badge/Flutter-3.0+-02569B?style=for-the-badge&logo=flutter)
![Production Ready](https://img.shields.io/badge/Production-Ready-4CAF50?style=for-the-badge)

**منصة بث فيديو احترافية ومتكاملة مع تطبيق موبايل**

[🚀 التجربة المباشرة](#demo) • [📖 التوثيق](#documentation) • [🛠️ التثبيت](#installation) • [🎯 الميزات](#features)

</div>

---

## 🌟 **نظرة عامة**

**Shahid Platform** هي منصة بث فيديو احترافية ومتكاملة تشبه Netflix، مصممة خصيصاً للمحتوى العربي مع دعم كامل للمحتوى العالمي. تتضمن المنصة:

- 🌐 **موقع ويب متجاوب** مع واجهة مستخدم حديثة
- 📱 **تطبيق Flutter** للأندرويد و iOS
- 🎛️ **لوحة إدارة شاملة** لإدارة المحتوى والمستخدمين
- 🔗 **API متقدم** للتكامل مع التطبيقات
- 💳 **نظام دفع متكامل** مع اشتراكات متعددة
- 📊 **تحليلات متقدمة** ومراقبة الأداء

## 🏗️ هيكل المشروع

```
shahid/
├── backend/                 # PHP Backend
│   ├── config/             # إعدادات قاعدة البيانات والتطبيق
│   ├── core/               # الفئات الأساسية (Router, Controller, Model, etc.)
│   ├── controllers/        # تحكم في المنطق
│   ├── models/            # نماذج قاعدة البيانات
│   ├── views/             # ملفات العرض
│   ├── api/               # REST API endpoints
│   └── admin/             # لوحة الإدارة
├── frontend/               # Frontend Web
│   ├── assets/            # الصور والملفات الثابتة
│   ├── css/               # ملفات التنسيق
│   ├── js/                # ملفات JavaScript
│   └── player/            # مشغل الفيديو
├── lib/                   # Flutter App
│   ├── screens/           # شاشات التطبيق
│   ├── providers/         # إدارة الحالة
│   ├── services/          # الخدمات والـ API
│   ├── models/            # نماذج البيانات
│   ├── widgets/           # المكونات المخصصة
│   └── utils/             # الأدوات المساعدة
├── database/              # ملفات قاعدة البيانات
├── uploads/               # ملفات الرفع
└── install/               # نظام التثبيت
```

## 🚀 المميزات الرئيسية

### 🎥 إدارة المحتوى
- رفع وإدارة الأفلام والمسلسلات
- دعم جودات متعددة (360p, 480p, 720p, 1080p, 4K)
- إضافة ترجمات متعددة اللغات
- مسارات صوتية متعددة
- صور مصغرة تلقائية

### 👥 إدارة المستخدمين
- تسجيل دخول وإنشاء حسابات
- ملفات شخصية مخصصة
- سجل المشاهدة
- قائمة المفضلة
- استئناف المشاهدة من آخر نقطة

### 💳 نظام الاشتراكات
- باقات متعددة (مجانية، شهرية، سنوية، VIP)
- دفع آمن عبر Stripe و PayPal
- فواتير PDF تلقائية
- كوبونات خصم
- تجديد تلقائي

### 📱 تطبيق الموبايل
- تصميم Material Design
- تحميل للمشاهدة دون إنترنت
- إشعارات فورية
- دعم Chromecast
- وضع ليلي تلقائي

### 🛡️ الأمان
- حماية من SQL Injection
- CSRF Protection
- تشفير كلمات المرور
- حماية ملفات الفيديو بـ tokens
- تسجيل العمليات

### 📊 SEO والتحليلات
- Google Analytics
- Google Search Console
- Structured Data (JSON-LD)
- Meta tags ديناميكية
- خريطة الموقع

## 🛠️ التقنيات المستخدمة

### Backend
- **PHP 8.0+** - لغة البرمجة الأساسية
- **MySQL 8.0+** - قاعدة البيانات
- **PDO** - للاتصال الآمن بقاعدة البيانات
- **JWT** - للمصادقة في API

### Frontend
- **HTML5** - هيكل الصفحات
- **CSS3** - التنسيق والتصميم
- **JavaScript ES6+** - التفاعل
- **Video.js / Plyr.js** - مشغل الفيديو

### Mobile App
- **Flutter 3.16+** - إطار العمل
- **Dart** - لغة البرمجة
- **Provider** - إدارة الحالة
- **Dio** - HTTP requests
- **Video Player** - تشغيل الفيديو

### Payment
- **Stripe** - معالجة المدفوعات
- **PayPal** - دفع بديل

### Infrastructure
- **Apache/Nginx** - خادم الويب
- **Firebase** - الإشعارات الفورية
- **CDN** - توزيع المحتوى

## 📋 المتطلبات

### Server Requirements
- PHP 8.0 أو أحدث
- MySQL 8.0 أو أحدث
- Apache/Nginx
- SSL Certificate
- 4GB RAM (الحد الأدنى)
- 100GB Storage (للمحتوى)

### Development Requirements
- Flutter SDK 3.16+
- Dart SDK
- Android Studio / VS Code
- Git

## 🔧 التثبيت

### 1. استنساخ المشروع
```bash
git clone https://github.com/your-repo/shahid.git
cd shahid
```

### 2. إعداد Backend
```bash
cd backend
cp config/config.example.php config/config.php
# قم بتحرير إعدادات قاعدة البيانات
```

### 3. إعداد قاعدة البيانات
- قم بزيارة `/install.php`
- اتبع خطوات التثبيت
- أدخل بيانات قاعدة البيانات
- إنشاء حساب المشرف

### 4. إعداد Flutter App
```bash
cd ../
flutter pub get
flutter run
```

## 🎯 خطة التطوير

### المرحلة 1: الأساسيات ✅
- [x] إعداد هيكل المشروع
- [x] تصميم قاعدة البيانات
- [x] نظام المصادقة الأساسي
- [x] تطبيق Flutter الأساسي

### المرحلة 2: المحتوى والمشغل
- [ ] نظام رفع الفيديو
- [ ] مشغل فيديو متقدم
- [ ] دعم الترجمة
- [ ] جودات متعددة

### المرحلة 3: المدفوعات والاشتراكات
- [ ] تكامل Stripe
- [ ] تكامل PayPal
- [ ] نظام الباقات
- [ ] فواتير PDF

### المرحلة 4: التطبيق المتقدم
- [ ] تحميل للمشاهدة دون إنترنت
- [ ] إشعارات فورية
- [ ] دعم Chromecast
- [ ] تحسينات الأداء

### المرحلة 5: الإدارة والتحليلات
- [ ] لوحة إدارة شاملة
- [ ] تقارير مفصلة
- [ ] تحليلات المشاهدة
- [ ] SEO متقدم

## 📞 الدعم والمساهمة

للدعم أو المساهمة في المشروع:
- فتح issue في GitHub
- إرسال Pull Request
- التواصل عبر البريد الإلكتروني

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 🙏 شكر وتقدير

شكر خاص لجميع المساهمين والمطورين الذين ساعدوا في إنجاز هذا المشروع.
