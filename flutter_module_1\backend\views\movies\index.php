<?php include 'views/layout/header.php'; ?>

<!-- Page Header -->
<div class="page-header-section">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h1 class="page-title">الأفلام</h1>
                <p class="page-subtitle">اكتشف أفضل الأفلام من جميع أنحاء العالم</p>
            </div>
            <div class="col-md-6">
                <div class="page-stats">
                    <span class="stat-item">
                        <i class="fas fa-film me-2"></i>
                        <?= number_format($total_movies) ?> فيلم
                    </span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filters Section -->
<div class="filters-section">
    <div class="container">
        <div class="filters-card">
            <form method="GET" class="filters-form">
                <div class="row align-items-end">
                    <div class="col-md-3 mb-3">
                        <label class="filter-label">التصنيف</label>
                        <select name="genre" class="form-select">
                            <option value="">جميع التصنيفات</option>
                            <?php foreach ($genres as $genreOption): ?>
                                <option value="<?= htmlspecialchars($genreOption) ?>" 
                                        <?= $genre === $genreOption ? 'selected' : '' ?>>
                                    <?= htmlspecialchars($genreOption) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <label class="filter-label">السنة</label>
                        <select name="year" class="form-select">
                            <option value="">جميع السنوات</option>
                            <?php foreach ($years as $yearOption): ?>
                                <option value="<?= $yearOption ?>" 
                                        <?= $year == $yearOption ? 'selected' : '' ?>>
                                    <?= $yearOption ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <label class="filter-label">ترتيب حسب</label>
                        <select name="sort" class="form-select">
                            <option value="latest" <?= $sort === 'latest' ? 'selected' : '' ?>>الأحدث</option>
                            <option value="popular" <?= $sort === 'popular' ? 'selected' : '' ?>>الأكثر مشاهدة</option>
                            <option value="rating" <?= $sort === 'rating' ? 'selected' : '' ?>>الأعلى تقييماً</option>
                            <option value="title" <?= $sort === 'title' ? 'selected' : '' ?>>الاسم</option>
                        </select>
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-filter me-2"></i>تطبيق الفلاتر
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Movies Grid -->
<div class="content-section">
    <div class="container">
        <?php if (!empty($movies)): ?>
            <div class="row">
                <?php foreach ($movies as $movie): ?>
                    <div class="col-xl-2 col-lg-3 col-md-4 col-sm-6 col-6 mb-4">
                        <div class="movie-card">
                            <div class="movie-poster">
                                <img src="<?= $movie['poster'] ?? '/assets/images/no-poster.jpg' ?>" 
                                     alt="<?= htmlspecialchars($movie['title']) ?>" 
                                     class="img-fluid"
                                     loading="lazy">
                                
                                <div class="movie-overlay">
                                    <div class="movie-actions">
                                        <a href="/watch/<?= $movie['id'] ?>?type=movie" 
                                           class="btn btn-play">
                                            <i class="fas fa-play"></i>
                                        </a>
                                        <button class="btn btn-favorite favorite-btn" 
                                                data-id="<?= $movie['id'] ?>" 
                                                data-type="movie">
                                            <i class="far fa-heart"></i>
                                        </button>
                                        <a href="/movies/<?= $movie['slug'] ?>" 
                                           class="btn btn-info">
                                            <i class="fas fa-info"></i>
                                        </a>
                                    </div>
                                    
                                    <?php if ($movie['premium']): ?>
                                        <span class="premium-badge">
                                            <i class="fas fa-crown"></i>
                                            Premium
                                        </span>
                                    <?php endif; ?>
                                    
                                    <?php if ($movie['rating']): ?>
                                        <div class="movie-rating">
                                            <i class="fas fa-star"></i>
                                            <?= $movie['rating'] ?>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                            
                            <div class="movie-info">
                                <h6 class="movie-title">
                                    <a href="/movies/<?= $movie['slug'] ?>">
                                        <?= htmlspecialchars($movie['title']) ?>
                                    </a>
                                </h6>
                                <div class="movie-meta">
                                    <span class="movie-year"><?= $movie['year'] ?></span>
                                    <?php if ($movie['duration']): ?>
                                        <span class="movie-duration">
                                            <?= floor($movie['duration'] / 60) ?>س <?= $movie['duration'] % 60 ?>د
                                        </span>
                                    <?php endif; ?>
                                    <span class="movie-views">
                                        <?= number_format($movie['views']) ?> مشاهدة
                                    </span>
                                </div>
                                
                                <?php if (!empty($movie['genres'])): ?>
                                    <div class="movie-genres">
                                        <?php 
                                        $genres = is_string($movie['genres']) ? json_decode($movie['genres'], true) : $movie['genres'];
                                        if ($genres && is_array($genres)):
                                            foreach (array_slice($genres, 0, 2) as $genre): 
                                        ?>
                                            <span class="genre-tag"><?= htmlspecialchars($genre) ?></span>
                                        <?php 
                                            endforeach;
                                        endif; 
                                        ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
            
            <!-- Pagination -->
            <?php if ($total_pages > 1): ?>
                <div class="pagination-section">
                    <nav aria-label="Movies pagination">
                        <ul class="pagination justify-content-center">
                            <!-- Previous Page -->
                            <?php if ($current_page > 1): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=<?= $current_page - 1 ?>&genre=<?= urlencode($genre) ?>&year=<?= $year ?>&sort=<?= urlencode($sort) ?>">
                                        <i class="fas fa-chevron-right"></i>
                                    </a>
                                </li>
                            <?php endif; ?>
                            
                            <!-- Page Numbers -->
                            <?php
                            $start_page = max(1, $current_page - 2);
                            $end_page = min($total_pages, $current_page + 2);
                            
                            if ($start_page > 1): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=1&genre=<?= urlencode($genre) ?>&year=<?= $year ?>&sort=<?= urlencode($sort) ?>">1</a>
                                </li>
                                <?php if ($start_page > 2): ?>
                                    <li class="page-item disabled">
                                        <span class="page-link">...</span>
                                    </li>
                                <?php endif; ?>
                            <?php endif; ?>
                            
                            <?php for ($i = $start_page; $i <= $end_page; $i++): ?>
                                <li class="page-item <?= $i === $current_page ? 'active' : '' ?>">
                                    <a class="page-link" href="?page=<?= $i ?>&genre=<?= urlencode($genre) ?>&year=<?= $year ?>&sort=<?= urlencode($sort) ?>">
                                        <?= $i ?>
                                    </a>
                                </li>
                            <?php endfor; ?>
                            
                            <?php if ($end_page < $total_pages): ?>
                                <?php if ($end_page < $total_pages - 1): ?>
                                    <li class="page-item disabled">
                                        <span class="page-link">...</span>
                                    </li>
                                <?php endif; ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=<?= $total_pages ?>&genre=<?= urlencode($genre) ?>&year=<?= $year ?>&sort=<?= urlencode($sort) ?>">
                                        <?= $total_pages ?>
                                    </a>
                                </li>
                            <?php endif; ?>
                            
                            <!-- Next Page -->
                            <?php if ($current_page < $total_pages): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=<?= $current_page + 1 ?>&genre=<?= urlencode($genre) ?>&year=<?= $year ?>&sort=<?= urlencode($sort) ?>">
                                        <i class="fas fa-chevron-left"></i>
                                    </a>
                                </li>
                            <?php endif; ?>
                        </ul>
                    </nav>
                    
                    <div class="pagination-info text-center mt-3">
                        <small class="text-muted">
                            عرض <?= ($current_page - 1) * 20 + 1 ?> - <?= min($current_page * 20, $total_movies) ?> 
                            من أصل <?= number_format($total_movies) ?> فيلم
                        </small>
                    </div>
                </div>
            <?php endif; ?>
            
        <?php else: ?>
            <!-- No Movies Found -->
            <div class="no-content">
                <div class="text-center">
                    <i class="fas fa-film no-content-icon"></i>
                    <h3>لا توجد أفلام</h3>
                    <p class="text-muted">لم يتم العثور على أفلام تطابق معايير البحث الخاصة بك.</p>
                    <a href="/movies" class="btn btn-primary">عرض جميع الأفلام</a>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>

<script>
// Favorite functionality
$('.favorite-btn').click(function() {
    const btn = $(this);
    const movieId = btn.data('id');
    const icon = btn.find('i');
    
    <?php if (isset($user) && $user): ?>
    $.post('/movies/toggle-favorite', {
        movie_id: movieId,
        action: icon.hasClass('fas') ? 'remove' : 'add',
        csrf_token: $('meta[name="csrf-token"]').attr('content')
    })
    .done(function(response) {
        if (response.success) {
            icon.toggleClass('far fas');
            showToast(response.message, 'success');
        } else {
            showToast(response.message, 'error');
        }
    })
    .fail(function() {
        showToast('حدث خطأ، حاول مرة أخرى', 'error');
    });
    <?php else: ?>
    window.location.href = '/login';
    <?php endif; ?>
});

// Lazy loading for images
if ('IntersectionObserver' in window) {
    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src;
                img.classList.remove('lazy');
                imageObserver.unobserve(img);
            }
        });
    });

    document.querySelectorAll('img[data-src]').forEach(img => {
        imageObserver.observe(img);
    });
}

// Filter form auto-submit on change
$('.filters-form select').change(function() {
    $(this).closest('form').submit();
});

// Smooth scroll to content after filter
if (window.location.search.includes('genre=') || window.location.search.includes('year=') || window.location.search.includes('sort=')) {
    $('html, body').animate({
        scrollTop: $('.content-section').offset().top - 100
    }, 500);
}

function showToast(message, type) {
    const toast = $(`
        <div class="toast-notification toast-${type}">
            ${message}
        </div>
    `);
    
    $('body').append(toast);
    toast.fadeIn().delay(3000).fadeOut(function() {
        $(this).remove();
    });
}
</script>

<?php 
$additional_css = ['/assets/css/movies.css'];
include 'views/layout/footer.php'; 
?>
