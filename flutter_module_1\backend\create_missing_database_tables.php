<?php
/**
 * إنشاء الجداول المفقودة من المراجعة الشاملة
 * Create Missing Database Tables from Comprehensive Review
 */

echo "<h1>🗄️ إنشاء الجداول المفقودة</h1>";

try {
    $pdo = new PDO("mysql:host=localhost;dbname=shahid_platform;charset=utf8mb4", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>✅ تم الاتصال بقاعدة البيانات بنجاح</h3>";
    echo "</div>";
    
    // تعريف الجداول المفقودة
    $missingTables = [
        'subtitles' => "
            CREATE TABLE IF NOT EXISTS `subtitles` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `content_type` enum('movie','series','episode') NOT NULL,
                `content_id` int(11) NOT NULL,
                `language` varchar(10) NOT NULL DEFAULT 'ar',
                `language_name` varchar(50) NOT NULL,
                `file_path` varchar(500) NOT NULL,
                `file_format` enum('srt','vtt','ass','ssa') DEFAULT 'srt',
                `is_default` tinyint(1) DEFAULT 0,
                `is_active` tinyint(1) DEFAULT 1,
                `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                KEY `idx_content` (`content_type`, `content_id`),
                KEY `idx_language` (`language`),
                KEY `idx_active` (`is_active`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ",
        
        'audio_tracks' => "
            CREATE TABLE IF NOT EXISTS `audio_tracks` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `content_type` enum('movie','series','episode') NOT NULL,
                `content_id` int(11) NOT NULL,
                `language` varchar(10) NOT NULL DEFAULT 'ar',
                `language_name` varchar(50) NOT NULL,
                `file_path` varchar(500) NOT NULL,
                `quality` enum('128k','192k','256k','320k') DEFAULT '192k',
                `is_default` tinyint(1) DEFAULT 0,
                `is_active` tinyint(1) DEFAULT 1,
                `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                KEY `idx_content` (`content_type`, `content_id`),
                KEY `idx_language` (`language`),
                KEY `idx_active` (`is_active`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ",
        
        'subscriptions' => "
            CREATE TABLE IF NOT EXISTS `subscriptions` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `name` varchar(100) NOT NULL,
                `name_en` varchar(100) DEFAULT NULL,
                `description` text DEFAULT NULL,
                `price` decimal(10,2) NOT NULL DEFAULT 0.00,
                `currency` varchar(3) DEFAULT 'USD',
                `duration_days` int(11) NOT NULL DEFAULT 30,
                `features` json DEFAULT NULL,
                `max_devices` int(11) DEFAULT 1,
                `max_quality` enum('SD','HD','FHD','4K') DEFAULT 'HD',
                `download_allowed` tinyint(1) DEFAULT 0,
                `ads_free` tinyint(1) DEFAULT 0,
                `is_active` tinyint(1) DEFAULT 1,
                `sort_order` int(11) DEFAULT 0,
                `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                KEY `idx_active` (`is_active`),
                KEY `idx_sort` (`sort_order`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ",
        
        'user_subscriptions' => "
            CREATE TABLE IF NOT EXISTS `user_subscriptions` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `user_id` int(11) NOT NULL,
                `subscription_id` int(11) NOT NULL,
                `start_date` datetime NOT NULL,
                `end_date` datetime NOT NULL,
                `status` enum('active','expired','cancelled','suspended') DEFAULT 'active',
                `payment_method` varchar(50) DEFAULT NULL,
                `transaction_id` varchar(255) DEFAULT NULL,
                `auto_renew` tinyint(1) DEFAULT 1,
                `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                KEY `idx_user` (`user_id`),
                KEY `idx_subscription` (`subscription_id`),
                KEY `idx_status` (`status`),
                KEY `idx_dates` (`start_date`, `end_date`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ",
        
        'payments' => "
            CREATE TABLE IF NOT EXISTS `payments` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `user_id` int(11) NOT NULL,
                `subscription_id` int(11) NOT NULL,
                `amount` decimal(10,2) NOT NULL,
                `currency` varchar(3) DEFAULT 'USD',
                `payment_method` varchar(50) NOT NULL,
                `transaction_id` varchar(255) NOT NULL,
                `gateway_response` json DEFAULT NULL,
                `status` enum('pending','completed','failed','refunded','cancelled') DEFAULT 'pending',
                `payment_date` datetime DEFAULT NULL,
                `refund_date` datetime DEFAULT NULL,
                `refund_amount` decimal(10,2) DEFAULT NULL,
                `notes` text DEFAULT NULL,
                `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                UNIQUE KEY `transaction_id` (`transaction_id`),
                KEY `idx_user` (`user_id`),
                KEY `idx_subscription` (`subscription_id`),
                KEY `idx_status` (`status`),
                KEY `idx_payment_date` (`payment_date`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ",
        
        'admins' => "
            CREATE TABLE IF NOT EXISTS `admins` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `username` varchar(50) NOT NULL UNIQUE,
                `email` varchar(100) NOT NULL UNIQUE,
                `password` varchar(255) NOT NULL,
                `full_name` varchar(100) NOT NULL,
                `role` enum('super_admin','admin','moderator','editor') DEFAULT 'admin',
                `permissions` json DEFAULT NULL,
                `avatar` varchar(255) DEFAULT NULL,
                `last_login` datetime DEFAULT NULL,
                `login_attempts` int(11) DEFAULT 0,
                `is_active` tinyint(1) DEFAULT 1,
                `created_by` int(11) DEFAULT NULL,
                `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                KEY `idx_username` (`username`),
                KEY `idx_email` (`email`),
                KEY `idx_role` (`role`),
                KEY `idx_active` (`is_active`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ",
        
        'reports' => "
            CREATE TABLE IF NOT EXISTS `reports` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `user_id` int(11) DEFAULT NULL,
                `content_type` enum('movie','series','episode','user','comment') NOT NULL,
                `content_id` int(11) NOT NULL,
                `report_type` enum('inappropriate','copyright','spam','broken_link','other') NOT NULL,
                `description` text NOT NULL,
                `status` enum('pending','reviewed','resolved','dismissed') DEFAULT 'pending',
                `admin_notes` text DEFAULT NULL,
                `reviewed_by` int(11) DEFAULT NULL,
                `reviewed_at` datetime DEFAULT NULL,
                `ip_address` varchar(45) DEFAULT NULL,
                `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                KEY `idx_user` (`user_id`),
                KEY `idx_content` (`content_type`, `content_id`),
                KEY `idx_status` (`status`),
                KEY `idx_type` (`report_type`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ",
        
        'api_keys' => "
            CREATE TABLE IF NOT EXISTS `api_keys` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `name` varchar(100) NOT NULL,
                `api_key` varchar(255) NOT NULL UNIQUE,
                `secret_key` varchar(255) DEFAULT NULL,
                `permissions` json DEFAULT NULL,
                `rate_limit` int(11) DEFAULT 1000,
                `allowed_ips` text DEFAULT NULL,
                `user_id` int(11) DEFAULT NULL,
                `is_active` tinyint(1) DEFAULT 1,
                `last_used` datetime DEFAULT NULL,
                `usage_count` int(11) DEFAULT 0,
                `expires_at` datetime DEFAULT NULL,
                `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                UNIQUE KEY `api_key` (`api_key`),
                KEY `idx_user` (`user_id`),
                KEY `idx_active` (`is_active`),
                KEY `idx_expires` (`expires_at`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        "
    ];
    
    echo "<h2>🔄 جاري إنشاء الجداول المفقودة...</h2>";
    
    $createdTables = [];
    $existingTables = [];
    $errorTables = [];
    
    // فحص الجداول الموجودة
    $stmt = $pdo->query("SHOW TABLES");
    $currentTables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    foreach ($missingTables as $tableName => $sql) {
        if (in_array($tableName, $currentTables)) {
            $existingTables[] = $tableName;
            echo "<p style='color: #4CAF50;'>✅ الجدول <strong>$tableName</strong> موجود مسبقاً</p>";
        } else {
            try {
                $pdo->exec($sql);
                $createdTables[] = $tableName;
                echo "<p style='color: #2196F3;'>🆕 تم إنشاء الجدول <strong>$tableName</strong> بنجاح</p>";
            } catch (Exception $e) {
                $errorTables[] = ['table' => $tableName, 'error' => $e->getMessage()];
                echo "<p style='color: #F44336;'>❌ فشل إنشاء الجدول <strong>$tableName</strong>: " . htmlspecialchars($e->getMessage()) . "</p>";
            }
        }
    }
    
    // إدراج بيانات تجريبية للجداول الجديدة
    if (in_array('subscriptions', $createdTables)) {
        echo "<h3>💳 إدراج خطط الاشتراك التجريبية...</h3>";
        
        $subscriptionPlans = [
            ['مجاني', 'Free', 'خطة مجانية مع إعلانات', 0.00, 'USD', 30, '["ads", "sd_quality"]', 1, 'SD', 0, 0],
            ['أساسي', 'Basic', 'خطة أساسية بدون إعلانات', 9.99, 'USD', 30, '["no_ads", "hd_quality"]', 2, 'HD', 0, 1],
            ['متقدم', 'Premium', 'خطة متقدمة مع تحميل', 19.99, 'USD', 30, '["no_ads", "fhd_quality", "download"]', 4, 'FHD', 1, 1],
            ['VIP', 'VIP', 'خطة VIP مع جميع الميزات', 29.99, 'USD', 30, '["no_ads", "4k_quality", "download", "early_access"]', 6, '4K', 1, 1]
        ];
        
        $stmt = $pdo->prepare("INSERT INTO subscriptions (name, name_en, description, price, currency, duration_days, features, max_devices, max_quality, download_allowed, ads_free, sort_order) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
        
        foreach ($subscriptionPlans as $index => $plan) {
            try {
                $plan[] = $index + 1; // sort_order
                $stmt->execute($plan);
                echo "<p style='color: #4CAF50;'>✅ تم إدراج خطة: <strong>{$plan[0]}</strong></p>";
            } catch (Exception $e) {
                echo "<p style='color: #FF9800;'>⚠️ خطة {$plan[0]} موجودة مسبقاً</p>";
            }
        }
    }
    
    if (in_array('admins', $createdTables)) {
        echo "<h3>👤 إنشاء حساب إداري رئيسي...</h3>";
        
        $adminPassword = password_hash('admin123', PASSWORD_DEFAULT);
        $adminPermissions = json_encode([
            'users' => ['create', 'read', 'update', 'delete'],
            'content' => ['create', 'read', 'update', 'delete'],
            'subscriptions' => ['create', 'read', 'update', 'delete'],
            'payments' => ['read', 'update'],
            'reports' => ['read', 'update', 'delete'],
            'settings' => ['read', 'update'],
            'logs' => ['read']
        ]);
        
        try {
            $stmt = $pdo->prepare("INSERT INTO admins (username, email, password, full_name, role, permissions, is_active) VALUES (?, ?, ?, ?, ?, ?, ?)");
            $stmt->execute(['superadmin', '<EMAIL>', $adminPassword, 'مدير النظام الرئيسي', 'super_admin', $adminPermissions, 1]);
            echo "<p style='color: #2196F3;'>👤 تم إنشاء حساب إداري: superadmin / admin123</p>";
        } catch (Exception $e) {
            echo "<p style='color: #FF9800;'>⚠️ الحساب الإداري موجود مسبقاً</p>";
        }
    }
    
    // إدراج إعدادات النظام إذا تم إنشاء جدول settings
    if (in_array('settings', $createdTables) || in_array('settings', $currentTables)) {
        echo "<h3>⚙️ إدراج إعدادات النظام...</h3>";
        
        $systemSettings = [
            ['site_name', 'Shahid Platform', 'string', 'اسم الموقع', 'general', 1],
            ['site_description', 'منصة شاهد للأفلام والمسلسلات العربية', 'string', 'وصف الموقع', 'general', 1],
            ['site_logo', '/assets/images/logo.png', 'string', 'شعار الموقع', 'general', 1],
            ['maintenance_mode', '0', 'boolean', 'وضع الصيانة', 'system', 0],
            ['registration_enabled', '1', 'boolean', 'تفعيل التسجيل', 'user', 0],
            ['max_login_attempts', '5', 'number', 'محاولات تسجيل الدخول القصوى', 'security', 0],
            ['session_timeout', '3600', 'number', 'انتهاء صلاحية الجلسة (ثانية)', 'security', 0],
            ['default_language', 'ar', 'string', 'اللغة الافتراضية', 'general', 1],
            ['items_per_page', '20', 'number', 'عدد العناصر في الصفحة', 'display', 1],
            ['video_quality_default', 'HD', 'string', 'جودة الفيديو الافتراضية', 'video', 1],
            ['payment_gateway', 'stripe', 'string', 'بوابة الدفع الافتراضية', 'payment', 0],
            ['currency_default', 'USD', 'string', 'العملة الافتراضية', 'payment', 1],
            ['email_notifications', '1', 'boolean', 'تفعيل إشعارات البريد الإلكتروني', 'notifications', 0],
            ['sms_notifications', '0', 'boolean', 'تفعيل إشعارات SMS', 'notifications', 0],
            ['backup_enabled', '1', 'boolean', 'تفعيل النسخ الاحتياطي', 'system', 0]
        ];
        
        // التحقق من وجود جدول settings
        try {
            $stmt = $pdo->prepare("INSERT IGNORE INTO settings (setting_key, setting_value, setting_type, description, category, is_public) VALUES (?, ?, ?, ?, ?, ?)");
            
            foreach ($systemSettings as $setting) {
                $stmt->execute($setting);
            }
            echo "<p style='color: #4CAF50;'>✅ تم إدراج إعدادات النظام</p>";
        } catch (Exception $e) {
            // إنشاء جدول settings إذا لم يكن موجوداً
            $settingsSQL = "
                CREATE TABLE IF NOT EXISTS `settings` (
                    `id` int(11) NOT NULL AUTO_INCREMENT,
                    `setting_key` varchar(100) NOT NULL UNIQUE,
                    `setting_value` text DEFAULT NULL,
                    `setting_type` enum('string','number','boolean','json') DEFAULT 'string',
                    `description` varchar(255) DEFAULT NULL,
                    `category` varchar(50) DEFAULT 'general',
                    `is_public` tinyint(1) DEFAULT 0,
                    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    PRIMARY KEY (`id`),
                    KEY `idx_key` (`setting_key`),
                    KEY `idx_category` (`category`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ";
            
            try {
                $pdo->exec($settingsSQL);
                echo "<p style='color: #2196F3;'>🆕 تم إنشاء جدول settings</p>";
                
                $stmt = $pdo->prepare("INSERT IGNORE INTO settings (setting_key, setting_value, setting_type, description, category, is_public) VALUES (?, ?, ?, ?, ?, ?)");
                foreach ($systemSettings as $setting) {
                    $stmt->execute($setting);
                }
                echo "<p style='color: #4CAF50;'>✅ تم إدراج إعدادات النظام</p>";
            } catch (Exception $e2) {
                echo "<p style='color: #F44336;'>❌ خطأ في إنشاء جدول settings: " . htmlspecialchars($e2->getMessage()) . "</p>";
            }
        }
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>❌ خطأ في الاتصال بقاعدة البيانات:</h3>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🗄️ إنشاء الجداول المفقودة - Shahid Platform</title>
    <style>
        body {
            font-family: 'Segoe UI', 'Cairo', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #2d2d2d 100%);
            color: #fff;
            min-height: 100vh;
            padding: 2rem;
            margin: 0;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: rgba(47, 47, 47, 0.95);
            border-radius: 20px;
            padding: 3rem;
            border: 2px solid rgba(229, 9, 20, 0.3);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
        }
        h1, h2, h3 { 
            color: #E50914; 
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
        }
        h1 {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 2rem;
            background: linear-gradient(45deg, #E50914, #FF6B35);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        p {
            line-height: 1.6;
            margin: 0.5rem 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- المحتوى يتم عرضه من PHP أعلاه -->
    </div>
</body>
</html>
