class User {
  final int id;
  final String name;
  final String email;
  final String? phone;
  final String? avatar;
  final String role;
  final String status;
  final DateTime? dateOfBirth;
  final String? gender;
  final String? country;
  final String? language;
  final String? timezone;
  final DateTime createdAt;
  final DateTime? lastLogin;
  final UserSubscription? subscription;
  final UserPreferences? preferences;

  User({
    required this.id,
    required this.name,
    required this.email,
    this.phone,
    this.avatar,
    required this.role,
    required this.status,
    this.dateOfBirth,
    this.gender,
    this.country,
    this.language,
    this.timezone,
    required this.createdAt,
    this.lastLogin,
    this.subscription,
    this.preferences,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'],
      name: json['name'],
      email: json['email'],
      phone: json['phone'],
      avatar: json['avatar'],
      role: json['role'] ?? 'user',
      status: json['status'] ?? 'active',
      dateOfBirth: json['date_of_birth'] != null 
          ? DateTime.parse(json['date_of_birth'])
          : null,
      gender: json['gender'],
      country: json['country'],
      language: json['language'],
      timezone: json['timezone'],
      createdAt: DateTime.parse(json['created_at']),
      lastLogin: json['last_login'] != null 
          ? DateTime.parse(json['last_login'])
          : null,
      subscription: json['subscription'] != null
          ? UserSubscription.fromJson(json['subscription'])
          : null,
      preferences: json['preferences'] != null
          ? UserPreferences.fromJson(json['preferences'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'phone': phone,
      'avatar': avatar,
      'role': role,
      'status': status,
      'date_of_birth': dateOfBirth?.toIso8601String(),
      'gender': gender,
      'country': country,
      'language': language,
      'timezone': timezone,
      'created_at': createdAt.toIso8601String(),
      'last_login': lastLogin?.toIso8601String(),
      'subscription': subscription?.toJson(),
      'preferences': preferences?.toJson(),
    };
  }

  User copyWith({
    int? id,
    String? name,
    String? email,
    String? phone,
    String? avatar,
    String? role,
    String? status,
    DateTime? dateOfBirth,
    String? gender,
    String? country,
    String? language,
    String? timezone,
    DateTime? createdAt,
    DateTime? lastLogin,
    UserSubscription? subscription,
    UserPreferences? preferences,
  }) {
    return User(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      avatar: avatar ?? this.avatar,
      role: role ?? this.role,
      status: status ?? this.status,
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      gender: gender ?? this.gender,
      country: country ?? this.country,
      language: language ?? this.language,
      timezone: timezone ?? this.timezone,
      createdAt: createdAt ?? this.createdAt,
      lastLogin: lastLogin ?? this.lastLogin,
      subscription: subscription ?? this.subscription,
      preferences: preferences ?? this.preferences,
    );
  }

  bool get hasActiveSubscription => 
      subscription != null && subscription!.isActive;
  
  bool get isPremiumUser => hasActiveSubscription;
  
  String get displayName => name.isNotEmpty ? name : email.split('@').first;
  
  String get avatarUrl => avatar ?? '';
}

class UserSubscription {
  final int id;
  final String planName;
  final double price;
  final String currency;
  final String status;
  final DateTime startDate;
  final DateTime expiresAt;
  final bool autoRenew;
  final String paymentMethod;
  final Map<String, dynamic>? features;

  UserSubscription({
    required this.id,
    required this.planName,
    required this.price,
    required this.currency,
    required this.status,
    required this.startDate,
    required this.expiresAt,
    required this.autoRenew,
    required this.paymentMethod,
    this.features,
  });

  factory UserSubscription.fromJson(Map<String, dynamic> json) {
    return UserSubscription(
      id: json['id'],
      planName: json['plan_name'],
      price: (json['price'] as num).toDouble(),
      currency: json['currency'],
      status: json['status'],
      startDate: DateTime.parse(json['start_date']),
      expiresAt: DateTime.parse(json['expires_at']),
      autoRenew: json['auto_renew'] ?? false,
      paymentMethod: json['payment_method'],
      features: json['features'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'plan_name': planName,
      'price': price,
      'currency': currency,
      'status': status,
      'start_date': startDate.toIso8601String(),
      'expires_at': expiresAt.toIso8601String(),
      'auto_renew': autoRenew,
      'payment_method': paymentMethod,
      'features': features,
    };
  }

  bool get isActive => status == 'active' && expiresAt.isAfter(DateTime.now());
  bool get isExpired => expiresAt.isBefore(DateTime.now());
  bool get isExpiringSoon => 
      expiresAt.difference(DateTime.now()).inDays <= 7;
  
  int get daysRemaining => 
      isActive ? expiresAt.difference(DateTime.now()).inDays : 0;
}

class UserPreferences {
  final String language;
  final String subtitleLanguage;
  final String videoQuality;
  final bool autoplay;
  final bool notificationsEmail;
  final bool notificationsPush;
  final bool adultContent;
  final String theme;

  UserPreferences({
    required this.language,
    required this.subtitleLanguage,
    required this.videoQuality,
    required this.autoplay,
    required this.notificationsEmail,
    required this.notificationsPush,
    required this.adultContent,
    required this.theme,
  });

  factory UserPreferences.fromJson(Map<String, dynamic> json) {
    return UserPreferences(
      language: json['language'] ?? 'ar',
      subtitleLanguage: json['subtitle_language'] ?? 'ar',
      videoQuality: json['video_quality'] ?? 'auto',
      autoplay: json['autoplay'] ?? true,
      notificationsEmail: json['notifications_email'] ?? true,
      notificationsPush: json['notifications_push'] ?? true,
      adultContent: json['adult_content'] ?? false,
      theme: json['theme'] ?? 'dark',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'language': language,
      'subtitle_language': subtitleLanguage,
      'video_quality': videoQuality,
      'autoplay': autoplay,
      'notifications_email': notificationsEmail,
      'notifications_push': notificationsPush,
      'adult_content': adultContent,
      'theme': theme,
    };
  }

  UserPreferences copyWith({
    String? language,
    String? subtitleLanguage,
    String? videoQuality,
    bool? autoplay,
    bool? notificationsEmail,
    bool? notificationsPush,
    bool? adultContent,
    String? theme,
  }) {
    return UserPreferences(
      language: language ?? this.language,
      subtitleLanguage: subtitleLanguage ?? this.subtitleLanguage,
      videoQuality: videoQuality ?? this.videoQuality,
      autoplay: autoplay ?? this.autoplay,
      notificationsEmail: notificationsEmail ?? this.notificationsEmail,
      notificationsPush: notificationsPush ?? this.notificationsPush,
      adultContent: adultContent ?? this.adultContent,
      theme: theme ?? this.theme,
    );
  }
}
