<?php
session_start();

// التحقق من تسجيل الدخول والصلاحيات
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit;
}

try {
    $pdo = new PDO("mysql:host=localhost;dbname=shahid_platform;charset=utf8mb4", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // التحقق من صلاحيات الإدارة
    $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ? AND (subscription_type = 'vip' OR email LIKE '%admin%')");
    $stmt->execute([$_SESSION['user_id']]);
    $admin = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$admin) {
        header('Location: ../index.php');
        exit;
    }

    $message = '';
    $error = '';

    // معالجة العمليات
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'add_plan':
                    $name = $_POST['name'];
                    $description = $_POST['description'];
                    $price = $_POST['price'];
                    $duration_days = $_POST['duration_days'];
                    $features = json_encode(explode("\n", $_POST['features']));

                    $stmt = $pdo->prepare("
                        INSERT INTO subscriptions (name, description, price, duration_days, features, status)
                        VALUES (?, ?, ?, ?, ?, 'active')
                    ");

                    if ($stmt->execute([$name, $description, $price, $duration_days, $features])) {
                        $message = "تم إضافة خطة الاشتراك بنجاح";
                    } else {
                        $error = "فشل في إضافة خطة الاشتراك";
                    }
                    break;

                case 'update_plan':
                    $id = $_POST['plan_id'];
                    $name = $_POST['name'];
                    $description = $_POST['description'];
                    $price = $_POST['price'];
                    $duration_days = $_POST['duration_days'];
                    $features = json_encode(explode("\n", $_POST['features']));

                    $stmt = $pdo->prepare("
                        UPDATE subscriptions
                        SET name = ?, description = ?, price = ?, duration_days = ?, features = ?
                        WHERE id = ?
                    ");

                    if ($stmt->execute([$name, $description, $price, $duration_days, $features, $id])) {
                        $message = "تم تحديث خطة الاشتراك بنجاح";
                    } else {
                        $error = "فشل في تحديث خطة الاشتراك";
                    }
                    break;

                case 'toggle_plan_status':
                    $id = $_POST['plan_id'];
                    $new_status = $_POST['new_status'];

                    $stmt = $pdo->prepare("UPDATE subscriptions SET status = ? WHERE id = ?");
                    if ($stmt->execute([$new_status, $id])) {
                        $message = "تم تحديث حالة الخطة";
                    } else {
                        $error = "فشل في تحديث حالة الخطة";
                    }
                    break;

                case 'delete_plan':
                    $id = $_POST['plan_id'];

                    $stmt = $pdo->prepare("DELETE FROM subscriptions WHERE id = ?");
                    if ($stmt->execute([$id])) {
                        $message = "تم حذف خطة الاشتراك";
                    } else {
                        $error = "فشل في حذف خطة الاشتراك";
                    }
                    break;

                case 'cancel_user_subscription':
                    $subscription_id = $_POST['subscription_id'];

                    $stmt = $pdo->prepare("UPDATE user_subscriptions SET status = 'cancelled' WHERE id = ?");
                    if ($stmt->execute([$subscription_id])) {
                        $message = "تم إلغاء اشتراك المستخدم";
                    } else {
                        $error = "فشل في إلغاء الاشتراك";
                    }
                    break;
            }
        }
    }

    // الحصول على خطط الاشتراك
    $plans = $pdo->query("
        SELECT s.*,
               COUNT(us.id) as subscribers_count,
               SUM(CASE WHEN us.status = 'active' THEN 1 ELSE 0 END) as active_subscribers
        FROM subscriptions s
        LEFT JOIN user_subscriptions us ON s.id = us.subscription_id
        GROUP BY s.id
        ORDER BY s.price ASC
    ")->fetchAll(PDO::FETCH_ASSOC);

    // الحصول على اشتراكات المستخدمين
    $userSubscriptions = $pdo->query("
        SELECT us.*, u.username, u.email, u.full_name, s.name as plan_name, s.price
        FROM user_subscriptions us
        JOIN users u ON us.user_id = u.id
        JOIN subscriptions s ON us.subscription_id = s.id
        ORDER BY us.created_at DESC
        LIMIT 50
    ")->fetchAll(PDO::FETCH_ASSOC);

    // إحصائيات
    $stats = [
        'total_plans' => $pdo->query("SELECT COUNT(*) FROM subscriptions")->fetchColumn(),
        'active_plans' => $pdo->query("SELECT COUNT(*) FROM subscriptions WHERE status = 'active'")->fetchColumn(),
        'total_subscribers' => $pdo->query("SELECT COUNT(*) FROM user_subscriptions WHERE status = 'active'")->fetchColumn(),
        'monthly_revenue' => $pdo->query("
            SELECT SUM(s.price)
            FROM user_subscriptions us
            JOIN subscriptions s ON us.subscription_id = s.id
            WHERE us.status = 'active' AND us.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
        ")->fetchColumn(),
        'free_users' => $pdo->query("SELECT COUNT(*) FROM users WHERE subscription_type = 'free'")->fetchColumn(),
        'premium_users' => $pdo->query("SELECT COUNT(*) FROM users WHERE subscription_type IN ('premium', 'vip')")->fetchColumn()
    ];

} catch (Exception $e) {
    $error = "خطأ في قاعدة البيانات: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مدير الاشتراكات - Shahid Platform</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', 'Cairo', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #2d2d2d 100%);
            color: #fff;
            min-height: 100vh;
        }

        .admin-container {
            display: flex;
            min-height: 100vh;
        }

        .sidebar {
            width: 280px;
            background: rgba(47, 47, 47, 0.95);
            border-left: 2px solid rgba(229, 9, 20, 0.3);
            padding: 2rem 0;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
        }

        .sidebar-header {
            padding: 0 2rem 2rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            margin-bottom: 2rem;
        }

        .logo {
            color: #E50914;
            font-size: 1.8rem;
            font-weight: bold;
            text-decoration: none;
        }

        .sidebar-menu {
            list-style: none;
            padding: 0 1rem;
        }

        .sidebar-menu li {
            margin-bottom: 0.5rem;
        }

        .sidebar-menu a {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            color: #ccc;
            text-decoration: none;
            border-radius: 10px;
            transition: all 0.3s ease;
        }

        .sidebar-menu a:hover,
        .sidebar-menu a.active {
            background: rgba(229, 9, 20, 0.2);
            color: #fff;
        }

        .main-content {
            flex: 1;
            margin-right: 280px;
            padding: 2rem;
        }

        .header {
            background: rgba(47, 47, 47, 0.8);
            padding: 1.5rem 2rem;
            border-radius: 15px;
            margin-bottom: 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            color: #E50914;
            font-size: 2rem;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            background: linear-gradient(45deg, #E50914, #B8070F);
            color: white;
            border: none;
            border-radius: 8px;
            text-decoration: none;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(229, 9, 20, 0.3);
        }

        .btn-secondary {
            background: linear-gradient(45deg, #2196F3, #1976D2);
        }

        .btn-danger {
            background: linear-gradient(45deg, #F44336, #D32F2F);
        }

        .btn-success {
            background: linear-gradient(45deg, #4CAF50, #45a049);
        }

        .btn-warning {
            background: linear-gradient(45deg, #FF9800, #F57C00);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: rgba(47, 47, 47, 0.8);
            padding: 1.5rem;
            border-radius: 15px;
            border: 2px solid rgba(229, 9, 20, 0.1);
            text-align: center;
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            border-color: rgba(229, 9, 20, 0.3);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #E50914;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: #ccc;
        }

        .content-section {
            background: rgba(47, 47, 47, 0.8);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            border: 2px solid rgba(229, 9, 20, 0.1);
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
        }

        .section-title {
            color: #E50914;
            font-size: 1.5rem;
        }

        .plans-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .plan-card {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 2rem;
            border: 2px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
            position: relative;
        }

        .plan-card:hover {
            transform: translateY(-5px);
            border-color: rgba(229, 9, 20, 0.3);
        }

        .plan-card.featured {
            border-color: rgba(229, 9, 20, 0.5);
            background: rgba(229, 9, 20, 0.1);
        }

        .plan-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .plan-name {
            font-size: 1.3rem;
            font-weight: bold;
            color: #E50914;
        }

        .plan-price {
            font-size: 1.5rem;
            font-weight: bold;
            color: #fff;
        }

        .plan-features {
            list-style: none;
            margin: 1rem 0;
        }

        .plan-features li {
            padding: 0.25rem 0;
            color: #ccc;
        }

        .plan-actions {
            display: flex;
            gap: 0.5rem;
            margin-top: 1rem;
        }

        .subscribers-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }

        .subscribers-table th,
        .subscribers-table td {
            padding: 1rem;
            text-align: right;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .subscribers-table th {
            background: rgba(229, 9, 20, 0.2);
            color: #E50914;
            font-weight: bold;
        }

        .subscribers-table tr:hover {
            background: rgba(255, 255, 255, 0.05);
        }

        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .status-active {
            background: rgba(76, 175, 80, 0.2);
            color: #4CAF50;
        }

        .status-inactive {
            background: rgba(244, 67, 54, 0.2);
            color: #F44336;
        }

        .status-cancelled {
            background: rgba(255, 152, 0, 0.2);
            color: #FF9800;
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            z-index: 1000;
        }

        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(47, 47, 47, 0.95);
            border-radius: 15px;
            padding: 2rem;
            max-width: 600px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: #E50914;
            font-weight: bold;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.1);
            color: #fff;
            font-size: 1rem;
        }

        .form-group textarea {
            resize: vertical;
            min-height: 100px;
        }

        .alert {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 2rem;
        }

        .alert-success {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid rgba(76, 175, 80, 0.5);
            color: #4CAF50;
        }

        .alert-error {
            background: rgba(244, 67, 54, 0.2);
            border: 1px solid rgba(244, 67, 54, 0.5);
            color: #F44336;
        }

        .close {
            position: absolute;
            top: 1rem;
            left: 1rem;
            background: none;
            border: none;
            color: #ccc;
            font-size: 1.5rem;
            cursor: pointer;
        }

        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }

            .main-content {
                margin-right: 0;
                padding: 1rem;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .plans-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <aside class="sidebar">
            <div class="sidebar-header">
                <a href="../index.php" class="logo">
                    <i class="fas fa-play-circle"></i> شاهد
                </a>
            </div>

            <ul class="sidebar-menu">
                <li><a href="dashboard.php"><i class="fas fa-tachometer-alt"></i> لوحة التحكم</a></li>
                <li><a href="content_manager.php"><i class="fas fa-film"></i> مدير المحتوى</a></li>
                <li><a href="upload_center.php"><i class="fas fa-upload"></i> مركز الرفع</a></li>
                <li><a href="users.php"><i class="fas fa-users"></i> إدارة المستخدمين</a></li>
                <li><a href="subscription_manager.php" class="active"><i class="fas fa-crown"></i> مدير الاشتراكات</a></li>
                <li><a href="payments.php"><i class="fas fa-credit-card"></i> المدفوعات</a></li>
                <li><a href="settings.php"><i class="fas fa-cog"></i> الإعدادات</a></li>
                <li><a href="../logout.php"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a></li>
            </ul>
        </aside>

        <main class="main-content">
            <div class="header">
                <h1><i class="fas fa-crown"></i> مدير الاشتراكات</h1>
                <button class="btn" onclick="openModal('planModal')">
                    <i class="fas fa-plus"></i> إضافة خطة جديدة
                </button>
            </div>

            <?php if ($message): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i> <?php echo $message; ?>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-circle"></i> <?php echo $error; ?>
                </div>
            <?php endif; ?>

            <!-- إحصائيات -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number"><?php echo number_format($stats['total_plans']); ?></div>
                    <div class="stat-label">إجمالي الخطط</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo number_format($stats['active_plans']); ?></div>
                    <div class="stat-label">خطط نشطة</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo number_format($stats['total_subscribers']); ?></div>
                    <div class="stat-label">مشتركين نشطين</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo number_format($stats['monthly_revenue'], 2); ?> ر.س</div>
                    <div class="stat-label">إيرادات الشهر</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo number_format($stats['free_users']); ?></div>
                    <div class="stat-label">مستخدمين مجانيين</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo number_format($stats['premium_users']); ?></div>
                    <div class="stat-label">مستخدمين مدفوعين</div>
                </div>
            </div>

            <!-- خطط الاشتراك -->
            <div class="content-section">
                <div class="section-header">
                    <h2 class="section-title"><i class="fas fa-list"></i> خطط الاشتراك</h2>
                </div>

                <div class="plans-grid">
                    <?php foreach ($plans as $plan): ?>
                        <div class="plan-card <?php echo $plan['price'] > 50 ? 'featured' : ''; ?>">
                            <div class="plan-header">
                                <div class="plan-name"><?php echo htmlspecialchars($plan['name']); ?></div>
                                <div class="plan-price"><?php echo number_format($plan['price'], 2); ?> ر.س</div>
                            </div>

                            <p style="color: #ccc; margin-bottom: 1rem;">
                                <?php echo htmlspecialchars($plan['description']); ?>
                            </p>

                            <div style="margin-bottom: 1rem;">
                                <strong>المدة:</strong> <?php echo $plan['duration_days']; ?> يوم
                            </div>

                            <div style="margin-bottom: 1rem;">
                                <strong>المشتركين:</strong> <?php echo $plan['active_subscribers']; ?> نشط من <?php echo $plan['subscribers_count']; ?>
                            </div>

                            <ul class="plan-features">
                                <?php
                                $features = json_decode($plan['features'], true) ?: [];
                                foreach ($features as $feature):
                                    if (trim($feature)):
                                ?>
                                    <li><i class="fas fa-check" style="color: #4CAF50; margin-left: 0.5rem;"></i> <?php echo htmlspecialchars(trim($feature)); ?></li>
                                <?php
                                    endif;
                                endforeach;
                                ?>
                            </ul>

                            <div style="margin: 1rem 0;">
                                <span class="status-badge <?php echo $plan['status'] === 'active' ? 'status-active' : 'status-inactive'; ?>">
                                    <?php echo $plan['status'] === 'active' ? 'نشط' : 'غير نشط'; ?>
                                </span>
                            </div>

                            <div class="plan-actions">
                                <button class="btn btn-secondary" onclick="editPlan(<?php echo htmlspecialchars(json_encode($plan)); ?>)" style="padding: 0.5rem 1rem;">
                                    <i class="fas fa-edit"></i> تعديل
                                </button>

                                <form method="POST" style="display: inline;">
                                    <input type="hidden" name="action" value="toggle_plan_status">
                                    <input type="hidden" name="plan_id" value="<?php echo $plan['id']; ?>">
                                    <input type="hidden" name="new_status" value="<?php echo $plan['status'] === 'active' ? 'inactive' : 'active'; ?>">
                                    <button type="submit" class="btn btn-warning" style="padding: 0.5rem 1rem;">
                                        <i class="fas fa-<?php echo $plan['status'] === 'active' ? 'pause' : 'play'; ?>"></i>
                                    </button>
                                </form>

                                <form method="POST" style="display: inline;" onsubmit="return confirm('هل أنت متأكد من حذف هذه الخطة؟')">
                                    <input type="hidden" name="action" value="delete_plan">
                                    <input type="hidden" name="plan_id" value="<?php echo $plan['id']; ?>">
                                    <button type="submit" class="btn btn-danger" style="padding: 0.5rem 1rem;">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>

            <!-- اشتراكات المستخدمين -->
            <div class="content-section">
                <div class="section-header">
                    <h2 class="section-title"><i class="fas fa-users"></i> اشتراكات المستخدمين</h2>
                </div>

                <table class="subscribers-table">
                    <thead>
                        <tr>
                            <th>المستخدم</th>
                            <th>البريد الإلكتروني</th>
                            <th>الخطة</th>
                            <th>السعر</th>
                            <th>تاريخ البداية</th>
                            <th>تاريخ الانتهاء</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($userSubscriptions as $subscription): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($subscription['full_name'] ?? $subscription['username']); ?></td>
                                <td><?php echo htmlspecialchars($subscription['email']); ?></td>
                                <td><?php echo htmlspecialchars($subscription['plan_name']); ?></td>
                                <td><?php echo number_format($subscription['price'], 2); ?> ر.س</td>
                                <td><?php echo date('Y-m-d', strtotime($subscription['start_date'])); ?></td>
                                <td><?php echo date('Y-m-d', strtotime($subscription['end_date'])); ?></td>
                                <td>
                                    <span class="status-badge status-<?php echo $subscription['status']; ?>">
                                        <?php
                                        switch($subscription['status']) {
                                            case 'active': echo 'نشط'; break;
                                            case 'cancelled': echo 'ملغي'; break;
                                            case 'expired': echo 'منتهي'; break;
                                            default: echo $subscription['status'];
                                        }
                                        ?>
                                    </span>
                                </td>
                                <td>
                                    <?php if ($subscription['status'] === 'active'): ?>
                                        <form method="POST" style="display: inline;" onsubmit="return confirm('هل أنت متأكد من إلغاء هذا الاشتراك؟')">
                                            <input type="hidden" name="action" value="cancel_user_subscription">
                                            <input type="hidden" name="subscription_id" value="<?php echo $subscription['id']; ?>">
                                            <button type="submit" class="btn btn-danger" style="padding: 0.5rem;">
                                                <i class="fas fa-times"></i> إلغاء
                                            </button>
                                        </form>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </main>
    </div>

    <!-- نافذة إضافة/تعديل خطة -->
    <div id="planModal" class="modal">
        <div class="modal-content">
            <button class="close" onclick="closeModal('planModal')">&times;</button>
            <h3 style="color: #E50914; margin-bottom: 2rem;" id="modalTitle">إضافة خطة اشتراك جديدة</h3>

            <form method="POST" id="planForm">
                <input type="hidden" name="action" value="add_plan" id="formAction">
                <input type="hidden" name="plan_id" id="planId">

                <div class="form-group">
                    <label>اسم الخطة</label>
                    <input type="text" name="name" id="planName" required>
                </div>

                <div class="form-group">
                    <label>الوصف</label>
                    <textarea name="description" id="planDescription" required></textarea>
                </div>

                <div class="form-group">
                    <label>السعر (ريال سعودي)</label>
                    <input type="number" name="price" id="planPrice" step="0.01" min="0" required>
                </div>

                <div class="form-group">
                    <label>مدة الاشتراك (بالأيام)</label>
                    <input type="number" name="duration_days" id="planDuration" min="1" required>
                </div>

                <div class="form-group">
                    <label>الميزات (كل ميزة في سطر منفصل)</label>
                    <textarea name="features" id="planFeatures" placeholder="مشاهدة غير محدودة&#10;جودة عالية HD&#10;تحميل للمشاهدة دون إنترنت" required></textarea>
                </div>

                <button type="submit" class="btn">
                    <i class="fas fa-save"></i> حفظ الخطة
                </button>
            </form>
        </div>
    </div>

    <script>
        function openModal(modalId) {
            document.getElementById(modalId).style.display = 'block';
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
            // إعادة تعيين النموذج
            document.getElementById('planForm').reset();
            document.getElementById('formAction').value = 'add_plan';
            document.getElementById('modalTitle').textContent = 'إضافة خطة اشتراك جديدة';
        }

        function editPlan(plan) {
            document.getElementById('modalTitle').textContent = 'تعديل خطة الاشتراك';
            document.getElementById('formAction').value = 'update_plan';
            document.getElementById('planId').value = plan.id;
            document.getElementById('planName').value = plan.name;
            document.getElementById('planDescription').value = plan.description;
            document.getElementById('planPrice').value = plan.price;
            document.getElementById('planDuration').value = plan.duration_days;

            // تحويل الميزات من JSON إلى نص
            const features = JSON.parse(plan.features || '[]');
            document.getElementById('planFeatures').value = features.join('\n');

            openModal('planModal');
        }

        // إغلاق النافذة عند النقر خارجها
        window.onclick = function(event) {
            if (event.target.classList.contains('modal')) {
                event.target.style.display = 'none';
            }
        }

        console.log('مدير الاشتراكات جاهز!');
    </script>
</body>
</html>