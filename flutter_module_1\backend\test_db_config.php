<?php
/**
 * Test Database Configuration
 * Quick test to verify database config is correct
 */

echo "<h1>🗄️ Database Configuration Test</h1>";

echo "<h2>📁 Config File Status:</h2>";

if (file_exists('config/database.php')) {
    echo "<p>✅ <strong>Config file exists:</strong> config/database.php</p>";
    
    try {
        $config = include 'config/database.php';
        
        echo "<h3>📋 Configuration Values:</h3>";
        echo "<table border='1' cellpadding='10' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>Key</th><th>Value</th><th>Status</th></tr>";
        
        $keys = ['host', 'name', 'username', 'password'];
        foreach ($keys as $key) {
            $value = isset($config[$key]) ? $config[$key] : 'NOT SET';
            $status = isset($config[$key]) ? '✅' : '❌';
            $displayValue = ($key === 'password' && !empty($value)) ? '***hidden***' : $value;
            echo "<tr><td><strong>$key</strong></td><td>$displayValue</td><td>$status</td></tr>";
        }
        echo "</table>";
        
        // Test connection
        echo "<h3>🔌 Connection Test:</h3>";
        
        if (isset($config['host']) && isset($config['name']) && isset($config['username'])) {
            try {
                $dsn = "mysql:host={$config['host']};dbname={$config['name']};charset=utf8mb4";
                $pdo = new PDO($dsn, $config['username'], $config['password']);
                $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                
                echo "<p>✅ <strong>Database Connection:</strong> SUCCESS</p>";
                
                // Test tables
                echo "<h3>📊 Tables Check:</h3>";
                $stmt = $pdo->query("SHOW TABLES");
                $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
                
                if (empty($tables)) {
                    echo "<p>⚠️ <strong>Tables:</strong> No tables found (need to run step 3)</p>";
                } else {
                    echo "<p>✅ <strong>Tables found:</strong> " . count($tables) . " tables</p>";
                    echo "<ul>";
                    foreach ($tables as $table) {
                        echo "<li>$table</li>";
                    }
                    echo "</ul>";
                }
                
            } catch (Exception $e) {
                echo "<p>❌ <strong>Database Connection:</strong> FAILED</p>";
                echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";
            }
        } else {
            echo "<p>❌ <strong>Configuration:</strong> Missing required keys</p>";
        }
        
    } catch (Exception $e) {
        echo "<p>❌ <strong>Config file error:</strong> " . $e->getMessage() . "</p>";
    }
    
} else {
    echo "<p>❌ <strong>Config file missing:</strong> config/database.php</p>";
    echo "<p>You need to run step 2 of the installation first.</p>";
}

echo "<h2>🔧 Fix Options:</h2>";

if (!file_exists('config/database.php')) {
    echo "<p>1. <a href='install_simple.php?step=2'>Go to Step 2 - Database Configuration</a></p>";
} else {
    $config = include 'config/database.php';
    if (!isset($config['host']) || !isset($config['name']) || !isset($config['username'])) {
        echo "<p>1. <a href='install_simple.php?step=2'>Reconfigure Database (Step 2)</a></p>";
    } else {
        echo "<p>1. <a href='install_simple.php?step=3'>Continue to Step 3 - Create Tables</a></p>";
    }
}

echo "<p>2. <a href='install_simple.php'>Start Fresh Installation</a></p>";

echo "<h2>🛠️ Manual Fix:</h2>";
echo "<p>If you want to manually edit the config file, it should look like this:</p>";
echo "<pre style='background: #f5f5f5; padding: 15px; border-radius: 5px;'>";
echo htmlspecialchars("<?php
return [
    'host' => 'localhost',
    'name' => 'shahid_db',
    'username' => 'root',
    'password' => 'your_password'
];");
echo "</pre>";

echo "<style>
body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 40px; background: #f5f5f5; }
h1 { color: #E50914; border-bottom: 3px solid #E50914; padding-bottom: 10px; }
h2 { color: #333; margin-top: 30px; border-left: 4px solid #E50914; padding-left: 15px; }
h3 { color: #555; margin-top: 20px; }
p { margin: 10px 0; padding: 8px; background: white; border-radius: 5px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
table { background: white; border-radius: 5px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
th { background: #E50914; color: white; }
td { padding: 8px; }
a { color: #E50914; text-decoration: none; font-weight: bold; }
a:hover { text-decoration: underline; }
pre { background: #f1f1f1; padding: 15px; border-radius: 5px; overflow-x: auto; }
ul { background: white; padding: 20px; border-radius: 5px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
li { margin: 5px 0; }
</style>";
?>
