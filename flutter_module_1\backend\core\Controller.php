<?php
/**
 * Shahid - Base Controller Class
 * Professional Video Streaming Platform
 */

class Controller {
    protected $db;
    protected $config;
    protected $auth;
    protected $security;
    
    public function __construct() {
        $this->config = include 'config/config.php';
        $database = new Database();
        $this->db = $database->getConnection();
        $this->auth = new Auth($this->db);
        $this->security = new Security();
    }
    
    protected function view($view, $data = []) {
        $viewFile = 'views/' . $view . '.php';
        if (!file_exists($viewFile)) {
            throw new Exception("View file not found: " . $viewFile);
        }
        
        // Extract data array to variables
        extract($data);
        
        // Include the view file
        include $viewFile;
    }
    
    protected function json($data, $status = 200) {
        http_response_code($status);
        header('Content-Type: application/json');
        echo json_encode($data);
        exit;
    }
    
    protected function redirect($url) {
        header('Location: ' . $url);
        exit;
    }
    
    protected function requireAuth() {
        if (!$this->auth->isLoggedIn()) {
            $this->redirect('/login');
        }
    }
    
    protected function requireAdmin() {
        if (!$this->auth->isAdmin()) {
            $this->redirect('/admin/login');
        }
    }
    
    protected function validateCSRF() {
        if (!$this->security->validateCSRF()) {
            $this->json(['error' => 'Invalid CSRF token'], 403);
        }
    }
    
    protected function sanitizeInput($input) {
        return $this->security->sanitizeInput($input);
    }
    
    protected function validateInput($data, $rules) {
        return $this->security->validateInput($data, $rules);
    }
}
?>
