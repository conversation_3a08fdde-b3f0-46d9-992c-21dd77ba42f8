<?php
/**
 * إعداد سريع لقاعدة البيانات
 * Quick Database Setup for XAMPP
 */

// تعطيل عرض الأخطاء للمستخدم النهائي
error_reporting(E_ALL);
ini_set('display_errors', 1);

// إعدادات قاعدة البيانات
$dbHost = 'localhost';
$dbUser = 'root';
$dbPass = '';
$dbName = 'shahid_platform';

$setupSteps = [];
$errors = [];
$success = false;

// بدء العملية إذا تم الضغط على زر الإعداد
if (isset($_POST['setup_database'])) {
    try {
        // الخطوة 1: الاتصال بـ MySQL
        $setupSteps[] = "🔌 الاتصال بخادم MySQL...";
        $pdo = new PDO("mysql:host=$dbHost;charset=utf8mb4", $dbUser, $dbPass);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        $setupSteps[] = "✅ تم الاتصال بخادم MySQL بنجاح";
        
        // الخطوة 2: إنشاء قاعدة البيانات
        $setupSteps[] = "🗄️ إنشاء قاعدة البيانات shahid_platform...";
        $pdo->exec("CREATE DATABASE IF NOT EXISTS `$dbName` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        $pdo->exec("USE `$dbName`");
        $setupSteps[] = "✅ تم إنشاء قاعدة البيانات بنجاح";
        
        // الخطوة 3: إنشاء الجداول الأساسية
        $setupSteps[] = "📋 إنشاء الجداول الأساسية...";
        
        // جدول المستخدمين
        $pdo->exec("CREATE TABLE IF NOT EXISTS users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            email VARCHAR(100) NOT NULL UNIQUE,
            password VARCHAR(255) NOT NULL,
            role ENUM('user','admin') DEFAULT 'user',
            subscription_type ENUM('free','basic','premium') DEFAULT 'free',
            subscription_end DATE NULL,
            status ENUM('active','inactive','banned') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_email (email),
            INDEX idx_role (role),
            INDEX idx_status (status)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
        
        // جدول الأفلام
        $pdo->exec("CREATE TABLE IF NOT EXISTS movies (
            id INT AUTO_INCREMENT PRIMARY KEY,
            title VARCHAR(255) NOT NULL,
            slug VARCHAR(255) NOT NULL UNIQUE,
            description TEXT,
            year INT,
            duration INT,
            rating DECIMAL(3,1) DEFAULT 0,
            poster VARCHAR(255),
            backdrop VARCHAR(255),
            trailer_url VARCHAR(255),
            video_url VARCHAR(255),
            director VARCHAR(255),
            cast JSON,
            genres JSON,
            language VARCHAR(10) DEFAULT 'ar',
            country VARCHAR(100),
            views INT DEFAULT 0,
            likes INT DEFAULT 0,
            featured BOOLEAN DEFAULT FALSE,
            status ENUM('draft','published','archived') DEFAULT 'published',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_status (status),
            INDEX idx_featured (featured),
            INDEX idx_year (year),
            INDEX idx_rating (rating)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
        
        // جدول المسلسلات
        $pdo->exec("CREATE TABLE IF NOT EXISTS series (
            id INT AUTO_INCREMENT PRIMARY KEY,
            title VARCHAR(255) NOT NULL,
            slug VARCHAR(255) NOT NULL UNIQUE,
            description TEXT,
            year INT,
            rating DECIMAL(3,1) DEFAULT 0,
            poster VARCHAR(255),
            backdrop VARCHAR(255),
            trailer_url VARCHAR(255),
            total_seasons INT DEFAULT 1,
            total_episodes INT DEFAULT 0,
            director VARCHAR(255),
            cast JSON,
            genres JSON,
            language VARCHAR(10) DEFAULT 'ar',
            country VARCHAR(100),
            views INT DEFAULT 0,
            likes INT DEFAULT 0,
            featured BOOLEAN DEFAULT FALSE,
            status ENUM('draft','published','archived') DEFAULT 'published',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_status (status),
            INDEX idx_featured (featured),
            INDEX idx_year (year)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
        
        // جدول التصنيفات
        $pdo->exec("CREATE TABLE IF NOT EXISTS categories (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            slug VARCHAR(100) NOT NULL UNIQUE,
            description TEXT,
            icon VARCHAR(50),
            color VARCHAR(7) DEFAULT '#E50914',
            sort_order INT DEFAULT 0,
            status ENUM('active','inactive') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_status (status),
            INDEX idx_sort_order (sort_order)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
        
        // جدول التقييمات
        $pdo->exec("CREATE TABLE IF NOT EXISTS ratings (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            content_id INT NOT NULL,
            content_type ENUM('movie','series') NOT NULL,
            rating INT NOT NULL CHECK (rating >= 1 AND rating <= 5),
            review TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
            UNIQUE KEY unique_rating (user_id, content_id, content_type),
            INDEX idx_content (content_id, content_type),
            INDEX idx_rating (rating)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
        
        $setupSteps[] = "✅ تم إنشاء الجداول الأساسية (5 جداول)";
        
        // الخطوة 4: إضافة بيانات تجريبية
        $setupSteps[] = "📊 إضافة البيانات التجريبية...";
        
        // إنشاء حساب المدير
        $adminPassword = password_hash('admin123', PASSWORD_DEFAULT);
        $pdo->prepare("INSERT IGNORE INTO users (name, email, password, role, subscription_type, subscription_end) VALUES (?, ?, ?, 'admin', 'premium', DATE_ADD(NOW(), INTERVAL 10 YEAR))")
            ->execute(['مدير النظام', '<EMAIL>', $adminPassword]);
        
        // إضافة تصنيفات
        $categories = [
            ['اكشن', 'action', 'أفلام ومسلسلات الأكشن والإثارة', '🎬'],
            ['دراما', 'drama', 'أفلام ومسلسلات الدراما', '🎭'],
            ['كوميديا', 'comedy', 'أفلام ومسلسلات الكوميديا', '😂'],
            ['رومانسي', 'romance', 'أفلام ومسلسلات رومانسية', '💕'],
            ['خيال علمي', 'sci-fi', 'أفلام الخيال العلمي', '🚀']
        ];
        
        foreach ($categories as $cat) {
            $pdo->prepare("INSERT IGNORE INTO categories (name, slug, description, icon) VALUES (?, ?, ?, ?)")
                ->execute($cat);
        }
        
        // إضافة أفلام تجريبية
        $movies = [
            ['فيلم الأكشن المثير', 'action-movie-1', 'فيلم أكشن مليء بالإثارة والتشويق', 2023, 120, 4.5],
            ['الدراما العائلية', 'family-drama-1', 'قصة عائلية مؤثرة ومليئة بالمشاعر', 2023, 110, 4.2],
            ['الكوميديا الرائعة', 'comedy-movie-1', 'فيلم كوميدي مضحك للعائلة', 2023, 95, 4.0]
        ];
        
        foreach ($movies as $movie) {
            $pdo->prepare("INSERT IGNORE INTO movies (title, slug, description, year, duration, rating) VALUES (?, ?, ?, ?, ?, ?)")
                ->execute($movie);
        }
        
        $setupSteps[] = "✅ تم إضافة البيانات التجريبية";
        
        // الخطوة 5: إنشاء ملف الإعدادات
        $setupSteps[] = "⚙️ إنشاء ملف الإعدادات...";
        
        if (!is_dir(__DIR__ . '/config')) {
            mkdir(__DIR__ . '/config', 0755, true);
        }
        
        $configContent = "<?php
return [
    'host' => '$dbHost',
    'name' => '$dbName',
    'username' => '$dbUser',
    'password' => '$dbPass'
];";
        
        file_put_contents(__DIR__ . '/config/database.php', $configContent);
        $setupSteps[] = "✅ تم إنشاء ملف الإعدادات";
        
        // الخطوة 6: التحقق النهائي
        $setupSteps[] = "🔍 التحقق من النجاح...";
        $stmt = $pdo->query("SHOW TABLES");
        $tablesCount = $stmt->rowCount();
        
        $stmt = $pdo->query("SELECT COUNT(*) FROM users WHERE role = 'admin'");
        $adminCount = $stmt->fetchColumn();
        
        $setupSteps[] = "✅ تم إنشاء $tablesCount جدول";
        $setupSteps[] = "✅ تم إنشاء $adminCount حساب مدير";
        
        $success = true;
        $setupSteps[] = "🎉 تم إعداد قاعدة البيانات بنجاح!";
        
    } catch (Exception $e) {
        $errors[] = "❌ خطأ: " . $e->getMessage();
        $setupSteps[] = "❌ فشل في الإعداد: " . $e->getMessage();
    }
}

// فحص حالة النظام الحالية
$currentStatus = [
    'xampp_running' => false,
    'mysql_running' => false,
    'database_exists' => false,
    'tables_count' => 0
];

try {
    $pdo = new PDO("mysql:host=$dbHost", $dbUser, $dbPass);
    $currentStatus['mysql_running'] = true;
    
    $stmt = $pdo->query("SHOW DATABASES LIKE '$dbName'");
    if ($stmt->rowCount() > 0) {
        $currentStatus['database_exists'] = true;
        $pdo->exec("USE $dbName");
        $stmt = $pdo->query("SHOW TABLES");
        $currentStatus['tables_count'] = $stmt->rowCount();
    }
} catch (Exception $e) {
    // MySQL غير متاح
}

// فحص Apache
$currentStatus['xampp_running'] = @file_get_contents('http://localhost/') !== false;
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>⚡ إعداد سريع لقاعدة البيانات</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Segoe UI', 'Cairo', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #2d2d2d 100%);
            color: #fff; min-height: 100vh; padding: 2rem;
        }
        .container {
            max-width: 800px; margin: 0 auto;
            background: rgba(47, 47, 47, 0.95); border-radius: 20px; padding: 3rem;
            border: 2px solid rgba(229, 9, 20, 0.3); box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
        }
        .header { text-align: center; margin-bottom: 3rem; padding-bottom: 2rem; border-bottom: 3px solid #E50914; }
        .header h1 { color: #E50914; font-size: 2.5rem; margin-bottom: 1rem; }
        .status-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin: 2rem 0; }
        .status-item { background: rgba(0, 0, 0, 0.3); padding: 1rem; border-radius: 10px; text-align: center; }
        .status-icon { font-size: 2rem; margin-bottom: 0.5rem; }
        .btn { background: linear-gradient(45deg, #E50914, #B8070F); color: white; border: none; padding: 1rem 2rem; border-radius: 10px; cursor: pointer; font-size: 1.1rem; font-weight: bold; transition: all 0.3s ease; text-decoration: none; display: inline-block; margin: 0.5rem; }
        .btn:hover { transform: translateY(-2px); box-shadow: 0 4px 15px rgba(229, 9, 20, 0.3); }
        .btn-success { background: linear-gradient(45deg, #4CAF50, #45a049); }
        .btn-disabled { background: #666; cursor: not-allowed; }
        .setup-log { background: rgba(0, 0, 0, 0.5); border-radius: 10px; padding: 1.5rem; margin: 2rem 0; max-height: 400px; overflow-y: auto; }
        .log-item { padding: 0.5rem 0; border-bottom: 1px solid rgba(255, 255, 255, 0.1); }
        .log-item:last-child { border-bottom: none; }
        .success { color: #4CAF50; }
        .error { color: #F44336; }
        .warning { color: #FF9800; }
        .alert { padding: 1rem; border-radius: 8px; margin: 1rem 0; }
        .alert-success { background: rgba(76, 175, 80, 0.2); border: 1px solid #4CAF50; color: #4CAF50; }
        .alert-error { background: rgba(244, 67, 54, 0.2); border: 1px solid #F44336; color: #F44336; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>⚡ إعداد سريع لقاعدة البيانات</h1>
            <p>إعداد قاعدة بيانات منصة شاهد على XAMPP بنقرة واحدة</p>
        </div>

        <!-- حالة النظام الحالية -->
        <div class="status-grid">
            <div class="status-item">
                <div class="status-icon"><?php echo $currentStatus['xampp_running'] ? '✅' : '❌'; ?></div>
                <div>XAMPP</div>
                <small><?php echo $currentStatus['xampp_running'] ? 'يعمل' : 'متوقف'; ?></small>
            </div>
            <div class="status-item">
                <div class="status-icon"><?php echo $currentStatus['mysql_running'] ? '✅' : '❌'; ?></div>
                <div>MySQL</div>
                <small><?php echo $currentStatus['mysql_running'] ? 'متصل' : 'غير متصل'; ?></small>
            </div>
            <div class="status-item">
                <div class="status-icon"><?php echo $currentStatus['database_exists'] ? '✅' : '⚠️'; ?></div>
                <div>قاعدة البيانات</div>
                <small><?php echo $currentStatus['database_exists'] ? 'موجودة' : 'غير موجودة'; ?></small>
            </div>
            <div class="status-item">
                <div class="status-icon"><?php echo $currentStatus['tables_count'] > 0 ? '✅' : '⚠️'; ?></div>
                <div>الجداول</div>
                <small><?php echo $currentStatus['tables_count']; ?> جدول</small>
            </div>
        </div>

        <?php if (!$currentStatus['mysql_running']): ?>
        <div class="alert alert-error">
            <strong>⚠️ تحذير:</strong> MySQL غير مشغل. يرجى تشغيل XAMPP أولاً.
            <br><a href="http://localhost/xampp/" target="_blank" style="color: #fff;">فتح XAMPP Control Panel</a>
        </div>
        <?php endif; ?>

        <!-- نموذج الإعداد -->
        <?php if ($currentStatus['mysql_running'] && !$success): ?>
        <form method="POST" style="text-align: center; margin: 2rem 0;">
            <button type="submit" name="setup_database" class="btn btn-success" style="font-size: 1.3rem; padding: 1.5rem 3rem;">
                🚀 إعداد قاعدة البيانات الآن
            </button>
            <p style="margin-top: 1rem; color: #ccc;">
                سيتم إنشاء قاعدة البيانات والجداول والبيانات التجريبية تلقائياً
            </p>
        </form>
        <?php endif; ?>

        <!-- سجل الإعداد -->
        <?php if (!empty($setupSteps)): ?>
        <div class="setup-log">
            <h3 style="margin-bottom: 1rem; color: #E50914;">📋 سجل الإعداد:</h3>
            <?php foreach ($setupSteps as $step): ?>
            <div class="log-item"><?php echo $step; ?></div>
            <?php endforeach; ?>
        </div>
        <?php endif; ?>

        <!-- رسائل النجاح -->
        <?php if ($success): ?>
        <div class="alert alert-success">
            <h3>🎉 تم إعداد قاعدة البيانات بنجاح!</h3>
            <p><strong>قاعدة البيانات:</strong> shahid_platform</p>
            <p><strong>حساب المدير:</strong> <EMAIL></p>
            <p><strong>كلمة المرور:</strong> admin123</p>
        </div>

        <div style="text-align: center; margin: 2rem 0;">
            <a href="homepage.php" class="btn btn-success">🏠 الصفحة الرئيسية</a>
            <a href="admin/dashboard.php" class="btn btn-success">🎛️ لوحة الإدارة</a>
            <a href="dashboard_live.php" class="btn btn-success">📊 مراقبة مباشرة</a>
            <a href="http://localhost/phpmyadmin/" target="_blank" class="btn">📊 phpMyAdmin</a>
        </div>
        <?php endif; ?>

        <!-- روابط مفيدة -->
        <div style="text-align: center; margin-top: 3rem; padding-top: 2rem; border-top: 1px solid rgba(255, 255, 255, 0.1);">
            <h4 style="color: #E50914; margin-bottom: 1rem;">🔗 روابط مفيدة:</h4>
            <a href="setup_xampp_database.php" class="btn">📋 دليل الإعداد المفصل</a>
            <a href="system_status.php" class="btn">📊 حالة النظام</a>
            <a href="fix_database.php" class="btn">🔧 إصلاح قاعدة البيانات</a>
        </div>
    </div>

    <script>
        console.log('⚡ أداة الإعداد السريع جاهزة');
        
        // تحديث الصفحة بعد الإعداد الناجح
        <?php if ($success): ?>
        setTimeout(() => {
            if (confirm('هل تريد الانتقال إلى الصفحة الرئيسية؟')) {
                window.location.href = 'homepage.php';
            }
        }, 3000);
        <?php endif; ?>
    </script>
</body>
</html>
